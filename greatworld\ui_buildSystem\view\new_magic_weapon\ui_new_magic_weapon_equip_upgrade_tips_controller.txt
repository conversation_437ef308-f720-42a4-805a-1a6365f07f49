﻿
--- ui_new_magic_weapon_equip_upgrade_tips_controller.txt
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by nyz.
--- DateTime: 
--- desc:    
---
local require = require
local table = table
local newclass = newclass

local event = require "event"
local module_scroll_list = require "scroll_list"
local controller_base = require "controller_base"
local player_mgr = require "player_mgr"
local flow_text = require "flow_text"
local log = require "log"
local game_scheme = require "game_scheme"
local windowMgr = require "ui_window_mgr"
local GWG = GWG
local net_drone_center_module = require "net_drone_center_module"
local lang = require "lang"
local iui_item_detail = require "iui_item_detail"
local item_data = require "item_data"
local evt_sourceSupply_define = require "evt_sourceSupply_define"
module("ui_new_magic_weapon_equip_upgrade_tips_controller")
local controller = nil
local UIController = newclass("ui_new_magic_weapon_equip_upgrade_tips_controller", controller_base)

--[[窗口初始化]]
function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self,view_name,controller_name) 
    self.data = data;
    self:TriggerUIEvent( "InitPanelData",data)
end

--[[界面被显示的时候调用]]
function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close()
    self.__base.Close(self)
    controller = nil
end

--会基类自动调用
function UIController:AutoSubscribeEvents()
    -- 注意 事件建议使用self:RegisterEvent(event_name,func)来注册，不需要手动维护注销了 
    self.updatePanel = function()
        log.Warning("刷新物品")
        self:TriggerUIEvent( "InitPanelData",self.data)
    end
    self:RegisterEvent(event.UPDATE_MAGIC_WEAPON,self.updatePanel)
    self:RegisterEvent(event.UPDATE_MAGIC_WEAPON_ITEM,self.updatePanel)
end
--会基类自动调用
function UIController:AutoUnsubscribeEvents()   

end
---********************功能函数区**********---

function UIController:OnUpgradeBtnClick()
    local sendData =
    {
        nDroneId = GWG.GWHomeMgr.droneData.GetDroneId(),
        nPartType = self.data.slotIndex + 1
    }
    net_drone_center_module.MSG_DRONECENTER_PARTUPGRADE_REQ(sendData)
end

function UIController:OnSynthesisBtnClick()
    local slot = GWG.GWHomeMgr.droneData.OnGetEquipLevelList()[self.data.slotIndex]
    local needCount = slot.result.getItemCount
    local slotCfg = GWG.GWHomeMgr.droneData.GetEquipCfgData(self.data.slotIndex)
    if slot.result.canSynthesis then
        -- - player_mgr.GetPlayerOwnNum(slotCfg.costGoods.data[0])
        local sendData =
        {
            slotIndex = self.data.slotIndex,
            itemIndex = slotCfg.costGoods.data[0],
            itemCount = needCount,
            isDecompose = slot.result.isDecompose,
        }
        windowMgr:ShowModule("ui_new_magic_weapon_equip_synthesis_tips",nil,nil,sendData)
    else
        --local needData = {
        --    list = {},
        --}
        --table.insert(needData.list,{
        --    goodsId = slotCfg.costGoods.data[0],
        --    needNum = 1
        --})
        --event.Trigger(evt_sourceSupply_define.Evt_ShowSupplyPanel,needData);
        flow_text.Add(lang.Get(602562))
    end
end

function UIController:OnGrayBtnClick()
    local slot = GWG.GWHomeMgr.droneData.OnGetEquipLevelList()[self.data.slotIndex]
    local slotCfg = GWG.GWHomeMgr.droneData.GetEquipCfgData(self.data.slotIndex)
    local needData = {
        list = {},
    }
    table.insert(needData.list,{
        goodsId = slotCfg.costGoods.data[0],
        needNum = slotCfg.costGoodsNum.data[0]
    })
    event.Trigger(evt_sourceSupply_define.Evt_ShowSupplyPanel,needData);
    --flow_text.Add(lang.Get(384255))
end

function UIController:OnBtn_closeBtnClickedProxy()
    windowMgr:UnloadModule("ui_new_magic_weapon_equip_upgrade_tips")
end

function UIController:OnShowOldEquipInfo()
    local slot = GWG.GWHomeMgr.droneData.GetEquipCfgData(self.data.slotIndex)
    iui_item_detail.Show(slot.costGoods.data[0], nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil)
end

function UIController:OnShowNewEquipInfo()
    local slotLv = GWG.GWHomeMgr.droneData.OnGetEquipLevelList()[self.data.slotIndex]
    local nextSlotCfg = game_scheme:MagicWeapon_0(GWG.GWHomeMgr.droneData.GetDroneId(),self.data.slotIndex + 1,slotLv.lv + 1,0)
    iui_item_detail.Show(nextSlotCfg.costGoods.data[0], nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil)
end

function UIController:OnShowCostEquipInfo()
    local slot = GWG.GWHomeMgr.droneData.GetEquipCfgData(self.data.slotIndex)
    iui_item_detail.Show(slot.costGoods.data[0], nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil)
end
---********************end功能函数区**********---
--region ModuleFunction 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
