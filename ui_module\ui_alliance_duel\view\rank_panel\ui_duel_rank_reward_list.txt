local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local type = type
local Common_Util = CS.Common_Util.UIUtil
local ui_window_mgr = require "ui_window_mgr"
local iui_item_detail = require "iui_item_detail"
local item_data = require "item_data"
local goods_item_new = require "goods_item_new"
local reward_mgr = require "reward_mgr"
local game_scheme = require "game_scheme"
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local class = require "class"
local binding = require "ui_duel_rank_reward_list_binding"
local alliance_duel_data = require "alliance_duel_data"
local util = require "util"
local GameObject = CS.UnityEngine.GameObject
local typeof = typeof
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local LayoutRebuilder = CS.UnityEngine.UI.LayoutRebuilder

--region View Life
module("ui_duel_rank_reward_list")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)

    self.VData = {}
   
    self.baseHeight = self.rtf_listLayout.sizeDelta.y
    self.fixHeight = self.rtf_listLayout.sizeDelta.y - 70
    
    self:InitScrollView()
    ---@field 应该显示哪个面板
    self.panelArr = {
        [1] = self.rtf_rankPanel,
        [2] = self.rtf_danPanel,
        [3] = self.rtf_leaguePanel
    }
    ---@field 段位显示面板是否初始化过了
    self.isDanInit = false
    ---@field 排行显示面板是否初始化过了
    self.isRankInit = false
    ---@field 对决联赛是否已经初始化
    self.isLeagueInit = false

    self.rank = -1 --当前显示的组别，没有参加对决联赛的联盟，此字段无意义
    self.showPage = 1
    
    self.showMatch = alliance_duel_data.GetDuelRoundID() or 0
    Common_Util.SetActive(self.obj_leagueTog,self.showMatch ~= 0)
    if self.showMatch ~= 0 then
        self.rank = alliance_duel_data.GetBattleInfoRank() --初始化时取自己的组别
        Common_Util.SetActive(self.obj_silverTips,self.rank == 1)
        Common_Util.SetActive(self.obj_goldTips,self.rank == 2)
        Common_Util.SetActive(self.obj_diamondTips,self.rank == 3)
        self.tog_silver.isOn = self.rank == 1
        self.tog_gold.isOn = self.rank == 2
        self.tog_diamond.isOn = self.rank == 3
        Common_Util.SetActive(self.obj_silverUnselect,self.rank ~= 1)
        Common_Util.SetActive(self.obj_silverSelect,self.rank == 1)
        Common_Util.SetActive(self.obj_goldUnselect,self.rank ~= 2)
        Common_Util.SetActive(self.obj_goldSelect,self.rank == 2)
        Common_Util.SetActive(self.obj_diamondUnselect,self.rank ~= 3)
        Common_Util.SetActive(self.obj_diamondSelect,self.rank == 3)
        Common_Util.SetSize(self.rtf_listLayout,710,self.fixHeight)
        Common_Util.SetActive(self.tog_leagueToggle,true)
    else
        Common_Util.SetSize(self.rtf_listLayout,710,self.baseHeight)
        Common_Util.SetActive(self.tog_leagueToggle,false)
    end
    self.isInit = true
    self.LeagueObj = {}
    self.leagueCache = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base:OnHide()
end

function UIView:Close()   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    if self.srt_danContent then
        self.srt_danContent:ItemsDispose()
    end
    self.isDanInit = false
    self.isRankInit = false
    
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic


function UIView:InitScrollView()
    self.srt_danContent.onItemRender = function(scroll_rect_item, index, dataItem)
        self:DanOnItemRender(scroll_rect_item, index, dataItem)
    end

    self.srt_danContent.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data then
            --rect_table Dispose时 Item上相关的资源是否需要Dispose     
            if scroll_rect_item.data.goodItemArr then
                for _, v in pairs(scroll_rect_item.data.goodItemArr) do
                    v:Dispose()
                end
                scroll_rect_item.data.goodItemArr = nil
            end
        end
    end
    
    self.srt_rankContent.onItemRender = function(scroll_rect_item, index, dataItem)
        self:RankOnItemRender(scroll_rect_item, index, dataItem)
    end
    
    self.srt_rankContent.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data then
            
            --rect_table Dispose时 Item上相关的资源是否需要Dispose     
            if scroll_rect_item.data.goodItemArr then
                for _, v in pairs(scroll_rect_item.data.goodItemArr) do
                    v:Dispose()
                end
                scroll_rect_item.data.goodItemArr = nil
            end
        end
    end
    
end

function UIView:SetRankReward(rank)
    if not self.isInit then
        return
    end
    self.rank = rank
    --rank发生变化，所以所有的界面都要重新设置
    self.isDanInit = false
    self.isRankInit = false
    self.isLeagueInit = false
    
    if self.showPage == 1 then
        self:RefreshRankReward()
    elseif self.showPage == 2 then
        self:RefreshNoDanReward()
    elseif self.showPage == 3 then
        self:RefreshLeagueReward()
    end
end

--region 段位奖励分页
---@public 初始化无段位时的段位奖励列表
function UIView:RefreshNoDanReward()
    --固定显示
    local rewardList = {}
    if self.showMatch ~= 0 then
        rewardList = {
            [1] = {id = 29 - self.rank * 3},
            [2] = {id = 30 - self.rank * 3},           
            [3] = {id = 31 - self.rank * 3}
        }
    else
        rewardList = {
            [1] = { id = 17, },
            [2] = { id = 18, },
            [3] = { id = 19, }
        }
    end
    
    if not self.isDanInit then
        local len = #rewardList
        self.srt_danContent:SetData(rewardList, len)
        self.srt_danContent:Refresh(0, -1)
        self.isDanInit = true
    end
end
---@public 段位奖励刷新列表
function UIView:DanOnItemRender(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or { index, dataItem }
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    
    local titleBg = scroll_rect_item:Get("titleBg")
    local title = scroll_rect_item:Get("title")
    local tipBtn = scroll_rect_item:Get("tipBtn")
    local rewardParent = scroll_rect_item:Get("rewardParent")
    
    local cfg = game_scheme:AllianceDuelRewards_0(dataItem.id)
    
    if cfg then
        
        if cfg.subType.data[0] == 1 then
            titleBg:Switch(0)
            title.text = lang.Get(1000794)
        elseif cfg.subType.data[0] == 2 then
            titleBg:Switch(0)
            title.text = lang.Get(1000795)
        elseif cfg.subType.data[0] == 3 then
            titleBg:Switch(1)
            title.text = lang.Get(1000796)
        end
        
        if not scroll_rect_item.data.goodItemArr then
            scroll_rect_item.data.goodItemArr = {}    
        end
        
        local rewardData = reward_mgr.GetRewardGoodsList(cfg.RewardID)
        for _, v in ipairs(rewardData) do
            local goodItem = scroll_rect_item.data.goodItemArr[v.id] or goods_item_new.CGoodsItem()
            goodItem:Init(rewardParent.transform, nil, 0.64)
            goodItem:SetGoods(nil, v.id, v.num, function()
                iui_item_detail.Show(v.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, v.num, nil, nil)
            end)
            goodItem:SetCountEnable(true)
            scroll_rect_item.data.goodItemArr[v.id] = goodItem
        end

        LayoutRebuilder.ForceRebuildLayoutImmediate(rewardParent.transform)
        local tipStr = string.format2(lang.Get(cfg.TipsLang),cfg.PointLimit.data[1])
        tipBtn.onClick:RemoveAllListeners()
        tipBtn.onClick:AddListener(function()
            --测试
            local data = {
                target = tipBtn,--目标UI
                content = {
                    [1] = { str_Name = tipStr },--一条内容元素
                }
            }
            ui_window_mgr:ShowModule("ui_pop_tips", nil, nil, data)
        end)
    end
end
--endregion

--region 排行奖励分页

---@public 刷新无段位时的排名奖励
function UIView:RefreshRankReward()
    local rankRewardList = {}
    if self.showMatch ~= 0 then
        rankRewardList = {
            [1] = { id = 17 - self.rank * 4 },
            [2] = { id = 18 - self.rank * 4 ,isTree = false },
            [3] = { id = 18 - self.rank * 4 ,isTree = true },
            [4] = { id = 19 - self.rank * 4 },
            [5] = { id = 20 - self.rank * 4 }
        }
    else
        rankRewardList = {
            [1] = { id = 1 },
            [2] = { id = 2 ,isTree = false },
            [3] = { id = 2 ,isTree = true },
            [4] = { id = 3 },
            [5] = { id = 4 }
        }
    end
    
    if not self.isRankInit then
        local len = #rankRewardList
        self.srt_rankContent:SetData(rankRewardList, len)
        self.srt_rankContent:Refresh(0, -1)
        self.isRankInit = true
    end
end

---@public 初始化排名奖励列表
function UIView:RankOnItemRender(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or { index, dataItem }
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    
    local bg = scroll_rect_item:Get("bg")
    local rankIcon = scroll_rect_item:Get("rankIcon")
    local rankingText = scroll_rect_item:Get("rankingText")
    local rtf_rewardParent = scroll_rect_item:Get("rtf_rewardParent")

    local cfg = game_scheme:AllianceDuelRewards_0(dataItem.id)

    if cfg then
        local rank = cfg.PersonalRank.data[0]
        --特殊处理要把排名 2-3 拆分成 2和3
        if rank == 2 and dataItem.isTree then
            rank = 3
        end

        if rank < 4 then
            bg:Switch(rank)
            rankIcon:Switch(rank-1)
            self:SetActive(rankIcon, true)
            self:SetActive(rankingText, false)
        else
            bg:Switch(4)
            rankingText.text = string.format("%s-%s",cfg.PersonalRank.data[0],cfg.PersonalRank.data[1])
            self:SetActive(rankIcon, false)
            self:SetActive(rankingText, true)
        end 
        
        --刷新奖励列表
        if not scroll_rect_item.data.goodItemArr then
            scroll_rect_item.data.goodItemArr = {}
        end

        local rewardData = reward_mgr.GetRewardGoodsList(cfg.RewardID)
        local maxIndex = 0
        for i, v in ipairs(rewardData) do
            local goodItem = scroll_rect_item.data.goodItemArr[i] or goods_item_new.CGoodsItem():Init(rtf_rewardParent.transform, nil, 0.64)
            goodItem:SetGoods(nil, v.id, v.num, function()
                iui_item_detail.Show(v.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, v.num, nil, nil)
            end)
            goodItem:SetCountEnable(true)
            scroll_rect_item.data.goodItemArr[i] = goodItem
            maxIndex = i
        end
        
        for i, v in ipairs(scroll_rect_item.data.goodItemArr) do
            if i > maxIndex then
                v:Dispose()
            end
        end
    end
    
end

function UIView:RefreshLeagueReward()
    if not self.isLeagueInit then
        local rankRewardList = {
            [1] = {{id = 56 - self.rank * 9},{id = 57 - self.rank * 9},{id = 58 - self.rank * 9}},
            [2] = {{id = 59 - self.rank * 9},{id = 60 - self.rank * 9},{id = 61 - self.rank * 9}},
            [3] = {{id = 62 - self.rank * 9},{id = 63 - self.rank * 9},{id = 64 - self.rank * 9}},
        }
        local allianceBattleRank = alliance_duel_data.GetBattleInfoRank()
        local myAllianceRank = alliance_duel_data.GetMyAllianceRank()
        local myRank = alliance_duel_data.GetMyRank()
        local targetScore = 0
        for i,v in ipairs(rankRewardList) do
            local cfg = game_scheme:AllianceDuelRewards_0(v[1].id) --取第一个作为显示基准
            if not self.LeagueObj[i] then
                local go = GameObject.Instantiate(self.obj_ListItem,self.rtf_rankContent)
                self.LeagueObj[i] = {
                    baseItem = Common_Util.GetComponent(go.transform,typeof(ScrollRectItem)),
                    item = {}
                }
            end
            if self.LeagueObj[i] and cfg then
                local scroll_rect_item = self.LeagueObj[i].baseItem
                Common_Util.SetActive(scroll_rect_item,true)
                local titleTog = scroll_rect_item:Get("titleTog")
                local arrow = scroll_rect_item:Get("arrow")
                local Title = scroll_rect_item:Get("Title")
                local layoutBg = scroll_rect_item:Get("layoutBg")
                local RankObj = scroll_rect_item:Get("RankObj")
                local titleTogSS = scroll_rect_item:Get("titleTogSS")

                Common_Util.SetActive(arrow,self.leagueCache[i] == true)
                Common_Util.SetActive(layoutBg,self.leagueCache[i] == true)
                titleTog.onValueChanged:RemoveAllListeners()
                titleTog.isOn = self.leagueCache[i] == true
                titleTog.onValueChanged:AddListener(function(value)
                    Common_Util.SetActive(arrow,value == true)
                    Common_Util.SetActive(layoutBg,value == true)
                    if value then
                        self.leagueCache[i] = true
                    else
                        self.leagueCache[i] = false
                    end
                    LayoutRebuilder.ForceRebuildLayoutImmediate(self.rtf_rankContent)
                end)
                local isMyAllianceRank = false
                if cfg.subType.count > 1 then
                    Title.text = string.format2(lang.Get(1000777),cfg.subType.data[0],cfg.subType.data[1])
                    if myAllianceRank <= cfg.subType.data[1] and myAllianceRank >= cfg.subType.data[0] then
                        if allianceBattleRank == self.rank then
                            titleTogSS:Switch(1)
                            isMyAllianceRank = true
                        else
                            titleTogSS:Switch(0)
                        end
                        targetScore = cfg.PointLimit.data[1]
                    else
                        titleTogSS:Switch(0)
                    end
                else
                    Title.text = string.format2(lang.Get(1000776),cfg.subType.data[0])
                    if allianceBattleRank == self.rank and myAllianceRank == cfg.subType.data[0] then
                        if allianceBattleRank == self.rank then
                            titleTogSS:Switch(1)
                            isMyAllianceRank = true
                        else
                            titleTogSS:Switch(0)
                        end
                        targetScore = cfg.PointLimit.data[1]
                    else
                        titleTogSS:Switch(0)
                    end
                end
                for j,k in ipairs(v) do
                    if not self.LeagueObj[i].item[j] then
                        local go = GameObject.Instantiate(RankObj,layoutBg)
                        self.LeagueObj[i].item[j] = {
                            baseItem = Common_Util.GetComponent(go.transform,typeof(ScrollRectItem)),
                            goodItemArr = {}
                        }
                    end
                    local scroll_rect_item1 = self.LeagueObj[i].item[j].baseItem
                    Common_Util.SetActive(scroll_rect_item1,true)
                    local bg = scroll_rect_item1:Get("Bg")
                    local RankText = scroll_rect_item1:Get("Rank")
                    local RewardParent = scroll_rect_item1:Get("rtf_rewardParent")
                    local cfgData = game_scheme:AllianceDuelRewards_0(k.id)
                    local rewardData = reward_mgr.GetRewardGoodsList(cfgData.RewardID)
                    local maxIndex = 0
                    for l, m in ipairs(rewardData) do
                        local goodItem = self.LeagueObj[i].item[j].goodItemArr[l] or goods_item_new.CGoodsItem():Init(RewardParent, nil, 0.64)
                        goodItem:SetGoods(nil, m.id, m.num, function()
                            iui_item_detail.Show(m.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, m.num, nil, nil)
                        end)
                        goodItem:SetCountEnable(true)
                        self.LeagueObj[i].item[j].goodItemArr[l] = goodItem
                        maxIndex = l
                    end
                    for l, m in ipairs(self.LeagueObj[i].item[j].goodItemArr) do
                        if l > maxIndex then
                            m:Dispose()
                        end
                    end
                    
                    if isMyAllianceRank and myRank <= cfgData.PersonalRank.data[1] and myRank >= cfgData.PersonalRank.data[0] then
                        bg:Switch(1)
                    else
                        bg:Switch(0)
                    end
                    RankText.text = string.format2("{%s1}-{%s2}",cfgData.PersonalRank.data[0],cfgData.PersonalRank.data[1])
                end
            end
        end
        self.isLeagueInit = true
        local myScore = alliance_duel_data.GetMyScore()
        local scoreColor = myScore >= targetScore and "#319F38" or "#cd2723"
        local alliance_ui_util = require "alliance_ui_util"
        local myScoreTxt = string.format2("<color={%s1}>{%s2}</color>",scoreColor,alliance_ui_util.KiloSeparator(myScore))
        self.txt_leagueTips.text = string.format2(lang.Get(1000775),myScoreTxt,alliance_ui_util.KiloSeparator(targetScore))
    end

end

--endregion


---@public 切换面板
function UIView:SwitchPanel(index)
    for i, v in ipairs(self.panelArr) do
        self:SetActive(v, i == index)
    end
    --self.panelArr[index]:SetActive(true)
    
    if index == 1 then
        self.txt_title.text = lang.Get(1001071)
    elseif index == 2 then
        self.txt_title.text = lang.Get(1001069)
    elseif index == 3 then
        self.txt_title.text = lang.Get(1001070)
    end
    self.showPage = index
end



--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
		window.isBlurBg = true

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, true,true)
        else
			window:LoadUIResource(ui_path, nil, nil, nil, true,true)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
