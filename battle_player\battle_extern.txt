-- battle_extern.txt ------------------------------------------
-- author:  梁骐显
-- date:    2022.2.24
-- ver:     1.0
-- desc:    外部使用
--------------------------------------------------------------
local require = require
local string = string
local tostring = tostring
local pairs = pairs
local ipairs = ipairs
local table = table
local print = print
local tonumber = tonumber
local PlayerPrefs         = CS.UnityEngine.PlayerPrefs
local flow_text = require "flow_text"
module("battle_extern")
local message_box             = require "message_box"
local lang                    = require "lang"
local game_scheme             = require "game_scheme"
local common_pb               = require "common_new_pb"
local windowMgr               = require "ui_window_mgr"
local event                   = require "event"
local util                    = require "util"
local player_mgr              = require "player_mgr"
local battle_data             = require "battle_data"
local weapon_data             = require "weapon_data"
local log                     = require "log"
local gw_hero_mgr                = require "gw_hero_mgr"
local cfg_util = require "cfg_util"
local ui_select_hero = require "ui_select_hero"
local battle_event_report = require "battle_event_report"
local scene_manager = require "scene_manager"
local ui_window_mgr = require "ui_window_mgr"
local menu_bot_data = require "menu_bot_data"
local battle_manager = require "battle_manager"
local ui_select_model_node = require "ui_select_model_node"
local hero_trial_mgr = require "hero_trial_mgr"
local gw_power_mgr = require "gw_power_mgr"
--=============================================================================
--============================统一设置战斗信息 START ============================
--=============================================================================

--- 设置武器(战斗类型，队伍，竞技场类型,多队伍)。
--- 注意：这个函数的参数 line 以 0 为开始下标，这意味着第一队的下标是 0，所以外部调用的时候第二队的下标是 1，不是 2，函数内部处理的时候会自己 +1。
---2024.12.10 原神器功能已经废弃，替换为全新的召唤兽系统，所以该模块全部注释！但考虑到扎根过深，所以此处一律返回0，视为没有穿戴。
function CommonSetupWeapon(battle_type,line,arenaType,multiLine,noUpdate,noUse)
    return 0

end

--设置怪物队伍（怪物teamID，队伍，碾压）
function SetupMonsterTeam(teamID,line,overPower)
    if not line then
        line = 1
    end
    if teamID then
        ui_select_hero.SetEnemyByMonsterTeam(teamID,nil,nil,line)
        local cfg_monster2 = game_scheme:monsterTeam_0(teamID)
        if cfg_monster2 then
            ui_select_hero.SetGrindThreshold(nil,cfg_monster2.Power,line)
        end
        if line > 1 then
            local twoData = ui_select_hero.GetHeroFormation(line)
            ui_select_hero.SetSaveHeroData(twoData,nil,nil,nil,line)
        end
    end
end

function NewShowArenaHeroSelectPanel(onFightCallback,onFightCloseCallback,onModuleShow)
    local menu_bot_data = require "menu_bot_data"
    local win = ui_window_mgr:ShowModule("ui_select_hero",onModuleShow)
    if win ~= nil then
        win:SetCloseEvent(function(window)
            if window ~= nil then
                window.onCloseEvent = nil
                window.onFightEvent = nil
                ui_window_mgr:UnloadModule("ui_select_hero")
                if onFightCloseCallback then
                    onFightCloseCallback(window)
                end
            end
        end)
        
        win:SetFightEvent(function(window,isGrind)
            if window ~= nil then
                
                --如果没有上阵英雄，不能开始战斗
                local heroList = window:GetSelectedHero()
               --[[ if not heroList or table.count(heroList) == 0 then
                    flow_text.Add("还没有上阵英雄，不能开始战斗(临时配置，需要策划配多语言!!)")
                    return
                end]]
                
                if onFightCallback then
                    local result = onFightCallback(window,isGrind)
                    if result then
                        return
                    end
                end
                if not isGrind then
                    ui_window_mgr:UnloadModule("ui_select_hero")
                    window.onFightEvent = nil
                    window.onCloseEvent = nil
                end
                CloseSlgUI()
                menu_bot_data.CloseAllPage()
            end
        end)
        
    end
    return win
end

--打开出战界面通用方法（不战斗关闭回调，战斗回调）
function ShowSelectHeroModule(onFightCallback,onFightCloseCallback,onModuleShow)
    local menu_bot_data = require "menu_bot_data"
    local win = ui_window_mgr:ShowModule("ui_select_hero",onModuleShow)

    if win ~= nil then
        win.onCloseEvent = function(window)
            if window ~= nil then
                window.onCloseEvent = nil
                window.onFightEvent = nil
                ui_window_mgr:UnloadModule("ui_select_hero")
                if onFightCloseCallback then
                    onFightCloseCallback(window)
                end
            end
        end

        win.onFightEvent = function(window,isGrind)
            if window ~= nil then
                if onFightCallback then
                    local result = onFightCallback(window,isGrind)
                    if result then
                        return
                    end
                end
                if not isGrind then
                    ui_window_mgr:UnloadModule("ui_select_hero")
                    window.onFightEvent = nil
                    window.onCloseEvent = nil
                end
                menu_bot_data.CloseAllPage()
            end
        end
    end
    return win
end



--=============================================================================
--============================统一设置战斗信息 END ============================
--=============================================================================

--位面战纪（主线战斗）
function SetupMainStoryChallenge(level)
    battle_data.SetReturnSelectFunc(function()
        SetupMainStoryChallenge(level)
    end)
    local story_mgr = require "story_mgr"
    story_mgr.SetCacheLevel(level)
    story_mgr.SetBattleType(common_pb.GameGoal)
    battle_manager.SetLastBattle(level) --记录
    battle_data.skipBattle = false
    local hook_hero_data = require "hook_hero_data"
    local hData = hook_hero_data.GetSaveHeroData()
    ui_select_hero.SetSaveBattleType(common_pb.GameGoal)
    ui_select_hero.SetIgnoreSoldier(true)
    
    ui_select_hero.SetAdjustBtnVisible(true,true)
    ui_select_model_node.SetBattleType(common_pb.GameGoal)
    ui_select_model_node.SetHookLevelGrade(level)

    local laymain_data = require "laymain_data"
    local maxID = PlayerPrefs.GetInt(player_mgr.GetPlayerRoleID().."NewHookScene_MaxID")
    if not maxID or maxID and maxID <= laymain_data.GetHangLevel() then
        --记录当前最大挂机关卡数据
        PlayerPrefs.SetInt(player_mgr.GetPlayerRoleID().."NewHookScene_MaxID", laymain_data.GetHangLevel())
    end
    --设置敌方数据
    local targetCfg = level and game_scheme:HookLevel_0(level)
    local levelCfg = targetCfg or laymain_data.GetNextLevelCfg( )
    ui_select_hero.SetLineUpState(levelCfg.CloseLineup and levelCfg.CloseLineup == 1 or false)
    if levelCfg then
        if levelCfg.AutoLineup and not string.empty(levelCfg.AutoLineup) then
            local npcListData = cfg_util.StringToArray(levelCfg.AutoLineup,";","#")
            local temp = {}
            for i,v in pairs(npcListData) do
                local value =
                {
                    pos = tonumber(v[2]),
                    heroId = tonumber(v[1]),
                }
                table.insert(temp,value);
            end
            ui_select_hero.SetNpcHero(temp);
        else
            --for i=1,4 do
            --    local tempHeroData = ui_select_hero.GetHeroFormation(i)
            --    ui_select_hero.SetSaveHeroData(tempHeroData,nil,nil,nil,i)
            --end
            ui_select_hero.SetSaveHeroData(hData) --这里使用的是服务器储存的挂机队列数据，如果需要本地数据，就把上面的放出来
        end
    end
    
    --是否是多队
    local isMulti = 1
    if levelCfg then
        ui_select_hero.SetEnemyByMonsterTeam(levelCfg.ranksID.data[0],nil,nil,1)
        local cfg_monster = game_scheme:monsterTeam_0(levelCfg.ranksID.data[0])
        local EnemyFaceID = cfg_monster and cfg_monster.faceID or 0


        local sand_attack_define = require "sand_attack_define"
        local soldierID, soldierNum = sand_attack_define.GetMonsterSoldierData(cfg_monster)
        local enemyInfo = {
            soldierID = soldierID,
            soldierNum = soldierNum,
        }
        battle_data.SetEnemyInfo(enemyInfo)
        battle_data.SetIsCancelOpenBattleAni(true)
        ui_select_hero.SetTopPowerData(true, targetCfg.checkNumber, EnemyFaceID)
        if cfg_monster and levelCfg.overpowerCERatio and levelCfg.overpowerCERatio > 0 then
            ui_select_hero.SetGrindThreshold(levelCfg.overpowerCERatio / 10000,cfg_monster.Power)
        end

        --多队战纪  不处理碾压问题
        if levelCfg.ranksID.data[1] then
            isMulti = 2
            SetupMonsterTeam(levelCfg.ranksID.data[1],2)
        end
        if levelCfg.ranksID.data[2] then
            isMulti = 3
            SetupMonsterTeam(levelCfg.ranksID.data[2],3)
        end
        if levelCfg.ranksID.data[3] then
            isMulti = 4
            SetupMonsterTeam(levelCfg.ranksID.data[3],4)
        end
    end
    --初始化多队结算战斗数据
    local ui_maze_multi_battle_result = require "ui_maze_multi_battle_result"
    ui_maze_multi_battle_result.ResetMultiBattle()
    ui_maze_multi_battle_result.ResetMercenaryData()

    -- = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
    -- 【注意】
    -- 函数 CommonSetupWeapon 的参数 line 以 0 为开始下标，这意味着第一队的下标是 0。
    -- 该函数内部处理的时候会自己 +1
    -- 所以调用传参的时候第二队的下标是 1，不是 2，不是 2，不是 2！！！
    -- = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
    CommonSetupWeapon(common_pb.GameGoal)
    if levelCfg.ranksID.data[1] then
        CommonSetupWeapon(common_pb.GameGoal,1)
    end
    if levelCfg.ranksID.data[2] then
        CommonSetupWeapon(common_pb.GameGoal,2)
    end
    if levelCfg.ranksID.data[3] then
        CommonSetupWeapon(common_pb.GameGoal,3)
    end
    -- 主线神器按钮
    --local ui_bs_arena_hero_select_panel = require "ui_bs_arena_hero_select_panel"
    --ui_select_hero.CheckIsShowArtifactInMainStory()
    local unforced_guide_mgr = require "unforced_guide_mgr"
    unforced_guide_mgr.SetBattleType(unforced_guide_mgr.BATTLE_TYPE_NUM.MAIN_STORY)

    local window = NewShowArenaHeroSelectPanel(function(window,isGrind)
        --进入战斗回调

        if isGrind then
            CloseSlgUI()
            menu_bot_data.CloseAllPage()
            local ui_battle_grind_victory = require "ui_battle_grind_victory"
            ui_battle_grind_victory.SetOldLevel(laymain_data.GetPassLevel(),common_pb.GameGoal)
        end
        local hasHelpHero = ui_select_hero.GetHelpHero()
        local selectedHero = window:GetSelectedHero()
        if not hasHelpHero then
            --这里是向服务器发送消息来储存挂机队列
            hook_hero_data.SaveHookBattleHeroData(selectedHero)
        end
        local battleData = {
            stageType = common_pb.GameGoal,
            stageLevel = level,
            heroData = selectedHero,
            grind = isGrind,
        }
        battleData.multiHeroData = {}
        battleData.multiHeroData[1] = {}
        battleData.multiHeroData[1].hero = selectedHero
        local wID1 = 0--weapon_data.GetLocalizedWeaponData(common_pb.GameGoal,0)
        battleData.multiHeroData[1].weaponId = wID1
        ui_select_hero.SetHeroFormation(selectedHero,1)
        --设置第二队阵容
        
        if levelCfg.ranksID.data[1] then
            battleData.multiHeroData[2] = {}
            local selectedHero2 = window:GetSelectedHero2()
            battleData.multiHeroData[2].hero = selectedHero2
            local wID2 = 0--weapon_data.GetLocalizedWeaponData(common_pb.GameGoal,1)
            battleData.multiHeroData[2].weaponId = wID2
            ui_select_hero.SetHeroFormation(selectedHero2,2)
        end

        --设置第三队阵容
        if levelCfg.ranksID.data[2] then
            battleData.multiHeroData[3] = {}
            local selectedHero3 = window:GetSelectedHero3()
            battleData.multiHeroData[3].hero = selectedHero3
            local wID3 = 0--weapon_data.GetLocalizedWeaponData(common_pb.GameGoal,2)
            battleData.multiHeroData[3].weaponId = wID3
            ui_select_hero.SetHeroFormation(selectedHero3,3)
        end

         --设置第四队阵容
         if levelCfg.ranksID.data[3] then
            battleData.multiHeroData[4] = {}
            local selectedHero4 = window:GetSelectedHero4()
            battleData.multiHeroData[4].hero = selectedHero4
            local wID3 = 0--weapon_data.GetLocalizedWeaponData(common_pb.GameGoal,3)
            battleData.multiHeroData[4].weaponId = wID3
             ui_select_hero.SetHeroFormation(selectedHero4,4)
        end
        battle_manager.SendBattleChallengeMessage(battleData)
        battle_manager.StartWaitTimer()
        local ceRate = laymain_data.GetStageCERate()
        battle_event_report.HookEnterReport(level,ceRate)
        
        local hookInfo = game_scheme:HookLevel_0(level)
        if hookInfo then
            local hookLevelStr = ""
            if hookInfo.ChapterID >= 20 then
                hookLevelStr = "over20"
            else
                --hookLevelStr = hookInfo.ChapterID .. "_" .. hookInfo.LevelID
                hookLevelStr = tostring(hookInfo.checkNumber)
            end
            local battle_table = game_scheme:GameEventReport_1("battle_level")
            if battle_table then
                local battle_event = battle_table.eventFormatParam
                if battle_event then
                    local needBattleEvent = util.SplitString(battle_event, "#")
                    if util.CheckStringInArray(needBattleEvent, hookLevelStr) then
                        local q1sdk = require "q1sdk"
                        q1sdk.AdjustAndFirebaseReport("battle_level", {value = hookInfo.checkNumber..""})
                        print("needBattleEvent need to report:"..hookLevelStr)
                    else
                        print("needBattleEvent not need to report:"..hookLevelStr)
                    end
                end
            end
        end
        --Test()

        util.StartWatch("loading battle", "准备战斗")
    end,function ()
        --不战斗关闭战斗回调
        --log.Error("不战斗关闭")
        event.Trigger(event.NEW_BATTLE_BACK)
        OpenSlgUILevel()
        --event.Trigger(event.CANCEL_HOT_BALL_HOOK_SCENE_TIME_OUT)--取消暂停热气球
    end)


    if window ~= nil then

        battle_manager.SetPrepareBattle(function ()
            --log.Error("战斗回调")
            menu_bot_data.CloseAllPage()
        end)
        battle_manager.SetFinalizeBattle(common_pb.GameGoal,function(victory, showui)
            if showui then
                local scene_manager_instance = scene_manager.instance()
                if scene_manager_instance:IsCurrentScene("scene_battle") then
                    OpenSlgUILevel()
                end
                local heroData = hook_hero_data.GetSaveHeroData()
                local teamPower = gw_power_mgr.GetHeroGroupPowerById(heroData)
                local ceRate = laymain_data.GetStageCERate()
                if victory == true then
                    -- 显示过关提示
                    if laymain_data.GetMaxID() and laymain_data.GetMaxID() < level then
                        --ui_window_mgr:ShowModule("ui_pass_tip")--2020.4.28暂时屏蔽
                        laymain_data.SetMaxID(level)
                    end
                    --battle_event_report.HookCompletedReport("hook_completed",level, teamPower, ceRate)
                    battle_event_report.HookCompletedReport_GW("Hook_success",level, teamPower,common_pb.GameGoal)
                    local Adjust = require "adjust"
                    if Adjust.HookLevelCfg[level] then
                        Adjust.TrackEvent(Adjust.HookLevelCfg[level])
                    end
                    local Firebase = require "firebase"
                    if Firebase.HookLevelCfg[level] then
                        Firebase.TrackEvent(Firebase.HookLevelCfg[level])
                    end
                    --util.DelayCallOnce(1.1, function()
                        event.Trigger(event.CHECK_HERO_REWARD)
                    --end)
                else
                    --battle_event_report.HookCompletedReport("hook_fail",level, teamPower, ceRate)
                    battle_event_report.HookCompletedReport_GW("Hook_fail",level, teamPower,common_pb.GameGoal)
                end
                if not battle_manager.GetIsReplay() then
                    battle_event_report.ReportBattleInfo(common_pb.GameGoal)
                end
                if not battle_manager.GetIsReplay() then
                    local battleID = battle_manager.GetBattleID()
                    battle_event_report.ReportMainStoryBattleInfo(nil,nil,battleID)
                    --英雄试用打点
                    hero_trial_mgr.GameEventReport_Use(battleID,common_pb.GameGoal,victory)
                end
                --检测通用弹窗
                local gw_popups_trigger_mgr = require "gw_popups_trigger_mgr"
                gw_popups_trigger_mgr.TriggerLevel(victory)
            end
        end)

        battle_manager.SetCanSkipBattle(function()
            return true
        end)
    end
end

--舰队远征
function SetupFleetExpeditionChallenge(level,chapterID)
end

--幻境之塔
function SetupIllusionTowerChallenge(_type, level, check, skipBattle)
end

--[[联盟Boss]]
function SetupLeagueBossChallenge(bossInfo, skipBattle)
    battle_data.SetReturnSelectFunc(function()
        SetupLeagueBossChallenge(bossInfo, skipBattle)
    end)
 
end

--[[勇者试炼选择英雄]]
function SelectTrialHero(teamIndex, filterHeroSidList)
    battle_data.SetReturnSelectFunc(function()
        SelectTrialHero(teamIndex, filterHeroSidList)
    end)

end

--[[时间之巅  准备战斗]]
function SetupPeakChallenge(trialInfo, skipBattle)
end

--[[勇者试炼  准备战斗]]
function SetupBraveTrialChallenge(trialInfo, skipBattle)
end

--专属本战斗
function SetupExclusiveCopyChallenge( level, skipBattle, monsterTeamID,monsterTeamID2,monsterTeamID3)
end



local instanceBattleType =
{
    [1] = common_pb.EquipEctype,
    [2] = common_pb.EquipEctypeFuWen1,
    [3] = common_pb.EquipEctypeFuWen2,
    [4] = common_pb.EquipEctypeFuWen3,
    [5] = common_pb.EquipEctypeFuWen4,
    [6] = common_pb.EquipEctypeFuWen5,
}

--[[副本战斗]]
function SetupInstanceChallenge(_type, level, skipBattle, monsterTeamID)


end

--[[星际探索战斗]]
function SetupSpaceExplorationChallenge(pos, skipBattle, monsterTeamID)

   
end

-- 英雄剧情战斗
function SetupExculsiveBattle(lvID,skipBattle,isNew)
    local spaceEnterType = {pop = 0,full = 1}
    local hero_exclusive_mgr = require "hero_exclusive_mgr"
    hero_exclusive_mgr.SetBattleID(lvID)
    ui_select_model_node.SetBattleType(isNew and common_pb.RookiePlot or  common_pb.Plot)

    local exclusiveCfg = game_scheme:ExclusiveFight_0(lvID)
    if isNew then
        exclusiveCfg = game_scheme:NewExclusiveFight_0(lvID)
    end
    local group = string.split(exclusiveCfg.DefaultHeroID,';')
    local slot_state = {}
    local heroData = {}
    local unlock_slots = {}
    local defaultHeros = {}
    local powers = {}
    local isPass = hero_exclusive_mgr.IsPassStage(lvID)
    local powerGroup = string.split(exclusiveCfg.Power,';')
    for k,v in pairs(powerGroup)do
        local arr = string.split(v,'#')
        local heroID = tonumber(arr[1])
        local power = tonumber(arr[2])
        powers[heroID] = power
    end


    for k,v in ipairs(group)do
        local v_group = string.split(v,'#',tonumber)
        local _heroID = v_group[1]
        local _slot = v_group[2]-1
        local _unlock = v_group[3]
        slot_state[_slot] = _unlock
        if _heroID ~= 0 then
            local fakeEntity = gw_hero_mgr.CreateVirtualEntity(_heroID)
            fakeEntity.heroSid = _heroID
            local _power = powers[_heroID]
            fakeEntity.battleProp = {
                power = _power,
            }
            heroData[_slot] = fakeEntity
            defaultHeros[_heroID] = _heroID
            table.insert(defaultHeros,_heroID)
        end
        if _unlock == 1 then
            -- 可用的槽位
            table.insert(unlock_slots,_slot)
        end
    end

    ui_select_hero.SetSaveHeroData(heroData)
    -- 设置空闲槽位的状态
    ui_select_hero.SetSlotState(slot_state)

    local summaryGroup = {}
    -- 设置英雄池
    local heros = {}
    local heroList = {}
    for i=0,#exclusiveCfg.HeroID.data do
        local heroID = exclusiveCfg.HeroID.data[i]
        if heroID then
            local fakeEntity = gw_hero_mgr.CreateVirtualEntity(heroID)
            fakeEntity.heroSid = heroID
            fakeEntity.Summary = exclusiveCfg.Summary.data[i]
            local _power = powers[heroID]
            fakeEntity.battleProp = {
                power = _power,
            }
            heroList[heroID] = fakeEntity
            table.insert(heros,fakeEntity)
        end
    end

    ui_select_hero.SetExclusiveHeroList(heros,isNew)
    ui_select_hero.SetEnemyByMonsterTeam(exclusiveCfg.MonsterteamID, nil, true)
    CommonSetupWeapon(instanceBattleType[1],nil,nil,nil,nil,true)
    local unforced_guide_mgr = require "unforced_guide_mgr"
    unforced_guide_mgr.SetBattleType(unforced_guide_mgr.BATTLE_TYPE_NUM.PLOT)

    battle_data.skipBattle = false
    local window = ShowSelectHeroModule(function (window,isGrind)
        --进入战斗回调
        local selectedHero = window:GetSelectedHero()
        -- instance_mgr.SetHeroFormation(_type, selectedHero)
        local isHadRecHero = false
        for k,v in pairs(selectedHero)do
            if heroList[v.heroID] then
                isHadRecHero = true
                break
            end
        end
        if not isHadRecHero then
            -- 未上阵推荐英雄
            local flow_text = require "flow_text"
            flow_text.Add(lang.Get(183015))
            return true
        end
        window.onCloseEvent = nil
        window.onFightEvent = nil

        local battleData = {
            stageType = isNew and common_pb.RookiePlot or  common_pb.Plot,
            stageLevel = lvID,
            skipBattle = skipBattle,
            heroData = selectedHero,
        }
        battle_manager.SendBattleChallengeMessage(battleData)
        ui_window_mgr:UnloadModule("ui_new_week_activity")
    end,nil,function ()
        ui_select_hero.UpdatePlotUI()
    end)

    ui_select_hero.SetDefaultHeros(defaultHeros)
    ui_select_hero.SetCheckFram(unlock_slots,unlock_slots)
    if window ~= nil then
        battle_manager.SetFinalizeBattle(isNew and common_pb.RookiePlot or common_pb.Plot,function(victory, showui)
            if showui then
                    if isNew then
                        -- local new_week_activity_data = require "new_week_activity_data"
                        -- new_week_activity_data.SetIsNewHero(true)
                    end
                hero_exclusive_mgr.SetIsAfterBattle(true)
                local ui_new_week_activity = require "ui_new_week_activity"
                ui_new_week_activity.SetSubIndex(isNew and 5 or 4)
                ui_window_mgr:ShowModule("ui_new_week_activity")
                if victory == true then
                    -- 播放剧情对话
                    hero_exclusive_mgr.PlayStory(hero_exclusive_mgr.STORY_TYPE.behind,isNew)
                end
            end

            local lastselectedHero = ui_select_hero.GetLastSelectHeros()
            if not battle_manager.GetIsReplay() then
                battle_event_report.NewHeroActivityTreasurePoltReort(lvID,victory,lastselectedHero)
            end
        end)

        battle_manager.SetCanSkipBattle(function()
            return true
        end)
    end
end

--[[时空擂台战斗]]
function SetupWeekendArena(roleID)
    local battleData = {
        stageType = common_pb.WeekendArena,
        stageLevel = roleID,
    }
    ui_select_model_node.SetBattleType(common_pb.WeekendArena)

    local battle_data = require "battle_data"
    battle_data.skipBattle = false
    battle_manager.SendBattleChallengeMessage(battleData)
    battle_manager.SetFinalizeBattle(common_pb.WeekendArena,function(victory, showui)
        ui_window_mgr:ShowModule("ui_weekend_arena",function ()
            ui_window_mgr:ShowModule("ui_weekend_arena_record")
        end)
    end)
    battle_manager.SetPrepareBattle(function ()
        ui_window_mgr:UnloadModule("ui_weekend_arena")
        ui_window_mgr:UnloadModule("ui_matchplace_entrance")
    end)


    battle_manager.SetCanSkipBattle(function()
        return true
    end)
end

-- @param enterType 0:弹窗 1：全屏
--[[时空裂缝战斗]]
function SetupSpaceGap(lvID,skipBattle,enterType)
end

--[[单人秘境战斗]]
function SetupSecretPlace(lvID,battleType,MonsterteamID)
end

--虚空擂台
function SetupVoidRingChallenge(voidData,upLevel,heroData,skipBattle,enemyData,enemyPower)

    local void_ring_data = require "void_ring_data"
    local hData = void_ring_data.GetHeroFormation()
    if util.get_len(hData) == 0 then
        local hook_hero_data = require "hook_hero_data"
        hData = hook_hero_data.GetSaveHeroData()
    end
    ui_select_hero.SetSaveHeroData(hData,true,heroData)
    ui_select_hero.SetEnemyData(enemyData,enemyPower,true)
    ui_select_model_node.SetBattleType(common_pb.VoidArena)

    CommonSetupWeapon(common_pb.VoidArena)
    battle_data.skipBattle = skipBattle or false
    local window = ShowSelectHeroModule(function (window,isGrind)
        --进入战斗回调
        ui_window_mgr:UnloadModule("ui_void_ring")
        ui_window_mgr:UnloadModule("ui_void_ring_fight")
        local selectedHero = window:GetSelectedHero()
        void_ring_data.SetHeroFormation(selectedHero,true)

        local battleData = {
            stageType = common_pb.VoidArena,
            voidData = voidData,
            upLevel = upLevel,
            skipBattle = skipBattle,
            heroData = selectedHero,
        }
        battle_manager.SendBattleChallengeMessage(battleData)
        --打点
        battle_event_report.VoidRingFightAddData("VoidArenabattle",voidData,enemyData,battle_manager.GetBattleID())

    end,function ()
        --不战斗关闭战斗回调
        ui_window_mgr:ShowModule("ui_void_ring")
        ui_window_mgr:ShowModule("ui_void_ring_content")
        local ui_void_ring_fight = require "ui_void_ring_fight"
        ui_void_ring_fight.SetInputParam(voidData)
        ui_window_mgr:ShowModule("ui_void_ring_fight")
    end)
    if window ~= nil then
        battle_manager.SetPrepareBattle(function ()
            ui_window_mgr:UnloadModule("ui_setting_base")
            ui_window_mgr:UnloadModule("ui_actor_base")
            ui_window_mgr:UnloadModule("ui_void_ring")
            ui_window_mgr:UnloadModule("ui_void_ring_fight")
            CloseSlgUI()

            menu_bot_data.CloseAllPage()
        end)
        battle_manager.SetFinalizeBattle(common_pb.VoidArena,function(victory, showui)
            if victory then
                battle_event_report.VoidRingFightAddData("VoidArenabattleWin",voidData,enemyData,battle_manager.GetBattleID())
            else
                battle_event_report.VoidRingFightAddData("VoidArenabattleLose",voidData,enemyData,battle_manager.GetBattleID())
            end
            if showui then
                OpenSlgUI()
                ui_window_mgr:ShowModule("ui_void_ring")
                ui_window_mgr:ShowModule("ui_void_ring_content")
                local ui_void_ring_fight = require "ui_void_ring_fight"
                ui_void_ring_fight.SetInputParam(voidData)
                --ui_window_mgr:ShowModule("ui_void_ring_fight")
            else

            end

        end)



        battle_manager.SetCanSkipBattle(function()
            return true
        end)
    end
end

--[[异界迷宫战斗]]
function SetupMazeChallenge(mazeinfo, heroData, skipBattle, enemyData, enemyPower,isMulti,mazeBttleType,finishEnemyList)
  
end

--[[竞技场]]
function SetUpTeamArenaBattle(arenaType, teamID, skipBattle, array)
    battle_data.SetReturnSelectFunc(function()
        SetUpTeamArenaBattle(arenaType, teamID, skipBattle, array)
    end)
    battle_manager.SetBattleType(arenaType)
    battle_data.skipBattle = skipBattle or false
    ui_select_model_node.SetBattleType(common_pb.Arena)

    local hData = {}
    for i, v in ipairs(array) do
        local heroEntity = player_mgr.GetPalPartDataBySid(v)
        if heroEntity then
            hData[i-1] = heroEntity
        end
    end
    local battleData = {
        stageType = common_pb.Arena,
        stageLevel = 0,
        heroData = hData,
        arenaInfo =  {arenaType = arenaType, rivalID = teamID,weaponId = 1},
        skipBattle = skipBattle,
    }
    battle_manager.SendBattleChallengeMessage(battleData)

    battle_event_report.ArenaEnterCommonReport(arenaType)
    RegisterTeamArenaBattle(true, arenaType)
end

function RegisterTeamArenaBattle(battleRet, arenaType)
    battle_manager.SetBattleType(arenaType)
    local entranceIsShown = false
    local historyIsShown = false
    local ui_matchplace_entrance = require "ui_matchplace_entrance"

    battle_manager.SetPrepareBattle(function()
       CloseSlgUI()

        menu_bot_data.CloseAllPage()
        entranceIsShown = entranceIsShown or ui_window_mgr:IsModuleShown("ui_matchplace_entrance")
        --ui_window_mgr:HideModule("ui_matchplace_entrance")
        ui_matchplace_entrance.Close()
        ui_window_mgr:UnloadModule("ui_task_achievement")
        historyIsShown = historyIsShown or ui_window_mgr:IsModuleShown("ui_match_battle_history")
        ui_window_mgr:UnloadModule("ui_match_battle_history")

        ui_window_mgr:UnloadModule("ui_multi_match")
        battle_event_report.ArenaEnterCommonReport(arenaType)
    end)
    battle_manager.SetFinalizeBattle(common_pb.Arena,function(victory, showui)
        if showui then
            if entranceIsShown then
                ui_window_mgr:ShowModule("ui_matchplace_entrance", nil ,function() OpenSlgUI() end)
            end
            if historyIsShown then
                ui_window_mgr:ShowModule("ui_match_battle_history")
            end

            entranceIsShown = false
            historyIsShown = false

            if battleRet then
                local ui_multi_match = require "ui_multi_match"
                ui_multi_match.InitialOffsetStep()

                local net_arena_module = require "net_arena_module"
                net_arena_module.As_Enter_Arena(arenaType)
            else
                ui_window_mgr:ShowModule("ui_multi_match")
                local ui_multi_match = require "ui_multi_match"
                ui_multi_match.ReSetOpenPage(3)
            end
        end

        if victory == true then
            battle_event_report.ArenaCompletedReportlocal(true,arenaType)
        else
            battle_event_report.ArenaCompletedReportlocal(false,arenaType)
        end

    end)



    battle_manager.SetCanSkipBattle(function()
        return true
    end)
end

function SetUpChampionBattle(arenaType, rivalID, skipBattle)
    battle_manager.SetBattleType(arenaType)
    battle_data.skipBattle = skipBattle or false
    local net_arena_module = require "net_arena_module"
    local getKey = function()
        return 'offence_multi_lineups_'..player_mgr.GetPlayerRoleID()
    end
    --@region 载入上次的设置
    ui_select_model_node.SetBattleType(common_pb.Arena)
    local str = PlayerPrefs.GetString(getKey(), '')
    local lineupList = {}
    if string.len(str) > 0 then
        net_arena_module.MineTeamLineup = {}
        local playerProp = player_mgr.GetPlayerProp() or {faceID = 1, roleName = '', lv=1}
        local me = {roleID = player_mgr.GetPlayerRoleID(), faceID = playerProp.faceID, level = playerProp.lv}
        net_arena_module.MineTeam = {captainID = 0, teamMemInfo = {me, me, me}, teamName = playerProp.roleName}
        local teamArrs = string.split(str, '|')
        for _,data in pairs(teamArrs) do
            local team = {}
            local pals = {}
            local array = string.split(data, ',', tonumber)
            for i, v in ipairs(array) do
                local heroEntity = player_mgr.GetPalPartDataBySid(v)
                if heroEntity then
                    local row,col = battle_data.GetRowAndColByIndex(i + 1)
                    table.insert(team, {palId = heroEntity.heroSid, row = row, col = col})
                    table.insert(pals, {palID = heroEntity.heroID, rowNo = row, colNo = col, level = heroEntity.numProp.lv, starLevel = heroEntity.numProp.starLv})
                    --table.insert(team, {palId = heroEntity.heroSid, row = i <= 3 and 0 or 1, col = i <= 3 and i-1 or (i - 4)})
                    --table.insert(pals, {palID = heroEntity.heroID, rowNo = i <= 3 and 0 or 1, colNo = i <= 3 and i-1 or (i - 4), level = heroEntity.numProp.lv, starLevel = heroEntity.numProp.starLv})
                end
            end
            table.insert(lineupList, team)
            table.insert(net_arena_module.MineTeamLineup, {pals = pals, roleID = 0})
        end

        local battleData = {
            stageType = common_pb.Arena,
            stageLevel = 0,
            heroData = lineupList,--TODO测试
            arenaInfo =  {arenaType = arenaType, rivalID = rivalID, lineupList = lineupList,weaponId = 1},
            skipBattle = skipBattle,
        }
        battle_manager.SendBattleChallengeMessage(battleData)
        RegisterSingleArenaBattle(true, arenaType)
        battle_event_report.ArenaEnterCommonReport(arenaType)
        return
    end
    --@endregion

    local ui_multiplayer_hero_select = require "ui_multiplayer_hero_select"
    ui_multiplayer_hero_select.Set4Defence(false)

    local hero_select = ui_window_mgr:ShowModule("ui_multiplayer_hero_select")
    hero_select.enableArrange = true
    hero_select.startHandle = function(_window)
        net_arena_module.MineTeamLineup = {}
        local playerProp = player_mgr.GetPlayerProp() or {faceID = 1, roleName = '', lv=1}
        local me = {roleID = player_mgr.GetPlayerRoleID(), faceID = playerProp.faceID, level = playerProp.lv}
        net_arena_module.MineTeam = {captainID = 0, teamMemInfo = {me, me, me}, teamName = playerProp.roleName}

        local lineupList = {}
        for i, v in ipairs(_window.heroData) do
            local team = {}
            local pals = {}
            for _k, _v in pairs(v.heroes) do
                local entity = player_mgr.GetPalPartDataBySid(_v)
                -- 1-6
                local row,col = battle_data.GetRowAndColByIndex(_k + 1)
                table.insert(team, {palId = entity.heroSid, row = row, col = col})
                table.insert(pals, {palID = entity.heroID, rowNo = row, colNo = col, level = entity.numProp.lv, starLevel = entity.numProp.starLv})
                --table.insert(team, {palId = entity.heroSid, row = _k <= 3 and 0 or 1, col = _k <= 3 and _k-1 or (_k - 4)})
                --table.insert(pals, {palID = entity.heroID, rowNo = _k <= 3 and 0 or 1, colNo = _k <= 3 and _k-1 or (_k - 4), level = entity.numProp.lv, starLevel = entity.numProp.starLv})
            end
            table.insert(lineupList, team)
            table.insert(net_arena_module.MineTeamLineup, {pals = pals, roleID = 0})
        end

        local selectedHeros = _window:GetSelectedHero()
        --@region 保存设置
        local array = {}
        local sids = {{0,0,0,0,0,0},{0,0,0,0,0,0},{0,0,0,0,0,0}}
        for i, v in pairs(selectedHeros) do
            for j,k in pairs(v) do
                sids[i][j+1] = k
            end
        end
        for teamId=1,3 do
            array[teamId] = table.concat(sids[teamId], ",")
        end
        PlayerPrefs.SetString(getKey(), table.concat(array, "|"))
        --@endregion

        local ok = true
        for i, v in ipairs(lineupList) do
            if #v <= 0 then
                ok = false
            end
        end

        if ok then
            local battleData = {
                stageType = common_pb.Arena,
                stageLevel = 0,
                heroData = selectedHeros,
                arenaInfo =  {arenaType = arenaType, rivalID = rivalID, lineupList = lineupList,weaponId = 1},
                skipBattle = skipBattle,
            }
            battle_manager.SendBattleChallengeMessage(battleData)
            _window:Close()
            battle_event_report.ArenaEnterCommonReport(arenaType)
        end
    end

    RegisterSingleArenaBattle(true, arenaType)
end

--[[高阶竞技场多队伍出战请求]]
function SetUpArenaAdvanceBattle(arenaType, rivalID, skipBattle)
    battle_data.SetReturnSelectFunc(function()
        SetUpArenaAdvanceBattle(arenaType, rivalID, skipBattle)
    end)
    battle_manager.SetBattleType(arenaType)
    battle_data.skipBattle = skipBattle or false

	local ui_match_senior_single_rank =  require "ui_match_senior_single_rank"
    ui_match_senior_single_rank.SetpreBattle(true)
	local ui_multi_hero_select = require "ui_multi_hero_select"
    local heroData, weaponData = ui_multi_hero_select.GetAdvanceHeroData()
    ui_select_model_node.SetBattleType(common_pb.Arena)

	local lineupList = {}
	for i, v in ipairs(heroData) do
		local team = {}
		for _k, heroEntity in pairs(v) do
            local row,col = battle_data.GetRowAndColByIndex(_k + 1)
			table.insert(team, {palId = heroEntity.heroSid, row = row, col = col})
		end
		table.insert(lineupList, team)
	end

    -- 进攻阵容
    local battleData = {
        stageType = common_pb.Arena,
        stageLevel = 0,
        heroData = {},
        arenaInfo =  {arenaType = arenaType, rivalID = rivalID,lineupList = lineupList, weaponIds = weaponData},
        skipBattle = skipBattle,
    }

    battle_event_report.ArenaEnterCommonReport(arenaType)
    battle_manager.SendBattleChallengeMessage(battleData)
    RegisterSingleArenaBattle(true, arenaType)
end

--新手竞技场
function SetUpArenaBattle(arenaType, rivalID, skipBattle, array, enemyInfo)
    battle_data.SetReturnSelectFunc(function()
        SetUpArenaBattle(arenaType, rivalID, skipBattle, array, enemyInfo)
    end)
    battle_manager.SetBattleType(arenaType)
    battle_data.skipBattle = skipBattle or false
    battle_data.SetEnemyInfo(enemyInfo)
    battle_data.SetIsCancelOpenBattleAni(true)
    local ui_match_single_rank =  require "ui_match_single_rank"
    ui_match_single_rank.SetpreBattle(true)
    ui_select_model_node.SetBattleType(common_pb.Arena)

    local net_arena_module = require "net_arena_module"
    local hData = {}
    for i = 1, 6 do
        if array[i] ~= nil then
            local v = array[i]
            local heroEntity = player_mgr.GetPalPartDataBySid(v)
            if heroEntity then
                hData[i-1] = heroEntity
            end
        end
    end

    local weaponID = array[7]  ~= 0 and array[7] or nil

    -- 进攻阵容
    local battleData = {
        stageType = common_pb.Arena,
        stageLevel = 0,
        heroData = hData,
        arenaInfo =  {arenaType = arenaType, rivalID = rivalID,weaponId = weaponID},
        skipBattle = skipBattle,
    }

    battle_event_report.ArenaEnterCommonReport(arenaType)
    battle_manager.SendBattleChallengeMessage(battleData)
    RegisterSingleArenaBattle(true, arenaType)
    --@endregion
end

function RegisterWeekendArenaBattle(battleRet, arenaType)
    battle_manager.SetBattleType(arenaType)
    local festival_activity_mgr = require "festival_activity_mgr"
local festival_activity_cfg = require "festival_activity_cfg"
    battle_manager.SetPrepareBattle(function()
        ui_window_mgr:UnloadModule("ui_match_battle_history_weekend")
        festival_activity_mgr.CloseActivityCenterUI()

    end)
    battle_manager.SetFinalizeBattle(arenaType,function(victory, showui)
        battle_manager.SetBattleType(nil)
        local arena_data = require "arena_data"
        arena_data.OpenAreaWeekend()
        ui_window_mgr:ShowModule("ui_match_battle_history_weekend")

    end)



    battle_manager.SetCanSkipBattle(function()
        return true
    end) 
end

function RegisterSingleArenaBattle(battleRet, arenaType)
    print("==========RegisterSingleArenaBattle==========")
    battle_manager.SetBattleType(arenaType)
    local entranceIsShown = false
    local historyIsShown = false
    local net_arena_module = require "net_arena_module"

    battle_manager.SetPrepareBattle(function()
        print("------------------prepareBattle---------------")
       CloseSlgUI()

       --[[ menu_bot_data.CloseAllPage()
        entranceIsShown = entranceIsShown or ui_window_mgr:IsModuleShown("ui_matchplace_entrance")
        ui_window_mgr:UnloadModule("ui_matchplace_entrance")]]

        ui_window_mgr:UnloadModule("ui_task_achievement")
        historyIsShown = historyIsShown or ui_window_mgr:IsModuleShown("ui_match_battle_history")
        ui_window_mgr:UnloadModule("ui_match_battle_history")

        ui_window_mgr:HideModule("ui_match_single_rank", false)
    end)
    battle_manager.SetFinalizeBattle(common_pb.Arena,function(victory, showui)
        print("------------------finalizeBattle---------------","victory:",victory,"showui:",showui)
        if showui then
            ui_window_mgr:ShowModule("ui_match_single_rank")
            if historyIsShown then
                net_arena_module.Send_ARENA_BATTLE_RECORD(arenaType)
                ui_window_mgr:ShowModule("ui_match_battle_history")
            end

            entranceIsShown = false
            historyIsShown = false
        end

        if victory == true then
            battle_event_report.ArenaCompletedReportlocal(true,arenaType)
        else
            battle_event_report.ArenaCompletedReportlocal(false,arenaType)
        end
		if arenaType == common_pb.CrystalCrown and not battle_manager.GetIsReplay() then
			battle_event_report.ReportBattleInfo(common_pb.Arena, common_pb.CrystalCrown)
		end

    end)



    battle_manager.SetCanSkipBattle(function()
        return true
    end)
end

function RegisterCarriageBattle(battleRet, arenaType)
    print("==========RegisterCarriageBattle==========")
    battle_manager.SetBattleType(arenaType)
    local entranceIsShown = false
    local historyIsShown = false
    local net_carriage_module = require "net_carriage_module"

    battle_manager.SetPrepareBattle(function()
       print("------------------prepareBattle---------------")
       CloseSlgUI()

        ui_window_mgr:UnloadModule("ui_task_achievement")
        historyIsShown = historyIsShown or ui_window_mgr:IsModuleShown("uintercity_trucks_history")
        ui_window_mgr:UnloadModule("uintercity_trucks_history")

        ui_window_mgr:HideModule("ui_intercity_trucks_main", false)
    end)
    battle_manager.SetFinalizeBattle(common_pb.Carriage,function(victory, showui)
        print("------------------finalizeBattle---------------","victory:",victory,"showui:",showui)
        if showui then
            local main_slg_data = require "main_slg_data"
            local gw_const = require "gw_const"
            local sceneType = main_slg_data.GetCurSceneType()
            if sceneType == gw_const.ESceneType.Truck then
                ui_window_mgr:ShowModule("ui_intercity_trucks_main")
            end
            if historyIsShown then
                net_carriage_module.MSG_CARRIAGE_TRADE_HISTORY_REQ()
                ui_window_mgr:ShowModule("uintercity_trucks_history")
            end

            entranceIsShown = false
            historyIsShown = false
        end

    end)



    battle_manager.SetCanSkipBattle(function()
        return true
    end)
end


--[[阿斯布地牢]]
function SetupDungeon(skipBattle)

end

--[[联盟boss战 准备战斗]]
function SetupSociatyBossWar(mTeamId,usedHeroList,attackCounts,skipBattle)
    battle_data.SetReturnSelectFunc(function()
        SetupSociatyBossWar(mTeamId,usedHeroList,attackCounts,skipBattle)
    end)
  
end

--[[破碎时空 活跃度boss 准备战斗]]--虚空竞标赛，枢纽试炼
function SetupBrokenTime(battleData, mTeamId, hpMpData, isActiveBoss, skipBattle,onFight,onFinalizeFight)
    battle_data.skipBattle = skipBattle
    if not isActiveBoss then
        ui_select_hero.CheckIsShowArtifactInDomination()
    end
    local ui_activity_broken_time = require "ui_activity_broken_time"
    -- local ui_sociaty_active_boss_main = require "ui_sociaty_active_boss_main"
    -- local hData = isActiveBoss and ui_sociaty_active_boss_main.GetLocalLineup() or ui_activity_broken_time.GetHeroFormation()
    local hData = ui_activity_broken_time.GetHeroFormation()
    local stageType = isActiveBoss and common_pb.LeaActivityBoss or common_pb.BrokenSpaceTime
    ui_select_hero.SetSaveBattleType(stageType)
    ui_select_hero.SetSaveHeroData(hData)

    ui_select_hero.SetEnemyByMonsterTeam(mTeamId,hpMpData)

    ui_select_model_node.SetBattleType(stageType)
    -- local uiName = isActiveBoss and "ui_sociaty_active_boss_main"-- or "ui_domination_main"
    local wID = CommonSetupWeapon(stageType)
    local unforced_guide_mgr = require "unforced_guide_mgr"
    unforced_guide_mgr.SetBattleType(unforced_guide_mgr.BATTLE_TYPE_NUM.BROKEN_TIME)

    battle_event_report.SpaceDominatiorEnterReport()


    local window = ShowSelectHeroModule(function (window,isGrind)
        --进入战斗回调
        local selectedHero = window:GetSelectedHero()
        if isActiveBoss then
            -- ui_sociaty_active_boss_main.SetLocalLineup(selectedHero)
        else
            ui_activity_broken_time.SetHeroFormation(selectedHero)
        end

        local battleData = {
            stageType = stageType,
            heroData = selectedHero,
            brokenTimeInfo = battleData,
            skipBattle = skipBattle,
        }
        battle_manager.SendBattleChallengeMessage(battleData)

        if onFight~= nil then
            onFight()
            else

            if not skipBattle or battle_manager.GetIsReplay() then
                -- ui_window_mgr:UnloadModule(uiName)
                if isActiveBoss then
                    menu_bot_data.CloseAllPage()
                end
            end

        end
    end,function ()
        --不战斗关闭战斗回调
        if isActiveBoss then
            -- ui_window_mgr:ShowModule(uiName)
            menu_bot_data.OpenUnionPage()
            OpenSlgUI()
        end
    end)

    if window ~= nil then
        battle_manager.SetPrepareBattle(function ()
            -- 大厅不可点击
            if not skipBattle or battle_manager.GetIsReplay() then

                menu_bot_data.CloseAllPage()
            end
        end)
        battle_manager.SetFinalizeBattle(stageType,function(victory, showui)
            if showui then
                if not skipBattle or battle_manager.GetIsReplay() then
                    local laymain_mgr = require "laymain_mgr"
                    if isActiveBoss then
                        laymain_mgr.SetCurSceneIndex(4)
                        menu_bot_data.OpenUnionPage()
                        OpenSlgUI()
                        ui_window_mgr:ShowModule("ui_union_scene")
                    end
                    --ui_window_mgr:ShowModule(uiName)
                end
            end
            local hData = ui_activity_broken_time.GetHeroFormation()
            local teamPower = gw_hero_mgr.GetHeroesPower(hData)
            if not battle_manager.GetIsReplay() then
                battle_event_report.ReportSpaceDominationBattleInfo(teamPower, isActiveBoss,battle_manager.GetBattleID())
            end

            if onFinalizeFight~=nil then
                onFinalizeFight()
            end

        end)

        if isActiveBoss then --进入战斗失败
            battle_manager.SetFinalizeBattle(0,function(victory, showui)
                local laymain_mgr = require "laymain_mgr"
                laymain_mgr.SetCurSceneIndex(4)
                menu_bot_data.OpenUnionPage()
                OpenSlgUI()
                ui_window_mgr:ShowModule("ui_union_scene")
            end)
            local ui_point_slider_controller = require "ui_point_slider_controller"
            ui_point_slider_controller.ExitSaveMode(true)
        end

        battle_manager.SetCanSkipBattle(function()
            return true
        end)
    end
end

--[[联盟探宝BOSS]]
function SetupExploreBossChallenge(moduleName,enemyDatas,enemyPower)
    battle_data.SetReturnSelectFunc(function()
        SetupExploreBossChallenge(moduleName,enemyDatas,enemyPower)
    end)
   
end

--[[联盟探宝BOSS扫荡]]
function SetupExploreBossSweepChallenge(sweepTimes,enemyDatas,enemyPower)
    battle_data.SetReturnSelectFunc(function()
        SetupExploreBossSweepChallenge(sweepTimes,enemyDatas,enemyPower)
    end)
  
end

--[[联盟切磋功能]]
function SetupLeagueChallenge(roleID)
    battle_data.SetReturnSelectFunc(function()
        SetupLeagueChallenge(roleID)
    end)
    battle_data.skipBattle = false

    local sociaty_data = require "sociaty_data"
    -- 关掉联盟相关界面
    local sociaty_scene = require "sociaty_scene"
    sociaty_scene.Close()
    ui_window_mgr:UnloadModule("ui_union_scene")
    ui_window_mgr:UnloadModule("ui_sociaty_hall")
    -- ui_window_mgr:UnloadModule("ui_sociaty_medal_entrance")

    local hData = sociaty_data.GetLeagueBattleHeroes()
    ui_select_hero.SetSaveHeroData(hData)
    ui_select_model_node.SetBattleType(common_pb.Compete)

    --敌方阵容
    local memberInfo = sociaty_data.GetMemberInfo(roleID)
    if memberInfo then
        local stLineUp = memberInfo.stLineUp
        if stLineUp then
            local enemyPower = stLineUp.personalCE or 0
            local enemyDatas = {}
            local heroArr = stLineUp.pals
            for i, hero in ipairs(heroArr) do
                local key = hero.rowNo*3 + hero.colNo
                enemyDatas[key] = gw_hero_mgr.CreateVirtualEntity(hero.palID,hero.level, hero.starLevel,(hero.llCurHp or 0) / (hero.llMaxHp or 100),nil,1,nil,nil,hero.skinID)
            end
            ui_select_hero.SetEnemyData(enemyDatas,enemyPower,false)
        end
    end

    local wID = CommonSetupWeapon(common_pb.Compete)

    local unforced_guide_mgr = require "unforced_guide_mgr"
    unforced_guide_mgr.SetBattleType(unforced_guide_mgr.BATTLE_TYPE_NUM.LEAGUE_CHALLENGE)
    local window = ShowSelectHeroModule(function (window,isGrind)
        --进入战斗回调
        local selectedHero = window:GetSelectedHero()
        local heroSids = {0, 0, 0, 0, 0, 0}
        for pos, hero in pairs(selectedHero) do
            if heroSids[pos + 1] then
                heroSids[pos + 1] = hero.heroSid
            end
        end
        sociaty_data.UpdateLeagueBattleHeroes(heroSids)

        local battleData = {
            stageType = common_pb.Compete,
            heroData = selectedHero,
            competeBattle = {dwRivalRoleId = roleID},
        }
        battle_manager.SendBattleChallengeMessage(battleData)
    end,function ()
        --不战斗关闭战斗回调
        sociaty_scene.Show()
        ui_window_mgr:ShowModule("ui_union_scene")
        -- ui_window_mgr:ShowModule("ui_sociaty_hall")
    end)
    if window ~= nil then
        battle_manager.SetPrepareBattle(function ()
            -- ui_window_mgr:UnloadModule("ui_sociaty_hall")

            menu_bot_data.CloseAllPage()
        end)
        battle_manager.SetFinalizeBattle(common_pb.Compete,function(victory, showui)

            if showui then

                menu_bot_data.OpenUnionPage()
                -- util.DelayCall(0.3, function()
                --     ui_window_mgr:ShowModule("ui_sociaty_hall")
                -- end)
            end
          
            if not battle_manager.GetIsReplay() then
                battle_event_report.ReportLeagueChallengeInfo(teamPower,battle_manager.GetBattleID())
            end

        end)

        battle_manager.SetCanSkipBattle(function()
            return true
        end)
    end
end

--盟战编辑阵容+战斗
--isRegister是否报名，非报名代表编辑阵容
--enemyTbsTroop敌方的布阵信息
--enemyId 敌方ID
--enemySociatyId 敌方工会id
function SetupSociatyWar(isRegister, enemyTbsTroop, enemyId, enemySociatyId, enemyPower)
    battle_data.SetReturnSelectFunc(function()
        SetupSociatyWar(isRegister, enemyTbsTroop, enemyId, enemySociatyId, enemyPower)
    end)
  

end

--[[好友切磋功能]]
function SetupFriendChallenge(_worldid, _dbid, stLineUp)
    battle_data.SetReturnSelectFunc(function()
        SetupFriendChallenge(_worldid, _dbid, stLineUp)
    end)
    battle_data.skipBattle = false

    local friend_mgr = require "friend_mgr"
    local hData = friend_mgr.GetHeroFormation()
    ui_select_hero.SetSaveBattleType(common_pb.Friend)
    ui_select_hero.SetSaveHeroData(hData)
    ui_select_model_node.SetBattleType(common_pb.Friend)

    if stLineUp then
        local enemyDatas = {}
        local power = 0
        local heroArr = stLineUp.pals
        for i, hero in ipairs(heroArr) do
            local key = hero.rowNo*3 + hero.colNo
            enemyDatas[key] = gw_hero_mgr.CreateVirtualEntity(hero.palID,hero.level, hero.starLevel,(hero.llCurHp or 0) / (hero.llMaxHp or 100),nil,1,nil,nil,hero.skinID)
            power = (power + hero.power) or 0
        end
        ui_select_hero.SetEnemyData(enemyDatas, power or 0, false)
    end

    local wID = CommonSetupWeapon(common_pb.Friend)

    local window = ShowSelectHeroModule(function (window,isGrind)
        --进入战斗回调
        local selectedHero = window:GetSelectedHero()

        friend_mgr.SetHeroFormation(selectedHero)

        local battleData = {
            stageType = common_pb.Friend,
            heroData = selectedHero,
            skipBattle = false,
            friendBattle = {worldid = _worldid, dbid = _dbid},
        }
        battle_manager.SendBattleChallengeMessage(battleData)
    end, function()
        OpenSlgUI()
    end)
    if window ~= nil then
        battle_manager.SetPrepareBattle(function ()
            ui_window_mgr:UnloadModule("ui_friend_main")
            menu_bot_data.CloseAllPage()
        end)
        battle_manager.SetFinalizeBattle(common_pb.Friend,function(victory, showui)
            if showui then
                OpenSlgUI()
                ui_window_mgr:ShowModule("ui_friend_main")
            end
            --英雄试用打点
            hero_trial_mgr.GameEventReport_Use(battle_manager.GetBattleID(),common_pb.Friend,victory)

        end)

        battle_manager.SetCanSkipBattle(function()
            return true
        end)
    end
end

--[[苦力战斗功能]]
function SetupCoolieChallenge(bossid, slaveid, battleType, stLineUp, stWID, conquerid)
   
end

--[[苦力跳过战斗]]
function SetupCoolieSkipChallenge(bossid, slaveid, battleType, conquerid)
    battle_data.skipBattle = true

    local slaveBattle = {
        bossid = bossid,
        slaveid = slaveid,
        type = battleType,
        conquerid = conquerid
    }

    local battleData = {
        stageType = common_pb.BossSlave,
        multiHeroData ={},
        skipBattle = true,
        slaveBattle = slaveBattle,
    }
    battle_manager.SendBattleChallengeMessage(battleData)
end

--[[节日活动3Boss试炼挑战]]
function SetupFestivalBossChallenge(skipBattle, monsterTeamID,activityID,isNewbee)
  
end

--雷达挑战任务
function SetupRadarChallenge(entitySid, monsterId)
    battle_data.SetReturnSelectFunc(function()
        SetupRadarChallenge(entitySid, monsterId)
    end)
    local sand_ui_event_define = require "sand_ui_event_define"
    event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_UI)
    --windowMgr:UnloadModule("ui_radar_challenge_tips")
    
    battle_data.skipBattle = false
    
    --local ui_bs_arena_hero_select_panel_controller = require "ui_bs_arena_hero_select_panel_controller"
    local hook_hero_data = require "hook_hero_data"
    local hData = hook_hero_data.GetSaveHeroData()
    ui_select_hero.SetSaveBattleType(common_pb.RadarMonika)
    ui_select_hero.SetSaveHeroData(hData)
    ui_select_hero.SetAdjustBtnVisible(true, true)
    ui_select_model_node.SetBattleType(common_pb.RadarMonika)
    --ui_select_model_node.SetHookLevelGrade(level)

    ui_select_hero.SetLineUpState(false)
    ui_select_hero.SetEnemyByMonsterTeam(monsterId, nil, nil, 1)
    local cfg_monster = game_scheme:monsterTeam_0(monsterId)
    if cfg_monster then
        ui_select_hero.SetGrindThreshold(1234, cfg_monster.Power)
        local EnemyFaceID = cfg_monster and cfg_monster.faceID or 0
        ui_select_hero.SetTopPowerData(true, nil, EnemyFaceID)
    end

    --SetupMonsterTeam(monsterId, 1)

    local window = NewShowArenaHeroSelectPanel(function(window, isGrind)
        --进入战斗回调
       
        --把新的协议赋值然后发送过去
        --region 沙盘副本战斗请求
        local sandbox_pb = require "sandbox_pb"
        local msg = sandbox_pb.TMSG_SANDBOX_GAME_BATTLE_REQ()
        msg.stageType = common_pb.RadarMonika
        --msg.lon
        msg.param1 = entitySid
        local selectedHero = window:GetSelectedHero()
        hook_hero_data.SaveHookBattleHeroData(selectedHero)

        if selectedHero then
            local t = {}
            for pos, hero in pairs(selectedHero) do
                table.insert(t,{pos=pos,hero=hero})
            end
            table.sort(t, function(v1, v2)
                return v1.pos < v2.pos
            end)

            for index, data in ipairs(t) do
                if data.pos >= 0 then--这里是为了防止一些偶然错误导致拖拽英雄位置为-100也存储了进来
                    --local dataInfo = msg.pals:add()
                    local dataInfo = msg.lineUp.palList:add()
                    dataInfo.palId = data.hero.heroSid
                    local row,col = battle_data.GetRowAndColByIndex(data.pos + 1)
                    dataInfo.row = row
                    dataInfo.col = col
                    --if data.pos < 3 then
                    --    dataInfo.row = 0
                    --    dataInfo.col = data.pos
                    --else
                    --    dataInfo.row = 1
                    --    dataInfo.col = data.pos-3
                    --end
                end
            end
        end

        --local common_new_pb = require "common_new_pb"
        --msg.lineUp = msg.lineUp or common_new_pb.BattleLineUp()
        --msg.lineUp.palList = msg.lineUp.palList or common_new_pb.BattlePalInfo()

        --local lineUp = common_new_pb.BattleLineUp()
        --local palList = common_new_pb.BattlePalInfo()

        --heroData 有了
        --lineUp.palList = palList
        --msg.lineUp = lineUp

        local net_sandbox_module =require "net_sandbox_module"
        net_sandbox_module.MSG_SANDBOX_GAME_BATTLE_REQ(msg)
        --endregion
        --battle_manager.SendBattleChallengeMessage(battleData)

    end, function()
        --不战斗关闭战斗回调
        OpenSlgUI()
    end)

    if window ~= nil then

        --设置战斗前
        battle_manager.SetPrepareBattle(function()

        end)

        --设置战斗结束？
        battle_manager.SetFinalizeBattle(common_pb.RadarMonika, function(victory, showui)
            if showui then
                local scene_manager_instance = scene_manager.instance()
                if scene_manager_instance:IsCurrentScene("scene_battle") then
                    OpenSlgUI()
                    local radar_define = require "radar_define"
                    event.Trigger(radar_define.RADAR_CHALLENGE_BATTLE_END,victory)
                    
                end
            end

        end)
        --是否跳过战斗
        --battle_manager.SetCanSkipBattle(function()
        --    return true
        --end)
    end

end



---巅峰赛修改逻辑(原时空擂台)
function SetupWeekendChallengeNew(roleid, arrlineup)
    battle_data.SetReturnSelectFunc(function()
        SetupWeekendChallengeNew(roleid, arrlineup)
    end)
    local weekend_arena_mgr = require "weekend_arena_mgr"
    ui_select_model_node.SetBattleType(common_pb.WeekendArena)
    --ui_window_mgr:CloseAll()
    local getKey = function()
        return 'attack_lineups_' .. player_mgr.GetPlayerRoleID() .. common_pb.WeekendArena
    end
    ---设置战斗英雄数据
    local str = PlayerPrefs.GetString(getKey(), '')
    local hData = {}
    local array = string.split(str, ',', tonumber)
    -- for i, v in ipairs(array) do
    --     local heroEntity = player_mgr.GetPalPartDataBySid(v)
    --     if heroEntity then
    --         hData[i-1] = heroEntity
    --     end
    -- end
    for i = 1, 6 do
        if array[i] ~= nil then
            local v = array[i]
            local heroEntity = player_mgr.GetPalPartDataBySid(v)
            if heroEntity then
                hData[i-1] = heroEntity
            end
        end
    end
    ui_select_hero.SetSaveHeroData(hData)
    --battle_data.SetEnemyInfo("ffff")
    local playerInfo = weekend_arena_mgr.GetRankInfoByRoleID(roleid)
    if playerInfo then
        local info = playerInfo.baseinfo
        local faceStr = info.faceID
        if info.faceStr and not string.IsNullOrEmpty(info.faceStr) then
            faceStr = info.faceStr
        end
        local enemyInfo = {
            maxSoldierLv = info.maxSoldierLv,
            playerName = info.name,
            leagueShortName = info.leagueShortName,
        }
        battle_data.SetEnemyInfo(enemyInfo)
        ui_select_hero.SetTopPowerData(true, nil, nil, info.frameID, faceStr)
    end

    battle_data.skipBattle = false
    battle_data.SetIsCancelOpenBattleAni(true)

    local finishCallBack = function()
        local isShowMain = battle_data.ShowMainPanel 
        if not isShowMain then
            local arena_data = require "arena_data"
            arena_data.OpenAreaWeekend()
            util.DelayCallOnce(0.5,function ()
                weekend_arena_mgr.MSG_WEEKEND_ARENA_CHALLENGE_RIVAL_REQ()
            end)
        else
            --local sand_ui_event_define = require "sand_ui_event_define"
            --event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
        end
    end
    
    local window = NewShowArenaHeroSelectPanel(function(window)
        --进入战斗回调
        if check and check() == false then
            return
        end
        
        
        local selectedHero = window:GetSelectedHero()

        local battleData = {
            stageType = common_pb.WeekendArena,
            stageLevel = roleid,
            heroData = selectedHero,
        }
        --  --print("#selectedHero",#selectedHero)
        --@region 保存设置
        local sorted = { 0, 0, 0, 0, 0 ,0 ,0}
        for pos, data in pairs(selectedHero) do
            sorted[pos + 1 ] = data.heroSid
        end
        --sorted[7] = window:GetSelectedWeapon()
        PlayerPrefs.SetString(getKey(), table.concat(sorted, ','))


        battle_manager.SendBattleChallengeMessage(battleData)

    end,finishCallBack
    ,function ()
        event.Trigger(event.ARENA_PLAYER_DETAILINFO,arrlineup)
    end
    )
    if window ~= nil then

        battle_manager.SetPrepareBattle(function()
            ui_window_mgr:UnloadModule("ui_weekend_arena")
            ui_window_mgr:UnloadModule("ui_weekend_challenge")
            ui_window_mgr:UnloadModule("ui_matchplace_entrance")
        end)

        battle_manager.SetCanSkipBattle(function()
            return true
        end)

        battle_manager.SetFinalizeBattle(common_pb.WeekendArena, function(victory, showui)

            finishCallBack()
        end)
    end


end

--
function RowColumnToPos(row, col)
    
end

--阵营试炼，
function SetupTrialTowerChallenge(_type, difficulty,level,skipBattle,isQuickBattle,startBattleLv)
    battle_data.SetReturnSelectFunc(function()
        SetupTrialTowerChallenge(_type, difficulty,level,skipBattle,isQuickBattle,startBattleLv)
    end)
    ui_select_hero.SetSaveBattleType(common_pb.TheTowerOfIllusion)
    local camp_trial_data = require "camp_trial_data"
    local ui_camp_trial_mgr = require "ui_camp_trial_mgr"
    battle_data.skipBattle = skipBattle
    local trialData ={}
    trialData.trialType = _type
    trialData.difficulty = difficulty
    trialData.curLevel = level
    trialData.isQuickBattle = isQuickBattle
    trialData.skipBattle = skipBattle
    trialData.startBattleLv = startBattleLv

    camp_trial_data.SetTrialData(trialData)
    battle_manager.SetBattleType(common_pb.CampTrial)
    ui_select_model_node.SetBattleType(common_pb.CampTrial)
    local lineUp =  camp_trial_data.GetLineUpByType(_type)
    local length = #lineUp.palList
    --倒序遍历
    for i = length, 1, -1 do
        local canUp = camp_trial_data.GetCanUpBySid(common_pb.CampTrial,lineUp.palList[i].palId)
        if not canUp then
            table.remove(lineUp.palList, i)
        end
    end
    local hData = battle_data.LineUpToHeroData(lineUp)
    
    local cfg = camp_trial_data.GetCampTrialLevelCfg(_type, difficulty, level)
    if not cfg then
        return
    end
    local monsterId = cfg.enemyID

    ui_select_hero.SetSaveHeroData(hData)
    --ui_select_model_node.SetBattleType(common_pb.TheTowerOfIllusion)
    local cfg_monster = game_scheme:monsterTeam_0(monsterId)
    local EnemyFaceID = cfg_monster and cfg_monster.faceID or 0
--[[    local enemyInfo = {
        maxSoldierLv = info.maxSoldierLv,
        playerName = info.name,
        leagueShortName = info.leagueShortName,
    }
    battle_data.SetEnemyInfo(enemyInfo)
    battle_data.SetIsCancelOpenBattleAni(true)]]
    ui_select_hero.SetTopPowerData(true, nil, EnemyFaceID)
    ui_select_hero.SetEnemyByMonsterTeam(monsterId, nil, nil, 1)
    --local cfg_monster = game_scheme:monsterTeam_0(monsterId)
    --if cfg_monster then
    --    ui_bs_arena_hero_select.SetGrindThreshold(1234, cfg_monster.Power)
    --end
    local window = NewShowArenaHeroSelectPanel(function(window,isGrind)
        --进入战斗回调

        if camp_trial_data.GetIsReset() then
            --提前返回阵营试炼主界面然后再弹窗
            ui_camp_trial_mgr.DoOnReset()
            ui_camp_trial_mgr.ShowResetTip()
            return
        end
        
        local selectedHero = window:GetSelectedHero()
        --[[ ui_illusion_tower.SetHeroFormation(_type, selectedHero)
         local stageType = ui_illusion_tower.GetStageType(5)
         local battleData = {
             stageType = stageType,
             stageLevel = level,
             heroData = selectedHero,
         }
         battle_manager.SendBattleChallengeMessage(battleData)
         ]]


        if isQuickBattle then
            ui_camp_trial_mgr.SendQuickChallReq(_type,selectedHero)
        else
            ui_camp_trial_mgr.SendChallReq(_type,selectedHero)
            util.StartWatch("loading battle", "准备战斗")

        end
    end,function ()

        if camp_trial_data.GetIsReset() then
            --提前返回阵营试炼主界面然后再弹窗
            ui_camp_trial_mgr.DoOnReset()
            ui_camp_trial_mgr.ShowResetTip()
            return
        end
        
        --不战斗关闭战斗回调
        --log.Error("不战斗关闭")
        event.Trigger(event.NEW_BATTLE_BACK)
        ui_camp_trial_mgr.EnterTower()
        --OpenSlgUI()
        --event.Trigger(event.CANCEL_HOT_BALL_HOOK_SCENE_TIME_OUT)--取消暂停热气球
    end,function ()
      
    end)


    ---战斗结束回调
    battle_manager.SetFinalizeBattle(common_pb.CampTrial,function(victory, showui)
        battle_manager.SetBattleType(nil)

    end)

end



--城际货车
function SetupCarriageChallenge(entitySid, enemyData,EnemyFaceID, playerName, leagueShortName)
    battle_data.SetReturnSelectFunc(function()
        SetupCarriageChallenge(entitySid, enemyData,EnemyFaceID, playerName, leagueShortName)
    end)
    local sand_ui_event_define = require "sand_ui_event_define"
    local net_sandbox_module = require "net_sandbox_module"
    event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_UI)
    battle_data.skipBattle = false
    local hData = battle_manager.GetHeroFormation("Carriage")
    ui_select_hero.SetSaveBattleType(common_pb.Carriage)
    ui_select_hero.SetSaveHeroData(hData)
    ui_select_hero.SetAdjustBtnVisible(true, false)
    ui_select_model_node.SetBattleType(common_pb.Carriage)
    --log.Error("Error SetupCarriageChallenge",EnemyFaceID)
    ui_select_hero.SetTopPowerData(true, nil, nil,nil,EnemyFaceID)

    local enemyDatas = {}
    local power = 0
    if enemyData and enemyData.pals then
        for i, hero in ipairs(enemyData.pals) do
            local key = battle_data.GetIndexByRowAndCol(hero.row,hero.col)--hero.row*2 + hero.col
            power = power + hero.heroPower
            enemyDatas[key] = gw_hero_mgr.CreateVirtualEntity(hero.heroID,hero.heroLevel, hero.heroStar,1)
        end
    end
    local enemyInfo = {
        maxSoldierLv = 1,       --TODO临时用默认等级处理
        playerName = playerName,
        leagueShortName = leagueShortName,
    }
    battle_data.SetEnemyInfo(enemyInfo)
    battle_data.SetIsCancelOpenBattleAni(true)
    ui_select_hero.SetEnemyData(enemyDatas,enemyData.power,nil,true,1,{weaponid =enemyData.weaponID,weaponLv = enemyData.weaponLevel})
    local window = NewShowArenaHeroSelectPanel(function(window, isGrind)
        --进入战斗回调
        --把新的协议赋值然后发送过去
        --region 沙盘副本战斗请求
        local sandbox_pb = require "sandbox_pb"
        local msg = sandbox_pb.TMSG_SANDBOX_GAME_BATTLE_REQ()
        msg.stageType = common_pb.Carriage
        --msg.lon
        msg.param1 = entitySid
        local sand_self_data = require "sand_self_data"
        msg.sandboxSid = sand_self_data.GetVisualSandBoxSid()
        local selectedHero = window:GetSelectedHero()
        battle_manager.SaveHeroFormation(selectedHero,"Carriage")
        if selectedHero then
            local t = {}
            for pos, hero in pairs(selectedHero) do
                table.insert(t,{pos=pos,hero=hero})
            end
            table.sort(t, function(v1, v2)
                return v1.pos < v2.pos
            end)

            for index, data in ipairs(t) do
                if data.pos >= 0 then--这里是为了防止一些偶然错误导致拖拽英雄位置为-100也存储了进来
                    local dataInfo = msg.lineUp.palList:add()
                    dataInfo.palId = data.hero.heroSid
                    local row,col = battle_data.GetRowAndColByIndex(data.pos + 1)
                    dataInfo.row = row
                    dataInfo.col = col
                end
            end
        end
        local net_sandbox_module =require "net_sandbox_module"
        net_sandbox_module.MSG_SANDBOX_GAME_BATTLE_REQ(msg)
        CloseSlgUI()
        ui_window_mgr:UnloadModule("ui_main_slg_top")
        --endregion
    end, function()
        --不战斗关闭战斗回调
        OpenSlgUI()
      
    end)

    if window ~= nil then
        --设置战斗前
        battle_manager.SetPrepareBattle(function()
            CloseSlgUI()
        end)
        --设置战斗结束？
        battle_manager.SetFinalizeBattle(common_pb.Carriage, function(victory, showui)
            --if showui then
            --
            --end
            local main_slg_data = require "main_slg_data"
            local gw_const = require "gw_const"
            if main_slg_data.GetCurSceneType() == gw_const.ESceneType.Sand then
                OpenSlgUI()
            end

        end)
        battle_manager.SetCanSkipBattle(function()
            return true
        end)
    end

end

--联盟火车
function SetupAllianceTrainChallenge(entity)
    battle_data.SetReturnSelectFunc(function()
    SetupAllianceTrainChallenge(entity)
    end)
    local sand_ui_event_define = require "sand_ui_event_define"
    event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_UI)
    battle_data.skipBattle = false
    --打开作战计划界面
    ui_window_mgr:ShowModule("ui_alliance_train_plan",nil,nil,{entity = entity})
end

--主线关卡
function OpenSlgUILevel()
    local gw_home_chapter_data = require "gw_home_chapter_data"
    if gw_home_chapter_data:CheckPassLevel() then
        windowMgr:ShowModule("new_hook_scene", function()
            local laymain_data = require "laymain_data"
            local level = laymain_data.GetPassLevel()
            event.Trigger(event.CHANGE_IDLE_MAP, level, true)
        end)
    else
        OpenSlgUI()
    end
end
function OpenSlgUI()
    -- local sand_ui_event_define = require "sand_ui_event_define"
    -- event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
end
function CloseSlgUI()
    -- local sand_ui_event_define = require "sand_ui_event_define"
    -- event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_MAIN)
end