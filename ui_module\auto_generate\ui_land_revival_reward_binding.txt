local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local Slider = CS.UnityEngine.UI.Slider
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable


module("ui_land_revival_reward_binding")

UIPath = "ui/prefabs/gw/gw_landrevival/uilandrevivalreward.prefab"

WidgetTable ={
	btn_closeBtn = { path = "btn_closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	txt_tips = { path = "Panel/Bg/tipsBg/txt_tips", type = Text, },
	txt_time = { path = "Panel/bgTime/txt_time", type = Text, },
	btn_vip = { path = "Panel/btn_vip", type = Button, event_name = "OnBtnVipClickedProxy"},
	txt_vipLv = { path = "Panel/btn_vip/txt_vipLv", type = Text, },
	sld_FirstProgress = { path = "Panel/ListTop/sld_FirstProgress", type = Slider, value_changed_event = "OnSliderFirstProgressValueChange"},
	txt_totalNum = { path = "Panel/ListTop/hammerBg/txt_totalNum", type = Text, },
	srt_RewardContent = { path = "Panel/rewardList/Viewport/srt_RewardContent", type = ScrollRectTable, },
	btn_receive = { path = "Panel/btn_receive", type = Button, event_name = "OnBtnReceiveClickedProxy"},
	btn_goTo = { path = "Panel/btn_goTo", type = Button, event_name = "OnBtnGoToClickedProxy"},

}
