local cysoldierssortie_performance_mgr = bc_Class("cysoldierssortie_performance_mgr")
local cysoldierssortie_performance_config = require("cysoldierssortie_performance_config")
local device_param_util = require "device_param_util"
local log = log
local event = require "event"
local tostring = tostring
local EventUploadState = 
{
}

-- 批量事件上报数据存储
local BatchEventData = {}
local BatchCounter = 0
function cysoldierssortie_performance_mgr:EventUpload(PerformanceItemType,Value)
    if EventUploadState and not EventUploadState[PerformanceItemType] then
        --[[event.Trigger(event.GAME_EVENT_REPORT,"SoldierSSortiePerformanceItemType", {
            performanceItemType = PerformanceItemType,
            value =  tostring(Value),
        })]]--
        -- 将事件数据存储到批量上报数据中，而不是立即上报
        BatchEventData[PerformanceItemType] = tostring(Value)
        EventUploadState[PerformanceItemType] = true
        BatchCounter = BatchCounter + 1
        local performanceItemType =  cysoldierssortie_performance_config.PerformanceItemType
        if BatchCounter >= performanceItemType.TypeCount then
            event.Trigger(event.GAME_EVENT_REPORT,"SoldierSSortiePerformanceItemType", {
                performanceItemType_Name_1 = performanceItemType.MaxEnemyViewNum,
                performanceItemType_Value_1 = BatchEventData[performanceItemType.MaxEnemyViewNum] or "100",

                performanceItemType_Name_2 = performanceItemType.PostProcess,
                performanceItemType_Value_2 = BatchEventData[performanceItemType.PostProcess] or "true",

                performanceItemType_Name_3 = performanceItemType.RealTimeShadow,
                performanceItemType_Value_3 = BatchEventData[performanceItemType.RealTimeShadow] or "true",

                performanceItemType_Name_4 = performanceItemType.PlaneShadow,
                performanceItemType_Value_4 = BatchEventData[performanceItemType.PlaneShadow] or "37",

                performanceItemType_Name_5 = performanceItemType.LevelUpEffectNum,
                performanceItemType_Value_5 = BatchEventData[performanceItemType.LevelUpEffectNum] or "10",

                performanceItemType_Name_6 = performanceItemType.RenderScale,
                performanceItemType_Value_6 = BatchEventData[performanceItemType.RenderScale] or "0.7",

                performanceItemType_Name_7 = performanceItemType.AsyncECSCallBackBatchLimit,
                performanceItemType_Value_7 = BatchEventData[performanceItemType.AsyncECSCallBackBatchLimit] or "30",
                
                performanceItemType_Name_8 = performanceItemType.UpdateSpeedRate,
                performanceItemType_Value_8 = BatchEventData[performanceItemType.UpdateSpeedRate] or "1",

                performanceItemType_Name_9 = performanceItemType.PhysicDeltaTime,
                performanceItemType_Value_9 = BatchEventData[performanceItemType.PhysicDeltaTime] or "0.04",

                performanceItemType_Name_10 = performanceItemType.HUDText,
                performanceItemType_Value_10 = BatchEventData[performanceItemType.HUDText] or "true",

                performanceItemType_Name_11 = performanceItemType.BeHitEffect,
                performanceItemType_Value_11 = BatchEventData[performanceItemType.BeHitEffect] or "true",
                
                performanceItemType_Name_12 = performanceItemType.FeedCompEnable,
                performanceItemType_Value_12 = BatchEventData[performanceItemType.FeedCompEnable] or "true"
            })
        end
    end
end

function cysoldierssortie_performance_mgr:GetEnemyViewNum()
    local level = device_param_util.JudgeDeviceLevel()
    if level then
        local performanceItemType =  cysoldierssortie_performance_config.PerformanceItemType
        local performanceConfig = cysoldierssortie_performance_config.PerformanceConfig
        local maxEnemyViewConfig =  performanceConfig[performanceItemType.MaxEnemyViewNum]
        local maxViewNum = maxEnemyViewConfig[level]
        self:EventUpload(performanceItemType.MaxEnemyViewNum,maxViewNum)
        return maxViewNum
    end
    return 60
end

function cysoldierssortie_performance_mgr:IsPostProcess()
    local level = device_param_util.JudgeDeviceLevel()
    if level then
        local performanceItemType =  cysoldierssortie_performance_config.PerformanceItemType
        local performanceConfig = cysoldierssortie_performance_config.PerformanceConfig
        local postProcessConfig =  performanceConfig[performanceItemType.PostProcess]
        local enable = postProcessConfig[level]
        self:EventUpload(performanceItemType.PostProcess,enable)
        return enable
    end
    return false
end

function cysoldierssortie_performance_mgr:IsRealTimeShadow()
    local level = device_param_util.JudgeDeviceLevel()
    if level then
        local performanceItemType =  cysoldierssortie_performance_config.PerformanceItemType
        local performanceConfig = cysoldierssortie_performance_config.PerformanceConfig
        local realTimeShadow =  performanceConfig[performanceItemType.RealTimeShadow]
        local enable = realTimeShadow[level]
        self:EventUpload(performanceItemType.RealTimeShadow,enable)
        return enable
    end
    return false
end

function cysoldierssortie_performance_mgr:GetPlaneShadowDis()
    local level = device_param_util.JudgeDeviceLevel()
    if level then
        local performanceItemType =  cysoldierssortie_performance_config.PerformanceItemType
        local performanceConfig = cysoldierssortie_performance_config.PerformanceConfig
        local planeShadow =  performanceConfig[performanceItemType.PlaneShadow]
        local shadowDis = planeShadow[level]
        self:EventUpload(performanceItemType.PlaneShadow,shadowDis)
        return shadowDis
    end
    return 17
end

function cysoldierssortie_performance_mgr:GetLevelUpEffectNum()
    local level = device_param_util.JudgeDeviceLevel()
    if level then
        local performanceItemType =  cysoldierssortie_performance_config.PerformanceItemType
        local performanceConfig = cysoldierssortie_performance_config.PerformanceConfig
        local levelUpEffectNum =  performanceConfig[performanceItemType.LevelUpEffectNum]
        local num = levelUpEffectNum[level]
        self:EventUpload(performanceItemType.LevelUpEffectNum,num)
        return num
    end
    return 3
end

function cysoldierssortie_performance_mgr:GetRenderScale()
    local level = device_param_util.JudgeDeviceLevel()
    if level then
        local performanceItemType =  cysoldierssortie_performance_config.PerformanceItemType
        local performanceConfig = cysoldierssortie_performance_config.PerformanceConfig
        local renderScale =  performanceConfig[performanceItemType.RenderScale]
        local scale = renderScale[level]
        self:EventUpload(performanceItemType.RenderScale,scale)
        return scale
    end
    return 0.8
end

function cysoldierssortie_performance_mgr:GetAsyncECSCallBackBatchLimit()
    local level =  device_param_util.JudgeDeviceLevel()
    if level then
        local performanceItemType =  cysoldierssortie_performance_config.PerformanceItemType
        local performanceConfig = cysoldierssortie_performance_config.PerformanceConfig
        local Limit =  performanceConfig[performanceItemType.AsyncECSCallBackBatchLimit]
        local limit = Limit[level]
        self:EventUpload(performanceItemType.AsyncECSCallBackBatchLimit,limit)
        return limit
    end
    return 5
end

function cysoldierssortie_performance_mgr:GetUpdateSpeedRate()
    local level =   device_param_util.JudgeDeviceLevel()
    if level then
        local performanceItemType =  cysoldierssortie_performance_config.PerformanceItemType
        local performanceConfig = cysoldierssortie_performance_config.PerformanceConfig
        local updateSpeedRates =  performanceConfig[performanceItemType.UpdateSpeedRate]
        local rate = updateSpeedRates[level]
        self:EventUpload(performanceItemType.UpdateSpeedRate,rate)
        return rate
    end
    return 1
end

function cysoldierssortie_performance_mgr:GetPhysicDeltaTime()
    local level =   device_param_util.JudgeDeviceLevel()
    if level then
        local performanceItemType =  cysoldierssortie_performance_config.PerformanceItemType
        local performanceConfig = cysoldierssortie_performance_config.PerformanceConfig
        local deltaTimes =  performanceConfig[performanceItemType.PhysicDeltaTime]
        local deltaTime = deltaTimes[level]
        self:EventUpload(performanceItemType.PhysicDeltaTime,deltaTime)
        return deltaTime
    end
    return 0.04
end

function cysoldierssortie_performance_mgr:EnableHUDText()
    local level =   device_param_util.JudgeDeviceLevel()
    if level then
        local performanceItemType =  cysoldierssortie_performance_config.PerformanceItemType
        local performanceConfig = cysoldierssortie_performance_config.PerformanceConfig
        local hudTexts =  performanceConfig[performanceItemType.HUDText]
        local enable = hudTexts[level]
        self:EventUpload(performanceItemType.HUDText,enable)
        return enable
    end
    return true
end

function cysoldierssortie_performance_mgr:EnableBeHitEffect()
    local level =   device_param_util.JudgeDeviceLevel()
    if level then
        local performanceItemType =  cysoldierssortie_performance_config.PerformanceItemType
        local performanceConfig = cysoldierssortie_performance_config.PerformanceConfig
        local enableBeHitEffects =  performanceConfig[performanceItemType.BeHitEffect]
        local enable = enableBeHitEffects[level]
        self:EventUpload(performanceItemType.BeHitEffect,enable)
        return enable
    end
    return true
end

function cysoldierssortie_performance_mgr:EnableFeedComp()
    local level =   device_param_util.JudgeDeviceLevel()
    if level then
        local performanceItemType =  cysoldierssortie_performance_config.PerformanceItemType
        local performanceConfig = cysoldierssortie_performance_config.PerformanceConfig
        local feedCompsEnable =  performanceConfig[performanceItemType.FeedCompEnable]
        local enable = feedCompsEnable[level]
        self:EventUpload(performanceItemType.FeedCompEnable,enable)
        return enable
    end
    return true
end

return cysoldierssortie_performance_mgr