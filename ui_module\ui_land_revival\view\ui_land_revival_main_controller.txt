local print = print
local require = require
local pairs = pairs
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local event = require "event"
local land_revival_mgr = require "land_revival_mgr"
local festival_activity_mgr = require "festival_activity_mgr"
local land_revival_data = require "land_revival_data"
local log = require "log"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"

--region Controller Life
module("ui_land_revival_main_controller")
local controller = nil
local UIController = newClass("ui_land_revival_main_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)
    self.curDay = land_revival_data.GetCurDayNew()
    
    self:GetActivityEndTime()
    self:CreateDayTaskData()
    self:GetVipTaskProgress()
    self:GetVipBoxRewardData()
    
    land_revival_mgr.RecordEnterActivityTime()
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents()
    --道具补充监听
    self.goodsChangeFun = function(e,id,sid,num)
        if id == land_revival_data.GetHammerItemID() then
            self:GetVipTaskProgress()
        end
    end
    self:RegisterEvent(event.UPDATE_GOODS_NUM_CHANGE, self.goodsChangeFun)
end

function UIController:AutoUnsubscribeEvents()
end
--endregion

--region Controller Logic
function UIController:OnBtnHelpClickedProxy()
    ui_window_mgr:ShowModule("ui_land_revival_help")
end
function UIController:OnSliderLeftSliderValueChange(value)
end
function UIController:OnBtnBoxIconClickedProxy()
    ui_window_mgr:ShowModule("ui_land_revival_reward")
end

---@public function 创建每日任务数据
function UIController:CreateDayTaskData()
    local viewData = {}
    local dayTaskCfgArr = land_revival_data.GetDayTaskData()
    for i = 1, 7 do
        local tempData = {}
        tempData.clickFunc = function()
            land_revival_data.SetMainJumpDay(i)
            festival_activity_mgr.OpenActivityUIByActivityID(land_revival_data.GetActivityTaskID(),{ jumpDay = i})
        end
        tempData.dayText = string.format2(lang.Get(1000798),i)
        tempData.iconID = dayTaskCfgArr[i].Icon
        tempData.isLock = i > self.curDay
        tempData.isCurDay = i == self.curDay 
        tempData.isUp = i < 4
        table.insert(viewData, tempData)
    end
    
    self:TriggerUIEvent("InitDayItem", viewData)
end

---@public function 获取活动结束时间
function UIController:GetActivityEndTime()
    local activityData = land_revival_data.GetActivityData()
    if activityData then
        local endTime = activityData.endTimeStamp
        self:TriggerUIEvent("SetActivityTimer", endTime)
    end
end

---@public function 获取vip任务总进度
function UIController:GetVipTaskProgress()
    local curProgress, totalProgressRate = land_revival_data.GetVipTaskTotalProgress()
    self:TriggerUIEvent("SetVipTaskProgress", curProgress, totalProgressRate)
end

---@public function 获取vip任务宝箱奖励
function UIController:GetVipBoxRewardData()
    self:TriggerUIEvent("InitBoxRewardShow", land_revival_data.GetVipTaskMergeReward())
end
--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
