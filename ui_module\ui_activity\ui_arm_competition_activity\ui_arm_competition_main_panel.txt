﻿--region FileHead
--- ui_arm_competition_main_panel.txt
-- author:  author
-- ver:     1.0
-- desc:    
-------------------------------------------------
--endregion 

--region Require
local require = require
local type = type
local pairs = pairs
local ipairs = ipairs
local string = string
local table = table

local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local enum_define = require "enum_define"
local class = require "class"
local lang = require "lang"
local ui_base = require "ui_base"
local module_scroll_list = require "scroll_list"
local Common_Util = CS.Common_Util.UIUtil
local game_scheme = require "game_scheme"
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local Animation = CS.UnityEngine.Animation
local goods_item = require "goods_item_new"
local item_data = require "item_data"
local iui_item_detail = require "iui_item_detail"
local	string_util = require "string_util"

local RectTransform = CS.UnityEngine.RectTransform
local windowMgr = require "ui_window_mgr"
local os = os
local math = math
local time_util = require "time_util"
local util = require "util"
local player_mgr = require "player_mgr"
local arm_competition_activity_data = require "gw_arm_competition_activity_data"
local arm_competition_task_obj = require "arm_competition_task_obj"
local arm_competition_rank_obj = require "arm_competition_rank_obj"
local face_item = require "face_item_new"
local LayoutRebuilder = CS.UnityEngine.UI.LayoutRebuilder
local SortingGroup = CS.UnityEngine.Rendering.SortingGroup
local Canvas = CS.UnityEngine.Canvas
local Time = CS.UnityEngine.Time
local DynamicGameObjectLoader = CS.War.UI.DynamicGameObjectLoader
--endregion 

--region ModuleDeclare
module("ui_arm_competition_main_panel")
local ui_path = "ui/prefabs/gw/activity/armcompetitionactivity/uiarmcompetitionmainpanel.prefab"
local window = nil
local UIView = {}
--endregion 

--region WidgetTable
UIView.widget_table = 
{
    bannerImg = {path = "BaseScrollView/Viewport/Content/TopObj/Bottom/Banner",type = SpriteSwitcher},
    bannerTitle = {path = "BaseScrollView/Viewport/Content/TopObj/Bottom/Banner/Title", type = "Text"},
    bannerTipsBtn = {path = "BaseScrollView/Viewport/Content/TopObj/Bottom/Banner/TipsBtn", type = "Button",event_name = "ShowTips"},
    bannerLevel = {path = "BaseScrollView/Viewport/Content/TopObj/Bottom/Banner/Level/Value",type = "Text"},
    
    bannerShowCalendarBtn = {path = "BaseScrollView/Viewport/Content/TopObj/Bottom/Calendar",type = "Button",event_name = "ShowCalendar"},
    bannerShowRankRewardBtn = {path = "BaseScrollView/Viewport/Content/TopObj/Bottom/Rank",type = "Button",event_name = "ShowRankReward"},
    
    totalSlider = {path = "BaseScrollView/Viewport/Content/TopObj/Bottom/TotalSlider/Slider",type = "Slider"},
    totalPointTxt = {path = "BaseScrollView/Viewport/Content/TopObj/Bottom/TotalSlider/Icon/Text",type = "Text"},
    
    totalPoint_1_Text = {path = "BaseScrollView/Viewport/Content/TopObj/Top/TopPoint_1/Text",type = "Text"},
    totalPoint_1_Chess = {path = "BaseScrollView/Viewport/Content/TopObj/Top/TopPoint_1/Chess",type = SpriteSwitcher},
    totalPoint_1_Chess_RedDot = {path = "BaseScrollView/Viewport/Content/TopObj/Top/TopPoint_1/Chess/RedDot",type = "RectTransform"},
    totalPoint_2_Text = {path = "BaseScrollView/Viewport/Content/TopObj/Top/TopPoint_2/Text",type = "Text"},
    totalPoint_2_Chess = {path = "BaseScrollView/Viewport/Content/TopObj/Top/TopPoint_2/Chess",type = SpriteSwitcher},
    totalPoint_2_Chess_RedDot = {path = "BaseScrollView/Viewport/Content/TopObj/Top/TopPoint_2/Chess/RedDot",type = "RectTransform"},
    totalPoint_3_Text = {path = "BaseScrollView/Viewport/Content/TopObj/Top/TopPoint_3/Text",type = "Text"},
    totalPoint_3_Chess = {path = "BaseScrollView/Viewport/Content/TopObj/Top/TopPoint_3/Chess",type = SpriteSwitcher},
    totalPoint_3_Chess_RedDot = {path = "BaseScrollView/Viewport/Content/TopObj/Top/TopPoint_3/Chess/RedDot",type = "RectTransform"},
    totalPoint_1_Chess_Btn = {path = "BaseScrollView/Viewport/Content/TopObj/Top/TopPoint_1/Chess",type = "Button",event_name = "GetTotalChess_1"},
    totalPoint_2_Chess_Btn = {path = "BaseScrollView/Viewport/Content/TopObj/Top/TopPoint_2/Chess",type = "Button",event_name = "GetTotalChess_2"},
    totalPoint_3_Chess_Btn = {path = "BaseScrollView/Viewport/Content/TopObj/Top/TopPoint_3/Chess",type = "Button",event_name = "GetTotalChess_3"},
    --totalPoint_1_Particle = {path = "BaseScrollView/Viewport/Content/TopObj/Particle/particleTopPoint_1",type = "RectTransform"},
    --totalPoint_2_Particle = {path = "BaseScrollView/Viewport/Content/TopObj/Particle/particleTopPoint_2",type = "RectTransform"},
    --totalPoint_3_Particle = {path = "BaseScrollView/Viewport/Content/TopObj/Particle/particleTopPoint_3",type = "RectTransform"},
    totalPoint_1_Particle_root = {path = "BaseScrollView/Viewport/Content/TopObj/Particle/particleTopPoint_1_root",type = DynamicGameObjectLoader},
    totalPoint_2_Particle_root = {path = "BaseScrollView/Viewport/Content/TopObj/Particle/particleTopPoint_2_root",type = DynamicGameObjectLoader},
    totalPoint_3_Particle_root = {path = "BaseScrollView/Viewport/Content/TopObj/Particle/particleTopPoint_3_root",type = DynamicGameObjectLoader},
    totalPoint_1_Chess_Anim = {path = "BaseScrollView/Viewport/Content/TopObj/Top/TopPoint_1/Chess",type = Animation},
    totalPoint_2_Chess_Anim = {path = "BaseScrollView/Viewport/Content/TopObj/Top/TopPoint_2/Chess",type = Animation},
    totalPoint_3_Chess_Anim = {path = "BaseScrollView/Viewport/Content/TopObj/Top/TopPoint_3/Chess",type = Animation},
    
    pointCountText = {path = "BaseScrollView/Viewport/Content/TopObj/Bottom/PointCountBg/Text",type = "Text"},
    timeLeftText = {path = "BaseScrollView/Viewport/Content/TopObj/Bottom/TimeLeftBg/Text",type = "Text"},
    pointSlider = {path = "BaseScrollView/Viewport/Content/TopObj/Bottom/PointSlider/Slider",type = "Slider"},
    
    point_1_Chess = {path = "BaseScrollView/Viewport/Content/TopObj/Top/Point_1_1/Chess",type = SpriteSwitcher},
    point_1_Chess_Btn = {path = "BaseScrollView/Viewport/Content/TopObj/Top/Point_1_1/Chess",type = "Button",event_name = "GetPointChess_1"},
    point_1_BubbleObj = {path = "BaseScrollView/Viewport/Content/TopObj/Top/Point_1_1/Bubble",type = "RectTransform"},
    point_1_BubbleText = {path = "BaseScrollView/Viewport/Content/TopObj/Top/Point_1_1/Bubble/Text",type = "Text"},
    point_1_Text = {path = "BaseScrollView/Viewport/Content/TopObj/Top/Point_1_1/Text",type = "Text"},
    --point_1_Particle = {path = "BaseScrollView/Viewport/Content/TopObj/Particle/particleBasePoint_1",type = "RectTransform"},
    point_1_Particle_root = {path = "BaseScrollView/Viewport/Content/TopObj/Particle/particleBasePoint_1_root",type = DynamicGameObjectLoader},
    point_1_Chess_RedDot = {path = "BaseScrollView/Viewport/Content/TopObj/Top/Point_1_1/Chess/RedDot",type = "RectTransform"},
    
    point_2_Chess = {path = "BaseScrollView/Viewport/Content/TopObj/Top/Point_1_2/Chess",type = SpriteSwitcher},
    point_2_Chess_Btn = {path = "BaseScrollView/Viewport/Content/TopObj/Top/Point_1_2/Chess",type = "Button",event_name = "GetPointChess_2"},
    point_2_BubbleObj = {path = "BaseScrollView/Viewport/Content/TopObj/Top/Point_1_2/Bubble",type = "RectTransform"},
    point_2_BubbleText = {path = "BaseScrollView/Viewport/Content/TopObj/Top/Point_1_2/Bubble/Text",type = "Text"},
    point_2_Text = {path = "BaseScrollView/Viewport/Content/TopObj/Top/Point_1_2/Text",type = "Text"},
    --point_2_Particle = {path = "BaseScrollView/Viewport/Content/TopObj/Particle/particleBasePoint_2",type = "RectTransform"},
    point_2_Particle_root = {path = "BaseScrollView/Viewport/Content/TopObj/Particle/particleBasePoint_2_root",type = DynamicGameObjectLoader},
    point_2_Chess_RedDot = {path = "BaseScrollView/Viewport/Content/TopObj/Top/Point_1_2/Chess/RedDot",type = "RectTransform"},
    
    point_3_Chess = {path = "BaseScrollView/Viewport/Content/TopObj/Top/Point_1_3/Chess",type = SpriteSwitcher},
    point_3_Chess_Btn = {path = "BaseScrollView/Viewport/Content/TopObj/Top/Point_1_3/Chess",type = "Button",event_name = "GetPointChess_3"},
    point_3_BubbleObj = {path = "BaseScrollView/Viewport/Content/TopObj/Top/Point_1_3/Bubble",type = "RectTransform"},
    point_3_BubbleText = {path = "BaseScrollView/Viewport/Content/TopObj/Top/Point_1_3/Bubble/Text",type = "Text"},
    point_3_Text = {path = "BaseScrollView/Viewport/Content/TopObj/Top/Point_1_3/Text",type = "Text"},
    --point_3_Particle = {path = "BaseScrollView/Viewport/Content/TopObj/Particle/particleBasePoint_3",type = "RectTransform"},
    point_3_Particle_root = {path = "BaseScrollView/Viewport/Content/TopObj/Particle/particleBasePoint_3_root",type = DynamicGameObjectLoader},
    point_3_Chess_RedDot = {path = "BaseScrollView/Viewport/Content/TopObj/Top/Point_1_3/Chess/RedDot",type = "RectTransform"},
    
    point_1_Chess_Anim = {path = "BaseScrollView/Viewport/Content/TopObj/Top/Point_1_1/Chess",type = Animation},
    point_2_Chess_Anim = {path = "BaseScrollView/Viewport/Content/TopObj/Top/Point_1_2/Chess",type = Animation},
    point_3_Chess_Anim = {path = "BaseScrollView/Viewport/Content/TopObj/Top/Point_1_3/Chess",type = Animation},
    
    taskTrans = {path = "BaseScrollView/Viewport/Content/task/ScrollList",type = "RectTransform"},
    totalTrans = {path = "BaseScrollView/Viewport/Content",type = "RectTransform"},
    playerListTrans = {path = "BaseScrollView/Viewport/Content/playerList/ScrollList",type = "RectTransform"},
    
    bigChessBubbleObj = {path = "BaseScrollView/Viewport/Content/TopObj/Top/TopPoint_3/BigChessTips",type = "RectTransform"},
    bigChessBubbleTrans = {path = "BaseScrollView/Viewport/Content/TopObj/Top/TopPoint_3/BigChessTips/RewardGrid",type = "RectTransform"},
    rankObj = {path = "BaseScrollView/Viewport/Content/playerList",type = "RectTransform"},
    noRankObj = {path = "BaseScrollView/Viewport/Content/empty",type = "RectTransform"},

    sortGroup_eff = { path = "BaseScrollView/Viewport/Content/TopObj/Particle", type = SortingGroup, },
    canvas_top = { path = "BaseScrollView/Viewport/Content/TopObj/Top", type = Canvas},

    rectTrans = { path = "ArmCompetitionRankObj", type = RectTransform },
    rankBtn = {path = "ArmCompetitionRankObj",type = "Button"},
    sliderObj = {path = "ArmCompetitionRankObj/Slider",type = "Slider"},
    sliderValue = {path = "ArmCompetitionRankObj/Slider/Value",type = "Text"},

    top3Img = {path = "ArmCompetitionRankObj/Top3Img",type = SpriteSwitcher},
    rankTxt = {path = "ArmCompetitionRankObj/RankTxt",type = "Text"},
    top3Icon = {path = "ArmCompetitionRankObj/Icon",type = SpriteSwitcher},
    nameTxt = {path = "ArmCompetitionRankObj/Name",type = "Text"},
    headTrans = {path = "ArmCompetitionRankObj/Head",type = RectTransform},
    sexIcon = {path = "ArmCompetitionRankObj/Sex",type = SpriteSwitcher},

    rankChangeObj = {path = "ArmCompetitionRankObj/RankChangeIcon",type = SpriteSwitcher},
    rankChangeTextDown = {path = "ArmCompetitionRankObj/RankChangeIcon/DownText",type = "Text"},
    rankChangeTextUp = {path = "ArmCompetitionRankObj/RankChangeIcon/UpText",type = "Text"},
    
    errorTips = {path = "BaseScrollView/Viewport/Content/errorTips",type = "RectTransform"}
    
}
--endregion 

--region function 设置View-Controller模式的UI
-- return type  ---- 未定义/VC/纯V   
-- 注意，View-Controller模式的ui必须要重写这个接口
function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end
--endregion 

--region WindowInit
--[[窗口初始化]]
function UIView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)
    self:SubscribeEvents()   
    self.timeTicker = nil;
    self.taskItem = {}
    self.playerItem = {}
    self.bubbleItem = {}
    self.pointChessObj =
    {
        [1] = self.point_1_Chess.transform,
        [2] = self.point_2_Chess.transform,
        [3] = self.point_3_Chess.transform,
    }
    self.totalChessObj =
    {
        [1] = self.totalPoint_1_Chess.transform,
        [2] = self.totalPoint_2_Chess.transform,
        [3] = self.totalPoint_3_Chess.transform,
    }
    --region User
    --endregion 
end --///<<< function

--endregion 


--region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIView:OnShow()
    local st = Time.realtimeSinceStartup
    self.__base:OnShow()
    --self:UpdateUI()
end --///<<< function

--endregion 

--region WindowOnHide
--[[界面隐藏时调用]]
function UIView:OnHide()
    self.__base:OnHide()    
end --///<<< function

--endregion 


--region WindowClose
function UIView:Close()
    self.__base:Close()
    self:UnsubscribeEvents() 
    window = nil
    if self.taskItem then
        for i,v in ipairs(self.taskItem) do
            v:Dispose()
            v = nil
        end
    end
    if self.playerItem then
        for i,v in ipairs(self.playerItem) do
            v:Dispose()
            v = nil
        end
    end
    if self.bubbleItem then
        for i,v in ipairs(self.bubbleItem) do
            v:Dispose()
            v = nil
        end
    end
    if self.faceItem then
        self.faceItem:Dispose()
        self.faceItem = nil
    end
    if self.timeTicker then
        util.RemoveDelayCall(self.timeTicker)
        self.timeTicker = nil
    end

    if self.TweenTimeTicker then
        util.RemoveDelayCall(self.TweenTimeTicker)
        self.TweenTimeTicker = nil
    end
    
    --region User
    --endregion 
end --///<<< function
--endregion 
--region 事件注册
function UIView:SubscribeEvents()    
    -- 注意 事件建议使用self:RegisterEvent(event_name,func)来注册，不需要手动维护注销了    
end
function UIView:UnsubscribeEvents()    
    
end

--endregion

--region 功能函数区
---********************功能函数区**********---
function UIView:UpdateUI()
    self.sortGroup_eff.sortingOrder = self.curOrder+2
    self.canvas_top.sortingOrder = self.curOrder+5
end

function UIView:ShowPanel(data,state)
    self.raceId = data.ArmsRaceRoudsID;
    local raceCfg = game_scheme:ArmsRaceRounds_0(self.raceId)
    if not raceCfg then
        self:ShowErrorPanel()
        return;
    end
    self.timeId = data.ArmsRaceThemeTimeID;
    self.endTime = data.endTime;
    self.point = data.point;
    Common_Util.SetActive(self.errorTips,false)
    Common_Util.SetActive(self.noRankObj, not state)
    Common_Util.SetActive(self.rectTrans,state)
    Common_Util.SetActive(self.rankObj, state)
    Common_Util.SetActive(self.bigChessBubbleObj,state)
    --self.rate = data.rate;
    self.pointProcess = data.pointProcess;
    self.goalProcess = data.goalProcess;
    self.pointRewardList = {}
    

    self.pointRewardList =
    {
        [1] = {
            rewardId = raceCfg.BoxreRward1,
            value = raceCfg.ValueBubble1,
        },
        [2] = {
            rewardId = raceCfg.BoxreRward2,
            value = raceCfg.ValueBubble2,
        },
        [3] = {
            rewardId = raceCfg.BoxreRward3,
            value = raceCfg.ValueBubble3,
        },
    }
    local themeTime = game_scheme:ArmsRaceThemeTime_0(self.timeId)
    
    self.bannerLevel.text = "Lv."..raceCfg.MaincityLevel.data[0].."—"..raceCfg.MaincityLevel.data[1]
    self.bannerTitle.text = lang.Get(themeTime.ThemeDes)
    self.bannerImg:Switch(themeTime.ThemeId - 1)
    
    local timeLeft = os.server_time()
    local secondsToTime = math.ceil(data.endTime - timeLeft);
    self.timeLeftText.text = time_util.FormatTime5(secondsToTime);
    if self.timeTicker then
        util.RemoveDelayCall(self.timeTicker)
        self.timeTicker = nil
    end
    self.timeTicker = util.IntervalCall(1,function()
        secondsToTime = secondsToTime - 1;
        self.timeLeftText.text = time_util.FormatTime5(secondsToTime);
        if secondsToTime <= 0 then
            if self.timeTicker then
                util.RemoveDelayCall(self.timeTicker)
                self.timeTicker = nil
                self.timeLeftText.text = time_util.FormatTime5(0);
            end
        end
    end)
    
    local pointIndex = game_scheme:InitBattleProp_0(8126).szParam.data[0]
    self.rate = player_mgr.GetPlayerOwnNum(pointIndex)
    self.totalPointTxt.text = self.rate--itemCount
    local totalTaskData = arm_competition_activity_data.GetArmsRaceTarget()
    local point_1 = totalTaskData[1].ConditionValue1
    local point_2 = totalTaskData[2].ConditionValue1
    local point_3 = totalTaskData[3].ConditionValue1
    
    self.totalPoint_1_Text.text = point_1
    self.totalPoint_2_Text.text = point_2
    self.totalPoint_3_Text.text = point_3
    local tempTotalList = {}
    for i,v in ipairs(self.goalProcess) do
        tempTotalList[v] = true;
    end
    local baseTotalSlider = 0
    --先全部隐藏
    Common_Util.SetActive(self.totalPoint_1_Particle_root,false)
    Common_Util.SetActive(self.totalPoint_2_Particle_root,false)
    Common_Util.SetActive(self.totalPoint_3_Particle_root,false)
    Common_Util.SetActive(self.totalPoint_1_Chess_RedDot,false)
    Common_Util.SetActive(self.totalPoint_2_Chess_RedDot,false)
    Common_Util.SetActive(self.totalPoint_3_Chess_RedDot,false)
    self.totalPoint_1_Chess_Anim:Stop()
    self.totalPoint_2_Chess_Anim:Stop()
    self.totalPoint_3_Chess_Anim:Stop()
    Common_Util.SetRotation(self.totalPoint_1_Chess_Anim.transform);
    Common_Util.SetRotation(self.totalPoint_2_Chess_Anim.transform);
    Common_Util.SetRotation(self.totalPoint_3_Chess_Anim.transform);
    if self.rate < point_1 then
        baseTotalSlider = self.rate / point_1 / 3
    else
        baseTotalSlider = 0.33
        Common_Util.SetActive(self.totalPoint_1_Particle_root,not tempTotalList[1])
        Common_Util.SetActive(self.totalPoint_1_Chess_RedDot,not tempTotalList[1])
        if not tempTotalList[1] then
            self.totalPoint_1_Chess_Anim:Play()
        end
        if self.rate < point_2 then
            baseTotalSlider = baseTotalSlider + ((self.rate - point_1) / (point_2 - point_1) / 3)
        else
            baseTotalSlider = 0.66
            Common_Util.SetActive(self.totalPoint_2_Particle_root,not tempTotalList[2])
            Common_Util.SetActive(self.totalPoint_2_Chess_RedDot,not tempTotalList[2])
            if not tempTotalList[2] then
                self.totalPoint_2_Chess_Anim:Play()
            end
            if self.rate < point_3 then
                baseTotalSlider = baseTotalSlider + ((self.rate - point_2) / (point_3 - point_2) / 3)
            else
                Common_Util.SetActive(self.totalPoint_3_Particle_root,not tempTotalList[3])
                Common_Util.SetActive(self.totalPoint_3_Chess_RedDot,not tempTotalList[3])
                baseTotalSlider = 1
                if not tempTotalList[3] then
                    self.totalPoint_3_Chess_Anim:Play()
                end
            end
        end
    end
    
    local timeLeft2 = 0.375
    local eachTime = Time.deltaTime --每帧间隔
    local baseValue = self.totalSlider.value
    local targetValue = baseTotalSlider
    local eachValue = (targetValue - baseValue) * eachTime / 0.375 --每帧走多少

    if self.TweenTimeTicker then
        util.RemoveDelayCall(self.TweenTimeTicker)
        self.TweenTimeTicker = nil
    end
    self.TweenTimeTicker = util.IntervalCall(eachTime,function()
        timeLeft2 = timeLeft2 - eachTime
        self.totalSlider.value = self.totalSlider.value + eachValue
        if timeLeft2 <= 0 then
            util.RemoveDelayCall(self.TweenTimeTicker)
            self.TweenTimeTicker = nil
            self.totalSlider.value = targetValue
        end
    end)
    
    --self.totalSlider.value = baseTotalSlider

    self.totalPoint_1_Chess:Switch(tempTotalList[1] and 1 or 0)
    self.totalPoint_2_Chess:Switch(tempTotalList[2] and 1 or 0)
    self.totalPoint_3_Chess:Switch(tempTotalList[3] and 1 or 0)
    
    local task_1_point = raceCfg.Points1;
    local task_2_point = raceCfg.Points2;
    local task_3_point = raceCfg.Points3;
    
    self.point_1_Text.text = string_util.toBalancesString(task_1_point,true);
    self.point_1_BubbleText.text = string_util.toBalancesString(raceCfg.ValueBubble1,true);
    --Common_Util.SetActive(self.point_1_BubbleObj,false);
    self.point_2_Text.text = string_util.toBalancesString(task_2_point,true);
    self.point_2_BubbleText.text = string_util.toBalancesString(raceCfg.ValueBubble2,true);
    --Common_Util.SetActive(self.point_2_BubbleObj,false);
    self.point_3_Text.text = string_util.toBalancesString(task_3_point,true);
    self.point_3_BubbleText.text = string_util.toBalancesString(raceCfg.ValueBubble3,true);
    --Common_Util.SetActive(self.point_3_BubbleObj,false);
    local tempTotalList2 = {}
    for i,v in ipairs(self.pointProcess) do
        tempTotalList2[v] = true;
    end
    Common_Util.SetActive(self.point_1_Particle_root,false)
    Common_Util.SetActive(self.point_2_Particle_root,false)
    Common_Util.SetActive(self.point_3_Particle_root,false)
    Common_Util.SetActive(self.point_1_Chess_RedDot,false)
    Common_Util.SetActive(self.point_2_Chess_RedDot,false)
    Common_Util.SetActive(self.point_3_Chess_RedDot,false)
    self.point_1_Chess_Anim:Stop()
    self.point_2_Chess_Anim:Stop()
    self.point_3_Chess_Anim:Stop()
    Common_Util.SetRotation(self.point_1_Chess_Anim.transform);
    Common_Util.SetRotation(self.point_2_Chess_Anim.transform);
    Common_Util.SetRotation(self.point_3_Chess_Anim.transform);
    self.pointCountText.text = self.point
    local baseTotalSlider2 = 0
    if self.point < task_1_point then
        baseTotalSlider2 = self.point / task_1_point * 0.15
    else
        baseTotalSlider2 = 0.15
        Common_Util.SetActive(self.point_1_Particle_root,not tempTotalList2[1])
        Common_Util.SetActive(self.point_1_Chess_RedDot,not tempTotalList2[1])
        if not tempTotalList2[1] then
            self.point_1_Chess_Anim:Play()
        end
        if self.point < task_2_point then
            baseTotalSlider2 = baseTotalSlider2 + ((self.point - task_1_point) / (task_2_point - task_1_point) * 0.4)
        else
            baseTotalSlider2 = 0.55
            Common_Util.SetActive(self.point_2_Particle_root,not tempTotalList2[2])
            Common_Util.SetActive(self.point_2_Chess_RedDot,not tempTotalList2[2])
            if not tempTotalList2[2] then
                self.point_2_Chess_Anim:Play()
            end
            if self.point < task_3_point then
                baseTotalSlider2 = baseTotalSlider2 + ((self.point - task_2_point) / (task_3_point - task_2_point) * 0.45)
            else
                Common_Util.SetActive(self.point_3_Particle_root,not tempTotalList2[3])
                Common_Util.SetActive(self.point_3_Chess_RedDot,not tempTotalList2[3])
                if not tempTotalList2[3] then
                    self.point_3_Chess_Anim:Play()
                end
                baseTotalSlider2 = 1
            end
        end
    end

    self.pointSlider.value = baseTotalSlider2

    self.point_1_Chess:Switch(tempTotalList2[1] and 1 or 0)
    self.point_2_Chess:Switch(tempTotalList2[2] and 1 or 0)
    self.point_3_Chess:Switch(tempTotalList2[3] and 1 or 0)
    
    local themeId = raceCfg.ThemeId
    local taskList = game_scheme:ArmsRaceTheme_0(themeId).TaskId.data;
    for i,v in ipairs(self.taskItem) do
        v:Dispose()
        v = nil
    end
    for i=0,game_scheme:ArmsRaceTheme_0(themeId).TaskId.count - 1 do
        local temp = arm_competition_task_obj:CItemArmCompetitionTaskItem()
        temp:Init(self.taskTrans,nil,1,function()
            LayoutRebuilder.ForceRebuildLayoutImmediate(self.taskTrans)
            LayoutRebuilder.ForceRebuildLayoutImmediate(self.totalTrans)
        end)
        local taskItem = game_scheme:TaskMain_0(taskList[i])
        temp:SetArmCompetitionTaskData(taskItem.TaskLang,taskItem.PointsRewardID.data[1])
        table.insert(self.taskItem,temp)
    end
    --if updateRank then
    --    self:UpdateRank()
    --end

    local cfg = game_scheme:ArmsRaceTarget_0(3)
    for i,v in ipairs(self.bubbleItem) do
        v:Dispose()
        v = nil
    end
    if cfg then
        local taskData = game_scheme:TaskMain_0(cfg.TaskMainID)
        if taskData then
            local reward_mgr = require "reward_mgr"
            local rewardData = reward_mgr.GetRewardGoodsList(taskData.TaskReward)
            local count = 0
            table.sort(rewardData,function(a, b) 
                local itemA = game_scheme:Item_0(a.id)
                local itemB = game_scheme:Item_0(b.id)
                if itemA and itemB then
                    return itemA.quality > itemB.quality
                end
            end)
            for i, v in ipairs(rewardData) do
                if count >= 2 then
                    break;
                end
                local item = goods_item.CGoodsItem():Init(self.bigChessBubbleTrans, function(p)
                    if not p then
                        return
                    end
                    p:DisplayInfo()
                end, 0.5)
                if item then
                    item:SetGoods(nil,v.id,v.num,function()
                        iui_item_detail.Show(v.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil)
                    end)
                    table.insert(self.bubbleItem,item)
                end
                count = count + 1
            end
            --windowMgr:ShowModule("ui_pop_tips", nil, nil, data)
        end
    end
end

function UIView:ShowErrorPanel()
    Common_Util.SetActive(self.noRankObj, false)
    Common_Util.SetActive(self.rectTrans,false)
    Common_Util.SetActive(self.rankObj, false)
    Common_Util.SetActive(self.bigChessBubbleObj,false)
    Common_Util.SetActive(self.errorTips,true)
end

function UIView:UpdateRank()
    self.rankInfo = arm_competition_activity_data.OnGetRankList();
    if not self.rankInfo or not self.rankInfo.data then
        local log = require "log"
        log.Warning("未获取到军备竞赛排行榜数据。")
        self:ShowErrorPanel()
        return
    end
    local gw_common_util = require "gw_common_util"
    local state = not gw_common_util.GetSandBaseCrossServiceState()
    Common_Util.SetActive(self.noRankObj, not state)
    Common_Util.SetActive(self.rectTrans,state)
    Common_Util.SetActive(self.rankObj, state)
    Common_Util.SetActive(self.bigChessBubbleObj,state)
    Common_Util.SetActive(self.errorTips,false)
    for i,v in ipairs(self.playerItem) do
        v:Dispose()
        v = nil
    end

    local totolTime = 0
    local selfInfo = {}
    local selfIndex = player_mgr.GetPlayerRoleID()
    for i,v in ipairs(self.rankInfo.data) do
        local temp = arm_competition_rank_obj:CArmCompetitionRankItem()
        temp:Init(self.playerListTrans,nil,1,function()
            LayoutRebuilder.ForceRebuildLayoutImmediate(self.playerListTrans)
            LayoutRebuilder.ForceRebuildLayoutImmediate(self.totalTrans)
        end)
        if selfIndex == v.dbid then
            selfInfo = v;
        end
        temp:SetArmCompetitionRankData(v.dbid,v.rank,v.score,v.roleLv,v.faceID,v.frameID,v.sex,v.playerName,v.leagueShortName,v.worldId,self.rankInfo.maxScore,v.showChange,v.changeValue)
        table.insert(self.playerItem,temp)
    end
    self:OnSetSelfRank(selfInfo)
    --LayoutRebuilder.ForceRebuildLayoutImmediate(self.playerListTrans)
end

function UIView:OnSetSelfRank(info)
    if not self:IsValid() then
        return
    end
    
    Common_Util.SetActive(self.rankChangeObj,info.showChange == true)
    Common_Util.SetActive(self.rankChangeTextUp,info.showChange and info.changeValue > 0 or false)
    Common_Util.SetActive(self.rankChangeTextDown,info.showChange and info.changeValue < 0 or false)
    if info.showChange then
        self.rankChangeObj:Switch(info.changeValue < 0 and 0 or 1)
        if info.changeValue and info.changeValue > 0 then
            self.rankChangeTextUp.text = string.format2("+{%s1}",info.changeValue)
        else
            self.rankChangeTextDown.text = info.changeValue
        end
    end
    Common_Util.SetActive(self.top3Icon,info.rank and info.rank <= 3 or false)
    Common_Util.SetActive(self.top3Img,info.rank and info.rank <= 3 or false)
    Common_Util.SetActive(self.rankTxt,info.rank and info.rank > 3 or false)
    if info.rank and info.rank <= 3 then
        self.top3Img:Switch(info.rank - 1)
        self.top3Icon:Switch(info.rank-1)
    else
        self.rankTxt.text = info.rank or ""
    end
    if info.sex == 0 then
        Common_Util.SetActive(self.sexIcon,false)
    else
        Common_Util.SetActive(self.sexIcon,true)
        self.sexIcon:Switch(info.sex - 1)
    end

    self.sliderObj.value = info.score / self.rankInfo.maxScore;
    self.sliderValue.text = info.score;
    local strings = {}
    local ui_util = require "ui_util"
    local worldId = ui_util.GetWorldIDToShowWorldID(info.worldId,nil,ui_util.WorldIDRangeType.Normal)
    if info.leagueShortName and info.leagueShortName ~= "" then
        strings = {"#",worldId,"[",info.leagueShortName,"]",info.playerName}
    else
        strings = {"#",worldId,info.playerName}
    end
    self.nameTxt.text = table.concat(strings);

    self.faceItem = self.faceItem or face_item:CFaceItem():Init(self.headTrans, function(p)
        if not p then
            return
        end
    end, 1)
    self.faceItem:SetFaceInfo(info.faceID,function()
        local mgr_personalInfo = require "mgr_personalInfo"
        mgr_personalInfo.ShowRoleInfoView(info.dbid)
    end)
    self.faceItem:SetFrameID(info.frameID, true)
    self.faceItem:SetActorLvText(true,info.roleLv,true)
end

function UIView:ShowTotalChessReward(id)
    local cfg = game_scheme:ArmsRaceTarget_0(id)
    if cfg then
        local taskData = game_scheme:TaskMain_0(cfg.TaskMainID)
        if taskData then
            local data = {
                target = self.totalChessObj[id],
                content = {},
                hideNum = true
            }
            local reward_mgr = require "reward_mgr"
            local ui_pop_tips_define = require "ui_pop_tips_define"
            local rewardData = reward_mgr.GetRewardGoodsList(taskData.TaskReward)
            for i, rewardInfo in pairs(rewardData) do
                rewardInfo.contentType = ui_pop_tips_define._ContentTypeEnum.Special_RewardTemplate
                table.insert(data.content, rewardInfo)
            end
            windowMgr:ShowModule("ui_pop_tips", nil, nil, data)
        end
    end
end

function UIView:ShowPointChessReward(id)
    local rewardId = self.pointRewardList[id].rewardId
    local data = {
        target = self.pointChessObj[id],
        title = 
        {
            des = string.format2(lang.Get(1000803)," "..self.pointRewardList[id].value),
            icon = game_scheme:Item_0(2).icon, --钻石icon
        },
        content = {},
        isScroll = true,
        size_y = 500,
        hideNum = true
    }
    local reward_mgr = require "reward_mgr"
    local ui_pop_tips_define = require "ui_pop_tips_define"
    local rewardData = reward_mgr.GetRewardGoodsList(rewardId)
    for i, rewardInfo in pairs(rewardData) do
        rewardInfo.contentType = ui_pop_tips_define._ContentTypeEnum.Special_RewardTemplate
        table.insert(data.content, rewardInfo)
    end
    windowMgr:ShowModule("ui_pop_tips", nil, nil, data)
end
---********************end功能函数区**********---
--endregion
--region WindowInherited
local CUIView = class(ui_base, nil, UIView)
--endregion 

--region static ModuleFunction 
-- 特别注意，当前并不是由controller层来驱动ui的生命流程的 
-- 当前因为需要view层 也就是ui_base来驱动ui的init  加载完成，show等流程，所以流程仍然保留，而controller层的流程逻辑受view流程影响，
-- view对应的Init/Show 加载完后，当前会通过事件同步调用controller层的Init/Show流程，controller层的流程逻辑才会执行。
--当前仍然保留了静态的Show，Close接口流程
function Show(data)
     if  data  and type(data) == "table" and data["uipath"] then
        ui_path = data["uipath"];   
    end  
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
        if data and type(data) == "table" then
            local uiPath = data.uiPath or ui_path
            local uiParent = data.uiParent or nil
            window:LoadUIResource(uiPath, nil, uiParent, nil, nil, nil, nil, nil, nil, true)
        else
            window:LoadUIResource(ui_path, nil, nil, nil, nil, nil, nil, nil, nil, true)
        end
    end
    --这里调用window:Show()  会造成多次调用 但hide后却又需要 uiwindowmgr有问题 TODO
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
