local print = print
local require = require
local pairs = pairs
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local typeof = typeof
local type = type
local UIUtil = CS.Common_Util.UIUtil
local GameObject = CS.UnityEngine.GameObject

local war_zone_duel_const = require "war_zone_duel_const"
local red_const = require "red_const"
local war_zone_duel_data = require "war_zone_duel_data"
local gw_task_const = require "gw_task_const"
local gw_task_mgr = require "gw_task_mgr"
local taskpart_pb = require "taskpart_pb"
local war_zone_item = require "war_zone_item"
local time_util = require "time_util"
local os = require "os"
local item_data = require "item_data"
local iui_item_detail = require "iui_item_detail"
local game_scheme = require "game_scheme"
local ui_pop_tips_define = require "ui_pop_tips_define"
local reward_mgr = require "reward_mgr"
local log = require "log"
local effect_item = require "effect_item"
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_war_zone_capitol_binding"
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local Vector3 = CS.UnityEngine.Vector3
local ui_window_mgr = require "ui_window_mgr"
local goods_item_new = require "goods_item_new"

local GWG = GWG

--region View Life
module("ui_war_zone_capitol")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
    self.effectPath = "art/effects/effects/effect_ui_arms_boxreceivelight3/prefabs/effect_ui_arms_boxreceivelight3.prefab"
    self:InitRewardList()

    self.actID = war_zone_duel_data.GetActivityID()

    self:BindUIRed(self.btn_receive.transform, red_const.Enum.WarZoneRewardBtn)

end

function UIView:OnShow()
    self.__base.OnShow(self)
    self:SetCongressAttackRed()
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    if self.srt_rewardContent then
        self.srt_rewardContent:ItemsDispose()
    end
    if self.endTimer then
        util.RemoveDelayCall(self.endTimer)
        self.endTimer = nil
    end
    self.timerData = nil

    if self.atkCDTimer then
        util.RemoveDelayCall(self.atkCDTimer)
        self.atkCDTimer = nil
    end

    if self.getPointsTimer then
        util.RemoveDelayCall(self.getPointsTimer)
        self.getPointsTimer = nil
    end

    self.VData = nil
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

function UIView:InitRewardList()
    self.srt_rewardContent.onItemRender = function(...)
        self:RewardRender(...)
    end
    self.srt_rewardContent.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data then
            --rect_table Dispose时 Item上相关的资源是否需要Dispose           
            if scroll_rect_item.data.goodsItem then
                scroll_rect_item.data.goodsItem:Dispose()
                scroll_rect_item.data.goodsItem = nil
            end
        end
    end
end

---@public function 刷新当前阶段、倒计时区域
function UIView:RefreshStageArea(stateTime, CapitolState)
    self.timerData = self.timerData or {}
    self.timerData.CapitolState = CapitolState
    self.timerData.stateTime = stateTime

    self.endTimer = self.endTimer or util.IntervalCall(1, function()
        local tempTime = self.timerData.stateTime - os.server_time()
        if tempTime < 0 then
            tempTime = 0
        end
        if not util.IsObjNull(self.txt_time) then
            local timeStr = time_util.FormatTime5(tempTime)
            if self.timerData.CapitolState == war_zone_duel_const.WarZoneCapitolState.Preview then
                timeStr = string.format2(lang.Get(676119), time_util.FormatTime5(tempTime))
                self.txt_titleText.text = lang.Get(676122)
            elseif self.timerData.CapitolState == war_zone_duel_const.WarZoneCapitolState.PointsRace then
                self.txt_titleText.text = lang.Get(676122)
            elseif self.timerData.CapitolState == war_zone_duel_const.WarZoneCapitolState.Capitol then
                self.txt_titleText.text = lang.Get(676123)
            elseif self.timerData.CapitolState == war_zone_duel_const.WarZoneCapitolState.RewardResult then
                self.txt_titleText.text = lang.Get(676124)
            end
            self.txt_time.text = timeStr
        end
        if tempTime <= 0 then
            self.endTimer = nil
            self.timerData = nil
            return true
        end
    end)
end

---@public function 刷新争夺双方战区
function UIView:RefreshCapitolWarZone(data)

    local leftItem = war_zone_item.new()
    leftItem:Init(self.rtf_leftWarZoneItem)
    leftItem:SetData(data.leftWarZoneData)

    local rightItem = war_zone_item.new()
    rightItem:Init(self.rtf_rightWarZoneItem)
    rightItem:SetData(data.rightWarZoneData)
end

function UIView:RewardRender(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or { index, dataItem }
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    local trans = scroll_rect_item:Get("ListItem")

    local goodItem = scroll_rect_item.data.goodsItem or goods_item_new.CGoodsItem()
    goodItem:Init(trans.transform, nil, 0.55)
    goodItem:SetGoods(nil, dataItem.id, dataItem.num, function()
        iui_item_detail.Show(dataItem.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, dataItem.num, nil, nil)
    end)
    goodItem:SetCountEnable(true)
    scroll_rect_item.data.goodsItem = goodItem
end

---@public function 初始化战区连胜奖励
function UIView:RefreshWinRewardList(data)
    --WinningStreakRewards
    if not data then
        return
    end
    local len = #data
    self.srt_rewardContent:SetData(data, len)
    self.srt_rewardContent:Refresh(0, -1)
end

---@public function 刷新奖励区域
function UIView:RefreshRewardArea(winReward)
    if winReward and winReward.multiple and winReward.multiple > 0 then
        self:SetActive(self.rtf_multipleBg, true)
        self.txt_multiple.text = string.format("x%s", winReward.multiple)
    else
        self:SetActive(self.rtf_multipleBg, false)
    end

    --读表拿奖励ID
    local id = war_zone_duel_data.GetActivityID()
    local showDownCfg = game_scheme:SrverShowdown_1(id)

    if showDownCfg then
        local limitPoints = showDownCfg.CompetitivePointsConditions
        self.txt_pointsLimit.text = string.format2(lang.Get(676068), limitPoints)
    end

    self.getPointsTimer = self.getPointsTimer or util.IntervalCall(1, function()
        local tempTime = winReward.getPointsTime - os.server_time()

        if not util.IsObjNull(self.txt_getPointsTime) then
            self.txt_getPointsTime.text = string.format2(lang.Get(676069), time_util.FormatTime5(tempTime))
        end
        if tempTime <= 0 then
            if not util.IsObjNull(self.txt_getPointsTime) then
                self.txt_getPointsTime.text = lang.Get(676212) --积分获取已开放
            end
            return true
        end
    end)

end

---@public function 初始化宝藏区域
function UIView:InitTreasureArea(data)
    for i, v in ipairs(data) do
        UIUtil.AddChild(self.rtf_pointsIParent.gameObject, self.rtf_pointsIcon.gameObject)
        --生成一个属性列表
        local itemGo = UIUtil.AddChild(self.rtf_boxArea.gameObject, self.rtf_treasureItem.gameObject)
        local scrollItem = itemGo:GetComponent(typeof(ScrollRectItem))

        self.treasureItemArr = self.treasureItemArr or {}
        self.treasureItemArr[i] = self.treasureItemArr[i] or {}
        self.treasureItemArr[i].scrollItem = scrollItem
        self.treasureItemArr[i].boxData = v

        local bubbleText = scrollItem:Get("bubbleText")
        local pointsText = scrollItem:Get("pointsText")
        local effectRawImage = scrollItem:Get("effectRawImage")
        local boxBtn = scrollItem:Get("boxBtn")
        local animBox = scrollItem:Get("animBox")
        animBox:Stop()
        local gw_ui_effect_utility = require "gw_ui_effect_utility"
        self.VData[i] = self.VData[i] or gw_ui_effect_utility.GetEffectRendererTexture(self.effectPath, function(item, renderTexture)
            effectRawImage.texture = renderTexture
            effectRawImage.transform.localScale = Vector3(1, 1, 1)
        end)

        self:AddOnClick(boxBtn.onClick, function()
            self:ClickBoxBubble(v.diamond, boxBtn, v.rewardID)
        end)

        bubbleText.text = v.diamond
        pointsText.text = v.points
    end
end

---@public function 刷新宝藏任务数据
function UIView:RefreshTreasureTask(data)

    for i, taskData in ipairs(data) do
        local treasureItem = self.treasureItemArr[i]
        local scrollItem = treasureItem.scrollItem
        local boxData = treasureItem.boxData

        local treasureBox = scrollItem:Get("treasureBox")
        local effectRawImage = scrollItem:Get("effectRawImage")
        local boxBtn = scrollItem:Get("boxBtn")
        local animBox = scrollItem:Get("animBox")
        animBox:Stop()
        if not taskData.status then
            self:SetActive(effectRawImage, taskData.rate >= boxData.points)
            if taskData.rate >= boxData.points then
                animBox:Play()
            end
        else
            self:SetActive(effectRawImage, false)
        end

        treasureBox:Switch(taskData.status and 1 or 0)

        self:AddOnClick(boxBtn.onClick, function()
            if taskData.rate >= boxData.points and not taskData.status then
                self:ReceiveBoxReward(taskData.taskID)
                animBox:Stop()
                self:SetActive(effectRawImage, false)
            else
                --不可领取 不可领取时点击宝箱将会弹出弹窗展示奖励
                self:ClickBoxBubble(boxData.diamond, boxBtn, boxData.rewardID)
            end
        end)
    end
end

---@public function 刷新宝藏区域数据
function UIView:RefreshTreasureProgress(data)
    ---@field  宝藏区域进度条规范 table
    self.sliderChunkArr = {}
    self.sliderChunkArr[1] = 0.045
    self.sliderChunkArr[2] = 0.15
    ---@field 下一个没达到的宝箱
    local nextBoxIndex = -1
    ---@field 宝箱进度条间隔
    local sldInterval = 0.12
    for i, v in ipairs(data.pointsArr) do
        if i > 2 then
            self.sliderChunkArr[i] = self.sliderChunkArr[i - 1] + sldInterval
        end
        if nextBoxIndex == -1 then
            if data.boxItemNum < v.points then
                nextBoxIndex = i
            end
        end
    end

    if nextBoxIndex == -1 then
        return
    end

    local newValue = 0
    if nextBoxIndex == 1 then
        local nextPoints = data.pointsArr[nextBoxIndex].points
        local partPer = data.boxItemNum / nextPoints --部分百分比
        newValue = self.sliderChunkArr[1] * partPer
    else
        local prePoints = data.pointsArr[nextBoxIndex - 1].points or 0
        local nextPoints = data.pointsArr[nextBoxIndex].points

        local preProgress = self.sliderChunkArr[nextBoxIndex - 1] --上一个宝箱的进度条value
        local partPer = (data.boxItemNum - prePoints) / (nextPoints - prePoints) --部分百分比
        newValue = sldInterval * partPer + preProgress
    end
    self.sld_points.value = newValue
end

---@public function 点击宝箱,弹出气泡
function UIView:ClickBoxBubble(diamond, boxBtn, rewardID)
    --不可领取 不可领取时点击宝箱将会弹出弹窗展示奖励
    local tipsData = {
        title = {
            des = string.format2(lang.Get(1000803), " " .. diamond),
            icon = game_scheme:Item_0(2).icon, --钻石icon
        },
        target = boxBtn.transform,
        content = {}
    }
    local rewardData = reward_mgr.GetRewardGoodsList(rewardID)
    for _, rewardInfo in pairs(rewardData) do
        rewardInfo.contentType = ui_pop_tips_define._ContentTypeEnum.Special_RewardTemplate
        table.insert(tipsData.content, rewardInfo)
    end
    ui_window_mgr:ShowModule("ui_pop_tips", nil, nil, tipsData)

    local rewardStr = ""
    for i, v in ipairs(rewardData) do
        rewardStr = rewardStr .. v.id .. "#"
    end
    local reportMsg = {
        Position_Reward_Group = rewardStr
    }
    local war_zone_duel_mgr = require "war_zone_duel_mgr"
    war_zone_duel_mgr.WarZoneEventReport("ServerShowdown_personal_progress_box", reportMsg)
end

---@public function 领取宝箱奖励
---@param taskID number 任务ID
function UIView:ReceiveBoxReward(taskID)
    local msg = taskpart_pb.TMSG_ACT_TASK_GETREWARD_REQ()
    msg.taskId:append(taskID)
    gw_task_mgr.TakeTaskReward(msg, self.actID, gw_task_const.TaskModuleType.WarZoneDuel)
end

---@public 初始化积分下拉区
function UIView:CreatePointsShowDown(data)
    for _, v in pairs(data) do
        if v[0] ~= nil then
            local tempData = v[0]
            local itemGo = UIUtil.AddChild(self.rtf_PointsItemList.gameObject, self.rtf_contentItem.gameObject)
            local simpleItem = itemGo:GetComponent(typeof(ScrollRectItem))
            local contentText = simpleItem:Get("contentText")
            local numText = simpleItem:Get("numText")

            contentText.text = lang.Get(tempData.strLangID)
            numText.text = "+" .. tempData.addPoints
        else
            --[[local downItemParent = nil
            for i, downData in ipairs(v) do
                -- i == 1 时，创建一个可展开的item，其他则为子词条
                if i == 1 then
                    local itemGo = UIUtil.AddChild(self.rtf_PointsItemList.gameObject, self.rtf_extendItem.gameObject)
                    local extendItem = itemGo:GetComponent(typeof(ScrollRectItem))
                    local extendBtn = extendItem:Get("extendBtn")
                    local contentText = extendItem:Get("contentText")
                    local numText = extendItem:Get("numText")
                    local icon = extendItem:Get("icon")
                    downItemParent = extendItem:Get("extendList")

                    contentText.text = lang.Get(downData.strLangID)
                    numText.text = "+" .. downData.addPoints
                    UIUtil.SetRotation(icon.transform, 0, 0, 180)
                    local isExpand = false
                    local clickExpandFun = function()
                        isExpand = not isExpand
                        self:SetActive(downItemParent, isExpand)
                        UIUtil.SetRotation(icon.transform, 0, 0, isExpand and 0 or 180)
                    end
                    self:AddOnClick(extendBtn.onClick, clickExpandFun)
                else
                    local itemGo = UIUtil.AddChild(downItemParent.gameObject, self.rtf_contentItem.gameObject)
                    local downItem = itemGo:GetComponent(typeof(ScrollRectItem))

                    local contentText = downItem:Get("contentText")
                    local numText = downItem:Get("numText")
                    local contentRect = downItem:Get("contentRect")

                    contentRect.anchoredPosition = { x = -70, y = 0 }
                    contentText.text = lang.Get(downData.strLangID)
                    numText.text = "+" .. downData.addPoints
                end
            end]]
            -- 改为，显示子词条，而不是展开收起
            -- 处理第一条,在第一条的点击事件里渲染子列表

            for i = 1, #v do
                -- 判断等级
                local currPlayerMaxSoldierLevel = GWG.GWHomeMgr.soldierData.GetCampusSoldierLevel()
                currPlayerMaxSoldierLevel = currPlayerMaxSoldierLevel == 0 and 1 or currPlayerMaxSoldierLevel

                local currSoldierLevel = v[i].soldierLevel or 0
                if currPlayerMaxSoldierLevel == currSoldierLevel then
                    local currItemData = v[i]
                    local itemGo = UIUtil.AddChild(self.rtf_PointsItemList.gameObject, self.rtf_extendItem.gameObject)
                    local extendItem = itemGo:GetComponent(typeof(ScrollRectItem))
                    local extendBtn = extendItem:Get("extendBtn")
                    local contentText = extendItem:Get("contentText")
                    local numText = extendItem:Get("numText")

                    contentText.text = lang.Get(currItemData.strLangID)
                    numText.text = "+" .. currItemData.addPoints

                    --- 叹号的点击事件
                    local clickExpandFun = function()
                        -- 渲染子词条，从第二条开始
                        for i = 1, #v do
                            local downData = v[i]
                            -- 子词条需要挂载在Tips的列表下
                            local itemGo = UIUtil.AddChild(self.obj_contentTipsExtendList.gameObject, self.rtf_contentTipsItem.gameObject)
                            local downItem = itemGo:GetComponent(typeof(ScrollRectItem))
                            local contentText = downItem:Get("contentText")
                            local numText = downItem:Get("numText")
                            local contentRect = downItem:Get("contentRect")
                            contentRect.anchoredPosition = { x = -70, y = 0 }
                            contentText.text = lang.Get(downData.strLangID)
                            numText.text = "+" .. downData.addPoints
                        end
                        -- 需要显隐的物体 改为Tips的列表 只显示出来，隐藏额外处理
                        self:SetActive(self.obj_contentTips, true)
                    end
                    self:AddOnClick(extendBtn.onClick, clickExpandFun)
                end
            end
        end
    end
end

---@public function 设置攻击按钮显隐
function UIView:SetAttackBtnActive(isShow)
    self:SetActive(self.btn_goToAtk, isShow)

end

---@public function 设置防御按钮显隐
function UIView:SetGoToDef(isShow)
    self:SetActive(self.btn_goToDef, isShow)
end

---@public function 设置攻击按钮CD
function UIView:SetAttackBtnCD(cdTimer)

    if os.server_time() < cdTimer then
        self.ss_goToAtk:Switch(1) --置灰
        self:SetActive(self.txt_atk, false)
        self:SetActive(self.txt_cdTime, true)

        self.atkCDTimer = self.atkCDTimer or util.IntervalCall(1, function()
            local tempTime = cdTimer - os.server_time()

            if not util.IsObjNull(self.txt_cdTime) then
                self.txt_cdTime.text = time_util.FormatTime5(tempTime)
            end

            if tempTime <= 0 then
                if not util.IsObjNull(self.ss_goToAtk) then
                    self.ss_goToAtk:Switch(0)
                end

                if not util.IsObjNull(self.txt_atk) then
                    self:SetActive(self.txt_atk, true)
                end

                if not util.IsObjNull(self.txt_cdTime) then
                    self:SetActive(self.txt_cdTime, false)
                end
                return true
            end
        end)
    else
        self.ss_goToAtk:Switch(0)
        self:SetActive(self.txt_atk, true)
        self:SetActive(self.txt_cdTime, false)
    end
end

---@public function 设置领取奖励按钮颜色
---@param index number 0灰色1黄色
function UIView:SetReceiveRewardBtn(index)
    self.ss_receive:Switch(index)
end

---@public function 关闭子列表Tips
function UIView:CloseTipsPanel()
    self:SetActive(self.obj_contentTips, false)
    local parentGameObject = self.obj_contentTipsExtendList.gameObject
    -- 先清空子物体
    local count = parentGameObject.transform.childCount
    for i = count - 1, 0, -1 do
        local child = parentGameObject.transform:GetChild(i).gameObject
        GameObject.Destroy(child)
    end
end

local atkRedPos = { x = 110, y = 40 }
function UIView:SetCongressAttackRed()
    self:BindUIRed(self.btn_goToAtk.transform, red_const.Enum.WarZoneCapitolClickBtn, nil, { pos = atkRedPos })
    self:BindUIRed(self.btn_goToDef.transform, red_const.Enum.WarZoneCapitolClickBtn, nil, { pos = atkRedPos })
end

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
            window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
            window:LoadUIResource(ui_path, nil, nil, nil, nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
