local require = require
local print = print
local ipairs = ipairs
local os = os
local table = table
local dump = dump
local pairs = pairs
local string = string
local tonumber = tonumber
local tostring = tostring

local event = require "event"
local prop_pb = require "prop_pb"
local msg_pb = require "msg_pb"
local chat_pb = require "chat_pb"
local leaguehelp_pb = require "leaguehelp_pb"
local net = require "net"
local net_route = require "net_route"
local flow_text = require "flow_text"
local lang = require "lang"
local chat_mgr_new = require "chat_mgr_new"
local util = require "util"
local server_data = require "setting_server_data"
local game_config = require "game_config"
local game_scheme = require "game_scheme"
local mq_common_pb = require "mq_common_pb"
local platform_pb = require "platform_pb"
local error_code_pb = require "error_code_pb"
local http_inst = require "http_inst"
local player_mgr = require "player_mgr"
local ui_setting_data = require "ui_setting_data"
local ui_setting_cfg = require "ui_setting_cfg"
local ui_window_mgr = require "ui_window_mgr"
local json = require "dkjson"
local item_data = require "item_data"
local chat_monitor_mgr = require "chat_monitor_mgr"
local ReviewingUtil = require "ReviewingUtil"
local url_mgr = require "url_mgr"
local log = require "log"
-- local force_weapon_mgr = require "force_weapon_mgr"


local HttpUtils = CS.War.Script.HttpUtils
local Utility = CS.War.Script.Utility
local Debug = CS.UnityEngine.Debug

local ui_setting_attr_enum = require "ui_setting_attr_enum"
local EnSubModel = ui_setting_attr_enum.EnSubModel
local EnBaseAttrKey = ui_setting_attr_enum.EnBaseAttrKey

module("net_chat_module_new")
-- local channelAllotUrl = "http://api-zaj.dev.q1op.com/games/2118/api/channelchat/allot?UserId=%s&RoleId=%d&ServerId=%d&LanguageId=%d"
-- local channelChangeUrl ="http://api-zaj.dev.q1op.com/games/2118/api/channelchat/change?NewChannelId=%d&UserId=%s&ServerId=%d&RoleId=%d&LanguageId=%d"
-- local channelMsgUrl = "http://api-zaj.dev.q1op.com/games/2118/api/channelchat/channelchatData?ChannelId=%d"
-- local channelConfigUrl = "http://api-zaj.dev.q1op.com/games/2118/api/channelchat/channelchatlist?LanguageId=%d"
-- local privateMsgUrl = "http://api-zaj.dev.q1op.com/games/2118/api/channelchat/roleChatData?RoleId=%d&ServerId=%d"


--///举报相关-------------------------
-- local ReplayWebUrl = {
-- 	{url_mgr.NEW_DOMAIN_NAME, "http://api-cn.q1.com/"},		-- 中国
-- 	{url_mgr.NEW_DOMAIN_NAME, "http://api-ea.q1.com/"},		-- 美加
-- 	{url_mgr.NEW_DOMAIN_NAME, "http://api-sa.q1.com/"},		-- 东南亚
-- }

--登录时服务器同步，用于请求平台接口校验
local loginKey = nil
local loginTime = nil
local loginWorldID = nil
local reportChannelTimer = nil--监听跨服聊天频道上报是否返回的定时器
local isReceiveReportChannelRsp = nil--是否接收到跨服聊天频道上报返回
local intervalTime = 2.5--跨服聊天频道上报监听返回时间间隔
local intervalNowCount = 0--跨服聊天频道上报监听当前次数
local intervalMaxCount = 3--跨服聊天频道上报监听最大次数，超过就不继续请求

function GetReplayWebUrl()
    local url = url_mgr.SelectUrlByEnvir(url_mgr.REPORT_URL)
    return url

    -- local url = ReplayWebUrl[ui_login_main.GetRegionID()]
    -- return game_config.ENABLE_Q1_DEBUG_MODE and url[1] or url[2]
end
function ReportPost(reportRoleId, reportRoleName, beReportRoleId, beReportRoleName, reportContext, reportType, desc, beReportWorldID)
    local const = require "const"
    local gameId = const.GAMEID
    local regionId = server_data.GetRegionID()
    local worldId = server_data.GetLoginWorldID()
    local type = "TMSG_CHAT_REPORT_2_GM_NTF"
    local timeStamp = os.time()

    local cmd = platform_pb.TMSG_CHAT_REPORT_2_GM_NTF()
    cmd.regionId = regionId
    cmd.subRegionId = worldId
    cmd.reportRoleId = reportRoleId
    cmd.reportRoleName = reportRoleName
    cmd.beReportRoleId = beReportRoleId
    cmd.beReportRoleName = beReportRoleName
    cmd.reportContext = reportContext or ""
    cmd.reportType = reportType
    cmd.desc = desc
    if beReportWorldID == nil or beReportWorldID == 0 then
        cmd.beReportWorldID = worldId
    else
        cmd.beReportWorldID = beReportWorldID
    end
    local cmdData = cmd:SerializeToString()

    local secretKey = "fJaq7Z:Vj<Sf12LoC^rdIt+!5Zz0w;1m"
    local domain = GetReplayWebUrl()

    -- local sign = Utility.Md5(string.format("%d%d%s%d%s", gameId, worldId, type, timeStamp, secretKey))
    -- local api = string.format("games/%d/api/chats/%d?worldId=%d&type=%s&timestamp=%d&sign=%s", gameId, gameId, worldId, type, timeStamp, sign)
    -- local api = string.format("games/%d/api/chats", gameId)
    local const = require "const"
    local uri = domain .. "games/" .. const.GAMEID .. "/api/chats"

    --print(uri)
    --dump({regionId = regionId,worldId=worldId,reportRoleId=reportRoleId,beReportRoleId=beReportRoleId,reportContext=reportContext})
    local log = require "log"
    log.Warning("uri:", uri, ",regionId:", regionId, ",worldId:", worldId)

    local ui_report = require "ui_report"
    if not string.IsNullOrEmpty(reportContext) then 
        HttpUtils.Instance:ReqPostBytes(uri, cmdData, ui_report.ReportCallBack)
    else
        local flow_text = require "flow_text"
        flow_text.Add(lang.Get(15255))
    end
    
end

--- 发送举报数据
function Send_MSG_CHAT_REPORT_MSG_REQ(chatInfo)
    -- log.Warning("Send_MSG_CHAT_REPORT_MSG_REQ:chatInfo.bereportedID",chatInfo.bereportedID,"chatInfo.channel",chatInfo.channel,"chatInfo.reason",chatInfo.reason,"chatInfo.content",chatInfo.content,"game_config.CHANNEL_ID",game_config.CHANNEL_ID)
    local msg = chat_pb.TMSG_CHAT_REPORT_BACKSTAGE_REQ()
    msg.bereportedID = chatInfo.bereportedID
    msg.channel = chatInfo.channel
    msg.reason:append(chatInfo.reason)
    msg.content = chatInfo.content
    msg.reporterpid = tostring(game_config.CHANNEL_ID)
    msg.remark = chatInfo.desc
    net.SendMessage(net.Endpoint_Client, net.Endpoint_AccountLogin, 0, msg_pb.MSG_CHAT_REPORT_BACKSTAGE_REQ, msg)
end

--屏蔽玩家 c->s
function Send_CHAT_BLOCK(bBlock, roleid)
    log.Warning("Send_CHAT_BLOCK")
	local msg = chat_pb.TMSG_CHAT_BLOCK_REQ()
	msg.bBlock = bBlock
    msg.roleid = roleid
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_CHAT_BLOCK_REQ, msg)
end

-- --屏蔽玩家回复 s->c
function Recv_CHAT_BLOCK(msg)
    log.Warning("Recv_CHAT_BLOCK")
	if msg.err ~= 0 then
		flow_text.Add(10000 + msg.err)
	else
		local content = {}
		content.bBlock = msg.bBlock
		content.roleid = msg.roleid

		chat_mgr_new.SetBlockPlayer(content)
	end
end

--请求屏蔽玩家列表 c->s
function Send_CHAT_BLOCK_LIST()
    log.Warning("Send_CHAT_BLOCK_LIST")
	local msg = chat_pb.TMSG_CHAT_BLOCK_LIST_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_AccountLogin, 0, msg_pb.MSG_CHAT_BLOCK_LIST_REQ,msg)
end

--屏蔽玩家列表回复 s->c
function Recv_CHAT_BLOCK_LIST(msg)
    log.Warning("Recv_CHAT_BLOCK_LIST")
	if msg.err ~= 0 then
		flow_text.Add(10000 + msg.err)
	else
        for i, v in ipairs(msg.shieldInfo) do
            log.Warning("Recv_CHAT_BLOCK_LIST  766 : ", v.leagueName, v.name, v.faceID)
		end

        event.Trigger(event.CHAT_BLOCK_PLAYER_LIST_RSP,msg.shieldInfo)
	end
end
--屏蔽玩家列表推送 s->c
function Recv_CHAT_BLOCK_LIST_NTF(msg)
    log.Warning("Recv_CHAT_BLOCK_LIST_NTF")
    for i, v in ipairs(msg.shieldInfo) do
        log.Warning("Recv_CHAT_BLOCK_LIST_NTF : ", v.leagueName, v.name, v.faceID)
    end
    event.Trigger(event.CHAT_BLOCK_PLAYER_LIST_RSP,msg.shieldInfo)
end



local t_channel
local t_bHideVip
local t_bAllRecruit
--屏蔽频道 c->s
function Send_CHAT_BLOCK_CHANNEL(bVip, bWorld, bGuild, bTerritory, bLang, bAllRecruit)
    local bit = require "bit"
    local bHideVip = bVip
    local blockChannel = 0x00
    if bWorld then
        blockChannel = bit.bor(blockChannel, chat_pb.Channel_World)
    end
    if bGuild then
        blockChannel = bit.bor(blockChannel, chat_pb.Channel_Guild)
    end
    if bTerritory then
        blockChannel = bit.bor(blockChannel, chat_pb.Channel_Territory)
    end
    local msg = chat_pb.TMSG_CHAT_BLOCK_CHANNEL_REQ()
    msg.bHideVip = bHideVip
    msg.channel = blockChannel
    msg.bRecruitMsg = bAllRecruit
    t_bHideVip = bHideVip
    t_channel = blockChannel
    t_bAllRecruit = bAllRecruit
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_CHAT_BLOCK_CHANNEL_REQ, msg)
end

--屏蔽频道回复 s->c
function Recv_CHAT_BLOCK_CHANNEL(msg)
    if msg.err ~= 0 then
        flow_text.Add(100000 + msg.err)
    else
        chat_mgr_new.SetBlockCtrl(t_channel, t_bHideVip, t_bAllRecruit)
    end
end


--封号/禁言 通知 s->c
function Recv_CHAT_BAN(msg)
    local content = {}
    content.endTime = msg.endTime
    content.reason = msg.reason
    local q1sdk = require "q1sdk"
    local message_box = require "message_box"
    if msg.commandType == mq_common_pb.enCommand_Freeze then
        if not game_config.Q1SDK_DOMESTIC then
            local function OnConfirm(callbackData, nRet)
                if nRet == message_box.RESULT_YES then
                    local url = url_mgr.FACEBOOK_ROOT_URL
                    q1sdk.ApplicationOpenURL(url)
                else
                    local login_module = require "net_login_module"
                    login_module.ReturnLogin()
                end
            end
            local displayUrl = "<a href=facebook>" .. url_mgr.FACEBOOK_ROOT_URL .. "</a>"
            message_box.Open(string.format(lang.Get(3606), util.FormatTime(msg.endTime), displayUrl),
                    message_box.STYLE_YESNO, OnConfirm, 0, lang.KEY_OK, nil, nil, nil, nil, nil, true, function()
                        local url = url_mgr.FACEBOOK_ROOT_URL
                        q1sdk.ApplicationOpenURL(url)
                    end)
        else
            message_box.Open(string.format(lang.Get(3603), util.FormatTime(msg.endTime)), message_box.STYLE_YES, function()
                local login_module = require "net_login_module"
                login_module.ReturnLogin()
            end, 0, lang.KEY_OK)
        end

    elseif msg.commandType == mq_common_pb.enCommand_Mute then
        chat_mgr_new.SetBan(content)
        if not game_config.Q1SDK_DOMESTIC then
            local displayUrl = "<a href=facebook>" .. url_mgr.FACEBOOK_ROOT_URL .. "</a>"
            message_box.Open(string.format(lang.Get(3607), util.FormatTime(msg.endTime), displayUrl),
                    message_box.STYLE_YESNO, function(callbackData, nRet)
                        if nRet == message_box.RESULT_YES then
                            local url = url_mgr.FACEBOOK_ROOT_URL
                            q1sdk.ApplicationOpenURL(url)
                        end
                    end, 0, lang.KEY_OK, nil, nil, nil, nil, nil, true, function()
                        local url = url_mgr.FACEBOOK_ROOT_URL
                        q1sdk.ApplicationOpenURL(url)
                    end)
        else

            message_box.Open(string.format(lang.Get(3604), util.FormatTime(msg.endTime)), message_box.STYLE_YES, function()

            end, 0, lang.KEY_OK)
        end
        chat_monitor_mgr.SetIsPriveteBan(nil)
    elseif msg.commandType == mq_common_pb.enCommand_PrivMute then
        --私密禁言
        ----        print(396,"私密禁言 通知",content.endTime)
        chat_monitor_mgr.SetIsPriveteBan(content.endTime)
    end
end

--解禁通知 s->c
function Recv_CHAT_BAN_REMOVE(msg)
    if msg.commandType == mq_common_pb.enCommand_RemoveMute then
        local content = { endTime = 0 }
        chat_mgr_new.SetBan(content)
    elseif msg.commandType == mq_common_pb.enCommand_RemovePrivMute then
        --解除私密禁言
        ----        print(396,"禁言解封 通知！")
        chat_monitor_mgr.SetIsPriveteBan(nil)
    end
end


--///频道、本服、私聊会话 聊天内容的收发==========================================

--玩家登录聊天消息通知(20条世界频道/公会频道消息) s->c
function Recv_CHAT_MSG(msg)
    chat_mgr_new.SetChatMsg(msg)
    --登录后刷新一次，确保一定显示气泡
    if util.get_len(msg.GuildMsg) > 0 then
        local radar_define = require "radar_define"
        event.Trigger(radar_define.RADAR_DIG_TREASURE_MSG_BUBBLE_UPDATE)
    end
end

-- 请求发言 C->S
---@description 请求发言 C->S
---@param channel number 聊天频道类型（必须传）
---@param sType number 聊天类型    （必须传）
---@param context string 聊天内容    （可选传）
---@param hero table    英雄信息        （可选传）
---@param goods table    物品信息        （可选传）
---@param shareLottery table 分享抽奖    （可选传）
---@param roomId number 房间ID        （可选传）
---@param sandboxShareInfo table 沙盘分享    （可选传）
---@param sandboxMassData table 沙盘集结    （可选传）
---@param allianceShareInfo table 联盟分享数据    （可选传）
function Send_CHAT_SPEAK(channel, sType, context, hero, goods, shareLottery, roomId, sandboxShareInfo, sandboxMassData, allianceShareInfo,isSystemMsg)
    if not channel or not sType then
        return
    end

    local net_chat_module_new = require "net_chat_module_new"
    local _, __, ___, LanguageId = net_chat_module_new.GetRoleInfo()
    local msg = chat_pb.TMSG_CHAT_SPEAK_REQ()
    msg.channel = channel
    msg.sType = sType
    msg.langtype = LanguageId
    msg.deviceLangType=ui_setting_data.GetDevicePlatformLangId()
    msg.isSystemMsg = isSystemMsg or false
    if (roomId) then
        msg.roomID = roomId
    end
    if sType == mq_common_pb.enSpeak_Text
            or sType == mq_common_pb.enSpeak_At
            or sType == mq_common_pb.enSpeak_ShareBattle
            or sType == mq_common_pb.enSpeak_ShareMultiBattle
            or sType == mq_common_pb.enSpeak_Announcement
            or sType == mq_common_pb.enSpeak_UrgentAnnouncement then
        msg.context = context
        if sType == mq_common_pb.enSpeak_Text  or sType == mq_common_pb.enSpeak_At then
            -- 增加临时聊天消息，先显示出来，等待后台回复
            chat_mgr_new.AddTempChatMsg(channel, context, sType,LanguageId)
            local atPlayerIDStr = chat_mgr_new.GetAtPlayeStr(context)
            if atPlayerIDStr then
                msg.extendinfo = atPlayerIDStr
            end
        end
        if sType == mq_common_pb.enSpeak_Announcement and roomId then
            --待办日程发布联盟公告特殊处理
            msg.nShareState = roomId        --实际是任务indexID
        end
        
    elseif sType == mq_common_pb.enSpeak_CityFireShare then
        msg.context = context
        -----灭火分享-------
        if sandboxShareInfo then
            msg.sandboxmarkData.bServerID = sandboxShareInfo.bServerID
            msg.sandboxmarkData.x = sandboxShareInfo.pos.x
            msg.sandboxmarkData.y = sandboxShareInfo.pos.y
            if sandboxShareInfo.sid then
                msg.sandboxmarkData.sid = sandboxShareInfo.sid
            end
            msg.sandboxmarkData.playerName = sandboxShareInfo.playerName
            
            if sandboxShareInfo.sandboxSid then
                msg.sandboxmarkData.sandboxSid = sandboxShareInfo.sandboxSid
            end
        end
        ----------------------
    elseif sType == mq_common_pb.enSpeak_ShareHero then
        for key, value in pairs(hero) do
            if key == "equip" then
                for k_1, sid in pairs(value) do
                    local tShareGoods = msg.hero.equip:add()
                    local entity = player_mgr.GetPacketPartDataBySid(sid)
                    if entity then
                        tShareGoods.goodsId = entity.goodsID
                        tShareGoods.lv = entity.numProp.equipStrLv
                        tShareGoods.resonanceproId = entity:GetEquipResonance() or 0
                        -- tShareGoods.profession = entity.numProp.careerType or 0
                        local proArr = nil
                        local itemCfg = game_scheme:Item_0(entity.goodsID)
                        local level = false
                        if level then
                            tShareGoods.secondArtifactLevel = level
                        end
                        if itemCfg and itemCfg.type == item_data.Item_Type_Enum.Sigil then
                            proArr = { entity.numProp.upgradeExp, entity.numProp.lock, entity.numProp.extendProp1, entity.numProp.extendProp2 }
                        else
                            proArr = entity:GetEquipAddProps()
                            local baseProp = entity:GetEquipBasePropID()
                            table.insert(proArr, 1, baseProp)
                            local equipLv = entity:GetEquipStrLv() or 0
                            table.insert(proArr, equipLv)
                            local extendProp = entity:GetEquipExtendProps()
                            if extendProp then
                                for k_2, v in pairs(extendProp) do
                                    if v ~= 0 then
                                        table.insert(proArr, v)
                                    end
                                end
                            end
                        end
                        for k_2, v in pairs(proArr) do
                            tShareGoods.proId:append(v)
                        end
                    end
                end
            else
                if key == "decorate" then
                    for i, v in pairs(value) do
                        local tShareDecorates = msg.hero.decorate:add()
                        if v.decorate and v.decorate.goodsId then
                            -- print("v.lv",v.lv,"goodsID",v.decorate.goodsId)
                            local good = tShareDecorates.decorate
                            good.goodsId = v.decorate.goodsId
                            good.lv = v.decorate.lv
                        end
                        tShareDecorates.lv = v.lv
                    end
                else
                    msg.hero[key] = value
                end
            end
        end
    elseif sType == mq_common_pb.enSpeak_ShareGoods or sType == mq_common_pb.enSpeak_ShareEquip or sType == mq_common_pb.enSpeak_ShareEquipRecast
            or sType == mq_common_pb.enSpeak_ShareEquipResonace or sType == mq_common_pb.enSpeak_ShareDecorate or sType == mq_common_pb.enSpeak_ShareTreasureRare
            or sType == mq_common_pb.enSpeak_ShareStarDiamond then
        for key, value in pairs(goods) do
            if key == "proId" then
                for _, v in ipairs(value) do
                    msg.goods[key]:append(v)
                end
            else
                msg.goods[key] = value
            end
        end
    elseif sType == mq_common_pb.enSpeak_ShareLottery then
        for key, value in pairs(shareLottery) do
            if key == "oHeroData" then
                for _, v in ipairs(value) do
                    local tShareHeros = msg.shareLottery.oHeroData:add()
                    tShareHeros.nID = v.nID
                    tShareHeros.nStar = v.nStar
                end
            else
                msg.shareLottery[key] = value
            end
        end
        --家园抽奖分享
    elseif sType == mq_common_pb.enSpeak_FarmLottery then
        msg.farmLottery.heroData.goodsId = goods
    elseif sType == mq_common_pb.enSpeak_Voice then
        --暂无
    elseif sType == mq_common_pb.enSpeak_Like then
        --无内容填充
    elseif sType == mq_common_pb.enSpeak_ShareCell then
        msg.context = context
        --无内容填充
    elseif sType == mq_common_pb.enSpeak_ShareSkin then
        msg.context = context
    elseif sType == mq_common_pb.enSpeak_AllSaintsDay_Pumpkin then
        msg.pumpkinData.pumpkinId = goods.pumpkinId
        msg.pumpkinData.score = goods.score
        msg.pumpkinData.scoreId = goods.scoreId
    elseif sType == mq_common_pb.enSpeak_AnniversaryJackpock then
        msg.context = context
        for key, value in pairs(goods) do
            if key == "proId" then
                for _, v in ipairs(value) do
                    msg.goods[key]:append(v)
                end
            else
                msg.goods[key] = value
            end
        end
    elseif sType == mq_common_pb.enSpeak_CarriageTruckShare then
        -----城际货车分享-------
        if sandboxShareInfo then
            msg.sandboxCarriageTruckShare.bServerID = sandboxShareInfo.bServerID
            --msg.sandboxCarriageTruckShare.x = sandboxShareInfo.pos.x
            --msg.sandboxCarriageTruckShare.y = sandboxShareInfo.pos.y
            if sandboxShareInfo.lottname then
                msg.sandboxCarriageTruckShare.lottname = sandboxShareInfo.lottname
            end
            if sandboxShareInfo.lottLeagueShortName then
                msg.sandboxCarriageTruckShare.lottLeagueShortName = sandboxShareInfo.lottLeagueShortName
            end
            msg.sandboxCarriageTruckShare.quality = sandboxShareInfo.quality
            msg.sandboxCarriageTruckShare.tradeid = sandboxShareInfo.tradeid
            msg.sandboxCarriageTruckShare.context = sandboxShareInfo.context
            msg.sandboxCarriageTruckShare.titleDes = sandboxShareInfo.titleDes
            msg.sandboxCarriageTruckShare.sandboxid = sandboxShareInfo.sandboxid
            msg.sandboxCarriageTruckShare.roleId = sandboxShareInfo.roleId
            msg.sandboxCarriageTruckShare.contentLang = sandboxShareInfo.contentLang
            for k_2, v in pairs(sandboxShareInfo.formats) do
                msg.sandboxCarriageTruckShare.formats:append(v)
            end
        end
        ----------------------
    elseif sType == mq_common_pb.enSpeak_SandboxMarkPos then
        -----新增沙盘分享-------
        if sandboxShareInfo then
            msg.sandboxmarkData.bServerID = sandboxShareInfo.bServerID
            msg.sandboxmarkData.x = sandboxShareInfo.pos.x
            msg.sandboxmarkData.y = sandboxShareInfo.pos.y
            if sandboxShareInfo.sid then
                msg.sandboxmarkData.sid = sandboxShareInfo.sid
            end
            msg.sandboxmarkData.context = sandboxShareInfo.context or ""
            msg.sandboxmarkData.playerName = sandboxShareInfo.playerName
            if sandboxShareInfo.quality then
                msg.sandboxmarkData.quality = sandboxShareInfo.quality
                local reportMsg =
                {
                    Truck_quality = sandboxShareInfo.quality , --货车品质
                    Looted_times =  sandboxShareInfo.lootcnt, --该货车被掠夺的次数
                }
                event.EventReport("TruckTrade_Share", reportMsg)
            end
            if not string.empty(sandboxShareInfo.leagueShortName) then
                msg.sandboxmarkData.leagueShortName = sandboxShareInfo.leagueShortName
            end
            if sandboxShareInfo.sandboxSid then
                msg.sandboxmarkData.sandboxSid = sandboxShareInfo.sandboxSid
            end
            if sandboxShareInfo.titleID then
                msg.sandboxmarkData.titleID = sandboxShareInfo.titleID
            end
            sandboxShareInfo.titleDes = sandboxShareInfo.titleDes or ""
            if not string.empty(sandboxShareInfo.entityLevel) then
                msg.sandboxmarkData.entityLevel = sandboxShareInfo.entityLevel
            end
            --[[if not string.empty(sandboxShareInfo.entityName) then
                msg.sandboxmarkData.entityName = sandboxShareInfo.entityName
            end]]
            if sandboxShareInfo.entityName and sandboxShareInfo.entityName ~= 0 then
                --msg.sandboxmarkData.entityName = lang.Get(sandboxShareInfo.entityName)
                msg.sandboxmarkData.entityName = sandboxShareInfo.entityName
            end
            if sandboxShareInfo.tavernTaskID then
                msg.sandboxmarkData.tavernTaskID = sandboxShareInfo.tavernTaskID
            end
        end
        ----------------------
    elseif sType == mq_common_pb.enSpeak_SandboxMass then
        -------新增游荡怪集结分享---------
        if sandboxMassData then
            msg.sandboxmassData.massTeamId = sandboxMassData.massTeamId
            msg.sandboxmassData.massFinishTime = sandboxMassData.massFinishTime
            msg.sandboxmassData.targetId = sandboxMassData.targetId
            msg.sandboxmassData.x = sandboxMassData.x
            msg.sandboxmassData.y = sandboxMassData.y
            msg.sandboxmassData.strName = sandboxMassData.strName
            msg.sandboxmassData.targetSid = sandboxMassData.targetSid
        end
        -----------------------------
    elseif sType == mq_common_pb.enSpeak_SandboxTreasure then
        --雷达宝藏分享
        if sandboxShareInfo then
            msg.sandboxTreasureData.bServerID = sandboxShareInfo.bServerID
            msg.sandboxTreasureData.x = sandboxShareInfo.posX
            msg.sandboxTreasureData.y = sandboxShareInfo.posY
            if sandboxShareInfo.sid then
                msg.sandboxTreasureData.treasureSid = sandboxShareInfo.sid
            end
            msg.sandboxTreasureData.context = context
        end
        ----------------------
    elseif sType == mq_common_pb.enSpeak_LeagueAchievement then
        --联盟成就分享
        if allianceShareInfo then
            msg.leagueAchievementData.achID = allianceShareInfo.achID
            msg.leagueAchievementData.state = allianceShareInfo.state
            msg.leagueAchievementData.finishCnt = allianceShareInfo.finishCnt
            msg.leagueAchievementData.reqCnt = allianceShareInfo.reqCnt
            msg.leagueAchievementData.unlockTime = allianceShareInfo.unlockTime
        end
    elseif sType == mq_common_pb.enSpeak_AllianceTrain then
        -----联盟火车分享-------
        if sandboxShareInfo then
            --log.Warning("sandboxShareInfo", Edump(sandboxShareInfo))
            local allianceTrain_pb = require "allianceTrain_pb"
            local pbMsg = allianceTrain_pb.TPbAllianceTrainChat()
            pbMsg.sandBoxSid = sandboxShareInfo.sandBoxSid
            pbMsg.trainType = sandboxShareInfo.trainType
            pbMsg.trainSid = sandboxShareInfo.trainSid
            pbMsg.worldId = sandboxShareInfo.worldId
            pbMsg.roleName = sandboxShareInfo.roleName
            pbMsg.allianceName = sandboxShareInfo.allianceName
            pbMsg.roleLv = sandboxShareInfo.roleLv
            pbMsg.rolePower = sandboxShareInfo.rolePower
            pbMsg.nAvatarId = sandboxShareInfo.nAvatarId
            pbMsg.nAvatarFrameId = sandboxShareInfo.nAvatarFrameId
            pbMsg.nCompleteness = sandboxShareInfo.nCompleteness
            pbMsg.pos.posX = sandboxShareInfo.pos.posX
            pbMsg.pos.posY = sandboxShareInfo.pos.posY
            pbMsg.faceStr = sandboxShareInfo.faceStr
            local dataToString = pbMsg:SerializeToString()
            --log.Warning("dataToString: ", dataToString, ", data: ", Edump(sandboxShareInfo), pbMsg)
            msg.extendinfopb = dataToString
            local reportMsg =
            {
                Train_Type = sandboxShareInfo.trainType , --火车类型（上报火车配置ID）
                Train_Server_ID =  sandboxShareInfo.worldId, --火车归属区服ID
                Position_Reward_Group =  sandboxShareInfo.reward, --各位置奖励组ID（道具1#道具2...)
            }
            event.EventReport("Trade_Share_Train", reportMsg)
        end
        ----------------------
    elseif sType == mq_common_pb.enSpeak_AcornPubTreasure then --宝藏碎片分享
        if goods then -- 订单信息
            local activity_pb = require "activity_pb"
            local pbMsg = activity_pb.SwapOrderData()
            pbMsg.orderId = goods.orderId
            pbMsg.createId = goods.createId
            pbMsg.createTime = goods.createTime
            pbMsg.status = goods.status
            pbMsg.consumeItemId = goods.consumeItemId
            pbMsg.consumeNum = goods.consumeNum
            pbMsg.targetItemId = goods.targetItemId
            pbMsg.targetItemNum = goods.targetItemNum
            pbMsg.tradeRoleId = goods.tradeRoleId
            pbMsg.tradeTime = goods.tradeTime
            local dataToString = pbMsg:SerializeToString()
            msg.extendinfopb = dataToString
        end
        
    elseif sType == mq_common_pb.enSpeak_Gathering then
        -----新增集结大作战分享-------
        if sandboxShareInfo then
            local activity_pb = require "activity_pb"
            local pbMsg = activity_pb.TGatheringChatPushinfo()
            pbMsg.playerName = sandboxShareInfo.playerName
            pbMsg.killMonsterNum = sandboxShareInfo.killMonsterNum
            pbMsg.rewardID = sandboxShareInfo.rewardID
            pbMsg.rewardNum = sandboxShareInfo.rewardNum
            pbMsg.rewardType = sandboxShareInfo.rewardType
            local dataToString = pbMsg:SerializeToString()
            msg.extendinfopb = dataToString
        end
        ----------------------
    elseif sType == mq_common_pb.enSpeak_GoldenEggsMaxReward then --惊喜盒金蛋运气最佳
        if goods then
            local pbMsg = mq_common_pb.TGoldenEggsMaxReward()
            pbMsg.playerId = goods.playerId
            pbMsg.playerName = goods.playerName
            pbMsg.playerFaceStr = goods.playerFaceStr
            pbMsg.playerFrameid = goods.playerFrameid
            pbMsg.launchID = goods.launchID
            pbMsg.launchName = goods.launchName
            pbMsg.redPacketId = goods.redPacketId
            pbMsg.goldenEggsId = goods.goldenEggsId
            local dataToString = pbMsg:SerializeToString()
            msg.extendinfopb = dataToString
        end
    elseif sType==mq_common_pb.enSpeak_BattleVicReportShare 
            or sType==mq_common_pb.enSpeak_BattleDefReportShare 
            or sType==mq_common_pb.enSpeak_DetectReportShare then
        if goods then
            local pbMsg = chat_pb.ChatShareBattleReport()
            pbMsg.sType = sType
            pbMsg.sId = goods.sId
            pbMsg.subMailType = goods.subMailType
            pbMsg.bIsVic = goods.bIsVic or false
            pbMsg.contentLangId = goods.contentLangId
            pbMsg.nameInfoLangId = goods.nameInfoLangId
            pbMsg.nameInfo = goods.nameInfo
            pbMsg.teamCount = tonumber(goods.teamCount) or 0
            pbMsg.teamPowerSum = tonumber(goods.teamPowerSum) or 0
            local dataToString = pbMsg:SerializeToString()
            msg.extendinfopb = dataToString
        end
    end

    --当前个人信息不展示战力最高的6个英雄
    --local topSixHero = player_mgr.GetSixTopHero()
    --for i = 1, #topSixHero do
    --    local heroMsg = msg.topHero:add()
    --    heroMsg.heroId = topSixHero[i].heroID
    --    heroMsg.heroLv = topSixHero[i].numProp.lv
    --    heroMsg.starLv = topSixHero[i].numProp.starLv
    --end

    if chat_monitor_mgr.CheckPrivateMute() and sType ~= mq_common_pb.enSpeak_LeagueMutualHelp then
        chat_monitor_mgr.PriveteBanSimulateChat(msg)
    else
        net.SendMessage(net.Endpoint_Client, net.Endpoint_AccountLogin, 0, msg_pb.MSG_CHAT_SPEAK_REQ, msg)
    end
end

--发言回复 C->S
function Recv_CHAT_SPEAK(msg)
    if msg.err ~= 0 then
        if msg.err >= 450 and msg.err <= 452 then
            local vipLevel = player_mgr.GetPlayerVipLevel()
            local vipcfg = game_scheme:InitBattleProp_0(708).szParam.data[0]
            local cfg = nil
            if vipLevel >= vipcfg then
                cfg = game_scheme:InitBattleProp_0(706).szParam.data
            else
                cfg = game_scheme:InitBattleProp_0(704).szParam.data
            end
            -- local cfg = game_scheme:InitBattleProp_0(55).szParam.data
            local id = cfg[msg.err - 450]
            local hookLevelCfg = game_scheme:HookLevel_0(id)
            if hookLevelCfg then
                flow_text.Add(string.format2(lang.Get(100000 + msg.err), hookLevelCfg.checkPointID))
            else
                flow_text.Add(100000 + msg.err)
            end
        elseif msg.err==error_code_pb.enErr_Alliance_UrgentAnnounce then
            chat_mgr_new.ShowUrgentAnnounceTimeCountDown()
        else
            flow_text.Add(lang.Get(100000 + msg.err))
        end
    else
        if msg.sType and msg.sType == mq_common_pb.enSpeak_ShareCell or msg.sType == mq_common_pb.enSpeak_ShareLabourDayInfo then
            event.Trigger(event.SHARE_CELL_SEND_SUCCESSFULLY)
        elseif msg.sType and msg.sType == mq_common_pb.enSpeak_MakeFoodInfo then
            --订单发送成功
            flow_text.Add(lang.Get(384283))
        elseif msg.sType and msg.sType == mq_common_pb.enSpeak_SandboxMarkPos then
            --log.Error("沙盘分享发送成功")
        elseif msg.sType and msg.sType == mq_common_pb.enSpeak_SandboxTreasure then
            --log.Error("分享走的回复？")
            --分享宝藏打点
            event.EventReport("Radar_ShareTreasure", {})
        else
            event.Trigger(event.CHAT_SEND_SUCCESSFULLY,msg)
        end
    end
end
-- 新消息通知 s->c
function Recv_CHAT_NEW_MSG(msg)
    ------print("Recv_CHAT_NEW_MSG>>>>>>>")

    chat_mgr_new.AddChatNewMsg(msg)
    --dump(msg)
end

-- 请求删除聊天数据
function Recv_CHAT_RECALL_MSG_REQ(channel, szChatID)
    local msg = chat_pb.TMSG_CHAT_RECALL_MSG_REQ()
    local langKey = ui_setting_data.GetAttrData(EnSubModel.En_Model_Base, EnBaseAttrKey.En_AttrKey_Lang)
    msg.channel = channel
    msg.languageType = ui_setting_cfg.LangMap[tonumber(langKey)].platformLangId
    msg.szChatID = szChatID
    net.SendMessage(net.Endpoint_Client, net.Endpoint_AccountLogin, 0, msg_pb.MSG_CHAT_RECALL_MSG_REQ, msg)
end

-- 删除聊天数据回复
function Recv_CHAT_RECALL_MSG_RSP(msg)
    if msg.errorCode ~= 0 then
        flow_text.Add(100000 + msg.errorCode)
    end
end

--[[
    个人消息，历史遗留问题，原本叫私人消息，为避免与下面私聊消息混淆，改名为个人消息,个人消息会显示在联盟频道里
    Recv_PRIVATE_MSG_SET_NTF 用于联盟频道里送花(or更多的互动)消息集的接收
    Send_PRIVATE_MSG_SPEAK 用于联盟模块里的送花互动
    Recv_PRIVATE_MSG_NTF 会用于联盟频道里送花(or更多的互动)消息和羁绊聊天消息的接收
]]
--个人消息集通知(登录时下发) s->c
function Recv_PRIVATE_MSG_SET_NTF(msg)
    chat_mgr_new.SetPersonalMsg(msg)
end
--请求发送个人消息 C->S 
function Send_PRIVATE_MSG_SPEAK(toRoleId, msgType, context, toRoleName)
    local msg = chat_pb.TMSG_PRIVATE_MSG_SPEAK_REQ()
    msg.toRoleId = toRoleId
    msg.msgType = msgType
    msg.context = context
    if toRoleName then
        msg.toRoleName = toRoleName
    end

    -- local topSixHero = player_mgr.GetSixTopHero()
    -- for i=1, #topSixHero do 
    -- 	local heroMsg = msg.topHero:add()
    -- 	heroMsg.heroId = topSixHero[i].heroID
    -- 	heroMsg.heroLv = topSixHero[i].numProp.lv
    -- 	heroMsg.starLv = topSixHero[i].numProp.starLv
    -- end

    net.SendMessage(net.Endpoint_Client, net.Endpoint_AccountLogin, 0, msg_pb.MSG_PRIVATE_MSG_SPEAK_REQ, msg)
end
--个人消息发言回复 C->S
function Recv_PRIVATE_MSG_SPEAK_RSP(msg)
end
--个人消息通知 s->c
function Recv_PRIVATE_MSG_NTF(msg)
    if msg.msg.sType == mq_common_pb.enSpeak_Mate then
        local friendship_house_data = require "friendship_house_data"
        friendship_house_data.ON_TMSG_PRIVATE_MSG_NTF(msg)
    else
        chat_mgr_new.AddPersonalNewMsg(msg)
    end
end

---@return string|string @ 登录时间|登录的key
function GetLoginInfo()
    return tostring(loginTime), tostring(loginKey)
end

--Get玩家信息
function GetRoleInfo()
    local player_mgr = require "player_mgr"
    local setting_server_data = require "setting_server_data"
    local langkey = ui_setting_data.GetAttrData(EnSubModel.En_Model_Base, EnBaseAttrKey.En_AttrKey_Lang)

    local UserId = nil
    local game = require "game"
    if game.actors and #game.actors > 0 then
        UserId = game.actors[1].BCUserID
    end
    UserId = (UserId ~= nil and UserId ~= "") and UserId or player_mgr.GetPlayerUserID() or "0"
    UserId = tostring(UserId)

    local RoleId = player_mgr.GetPlayerRoleID() or 0
    local ServerId = loginWorldID or setting_server_data.GetLoginWorldID() or 0
    local LanguageId = ui_setting_cfg.LangMap[tonumber(langkey)].platformLangId
    local LangIso = ui_setting_cfg.LangMap[tonumber(langkey)].iso
    return UserId, RoleId, ServerId, LanguageId, LangIso
end

--client->平台 登录、切换语言时申请分配语言频道，成功后设置当前频道信息、上报频道id、请求频道聊天数据
--**********gw已废弃
function Req_ALLOT_CHANNEL()
    -- 审核服不需要切换频道
    if ReviewingUtil.IsReviewing() then
        return
    end

    if not (loginKey and loginTime) then
        Debug.LogError("error:: loginKey==nil or loginTime==nil")
        flow_text.Add(lang.Get(928))
        return
    end

    local UserId, RoleId, ServerId, LanguageId = GetRoleInfo()

    local startTime = os.time()
    local urlFormat = url_mgr.SelectUrlByEnvir(url_mgr.CHANNEL_ALLOT_URL) or url_mgr.CHANNEL_ALLOT_URL[1][2]
    local url = string.format(urlFormat, UserId, RoleId, ServerId, LanguageId, tostring(loginTime), loginKey)
    --local log = require "log"
    --log.Error(" Req_ALLOT_CHANNEL ",url)
    print("Req_ALLOT_CHANNEL 开始请求分配频道:", url)
    http_inst.Req_Timeout(url, 30, function(str)
        local json = require "dkjson"
        --print("Req_ALLOT_CHANNEL~~~~~~~~~~~请求频道耗时：",os.time()-startTime,"str:",str)
        local data = json.decode(str) or {}
        --dump(data)
        if data.code == 1 then
            --chat_mgr_new.SetAllotChannelInfo(data)    --设置当前频道信息
            --CheckReportChannel()        --上报频道id
            --Req_LANGUAGE_CHANNEL_MSG()                --请求频道聊天数据
        else
            flow_text.Add(lang.Get(929))
            ----            print("Req_ALLOT_CHANNEL::error  msg=",data.message)
        end

    end)
end

--client->平台 切换频道时申请分配语言频道，成功后设置当前频道信息、上报频道id、请求频道聊天数据
--********gw已废弃
local tempChannelData = nil
function Req_CHANGE_CHANNEL(channelId)
    if not (loginKey and loginTime) then
        Debug.LogError("error:: loginKey==nil or loginTime==nil")
        flow_text.Add(lang.Get(928))
        return
    end

    local UserId, RoleId, ServerId, LanguageId = GetRoleInfo()
    tempChannelData = chat_mgr_new.GetChannelDataById(channelId)
    local url = string.format(url_mgr.SelectUrlByEnvir(url_mgr.CHANNEL_CHANGE_URL), channelId, UserId, ServerId, RoleId, LanguageId, tostring(loginTime), loginKey)
    --local log = require "log"
    --log.Error(" Req_CHANGE_CHANNEL ",url)
    http_inst.Req_Timeout(url, 30, function(str)
        local json = require "dkjson"
        ------print("Req_CHANGE_CHANNEL>1>>>>>>>>>>>>>>",str)
        local data = json.decode(str) or {}
        --dump(data)
        --event.Trigger(event.RES_CHANGE_CHANNEL,data) --用于ui提示
        if data.code == 1 then
            flow_text.Add(lang.Get(918))

            --chat_mgr_new.SetAllotChannelInfo({ data = tempChannelData })    --设置当前频道信息
            --CheckReportChannel()                            --上报频道id
            --Req_LANGUAGE_CHANNEL_MSG()                                    --请求频道聊天数据
        else
            flow_text.Add(lang.Get(917))
            ----            print("Req_CHANGE_CHANNEL::error code= msg=",data.code,data.message)
        end
    end)
end

--client->平台 请求频道聊天数据
--********** gw已废弃
function Req_LANGUAGE_CHANNEL_MSG()
    if not (loginKey and loginTime) then
        Debug.LogError("error:: loginKey==nil or loginTime==nil")
        flow_text.Add(lang.Get(928))
        return
    end

    local startTime = os.time()
    local UserId, RoleId, ServerId, LanguageId = GetRoleInfo()
    local curLangCHannelID = chat_mgr_new.GetCurLangChannelId()
    local url = string.format(url_mgr.SelectUrlByEnvir(url_mgr.CHANNEL_MSG_URL), curLangCHannelID, UserId, RoleId, ServerId, tostring(loginTime), loginKey)
    --local log = require "log"
    --log.Error(" Req_LANGUAGE_CHANNEL_MSG ",url)
    http_inst.Req_Timeout(url, 30, function(str)
        local json = require "dkjson"
        --print("Req_LANGUAGE_CHANNEL_MSG 请求频道聊天数据耗时 ~~~~~~~~~~~",os.time()-startTime,"str:",str)
        startTime = os.time()
        local data = json.decode(str) or {}
        --dump(data)
        if data.code == 1 then
            --chat_mgr_new.SetLangChannelMsg(data)
            --print("Req_LANGUAGE_CHANNEL_MSG> 解析频道数据耗时 ~~~~~~~~~~~~~",os.time()-startTime,"数量：",util.get_len(data.data))

        else
            flow_text.Add(lang.Get(930))
            ----            print("Req_LANGUAGE_CHANNEL_MSG::error code= msg=",data.code,data.message)
        end
    end)
end

--client->平台 登录时请求私人聊天数据
--********** gw已废弃
function Req_PRIVATE_MSG()
    if not (loginKey and loginTime) then
        Debug.LogError("error:: loginKey==nil or loginTime==nil")
        flow_text.Add(lang.Get(928))
        return
    end

    local startTime = os.time()

    local UserId, RoleId, ServerId, LanguageId = GetRoleInfo()
    local selectUrl = url_mgr.SelectUrlByEnvir(url_mgr.PRIVATE_MSG_URL)
    if not selectUrl then
        return
    end
    local url = string.format(selectUrl, RoleId, ServerId, UserId, tostring(loginTime), loginKey)
    --local log = require "log"
    --log.Error(" Req_PRIVATE_MSG ",url)
    http_inst.Req_Timeout(url, 30, function(str)
        local json = require "dkjson"
        --print("Req_PRIVATE_MSG> 请求私聊数据耗时~~~~~~~~~~~~~~~",os.time()-startTime,"str:",str)
        startTime = os.time()
        local data = json.decode(str) or {}
        --dump(data)
        if data.code == 1 then
            chat_mgr_new.SetPrivateMsg(data)
            --print("Req_PRIVATE_MSG> 解析私聊数据耗时~~~~~~~~~~~~~~~",os.time()-startTime,"数量：",util.get_len(data.data))

        else
            flow_text.Add(lang.Get(931))
            --print("Req_PRIVATE_MSG::error code= msg=",data.code,data.message)
        end
    end)
end

--client->平台 打开频道ui时请求频道配置
function Req_CHANNEL_CONFIG()
    if not (loginKey and loginTime) then
        Debug.LogError("error:: loginKey==nil or loginTime==nil")
        flow_text.Add(lang.Get(928))
        return
    end

    local UserId, RoleId, ServerId, LanguageId = GetRoleInfo()
    local url = string.format(url_mgr.SelectUrlByEnvir(url_mgr.CHANNEL_CONFIG_URL), LanguageId, UserId, RoleId, ServerId, tostring(loginTime), loginKey)
    http_inst.Req_Timeout(url, 30, function(str)
        local json = require "dkjson"
        ------print("Req_CHANNEL_CONFIG>1>>>>>>>>>>>>>>",str)
        local data = json.decode(str) or {}
        --dump(data)
        if data.code == 1 then
            chat_mgr_new.SetLangChannelConfig(data)
        else
            flow_text.Add(lang.Get(932))
            ----            print("Req_CHANNEL_CONFIG::error code= msg=",data.code,data.message)
        end
    end)
end

--填充私聊和频道聊天发送消息的公共部分
function FillSpeakMsg(msg, stype, context, hero, goods, extendinfo, shareLottery, sandboxShareInfo)
    local player_mgr = require "player_mgr"

    msg.context.stype = stype
    if context and context ~= "" and sType ~= mq_common_pb.enSpeak_SandboxMarkPos then
        msg.context.context = context
    end
    if hero then
        for key, value in pairs(hero) do
            if key == "equip" then
                for k_1, sid in pairs(value) do
                    local tShareGoods = msg.context.hero.equip:add()
                    local entity = player_mgr.GetPacketPartDataBySid(sid)
                    if entity then
                        tShareGoods.goodsId = entity.goodsID
                        tShareGoods.lv = entity.numProp.equipStrLv
                        tShareGoods.resonanceproId = entity:GetEquipResonance() or 0
                        -- tShareGoods.profession = entity.numProp.careerType or 0
                        local level = false
                        if level then
                            tShareGoods.secondArtifactLevel = level
                        end
                        local proArr = nil
                        local itemCfg = game_scheme:Item_0(entity.goodsID)
                        if itemCfg and itemCfg.type == item_data.Item_Type_Enum.Sigil then
                            proArr = { entity.numProp.upgradeExp, entity.numProp.lock, entity.numProp.extendProp1, entity.numProp.extendProp2 }

                        else
                            proArr = entity:GetEquipAddProps()
                            local baseProp = entity:GetEquipBasePropID()
                            table.insert(proArr, 1, baseProp)
                            local equipLv = entity:GetEquipStrLv() or 0
                            table.insert(proArr, equipLv)
                            local extendProp = entity:GetEquipExtendProps()
                            if extendProp then
                                for k_2, v in pairs(extendProp) do
                                    if v ~= 0 then
                                        table.insert(proArr, v)
                                    end
                                end
                            end
                        end
                        for k_2, v in pairs(proArr) do
                            tShareGoods.proId:append(v)
                        end
                    end
                end
            else
                if key == "decorate" then
                    for i, v in pairs(value) do
                        local tShareDecorates = msg.context.hero.decorate:add()
                        if v.decorate and v.decorate.goodsId then
                            -- print("v.lv",v.lv,"goodsID",v.decorate.goodsId)
                            local good = tShareDecorates.decorate
                            good.goodsId = v.decorate.goodsId
                            good.lv = v.decorate.lv
                        end
                        tShareDecorates.lv = v.lv
                    end
                else
                    -- print("msg.context.hero."..key.." = ",value)
                    msg.context.hero[key] = value
                end
            end
        end
    end
    if goods then
        for key, value in pairs(goods) do
            if key == "proId" then
                for _, v in ipairs(value) do
                    msg.context.goods[key]:append(v)
                end
            else
                msg.context.goods[key] = value
            end
        end
    end
    if extendinfo then
        msg.context.extendinfo = extendinfo
    end

    local topSixHero = player_mgr.GetSixTopHero()
    for i = 1, #topSixHero do
        local heroMsg = msg.context.topHero:add()
        heroMsg.heroId = topSixHero[i].heroID
        heroMsg.heroLv = topSixHero[i].numProp.lv
        heroMsg.starLv = topSixHero[i].numProp.starLv
    end
    if shareLottery then
        for key, value in pairs(shareLottery) do
            if key == "oHeroData" then
                for _, v in ipairs(value) do
                    local tShareHeros = msg.context.shareLottery.oHeroData:add()
                    tShareHeros.nID = v.nID
                    tShareHeros.nStar = v.nStar
                end
            else
                msg.context.shareLottery[key] = value
            end
        end
    end
    -----新增沙盘分享-------
    if sandboxShareInfo then
        msg.context.sandboxmarkData.bServerID = sandboxShareInfo.bServerID
        msg.context.sandboxmarkData.x = sandboxShareInfo.pos.x
        msg.context.sandboxmarkData.y = sandboxShareInfo.pos.y
        if sandboxShareInfo.sid then
            msg.context.sandboxmarkData.sid = sandboxShareInfo.sid
        end
        msg.context.sandboxmarkData.playerName = sandboxShareInfo.playerName
        msg.context.sandboxmarkData.entityLevel = sandboxShareInfo.entityLevel
        msg.context.sandboxmarkData.entityName = sandboxShareInfo.entityName
        msg.context.sandboxmarkData.quality = sandboxShareInfo.quality
        msg.context.sandboxmarkData.context = context
    end
    ----------------------

    return msg
end

--语言频道发言 C->S
function Send_CHAT_LANGUAGE_CHANNEL_SPEAK(stype, context, hero, goods, extendinfo, shareLottery)
    Send_CHAT_SPEAK(chat_pb.Channel_Language,stype,context,hero,goods,shareLottery)
    
    --local langChannelId = chat_mgr_new.GetCurLangChannelId()
    --local UserId, RoleId, ServerId, LanguageId = GetRoleInfo()
    --if langChannelId == nil or LanguageId == nil then
    --    ----        print("Send_CHAT_LANGUAGE_CHANNEL_SPEAK::langChannelId==nil or LanguageId==nil~~~~~~~~~~~~~~~")
    --    return
    --end
    --
    --local msg = chat_pb.TMSG_CHAT_LANGUAGE_CHANNEL_SPEAK_REQ()
    --msg.context.channel = langChannelId
    --msg.context.langtype = LanguageId
    --msg = FillSpeakMsg(msg, stype, context, hero, goods, extendinfo, shareLottery)
    --
    ----print(396,"语言频道发言 >>> ",tostring(msg))
    --if chat_monitor_mgr.CheckPrivateMute() then
    --    chat_monitor_mgr.PriveteBanCrossSvrSimulateChat(msg)
    --else
    --    log.Log("Send_CHAT_LANGUAGE_CHANNEL_SPEAK", langChannelId, LanguageId, context)
    --    net.SendMessage(net.Endpoint_Client, net.Endpoint_AccountLogin, 0, msg_pb.MSG_CHAT_LANGUAGE_CHANNEL_SPEAK_REQ, msg)
    --end

    -- local tContent = context
    -- if context == "压力测试" then
    -- 	for i=1,5000 do
    -- 		context = tContent.."####"..i
    -- 		msg.context.context = context
    -- 		net.SendMessage(net.Endpoint_Client, net.Endpoint_AccountLogin, 0, msg_pb.MSG_CHAT_LANGUAGE_CHANNEL_SPEAK_REQ,msg)
    -- 	end
    -- end

end

--语言频道发言回复 S->C
function Recv_CHAT_LANGUAGE_CHANNEL_SPEAK(msg)
    if msg.err ~= 0 then
        if msg.err >= 450 and msg.err <= 452 then
            local vipLevel = player_mgr.GetPlayerVipLevel()
            local vipcfg = game_scheme:InitBattleProp_0(708).szParam.data[0]
            local cfg = nil
            if vipLevel >= vipcfg then
                cfg = game_scheme:InitBattleProp_0(707).szParam.data
            else
                cfg = game_scheme:InitBattleProp_0(705).szParam.data
            end
            local id = cfg[msg.err - 450]
            local hookLevelCfg = game_scheme:HookLevel_0(id)
            if hookLevelCfg then
                flow_text.Add(string.format2(lang.Get(100000 + msg.err), hookLevelCfg.checkPointID))
            end
        else
            flow_text.Add(lang.Get(100000 + msg.err))
        end
    else
        if msg.sType and msg.sType == mq_common_pb.enSpeak_ShareCell or
                msg.sType == mq_common_pb.enSpeak_ShareLabourDayInfo then
            event.Trigger(event.SHARE_CELL_SEND_SUCCESSFULLY)
        else
            event.Trigger(event.CHAT_SEND_SUCCESSFULLY,msg)
        end
    end
end

--语言频道消息通知 s->c
--********** gw已废弃
function Recv_CHAT_LANGUAGE_CHANNEL_MSG(msg)
    chat_mgr_new.AddLangChannelNewMsg(ReBuildChatMsg(msg.msg))
end


--[[
	缓存自己发送出去的私聊消息，收到自己发出去的私聊消息时做个校验，填充一些必要数据：toRoleFaceId、toRoleName等等
	原因：收到自己的发送的私聊消息时，不带对方的信息
]]
local cacheRoleData = {}

function GetCacheRoleData()
    return cacheRoleData
end
function IsExistRoleData(id)
    return cacheRoleData[id]
end
function SetCacheRoleData(roleID, data)
    cacheRoleData[roleID] = data
end

function SetCacheRoleData(roleID, data)
    cacheRoleData[roleID] = data
end

--请求私聊 C->S
function Send_CROSS_SVR_CHAT_PRIVATE_SPEAK(stype, toRoleId, toworldid, isToGM, toTitleName, toName, toFaceId, toAvatarFrame, toLv, context, hero, goods, extendinfo, sandboxShareInfo)
    if toRoleId == nil or toworldid == nil then
        ----        print("Send_CROSS_SVR_CHAT_PRIVATE_SPEAK::toRoleId==nil or toworldid==nil~~~~~~~~~~~~~~~")
        return
    end
    --缓存数据
    cacheRoleData[toRoleId] = {
        c_toRoleId = toRoleId,
        c_toworldid = toworldid,
        c_toName = toName,
        c_toTitleName = toTitleName,
        c_toFaceId = toFaceId,
        c_toLv = toLv,
        c_toAvatarFrame = toAvatarFrame,
    }
    ------ print("cacheRoleData>>>>>>>>>>>>>>>>>>>>>")
    -- dump(cacheRoleData)
    local msg = chat_pb.TMSG_CROSS_SVR_CHAT_PRIVATE_SPEAK_REQ()
    msg.context.toRoleId = toRoleId
    msg.context.toworldid = toworldid
    msg.context.bgm = isToGM
    if toName then
        msg.context.toRoleName = toName
    end
    msg = FillSpeakMsg(msg, stype, context, hero, goods, extendinfo, nil, sandboxShareInfo)

    ----    print("Send_CROSS_SVR_CHAT_PRIVATE_SPEAK>>>>>>>>>>",toRoleId,toworldid,isToGM,context)
    print(396, "请求私聊 >>> ", tostring(msg))
    if chat_monitor_mgr.CheckPrivateMute() then
        chat_monitor_mgr.PriveteBanPrivateChatSimulateChat(msg, cacheRoleData[toRoleId])
    else
        net.SendMessage(net.Endpoint_Client, net.Endpoint_AccountLogin, 0, msg_pb.MSG_CROSS_SVR_CHAT_PRIVATE_SPEAK_REQ, msg)
    end

end

--请求私聊回复 S->C
function Recv_CROSS_SVR_CHAT_PRIVATE_SPEAK(msg)
    ------print("Recv_CROSS_SVR_CHAT_PRIVATE_SPEAK>>>>>>>>>>",msg.err)

    if msg.err ~= 0 then

        if msg.err == 461 or (msg.err >= 450 and msg.err <= 452) then
            local cfg = game_scheme:InitBattleProp_0(306).szParam.data
            local id = cfg[1]
            local hookLevelCfg = game_scheme:HookLevel_0(id)
            if hookLevelCfg then
                flow_text.Add(string.format2(lang.Get(100000 + msg.err), hookLevelCfg.checkPointID))
            end
        else
            flow_text.Add(lang.Get(100000 + msg.err))
        end
    else
        if msg.sType and msg.sType == mq_common_pb.enSpeak_ShareCell or msg.sType == mq_common_pb.enSpeak_ShareLabourDayInfo then
            event.Trigger(event.SHARE_CELL_SEND_SUCCESSFULLY)
        elseif msg.stype and msg.stype == mq_common_pb.enSpeak_MakeFoodInfo then
            --订单发送成功
            flow_text.Add(lang.Get(384283))
        else
            event.Trigger(event.CHAT_SEND_SUCCESSFULLY,msg)
        end
    end

end
-- 跨服私聊通知 s->c
function Recv_CROSS_SVR_CHAT_PRIVATE_MSG(msg)
    local rMsg = ReBuildChatMsg(msg.msg)
    local RoleId = player_mgr.GetPlayerRoleID()
    if rMsg.roleid == RoleId then
        local roleInfo = cacheRoleData[rMsg.toRoleId]
        if roleInfo then
            rMsg.toName = roleInfo.c_toName
            rMsg.toTitleName = roleInfo.c_toTitleName
            rMsg.toFaceId = roleInfo.c_toFaceId
            rMsg.toRoleLv = roleInfo.c_toLv
            rMsg.toAvatarFrame = roleInfo.c_toAvatarFrame
        end
    end

    chat_mgr_new.AddPrivateNewMsg(rMsg)

end
---重置上报聊天频道id计时器
function ResetReportChannelTimer()
    if reportChannelTimer then
        util.RemoveDelayCall(reportChannelTimer)
        reportChannelTimer = nil
    end
    isReceiveReportChannelRsp = nil
    intervalNowCount = 0
end

--上报聊天频道id c->s
function Send_CROSS_SVR_CHAT_PRIVATE_MSG()
    local curLangCHannelID = chat_mgr_new.GetCurLangChannelId()
    if curLangCHannelID ~= nil then
        local msg = chat_pb.TMSG_CHAT_REPORT_CHANNEL_REQ()
        local UserId, RoleId, ServerId, LanguageId = GetRoleInfo()
        msg.langtype = LanguageId
        msg.channelid = curLangCHannelID
        net.SendMessage(net.Endpoint_Client, net.Endpoint_AccountLogin, 0, msg_pb.MSG_CHAT_REPORT_CHANNEL_REQ, msg)
    else
        log.Error("CurLangChannelId is nil")
    end
end

--// 上报聊天频道id回复 s->c
function Recv_CHAT_REPORT_CHANNEL_RSP(msg)
    if msg.err and msg.err ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.err))
    end
    isReceiveReportChannelRsp = true
    ResetReportChannelTimer()
end
---上报频道id增加定时器，没返回就继续请求，因为进游戏的时候登录数据多，可能存在协议发了但是服务器没收到的情况
function CheckReportChannel()
    reportChannelTimer = reportChannelTimer or util.IntervalCall(intervalTime, function()
        if isReceiveReportChannelRsp then
            return true
        end
        Send_CROSS_SVR_CHAT_PRIVATE_MSG()
        intervalNowCount = intervalNowCount + 1
        if intervalNowCount > intervalMaxCount then
            return true
        end
    end)
end

function ReBuildChatMsg(crossSvrChatMsg)
    local new = {}
    new.roleid = crossSvrChatMsg.common.roleid
    new.faceId = crossSvrChatMsg.common.faceId
    new.name = crossSvrChatMsg.common.name
    new.roleLv = crossSvrChatMsg.common.roleLv
    new.bHideVip = crossSvrChatMsg.common.bHideVip
    new.nVipLv = crossSvrChatMsg.common.nVipLv
    new.ce = crossSvrChatMsg.common.ce
    new.passStage = crossSvrChatMsg.common.passStage
    new.worldid = crossSvrChatMsg.common.worldid
    new.guildname = crossSvrChatMsg.common.guildname
    new.title = crossSvrChatMsg.common.title
    new.sType = crossSvrChatMsg.common.sType or 0
    new.chatTime = crossSvrChatMsg.common.chatTime
    new.context = crossSvrChatMsg.common.context
    new.extendinfopb2 = crossSvrChatMsg.common.extendinfopb
    new.avatarFrame = crossSvrChatMsg.common.avatarFrame
    new.nameIcon = crossSvrChatMsg.common.nameIcon
    new.extendinfo = crossSvrChatMsg.common.extendinfo
    new.bgm = crossSvrChatMsg.common.bgm
    new.toRoleId = crossSvrChatMsg.toRoleId
    new.toWorldid = crossSvrChatMsg.toWorldid
    new.langtype = crossSvrChatMsg.langtype
    new.channelid = crossSvrChatMsg.channelid
    new.guildid = crossSvrChatMsg.guildid
    new.titleID = crossSvrChatMsg.common.titleID
    new.voidArenaID = crossSvrChatMsg.common.voidArenaID
    new.customtitle = crossSvrChatMsg.common.customtitle
    new.faceStr = crossSvrChatMsg.common.faceStr

    new.nFlag = crossSvrChatMsg.common.nFlag or 0    --聊天监控标记
    new.szChatID = crossSvrChatMsg.common.szChatID or string.format("%s-%s", new.roleid, new.chatTime)    --聊天ID，每条聊天唯一ID
    new.topHero = {}
    for k, heroInfo in ipairs(crossSvrChatMsg.common.topHero) do
        local hero = {}
        hero.heroId = heroInfo.heroId
        hero.heroLv = heroInfo.heroLv
        hero.starLv = heroInfo.starLv
        table.insert(new.topHero, hero)
    end
    -------沙盘定位分享----------
    new.sandboxmarkData = crossSvrChatMsg.common.sandboxmarkData
    ---

    if crossSvrChatMsg.common:HasField("hero") then
        new.hero = {
            heroId = crossSvrChatMsg.common.hero.heroId,
            lv = crossSvrChatMsg.common.hero.lv,
            advanceLv = crossSvrChatMsg.common.hero.advanceLv,
            starLv = crossSvrChatMsg.common.hero.starLv,
            ce = crossSvrChatMsg.common.hero.ce,
            hp = crossSvrChatMsg.common.hero.hp,
            attack = crossSvrChatMsg.common.hero.attack,
            defence = crossSvrChatMsg.common.hero.defence,
            speed = crossSvrChatMsg.common.hero.speed,
            equip = crossSvrChatMsg.common.hero.equip,
            skinId = crossSvrChatMsg.common.hero.skinId,
            weaponData = crossSvrChatMsg.common.hero.weaponData,
            nWeaponAddCE = crossSvrChatMsg.common.hero.nWeaponAddCE,
            bTrialHero = crossSvrChatMsg.common.hero.bTrialHero
        }
    end
    if crossSvrChatMsg.common:HasField("goods") then
        new.goods = {
            goodsId = crossSvrChatMsg.common.goods.goodsId,
            lv = crossSvrChatMsg.common.goods.lv,
            proId = {},
            resonanceproId = crossSvrChatMsg.common.goods.resonanceproId,
        }
        for k, pId in ipairs(crossSvrChatMsg.common.goods.proId) do
            table.insert(new.goods.proId, pId)
        end
    end
    if crossSvrChatMsg.common:HasField("langText") then
        new.langText = {
            iLangId = crossSvrChatMsg.common.langText.iLangId,
            arrLangParams = {},
        }
        for k, lp in ipairs(crossSvrChatMsg.common.langText.arrLangParams) do
            table.insert(new.langText.arrLangParams, lp)
        end
    end
    if crossSvrChatMsg.common:HasField("leagueHelp") then
        new.leagueHelp = {
            process = crossSvrChatMsg.common.leagueHelp.process,
            helpID = crossSvrChatMsg.common.leagueHelp.helpID,
            flagID = crossSvrChatMsg.common.leagueHelp.flagID,
        }
    end
    if crossSvrChatMsg.common:HasField("shareLottery") then
        new.shareLottery = {
            strName = crossSvrChatMsg.common.shareLottery.strName,
            nTime = crossSvrChatMsg.common.shareLottery.nTime,
            nScore = crossSvrChatMsg.common.shareLottery.nScore,
            nLotteryType = crossSvrChatMsg.common.shareLottery.nLotteryType,
            oHeroData = crossSvrChatMsg.common.shareLottery.oHeroData,
        }
    end
    if crossSvrChatMsg.common:HasField("shareInfo") then
        new.shareInfo = {
            nShareType = crossSvrChatMsg.common.shareInfo.nShareType,
            nDaySellPrice = crossSvrChatMsg.common.shareInfo.nDaySellPrice,
            nHostId = crossSvrChatMsg.common.shareInfo.nHostId,
            nStallId = crossSvrChatMsg.common.shareInfo.nStallId,
            nShareState = crossSvrChatMsg.common.shareInfo.nShareState,
            nHostAreaId = crossSvrChatMsg.common.shareInfo.nHostAreaId,
        }
    end
    if crossSvrChatMsg.common:HasField("exchangeInfo") then
        new.exchangeInfo = {
            originItemId = crossSvrChatMsg.common.exchangeInfo.originItemId,
            originItemNum = crossSvrChatMsg.common.exchangeInfo.originItemNum,
            targetItemId = crossSvrChatMsg.common.exchangeInfo.targetItemId,
            targetItemNum = crossSvrChatMsg.common.exchangeInfo.targetItemNum,
            orderId = crossSvrChatMsg.common.exchangeInfo.orderId,
            createTime = crossSvrChatMsg.common.exchangeInfo.createTime,
            activityID = crossSvrChatMsg.common.exchangeInfo.activityID,
        }
    end
    if crossSvrChatMsg.common:HasField("pickTheRouteArchived") then
        new.pickTheRouteArchived = {
            levelID = crossSvrChatMsg.common.pickTheRouteArchived.levelID,
            eventChooseID = crossSvrChatMsg.common.pickTheRouteArchived.eventChooseID,
            activityID = crossSvrChatMsg.common.pickTheRouteArchived.activityID,
        }
    end

    return new
end

--登录时服务器同步下来的签名，用于请求平台接口校验
function Recv_TMSG_ROLE_LOGIN_KEY_NTF(msg)
    ----    print("Recv_TMSG_ROLE_LOGIN_KEY_NTF>>>>>>>>>>>>>>>>>>>.",msg.loginKey,msg.time1)
    loginKey = msg.loginKey
    loginTime = msg.time1
    if msg.worldid then
        loginWorldID = msg.worldid
    end
    DelayReqChatData()
end



--///聊天翻译------------------------------------
local cacheStrArr
--聊天请求签名
function Req_MSG_CHAT_GET_SIGN(param)
    local msg = chat_pb.TMSG_CHAT_GET_SIGN_REQ()
    msg.signType = chat_pb.enSign_Translate
    msg.context = string.format("%s%s%s%s%s%s%s%s%s%s%s",
            param.gameId,
            param.userId,
            param.actorId,
            param.deviceId,
            param.msgId,
            param.q,
            param.source,
            param.target,
            param.profanity,
            param.textType,
            param.ext
    )
    msg.msgid = param.msgId
    net.SendMessage(net.Endpoint_Client, net.Endpoint_AccountLogin, 0, msg_pb.MSG_CHAT_GET_SIGN_REQ, msg)
end

--聊天请求签名回复
function Recv_MSG_CHAT_GET_SIGN(msg)
    if msg.err ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.err))
    else
        local chatMsgParam = cacheStrArr[msg.msgid]
        if chatMsgParam then
            cacheStrArr[msg.msgid] = nil

            chatMsgParam.time = msg.time1
            chatMsgParam.sign = msg.sign1

            local rawStr = chatMsgParam.rawStr
            chatMsgParam.rawStr = nil

            local json = require "dkjson"
            local url_mgr = require "url_mgr"
            local url = url_mgr.SelectUrlByEnvir(url_mgr.TRANSLATE_URL)
            local json_str = json.encode(chatMsgParam)

            http_inst.HttpPostJson(url, json_str, function(str)
                local data = json.decode(str) or {}
                ----                print(str)
                ----                print(data.data)
                if data.code == 1 and data.data then
                    data.data = string.gsub(data.data, "%[@[^%s%]]+%]", function(name)
                        --print("subbbbbbbbbbbbb>>>",name,string.sub(name,2,string.len(name)-1))
                        return string.sub(name, 2, string.len(name) - 1)
                    end)
                    ------print(data.data)
                    chat_mgr_new.SetTranslation(rawStr, data.data, chatMsgParam.target)
                else
                    ----                    print("Req_TRANSLATE::error msg=",data.code,data.message)
                end
            end)

        end
    end

end

--请求翻译
function Req_TRANSLATE(sStr)
    if string.IsNullOrEmpty(sStr) then
        log.Warning("Req_TRANSLATE::str is nil")
        return
    end
    local r_str = sStr
    sStr = string.gsub(sStr, "@[^%s]+", function(name)
        return string.format("[%s]", name)
    end)

    cacheStrArr = cacheStrArr or {}
    local userId, actorId, serverId, languageId, langIso = GetRoleInfo()
    local ui_setting_data = require "ui_setting_data"
    local sysLangKey = ui_setting_data.GetPhoneLanguageEx()
    local sysLangIso = ui_setting_cfg.LangMap[sysLangKey].iso
    local q1sdk = require "q1sdk"
    local msgId = Utility.Md5(sStr)
    local const = require "const"
    local len= string.len(r_str)--兼容长文本翻译
    cacheStrArr[msgId] = {
        gameId = const.GAMEID,
        userId = tostring(userId),
        actorId = tostring(actorId),
        deviceId = tostring(q1sdk.GetUUID()), --这里用uuid来代替设备id
        msgId = msgId,
        q = sStr,
        source = "en", --要翻译的文本的国际语言编码,接口会自动识别，如果识别不出才会用这个，这里默认填入英文
        target = langIso, --游戏设置语言
        --target = sysLangIso, --手机系统语言
        profanity = "off", --敏感词过滤 可选 默认off
        textType =len>1024 and "mail" or "chat", --文本类型 可选 默认chat
        ext = "", --扩展信息 可选
        time = nil, --时间戳 服务器回复时填充
        sign = nil, --签名 服务器回复时填充

        rawStr = r_str
    }
    
    Req_MSG_CHAT_GET_SIGN(cacheStrArr[msgId]) --请求签名
end

--///GM会话状态---
function Recv_TMSG_CHAT_GM_CLOSE_CONVERSATION_NTF(msg)
    ----    print("Recv_TMSG_CHAT_GM_CLOSE_CONVERSATION_NTF>>>>>>>>>>>>>>>.",msg.bClose)
    chat_mgr_new.SetGMState(msg.bClose == false) --bClose等于fasle是开启 true是关闭
end

--联盟互助消息通知
function S2C_PLEAGUEHELP_INFO_NTF(msg)
    if msg and msg.helpInfo then
        chat_mgr_new.SetLeagueHelpInfo(msg.helpInfo)
        event.Trigger(event.PLEAGUEHELP_INFO_CHANGE)
    end
end

--联盟红包消息通知
function S2C_REDPACKET_INFO_NTF(msg)
    if msg and msg.redPacketInfo then
        chat_mgr_new.SetLeagueRedPacketInfo(msg.redPacketInfo)
    end
end

function S2C_PLEAGUEGELP_REWARDINFO_NTF(msg)
    if msg then
        chat_mgr_new.SetTodayGetRewardTimes(msg.todayGetRewardTimes)
        chat_mgr_new.SetCurWeekHelpTimes(msg.curWeekHelpTimes)
        chat_mgr_new.SetNextReqTimes(msg.nextReqTimes)
        chat_mgr_new.SetTodayHelpTimes(msg.todayHelpTimes)
        event.Trigger(event.PLEAGUEGELP_REWARDINFO_CHANGE)
    end
end

function Send_PLEAGUEHELP_RECIEVEAWARD(flagID)
    local msg = leaguehelp_pb.TMSG_PLEAGUEHELP_RECIEVEAWARD_REQ()
    msg.flagID = flagID
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_PLEAGUEHELP_RECIEVEAWARD_REQ, msg)
end

function Recv_PLEAGUEHELP_RECIEVEAWARD(msg)
    if msg.errCode and msg.errCode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errCode))
    else
        local cfg_help = game_scheme:LeagueHelp_0(msg.helpID)
        local reward_mgr = require "reward_mgr"
        local flow_good = require "flow_good"
        local rewardData = {}
        if cfg_help then
            local reward = reward_mgr.GetRewardGoods(cfg_help.seekHelpReward.data[0])
            table.insert(rewardData, reward)
        end
        if rewardData then
            flow_good.Clear()
            flow_good.Add(rewardData)
        end

        if msg.helpList then
            local ui_chat_help_record = require "ui_chat_help_record"
            ui_chat_help_record.SetInputParam(msg.helpList)
            ui_window_mgr:ShowModule("ui_chat_help_record")
        end

        local roleName = tostring(player_mgr.GetRoleName()) or ""
        local sociaty_data = require "sociaty_data"
        local baseData = sociaty_data.GetLeagueData()
        if baseData and baseData.id then
            local json_str = json.encode({
                guild_id = baseData.id,
                guild_name = baseData.strName,
                help_longtime = msg.reqTime,
                reward_ID = cfg_help and cfg_help.seekHelpReward.data[0] or 0,
                repetition_helpnum = msg.reqTimes,
                role_name = roleName
            })
            event.Trigger(event.GAME_EVENT_REPORT, "Unhelp_getreward", json_str)
        end
    end
end
---请求今日抢红包的数量
function Send_ANNIVERSARY_GRAB_DATA_REQ()
    local sociaty_data = require "sociaty_data"
    local baseData = sociaty_data.GetLeagueData()
    if baseData and baseData.id then
        local msg = leaguehelp_pb.TMSG_ANNIVERSARY_GRAB_DATA_REQ()
        msg.leagueid = baseData.id
        net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_ANNIVERSARY_GRAB_DATA_REQ, msg)
    end
end
---今日抢红包数量返回
function Recv_ANNIVERSARY_GRAB_DATA_RSP(msg)
    if msg.errCode and msg.errCode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errCode))
        log.Error("errCode=", msg.errCode)
        return
    end
    event.Trigger(event.ANNIVERSARY_GRAB_DATA_RSP, msg)
end

---请求发送红包
function Send_NEWYEAR_GIVE_REDPACKET_REQ(redEnvelopId)
    local sociaty_data = require "sociaty_data"
    local baseData = sociaty_data.GetLeagueData()
    if baseData and baseData.id then
        local msg = leaguehelp_pb.TMSG_NEWYEAR_GIVE_REDPACKET_REQ()
        msg.leagueid = baseData.id
        msg.redEnvelopId = redEnvelopId
        net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_NEWYEAR_GIVE_REDPACKET_REQ, msg)
    end
end

---发包返回
function Recv_NEWYEAR_GIVE_REDPACKET_RSP(msg)
    if msg.errCode and msg.errCode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errCode))
        return
    end
    -- 埋点
    local sociaty_data = require "sociaty_data"
    local baseData = sociaty_data.GetLeagueData()
    if baseData and baseData.id then
        local eventData = {
            guild_id = baseData.id, --联盟id
            id = msg.redEnvelopId--红包档次id
        }
        event.EventReport("Hand_out_red_envelope", eventData)
    end

    event.Trigger(event.CHINESE_NEWYEAR_GIVE_REDPACKET_RSP, msg)
end

---请求赠送福卡
---@param targetID number 赠送对象id
---@param worldId number 赠送对象worldId
---@param fuCardId number 福卡id
function Send_NEWYEAR_SEND_FUCARD_REQ(targetID, worldId, fuCardId)
    local msg = leaguehelp_pb.TMSG_NEWYEAR_SEND_FUCARD_REQ()
    msg.targetID = targetID
    msg.targetWorld = worldId
    msg.fuCardId = fuCardId
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_NEWYEAR_SEND_FUCARD_REQ, msg)
end

---赠送福卡返回
function Recv_NEWYEAR_SEND_FUCARD_RSP(msg)
    local friend_mgr = require "friend_mgr"
    friend_mgr.HandleSendFuCardResp(msg)
end

---请求红包排行榜
function Send_NEWYEAR_GIVE_REDPACKET_RANK_REQ()
    local msg = leaguehelp_pb.TMSG_NEWYEAR_GIVE_REDPACKET_RANK_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_NEWYEAR_GIVE_REDPACKET_RANK_REQ, msg)
end

---红包排行榜返回
function Recv_NEWYEAR_GIVE_REDPACKET_RANK_RSP(msg)
    if msg.errCode and msg.errCode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errCode))
        return
    end
    event.Trigger(event.CHINESE_NEWYEAR_GIVE_REDPACKET_RANK_RSP, msg)
end

---请求领取红包
---@param sendRedEnvelopeParam SendRedEnvelopeParam 红包参数
function Send_NEWYEAR_GRAB_REDPACKET_REQ(sendRedEnvelopeParam)
    if not sendRedEnvelopeParam then
        log.Error("Send_NEWYEAR_GRAB_REDPACKET_REQ param is null")
        return
    end
    local sociaty_data = require "sociaty_data"
    local baseData = sociaty_data.GetLeagueData()
    if baseData and baseData.id then
        local msg = leaguehelp_pb.TMSG_NEWYEAR_GRAB_REDPACKET_REQ()
        msg.leagueid = baseData.id
        msg.roleID = sendRedEnvelopeParam.roldId
        msg.redPacketId = sendRedEnvelopeParam.redEnvelopeId
        net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_NEWYEAR_GRAB_REDPACKET_REQ, msg)
    end
end
---抢红包返回
function Recv_NEWYEAR_GRAB_REDPACKET_RSP(msg)
    print("msg.errCode", msg.errCode)
    if msg.errCode and msg.errCode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errCode))
        if NeedRemoveRedPacketInfo(msg.errCode) then
            --已经过期,已经抢光需要直接删除红包
            chat_mgr_new.RemoveRedPacketInfoById(msg.redPacketId)
            chat_mgr_new.TriggerChatEvent(chat_mgr_new.ENUM_CHANNEL.GUIDE)
            event.Trigger(event.CHINESE_NEWYEAR_REDPACKET_REFRESH_RED)
        end
        ui_window_mgr:UnloadModule("ui_chinese_red_envelope_open")
        -- ui_window_mgr:UnloadModule("ui_anniversary_red_package_open")
        return
    end
    local redPacketInfo = chat_mgr_new.GetRedPacketInfoById(msg.redPacketId)
    local redEnvelopeId = 0
    if redPacketInfo then
        redPacketInfo.hadGet = 1
        redEnvelopeId = redPacketInfo.redEnvelopId
    end
    -- 埋点
    local eventData = {
        reward_ID = msg.rewardId, --奖励id
        add_num = msg.redOrder, --第几个抢的红包
        userid = msg.roleID, --红包所属用户ID
        id = redEnvelopeId, --红包档次id
    }
    event.EventReport("Grab_a_red_envelope", eventData)

    event.Trigger(event.CHINESE_NEWYEAR_RED_ENVELOPE_GET_REWARD, msg)
    event.Trigger(event.CHINESE_NEWYEAR_REDPACKET_REFRESH_RED)
end

---请求红包详情
---@param sendRedEnvelopeParam SendRedEnvelopeParam 红包参数
function Send_NEWYEAR_REDPACKET_DETAIL_REQ(sendRedEnvelopeParam)
    if not sendRedEnvelopeParam then
        log.Error("Send_NEWYEAR_REDPACKET_DETAIL_REQ param is null")
        return
    end
    local msg = leaguehelp_pb.TMSG_NEWYEAR_REDPACKET_DETAIL_REQ()
    msg.roleID = sendRedEnvelopeParam.roldId
    msg.redPacketId = sendRedEnvelopeParam.redEnvelopeId
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_NEWYEAR_REDPACKET_DETAIL_REQ, msg)
end

---红包详情返回
function Recv_NEWYEAR_REDPACKET_DETAIL_RSP(msg)
    if msg.errCode and msg.errCode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errCode))
        if NeedRemoveRedPacketInfo(msg.errCode) then
            --已经过期,已经抢光需要直接删除红包
            chat_mgr_new.RemoveRedPacketInfoById(msg.redPacketId)
            chat_mgr_new.TriggerChatEvent(chat_mgr_new.ENUM_CHANNEL.GUIDE)
            event.Trigger(event.CHINESE_NEWYEAR_REDPACKET_REFRESH_RED)
        end
        ui_window_mgr:UnloadModule("ui_chinese_red_envelope_rob_detail")
        -- ui_window_mgr:UnloadModule("ui_anniversary_red_package_rob_detail")
        return
    end
    event.Trigger(event.CHINESE_NEWYEAR_REDPACKET_DETAIL_RSP, msg)
end

---是否需要移除红包信息
---@param code number 错误码
function NeedRemoveRedPacketInfo(code)
    if code == error_code_pb.enErr_NewYearAct_Redpacket_Expire or
            code == error_code_pb.enErr_NewYearAct_Redpacket_Numbers_Over or
            code == error_code_pb.enErr_NewYearAct_Redpacket_Expire_Over or
            code == error_code_pb.enErr_NewYearAct_Redpacket_Garb_Over then
        --已经过期,已经抢光需要直接删除红包
        return true
    end
    return false
end

--求助奖励
---@param helpID number 帮助id
function Send_PLEAGUEHELP_SEEKHELP(helpID)
    local msg = leaguehelp_pb.TMSG_PLEAGUEHELP_SEEKHELP_REQ()
    -- msg.nRoleID = roleID
    --msg.helpID = helpID
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_PLEAGUEHELP_SEEKHELP_REQ, msg)
    --打点
    local roleName = tostring(player_mgr.GetRoleName()) or ""
    local sociaty_data = require "sociaty_data"
    local baseData = sociaty_data.GetLeagueData()
    if baseData and baseData.id then
        local json_str = json.encode({
            guild_id = baseData.id,
            guild_name = baseData.strName,
            role_name = roleName,
        })
        event.Trigger(event.GAME_EVENT_REPORT, "Unhelp_clickeasktoo", json_str)
    end
end

function Recv_PLEAGUEHELP_SEEKHELP(msg)
    if msg.errCode and msg.errCode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errCode))
    end
end

--选择求助奖励
function Send_PLEAGUEHELP_SEL_SEEKHELP(helpID)
    local msg = leaguehelp_pb.TMSG_PLEAGUEHELP_SEL_SEEKHELP_REQ()
    -- msg.nRoleID = roleID
    msg.helpID = helpID
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_PLEAGUEHELP_SEL_SEEKHELP_REQ, msg)
    if helpID ~= 0 then
        --打点
        local roleName = tostring(player_mgr.GetRoleName()) or ""
        local sociaty_data = require "sociaty_data"
        local baseData = sociaty_data.GetLeagueData()
        local cfg = game_scheme:LeagueHelp_0(helpID)
        if baseData and baseData.id then
            local json_str = json.encode({
                guild_id = baseData.id,
                guild_name = baseData.strName,
                role_name = roleName,
                reward_ID = cfg and cfg.seekHelpReward and cfg.seekHelpReward.data[0] or 0
            })
            event.Trigger(event.GAME_EVENT_REPORT, "Unhelp_clickeask", json_str)
        end
    end
end

function Recv_PLEAGUEHELP_SEL_SEEKHELP(msg)
    if msg.errCode and msg.errCode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errCode))
    else
        Send_PLEAGUEHELP_SEEKHELP(0)
    end
end

function Send_PLEAGUEHELP_AIDALLIES(roleID)
    local msg = leaguehelp_pb.TMSG_PLEAGUEHELP_AIDALLIES_REQ()
    msg.nRoleID = roleID
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_PLEAGUEHELP_AIDALLIES_REQ, msg)
end

function Recv_PLEAGUEHELP_AIDALLIES(msg)
    if msg.errCode and msg.errCode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errCode))
    else
        local totalNum = game_scheme:InitBattleProp_0(365).szParam.data[0] or 0
        local dayNum = game_scheme:InitBattleProp_0(1455).szParam.data[0] or 0
        local curWeekHelpRewardTimes = msg.curWeekHelpRewardTimes or 0
        local todayHelpTimes = msg.todayHelpTimes or 0
        if todayHelpTimes <= dayNum and curWeekHelpRewardTimes < totalNum then
            local cfg_help = game_scheme:LeagueHelp_0(msg.helpID)
            local reward_mgr = require "reward_mgr"
            local flow_good = require "flow_good"
            local rewardData = {}
            if cfg_help then
                local reward = reward_mgr.GetRewardGoods(cfg_help.aidReward.data[0])
                table.insert(rewardData, reward)
            end
            if rewardData then
                flow_good.Clear()
                flow_good.Add(rewardData)
            end
        else
            flow_text.Add(lang.Get(16774))
        end

        event.Trigger(event.PLEAGUEGELP_HELP_ROLE_ID, msg.beHelpRoleID)
    end
end

function Recv_CHAT_LEAVE_LEAGUE_NTF()
    chat_mgr_new.ClearGuildMsg()
end

--玩家请求联盟互助奖池 -- 请求
function C2S_PLEAGUEHELP_SEEKHELP_POOL_REQ()
    local msg = leaguehelp_pb.TMSG_PLEAGUEHELP_SEEKHELP_POOL_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_PLEAGUEHELP_SEEKHELP_POOL_REQ, msg)
end

function S2S_PLEAGUEHELP_SEEKHELP_RSP(msg)
    if msg.errCode and msg.errCode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errCode))
    else
        local help_reward_choose_data = require "help_reward_choose_data"
        help_reward_choose_data.SetRewardList(msg)
    end
end

--角色改名字通知（目前只有玩家自己名字修改用到）
-- local newNameCach = {}
function S2S_USER_UPDATE_NAME_RSP(msg)
    if msg and msg.errcode and msg.errcode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errcode))
    else
        -- newNameCach[msg.roleid] = msg.worldid
        --Req_LANGUAGE_CHANNEL_MSG()
        local playerProp = player_mgr.GetPlayerProp()
        event.Trigger(event.PLAYER_NAME_UPDATE, msg.roleid, playerProp.roleName)
        print(396, "角色改名通知 >>>>>", msg.roleid, "修改后名字：", playerProp.roleName)
    end
end

--region 新请求重置语言频道数据
--请求语言频道数据
function C2S_TMSG_CHAT_LANGUAGE_MSG_REQ_Handler(langId)
    local msg = chat_pb.TMSG_CHAT_LANGUAGE_MSG_REQ()
    msg.languageType=langId
    net.SendMessage(net.Endpoint_Client, net.Endpoint_AccountLogin, 0, msg_pb.MSG_CHAT_LANGUAGE_MSG_REQ, msg)
end

--请求语言频道数据返回
function S2C_TMSG_CHAT_LANGUAGE_MSG_RSP_Handler(msg)
    if msg.errorCode~=0 then
        flow_text.Add(lang.Get(100000 + msg.errcode))
        return
    end
    chat_mgr_new.ResetLangChannelMsg(msg)
end
--endregion

-- /// 注册消杯
-------------------------------------------------------------------
local MessageTable = {
    { msg_pb.MSG_PLEAGUEHELP_SEEKHELP_RSP, Recv_PLEAGUEHELP_SEEKHELP, leaguehelp_pb.TMSG_PLEAGUEHELP_SEEKHELP_RSP },
    { msg_pb.MSG_PLEAGUEHELP_SEL_SEEKHELP_RSP, Recv_PLEAGUEHELP_SEL_SEEKHELP, leaguehelp_pb.TMSG_PLEAGUEHELP_SEL_SEEKHELP_RSP },
    { msg_pb.MSG_CHAT_MSG_NTF, Recv_CHAT_MSG, chat_pb.TMSG_CHAT_MSG_NTF },
    { msg_pb.MSG_CHAT_NEW_MSG_NTF, Recv_CHAT_NEW_MSG, chat_pb.TMSG_CHAT_NEW_MSG_NTF },
    { msg_pb.MSG_CHAT_SPEAK_RSP, Recv_CHAT_SPEAK, chat_pb.TMSG_CHAT_SPEAK_RSP },
    { msg_pb.MSG_CHAT_BLOCK_CHANNEL_RSP, Recv_CHAT_BLOCK_CHANNEL, chat_pb.TMSG_CHAT_BLOCK_CHANNEL_RSP },
    {msg_pb.MSG_CHAT_BLOCK_RSP, Recv_CHAT_BLOCK, chat_pb.TMSG_CHAT_BLOCK_RSP},
    {msg_pb.MSG_CHAT_BLOCK_LIST_RSP, Recv_CHAT_BLOCK_LIST, chat_pb.TMSG_CHAT_BLOCK_LIST_RSP},
    {msg_pb.MSG_CHAT_BLOCK_LIST_NTF, Recv_CHAT_BLOCK_LIST_NTF, chat_pb.TMSG_CHAT_BLOCK_LIST_NTF},
    { msg_pb.MSG_CHAT_BAN_NTF, Recv_CHAT_BAN, chat_pb.TMSG_CHAT_BAN_NTF },
    { msg_pb.MSG_CHAT_BAN_REMOVE_NTF, Recv_CHAT_BAN_REMOVE, chat_pb.TMSG_CHAT_BAN_REMOVE_NTF },
    { msg_pb.MSG_CHAT_RECALL_MSG_RSP, Recv_CHAT_RECALL_MSG_RSP, chat_pb.TMSG_CHAT_RECALL_MSG_RSP },

    { msg_pb.MSG_PRIVATE_MSG_NTF, Recv_PRIVATE_MSG_NTF, chat_pb.TMSG_PRIVATE_MSG_NTF },
    { msg_pb.MSG_PRIVATE_MSG_SET_NTF, Recv_PRIVATE_MSG_SET_NTF, chat_pb.TMSG_PRIVATE_MSG_SET_NTF },
    { msg_pb.MSG_PRIVATE_MSG_SPEAK_RSP, Recv_PRIVATE_MSG_SPEAK_RSP, chat_pb.TMSG_PRIVATE_MSG_SPEAK_RSP },
    {msg_pb.MSG_CHAT_LANGUAGE_MSG_RSP,S2C_TMSG_CHAT_LANGUAGE_MSG_RSP_Handler,chat_pb.TMSG_CHAT_LANGUAGE_MSG_RSP},

    { msg_pb.MSG_ROLE_LOGIN_KEY_NTF, Recv_TMSG_ROLE_LOGIN_KEY_NTF, prop_pb.TMSG_ROLE_LOGIN_KEY_NTF },
    { msg_pb.MSG_CHAT_LANGUAGE_CHANNEL_SPEAK_RSP, Recv_CHAT_LANGUAGE_CHANNEL_SPEAK, chat_pb.TMSG_CHAT_LANGUAGE_CHANNEL_SPEAK_RSP },
    { msg_pb.MSG_CHAT_LANGUAGE_CHANNEL_MSG_NTF, Recv_CHAT_LANGUAGE_CHANNEL_MSG, chat_pb.TMSG_CHAT_LANGUAGE_CHANNEL_MSG_NTF },
    { msg_pb.MSG_CROSS_SVR_CHAT_PRIVATE_SPEAK_RSP, Recv_CROSS_SVR_CHAT_PRIVATE_SPEAK, chat_pb.TMSG_CROSS_SVR_CHAT_PRIVATE_SPEAK_RSP },
    { msg_pb.MSG_CROSS_SVR_CHAT_PRIVATE_MSG_NTF, Recv_CROSS_SVR_CHAT_PRIVATE_MSG, chat_pb.TMSG_CROSS_SVR_CHAT_PRIVATE_MSG_NTF },
    { msg_pb.MSG_CHAT_REPORT_CHANNEL_RSP, Recv_CHAT_REPORT_CHANNEL_RSP, chat_pb.TMSG_CHAT_REPORT_CHANNEL_RSP },

    { msg_pb.MSG_CHAT_GET_SIGN_RSP, Recv_MSG_CHAT_GET_SIGN, chat_pb.TMSG_CHAT_GET_SIGN_RSP },
    { msg_pb.MSG_CHAT_GM_CLOSE_CONVERSATION_NTF, Recv_TMSG_CHAT_GM_CLOSE_CONVERSATION_NTF, chat_pb.TMSG_CHAT_GM_CLOSE_CONVERSATION_NTF },

    { msg_pb.MSG_PLEAGUEHELP_INFO_NTF, S2C_PLEAGUEHELP_INFO_NTF, leaguehelp_pb.TMSG_PLEAGUEHELP_INFO_NTF },
    { msg_pb.MSG_PLEAGUEGELP_REWARDINFO_NTF, S2C_PLEAGUEGELP_REWARDINFO_NTF, leaguehelp_pb.TMSG_PLEAGUEGELP_REWARDINFO_NTF },
    { msg_pb.MSG_PLEAGUEHELP_RECIEVEAWARD_RSP, Recv_PLEAGUEHELP_RECIEVEAWARD, leaguehelp_pb.TMSG_PLEAGUEHELP_RECIEVEAWARD_RSP },
    { msg_pb.MSG_PLEAGUEHELP_AIDALLIES_RSP, Recv_PLEAGUEHELP_AIDALLIES, leaguehelp_pb.TMSG_PLEAGUEHELP_AIDALLIES_RSP },
    { msg_pb.MSG_CHAT_LEAVE_LEAGUE_NTF, Recv_CHAT_LEAVE_LEAGUE_NTF, chat_pb.TMSG_CHAT_LEAVE_LEAGUE_NTF },
    { msg_pb.MSG_PLEAGUEHELP_SEEKHELP_POOL_RSP, S2S_PLEAGUEHELP_SEEKHELP_RSP, leaguehelp_pb.TMSG_PLEAGUEHELP_SEEKHELP_POOL_RSP },
    { msg_pb.MSG_USER_UPDATE_NAME_RSP, S2S_USER_UPDATE_NAME_RSP, platform_pb.TMSG_USER_UPDATE_NAME_RSP },

    { msg_pb.MSG_REDPACKET_INFO_NTF, S2C_REDPACKET_INFO_NTF, leaguehelp_pb.TMSG_REDPACKET_INFO_NTF },
    { msg_pb.MSG_NEWYEAR_GRAB_REDPACKET_RSP, Recv_NEWYEAR_GRAB_REDPACKET_RSP, leaguehelp_pb.TMSG_NEWYEAR_GRAB_REDPACKET_RSP },
    { msg_pb.MSG_NEWYEAR_REDPACKET_DETAIL_RSP, Recv_NEWYEAR_REDPACKET_DETAIL_RSP, leaguehelp_pb.TMSG_NEWYEAR_REDPACKET_DETAIL_RSP },
    { msg_pb.MSG_NEWYEAR_GIVE_REDPACKET_RSP, Recv_NEWYEAR_GIVE_REDPACKET_RSP, leaguehelp_pb.TMSG_NEWYEAR_GIVE_REDPACKET_RSP },
    { msg_pb.MSG_NEWYEAR_SEND_FUCARD_RSP, Recv_NEWYEAR_SEND_FUCARD_RSP, leaguehelp_pb.TMSG_NEWYEAR_SEND_FUCARD_RSP },
    { msg_pb.MSG_NEWYEAR_GIVE_REDPACKET_RANK_RSP, Recv_NEWYEAR_GIVE_REDPACKET_RANK_RSP, leaguehelp_pb.TMSG_NEWYEAR_GIVE_REDPACKET_RANK_RSP },
    { msg_pb.MSG_ANNIVERSARY_GRAB_DATA_RSP, Recv_ANNIVERSARY_GRAB_DATA_RSP, leaguehelp_pb.TMSG_ANNIVERSARY_GRAB_DATA_RSP }
}

net_route.RegisterMsgHandlers(MessageTable)

function OnSCENE_DESTROY()
    loginKey = nil
    loginTime = nil
    loginWorldID = nil
    ResetReportChannelTimer()
end


--登录成功后延时请求跨服聊天信息（语言和私聊频道）
function DelayReqChatData()
    local startTime = os.time()
    local delayReqChatData = function()
        --print("delayReqChatData延迟请求聊天信息", os.time() - startTime)
        chat_mgr_new.ReqChatDataAfterLogin()
    end

    util.DelayCallOnce(1, delayReqChatData)
end

event.Register(event.SCENE_DESTROY, OnSCENE_DESTROY)
event.Register(event.ACCOUNT_CHANGE_WORLD_RSP, OnSCENE_DESTROY)

