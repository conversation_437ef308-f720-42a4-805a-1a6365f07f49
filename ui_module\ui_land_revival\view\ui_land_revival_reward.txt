local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil

local ui_window_mgr = require "ui_window_mgr"
local time_util = require "time_util"
local os = require "os"
local iui_item_detail = require "iui_item_detail"
local item_data = require "item_data"
local goods_item_new = require "goods_item_new"
local reward_mgr = require "reward_mgr"
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_land_revival_reward_binding"

--region View Life
module("ui_land_revival_reward")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
    self.goodsEffectPath = "art/effects/effects/effect_ui_payreward/prefabs/effect_ui_payreward.prefab"
    self:InitScrollTable()
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil
    if self.endTimer then
        util.RemoveDelayCall(self.endTimer)
        self.endTimer = nil
    end
    if self.srt_RewardContent then
        self.srt_RewardContent:ItemsDispose()
    end
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

function UIView:InitScrollTable()
    self.srt_RewardContent.onItemRender = function(...)
        self:TaskRewardRender(...)
    end
    self.srt_RewardContent.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data then
            if scroll_rect_item.data.normalGoodsItem then
                scroll_rect_item.data.normalGoodsItem:Dispose()
                scroll_rect_item.data.normalGoodsItem = nil
            end
            if scroll_rect_item.data.vipGoodsItem then
                scroll_rect_item.data.vipGoodsItem:Dispose()
                scroll_rect_item.data.vipGoodsItem = nil
            end
        end
    end
end

function UIView:TaskRewardRender(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or { index, dataItem }
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    
    local sldProgress = scroll_rect_item:Get("sldProgress")
    local normalParent = scroll_rect_item:Get("normalParent")
    local vipParent = scroll_rect_item:Get("vipParent")
    local vipText = scroll_rect_item:Get("vipText")
    local upVipBtn = scroll_rect_item:Get("upVipBtn")
    local numText = scroll_rect_item:Get("numText")
    
    local normalData = dataItem.normalData
    numText.text = normalData.targetRate
    vipText.text = string.format("%s%s",lang.Get(1008714), dataItem.vipLevel)
    self:SetActive(sldProgress,not dataItem.isLastTask)
    sldProgress.value = dataItem.isFinish and 1 or 0
    
    local normalRewardID = dataItem.normalData.rewardID
    local vipRewardID = dataItem.vipData.rewardID

    local normalRewardData = reward_mgr.GetRewardGoodsList(normalRewardID)
    local vipRewardData = reward_mgr.GetRewardGoodsList(vipRewardID)
    
    local goodItem = scroll_rect_item.data.normalGoodsItem or goods_item_new.CGoodsItem():Init(normalParent.transform, nil, 0.675)
    goodItem:SetGoods(nil, normalRewardData[1].id, normalRewardData[1].num, function()
        iui_item_detail.Show(normalRewardData[1].id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, normalRewardData[1].num, nil, nil)
    end)
    
    local vipGoodItem = scroll_rect_item.data.vipGoodsItem or goods_item_new.CGoodsItem():Init(vipParent.transform, nil, 0.675)
    vipGoodItem:SetGoods(nil, vipRewardData[1].id, vipRewardData[1].num, function()
        iui_item_detail.Show(vipRewardData[1].id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, vipRewardData[1].num, nil, nil)
    end)
 
    vipGoodItem:SetCustomEffectEnable(true, self.goodsEffectPath, 1.5, self.curOrder + 3, false, nil, true)
    
    scroll_rect_item.data.normalGoodsItem = goodItem
    scroll_rect_item.data.vipGoodsItem = vipGoodItem
    
    self:AddOnClick(upVipBtn.onClick, function()
        ui_window_mgr:ShowModule("ui_vip_main_new")
    end)
    
    if dataItem.isLockVip then
        vipGoodItem:SetLockMaskEnable(true,true)
    else
        vipGoodItem:SetLockMaskEnable(false,false)
    end
    
    if dataItem.normalData.status then
        --已领取
        goodItem:SetBattleMaskEnable(true)
        goodItem:SetRedDotEnable(false)
    else
        if dataItem.normalData.rate >= dataItem.normalData.targetRate then
            --可领取
            goodItem:SetRedDotEnable(true)
        else
            --未完成
            goodItem:SetRedDotEnable(false)
        end
    end
    if dataItem.vipData.status then
        --已领取
        vipGoodItem:SetBattleMaskEnable(true)
        vipGoodItem:SetRedDotEnable(false)
        vipGoodItem:SetCustomEffectEnable(false)
    else
        if dataItem.vipData.rate >= dataItem.vipData.targetRate then
            if not dataItem.isLockVip then
                --可领取
                vipGoodItem:SetRedDotEnable(true)
            end
        else
            --未完成
            vipGoodItem:SetRedDotEnable(false)
        end
    end
    self:SetActive(upVipBtn, dataItem.normalData.status and not dataItem.vipData.status) --未领取普通奖励时不显示按钮
end

---@public function 刷新奖励列表
function UIView:RefreshRewardList(data)
    if not data then
        return
    end
    local len = #data
    self.srt_RewardContent:SetData(data, len)
    self.srt_RewardContent:Refresh(0, -1)
end

---@public function 设置第一个任务进度
function UIView:SetFirstTaskProgress(value)
    self.sld_FirstProgress.value = value or 0
end

---@public function 设置vip等级
function UIView:SetVipLevelShow(level)
    self.txt_vipLv.text = level
end

---@public function 设置活动结束时间
function UIView:SetActivityTimer(endTime)
    if endTime then
        self.endTimer = self.endTimer or util.IntervalCall(1, function()
            local tempTime = endTime - os.server_time()
            if not util.IsObjNull(self.txt_time) then
                self.txt_time.text = time_util.FormatTime5(tempTime)
            end
            if tempTime <= 0 then
                if not util.IsObjNull(self.txt_time) then
                    self.txt_time.text = lang.Get(102401) --"活动已结束"
                end
                self.endTimer = nil
                return true
            end
        end)
    end
end

---@public function 设置领取按钮状态
function UIView:SetRewardBtnState(isUnFinishTask,isCanReceiveTask)
    if isUnFinishTask then
        self:SetActive(self.btn_receive,isCanReceiveTask)
        self:SetActive(self.btn_goTo,not isCanReceiveTask)
    else
        self:SetActive(self.btn_receive,false)
        self:SetActive(self.btn_goTo,false)
    end
end

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
		window.isBlurBg = true

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, true, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil, true, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
