local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local tonumber = tonumber
local type = type
local log = log
local table_util = require "table_util"
local event_activity_define = require "event_activity_define"
local net_activity_module = require "net_activity_module"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"
local string_util = require "string_util"
local player_mgr =  require "player_mgr"
local alliance_mgr = require "alliance_mgr"
local data_personalInfo = require "data_personalInfo"
local custom_avatar_data = require"custom_avatar_data"
local event = require "event"
--region Controller Life
module("ui_common_damage_rank_controller")
local controller = nil
local UIController = newClass("ui_common_damage_rank_controller", controller_base)

function UIController:Init(view_name, controller_name, data)    
    self.__base.Init(self, view_name, controller_name)
    self.CData = {}
    if data then
        self.CData.selfRankId = data.selfRankId
        self.CData.rankType = data.rankType
        self.CData.rewardtype = data.rewardtype
        self.CData.subtype = data.subtype
    end
    --请求排名信息
    net_activity_module.MSG_COMM_ACTIVITY_RANK_REQ(data.rankType)
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close()   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.CData = nil
    self.__base.Close(self)
end

function UIController:AutoSubscribeEvents()
    self.onCommonActivityRankRsp = function(_,msg)
        if not msg then 
            return 
        end
       --监听倒排行版信息
        self.CData.rankInfos = msg.rankInfo
        local datas =  self:CustomRankInfoList()
        if  self.CData.rankInfos then 
            self:TriggerUIEvent("UpdateScrollList",datas,self.CData.rankInfos and #self.CData.rankInfos or 0) 
        end
        --获取自己的排名
        self.CData.selfRankId = -1
        for i, v in ipairs(msg.rankInfo) do
            if v.dbid == player_mgr.GetPlayerRoleID() then
                self.CData.selfRankId = i
                break
            end
        end        
        --设置自己的信息      
        self:SetSelfInfo(msg.selfRank)
    end
    self:RegisterEvent(event_activity_define.TMSG_COMM_ACTIVITY_RANK_RSP,self.onCommonActivityRankRsp)
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic
---@public 设置自己的信息，主要有时候自己没有上榜
---@param selfRankInfo table 服务器下发的自己的信息
function UIController:SetSelfInfo(selfRankInfo)
    if not selfRankInfo then
        return
    end
    local playerEntity = player_mgr.GetPlayerEntity()
    if not playerEntity then
        return
    end
    local setting_server_data = require "setting_server_data"
    local r_worldID = setting_server_data.GetLoginWorldID() or 0    
    --获取联盟相关
    self.CData.selfInfo = 
    {
        rank = selfRankInfo.rank;            	--排名
        dbid = selfRankInfo.dbid;            	--角色id playerEntity.roleID
        score = selfRankInfo.score;           	--分数
        roleLv = playerEntity.playerProp.lv;		 		--角色等级
        faceID = playerEntity.playerProp.faceID;				--头像ID
        frameID = playerEntity.playerProp.frameID; 			--头像框
        --sex = playerEntity.playerProp.;				--角色性别
        sex = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleSex);
        name = playerEntity.playerProp.roleName;				--玩家名字
        worldId = r_worldID;			--区服id
        leagueid = alliance_mgr.GetUserAllianceId();			--联盟ID
        leagueFlag = alliance_mgr.GetUserAllianceFlag();		--联盟旗帜
        leagueName = alliance_mgr.GetUserAllianceName();		--联盟名字
        leagueShortName	= alliance_mgr.GetUserAllianceShortName();	--联盟简称
        --extradata = "";			--排行榜额外数据
    }
    local rankContext = lang.Get(654043).." " ..string_util.ToScientificNotation(selfRankInfo.score)
    --检测有没有上榜  todo -- 检测自己是否在排行榜应该通过判断自己的排名是否高于最大的排名数
    local inRank = false
    if self.CData.rankInfos then
        for i, v in ipairs(self.CData.rankInfos) do
            if v.dbid == selfRankInfo.dbid then
                inRank = true
                break
            end
        end        
    end
    local info = {
        isSelf = true,
        rankInfo = self.CData.selfInfo,
        isInRank = inRank,
        detailBtnFunc = nil,
        rankContext = rankContext,
    }
    self:TriggerUIEvent("SetSelfInfo",info,self.CData.rankInfos ~= nil and #self.CData.rankInfos > 0)
end
---@public 定义排行版数据获取方式
function UIController:CustomRankInfoList(data_table)
    if not data_table then
        data_table = {}
    end
    function GetItem(t,k)
        --这里去实现自己对应的数据获取方式和来源      
        local curInfo = self.CData.rankInfos[k]
        if not curInfo then
            log.Error("getItem",k,"Error  not exist")
            return nil
        end          
        local rankContext = lang.Get(654043).." " ..string_util.ToScientificNotation(curInfo.score)
        local info = {
            isSelf = self.CData.selfInfo and self.CData.selfInfo.dbid == curInfo.dbid,
            rankInfo = curInfo,
            isInRank = true,
            detailBtnFunc = curInfo.rank <= 3 and OnDetailBtnFunc or nil,
            rankContext = rankContext
        }
        return info
    end
    --注意 只有你想要动态获取item的值时才需要设置，如果list很短，其实没必要，直接全部传过去
    table_util.SetDynamicGetItem(data_table,GetItem)
    return data_table
end


function  UIController:OnBtnCloseBtnClickedProxy()
	ui_window_mgr:UnloadModule(self.view_name)
end
---@public 奖励预览
function  UIController:OnBtnZ_blClickedProxy()
    local activity_pb = require "activity_pb"
    if self.CData.rankType == activity_pb.ACTTYPE_ZOMBIECOMING then
        event.EventReport("UndeadTreasure_ViewRankingReward", {})
    end
    local data = {selfRankId = self.CData.selfRankId,rewardtype = self.CData.rewardtype,subtype = self.CData.subtype}
    ui_window_mgr:ShowModule("ui_common_damage_rank_reward",nil,nil,data)   
end


function OnDetailBtnFunc(index)    
    local curRankInfo = controller.CData.rankInfos[index]
    if not curRankInfo then
        log.Error("OnDetailBtnFunc Error  is nil",index)
        return
    end
    local lineInfo
    local fightPower
    if not string.IsNullOrEmpty(curRankInfo.extradata) then
        lineInfo = {}
        local array = string.split(curRankInfo.extradata,":")
        local len = #array
        if array and len > 0 then
            fightPower = tonumber(array[1])
            if len > 1 then
                array =  string.split(array[2],",")
                for i, v in ipairs(array) do
                    local tempData = string.split(v,"_")
                     if tempData and #tempData == 3 then
                        table.insert(lineInfo,{heroID = tonumber(tempData[1]),heroLevel = tonumber(tempData[2]),heroStar = tonumber(tempData[3])})
                     end
                end
            end
        end        
    end
    local  popData = 
    {
        dbid = curRankInfo.dbid;
        faceId = curRankInfo.faceID;
        frameId = curRankInfo.frameID;
        worldId = curRankInfo.worldId;
        allianceShortName = curRankInfo.leagueShortName;
        playerName = curRankInfo.name;
        roleLv = curRankInfo.roleLv;
        sex = curRankInfo.sex;
        fightPower = fightPower; 
        lineInfo = lineInfo;
    }
    if curRankInfo.faceStr and not string.IsNullOrEmpty(curRankInfo.faceStr) then
        popData.faceId = curRankInfo.faceStr
    end
    if curRankInfo.dbid == player_mgr.GetPlayerRoleID() then
        local customHeadData = custom_avatar_data.GetMyAvatar()
        if customHeadData then
            popData.faceId = customHeadData.remoteUrl
        end
        popData.frameId = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.FrameID)
        popData.sex = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleSex)
        popData.RoleLevel = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleSex)
    end
    ui_window_mgr:ShowModule("ui_line_info_pop_ui_template1",nil,nil,popData)
end
--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
