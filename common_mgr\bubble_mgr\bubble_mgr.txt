--@region FileHead
-- bubble_mgr.txt ---------------------------------
-- author:  罗华冠
-- date:    2022/7/1 19:48:51
-- ver:     1.0
-- desc:    气泡提示弹窗管理器
-------------------------------------------------


--@region Require
local require   = require
local pairs     = pairs
local ipairs    = ipairs
local string    = string

local ui_window_mgr = require "ui_window_mgr"
local game_scheme   = require "game_scheme"
local util          = require "util"
local event         = require "event"

local GameObject 	= CS.UnityEngine.GameObject
local PlayerPrefs   = CS.UnityEngine.PlayerPrefs

module("bubble_mgr")

local isShowBubble = false      -- 当前是否有气泡显示
local curBubbleID = nil         -- 当前显示气泡的配置表id
local bubbleData = {}           -- 气泡的缓存数据
local bubbleCfg = {}
local bubbleWindow = nil        -- 气泡实例
local bubbleKey = nil
local bubbleTriggerData = nil

-- 气泡类型
BUBBLE_TYPE = {
    miniGame = 1,
}

-- 关闭小游戏气泡
function CloseMiniGameBubble()
    if isShowBubble and curBubbleID then
        if not bubbleCfg[curBubbleID] then
            bubbleCfg[curBubbleID] = game_scheme:BubbleTips_0(curBubbleID)
        end
        if bubbleCfg[curBubbleID] and bubbleCfg[curBubbleID].type == BUBBLE_TYPE.miniGame and bubbleWindow then
            bubbleWindow:BubbleCloseAni()
        end
    end
end

function CloseBubble()
    bubbleWindow = nil
    curBubbleID = nil
    isShowBubble = false
    ui_window_mgr:UnloadModule("ui_bubble_tip")
end

function ShowBubble(parent,data)
    if parent then
        local tObj=GameObject.Find(parent)
        if tObj and not util.IsObjNull(tObj) then
            isShowBubble = true
            SetBubbleTime(curBubbleID,1)
            local ui_bubble_tip = require "ui_bubble_tip"
            ui_bubble_tip.SetParent(tObj)       -- 设置挂载节点
            ui_bubble_tip.SetBubbleData(data)   -- 传递bubbleTip表数据
            bubbleWindow = ui_window_mgr:ShowModule("ui_bubble_tip")
        end
    end
end


function GetKey()
    if not bubbleKey then
        local player_mgr = require "player_mgr"
        bubbleKey = string.format("%d_bubbleTip",player_mgr.GetPlayerRoleID())
    end
    return bubbleKey
end

function SetBubbleTime(id,time)
    bubbleTriggerData[id] = time
end

-- 判断气泡是否达到触发上限
function CheckBubbleTimeLimit(id)
    if not bubbleCfg[id] then
        bubbleCfg[id] = game_scheme:BubbleTips_0(id)
    end
    if not bubbleTriggerData then
        ParsingBubbleData()
    end
    if bubbleCfg[id] then
        local curTime = bubbleTriggerData[id] and bubbleTriggerData[id] or 0
        local maxTime = bubbleCfg[id].limit
        return curTime < maxTime
    end
end

function ParsingBubbleData()
   local key = GetKey()
   local str = PlayerPrefs.GetString(key,"")
   if not string.empty(str) then
        local data = string.split(str,';')
        if data then
            for k,v in ipairs(data)do
                if v then
                    local d = string.split('v','#',tonumber)
                    if d then
                        local bubbleID = d[1]
                        local triggerTime = d[2]
                        bubbleTriggerData[bubbleID] = triggerTime
                    end
                end
            end
        end
   else
        bubbleTriggerData = {}
   end
end

function BubbleDataToString()
    local str = ""
    if bubbleTriggerData then
        for k,v in pairs(bubbleTriggerData) do
            local s = k..'#'..v
            if str == "" then
                str = s
            else
                str = str..';'..s
            end
        end
    end
end

function SaveBubbleData()
    local key = GetKey()
    local bubbleStr = BubbleDataToString()
    PlayerPrefs.SetString(key,bubbleStr)
    bubbleTriggerData = nil
end

function OnSceneDestroy()
    SaveBubbleData()
    isShowBubble = nil
    curBubbleID = nil
    if bubbleWindow then
        bubbleWindow:BubbleCloseAni()
        bubbleWindow = nil
    end
end
event.Register(event.SCENE_DESTROY, OnSceneDestroy)
event.Register(event.ACCOUNT_CHANGECHANGE_WORLD_RSP,OnSceneDestroy)