local require = require
local pairs = pairs
local type = type
local tostring = tostring
local tonumber = tonumber
local table = table
local string = string
local print = print
local ipairs = ipairs
local math = math
local loadstring = loadstring
local game_scheme = require "game_scheme"
local select = select
local unpack = unpack or table.unpack  -- 兼容 Lua 5.1 和 Lua 5.2+

local json = require "dkjson"
local lang = require "lang"
local log = require "log"
local const = require "const"
local Utility = CS.War.Script.Utility

module("string_util")

-- 分割字符串
-- brief : 分割字符串，返回一个字符串数组
-- @param str : 要分割的字符串
-- @param separator : 分割符,可以是一个长度1-n的字符串
-- @param filter    : 过滤器,预处理函数,可以为nil,比如可以传入tonumber,这样就全部转成数字了
-------------------------------------------------------------------
function SplitString(str, separator, filter)
    if str == nil then
        local log = require "log"
        log.Error("要分割的字符串为空,请检查！")
        return nil
    end

    local ret = {}
    local sp_len = string.len(separator)
    local pos = 1 - sp_len
    while true do
        local pre = pos + sp_len
        pos = string.find(str, separator, pre, true)
        if pos == nil then
            table.insert(ret, string.sub(str, pre))
            break
        else
            table.insert(ret, string.sub(str, pre, pos - 1))
        end
    end

    -- 预处理一下
    if filter ~= nil then
        for k, v in ipairs(ret) do
            ret[k] = filter(v)
        end
    end

    return ret
end

-- 判断是否以指定字符串开头
function IsStartWith(str, prefix)
    if not str or not prefix then
        return false        
    end 
    return string.sub(str, 1, #prefix) == prefix
end

-- 判断是否以指定字符串结尾
function IsEndWith(str, suffix)
    if not str or not suffix then
        return false
    end
    return #str >= #suffix and string.sub(str, -#suffix) == suffix
end

--[[
--限定字符串长度
function LimitString(maxLength, str)
    if Utility.LimitString == nil then
        return str
    end
    return Utility.LimitString(maxLength, str)
end
]]

function LimitString(maxLength, str)
    if not str or type(str) ~= "string" or #str <= 0 then
        return ""
    end
    if #str <= maxLength then
        return str
    end
    local length = 0  -- 字符的个数
    local i = 1
    while true do
        local curByte = string.byte(str, i)
        local byteCount = 1
        if curByte > 239 then
            byteCount = 4  -- 4字节字符
        elseif curByte > 223 then
            byteCount = 3  -- 汉字
        elseif curByte > 128 then
            byteCount = 2  -- 双字节字符
        else
            byteCount = 1  -- 单字节字符
        end
        i = i + byteCount
        if i > maxLength then
            local name = string.sub(str, 1, i - 1)
            name = string.format("%s%s", name, "...")
            --print("name;",name)
            return name
        end
    end
    return str
end

--获取字符串字节长度（中文占两个字节）
function GetStringByteLen(str)
    if Utility.GetStringByteLen == nil then
        return string.len(str)
    end
    return Utility.GetStringByteLen(str)
end

--[[将一个值转换成字符串]]
function ToStringEx(value)
    if type(value) == 'table' then
        return TableToString(value)
    elseif type(value) == 'string' then
        return "\"" .. value .. "\""
    else
        return tostring(value)
    end
end

--[[将一个table转换成一个字符串]]
function TableToString(t)
    local retstr = "{"

    local i = 1
    for key, value in pairs(t) do
        local signal = ","
        if i == 1 then
            signal = ""
        end

        --		if key==i then
        --			-- 不加键值
        --
        --			retstr = retstr..signal..tostringEx(value)
        --		else
        if type(key) == 'number' then
            retstr = retstr .. signal .. '[' .. key .. "]=" .. ToStringEx(value)
        else
            retstr = retstr .. signal .. key .. "=" .. ToStringEx(value)
        end
        --		end

        i = i + 1

    end

    retstr = retstr .. "}"
    return retstr
end

function StringToTableHelper(t)
    return t
end

function StringToTable(str)
    local f = loadstring("return " .. str)
    if f == nil then
        return
    end
    return f()
end

function toJsonString(t, indent, exceptionHandler)
    local json = require "dkjson"
    return json.encode(t, { indent = indent, exception = exceptionHandler })
end

--- 余额展示 18122688 --> 18M  122688--> 122,688
function toBalancesString(number, notSymbol)
    if type(number) ~= "number" then
        return tostring(number)
    end

    local formatted = nil
    local sym = nil
    -- 计算百万部分
    if number >= 1000000 and not notSymbol then
        --  如果大于100M 则不显示小数点
        local millions = number / 1000000
        sym = "M"
        if number >= 100000000 then
            formatted = tostring(math.floor(millions))
        else
            -- 保留一位小数
            formatted = string.format("%.1f", millions)
            return formatted .. sym
        end
    else
        formatted = tostring(number)  -- 小于百万直接转换
    end

    local k = 1
    local times = 10
    while (times > 0) do
        formatted, k = string.gsub(formatted, "^(-?%d+)(%d%d%d)", '%1,%2')
        if (k == 0) then
            break
        end
        times = times - 1
    end

    if sym then
        return formatted .. sym
    else
        return formatted
    end
end

--- 数字展示 1898761621766 --> 1898.7G 18122688 --> 18.1M  122688--> 122.6k 1076->1,076  688->688 
---统一改成科学计数法，默认保留一位小数（如果小数为0则省略） 特别注意向下取整
---规则，1，如果>=G,则取G，且保留一位小数，直接返回
---2，如果>=M，则取M，且保留一位小数 直接返回
---3, 如果< 10000， 则转换成千分位逗号直接返回 eg 8636->8,636
---4，如果>=10K，取K，且保留一位小数 返回
local gNumber = 1000000000
local mNumber = 1000000
local kNumber = 1000
function ToScientificNotation(number)
    if type(number) ~= "number" then
        return tostring(number)
    end
    if number < 10000 then
        return toBalancesString(number)
    end
    local formatted = nil
    local sym = ""    
    local curNumberLevel = gNumber
    if number >= gNumber then
        sym = "G"
        curNumberLevel = gNumber
    elseif number >= mNumber then
        sym = "M"
        curNumberLevel = mNumber
    elseif number >= kNumber then
        sym = "K" 
        curNumberLevel = kNumber
    end

    if number >= curNumberLevel  then
        local intNumber = number / curNumberLevel
        -- 保留一位小数，但向下取整
        intNumber = math.floor(intNumber * 10) / 10
        if intNumber % 1 == 0 then
            formatted = string.format("%.0f", intNumber)
        else
            formatted = string.format("%.1f", intNumber)
        end

        return formatted .. sym
    else
        formatted = tostring(number)
    end

    local k = 1
    local times = 10
    while (times > 0) do
        formatted, k = string.gsub(formatted, "^(-?%d+)(%d%d%d)", '%1,%2')
        if (k == 0) then
            break
        end
        times = times - 1
    end
    if sym then
        return formatted .. sym
    else
        return formatted
    end
end

-- 余额展示 500 --> 5% 转化为万分比
function toTenThousandthPercentage(number)
    if number and (type(number) == "number" or number % 1 == 0) then
        local percentage = (number / 10000) * 100
        return math.floor(percentage) .. "%"  -- 将百分比取整并添加百分号
    end
end

-- 转化成颜色富文本 16进制 color = boc4cc 
function toColorRichText(text, color)
    local content = text or ""
    if color == nil then
        color = "ffffff"
    end
    return string.format("<color=#%s>%s</color>", color, content)
end

-- - - - - - - - - - - - - - - - - - - - - XMan NEW 旧功能不能满足得时候 新加方法写下面 - - - - - - - - - - - - - - - - - - - -
---统一获取等级的字符串
function GetLvStr(lv)
    return lv and "Lv." .. lv or ""
end
function NumberWithUnit(number, keepNum)
    keepNum = keepNum or "%.2f"
    local unit, divisor = "", 1

    if number >= 1000000 then
        unit, divisor = "M", 1000000
    elseif number >= 1000 then
        unit, divisor = "K", 1000
    end

    if divisor > 1 then
        local num = number / divisor
        local formattedNum = string.format(keepNum, num)
        return formattedNum .. unit
    end

    return tostring(number)
end
-- 超过K显示K，超过M显示M
function NumberWithUnit1(number)
    if not number then
        return 0
    end
    if number < 1000 then
        return number
    elseif number < 1000000 then
        local num = number / 1000
        local point = string.split(tostring(num), ".")[2]
        if point and string.len(point) > 1 then
            num = string.format("%.1f", math.floor(num * 10) / 10)
        end
        -- number = math.floor(number / 1000)
        return tostring(num) .. "K"
    else
        local num = number / 1000000
        local point = string.split(tostring(num), ".")[2]
        if point and string.len(point) > 1 then
            num = string.format("%.1f", math.floor(num * 10) / 10)
        end
        -- number = math.floor(number / 1000000)
        return tostring(num) .. "M"
    end
end

---获取lang字符串，标识符要跟服务器，策划 商议好
function GetLangStrFromJson(langStr, jsonInfo)
    local keys = {}
    local luaInfo = json.decode(jsonInfo)
    -- 使用 pattern 匹配大括号内的内容
    for key in string.gmatch(langStr, "{(.-)}") do
        keys[key] = ""
    end

    for i, v in pairs(keys) do
        if luaInfo[i] then
            keys[i] = luaInfo[i]
        else
            log.Error("服务端数据缺少key：" .. i)
        end
    end

    local result = string.gsub(langStr, "{(.-)}", function(key)
        return keys[key] or key  -- 使用提供的替换值，或返回原键
    end)

    return result
end

---获取服务器中带有flag标识符的字符串
---服务器json结构 "sContent":"{\"flag\":\"1\", \"luaValue\":\"NAME##ABC##285"}"
function GetStrFromFlagJson_lang(langStr, jsonInfo)
    local luaInfo = json.decode(jsonInfo)
    if not luaInfo then
        return jsonInfo
    end
    local isHaveFlag = false
    for i, v in pairs(luaInfo) do
        if i == "flag" and v == 1 then
            isHaveFlag = true
            break
        end
    end
    local str = ""
    if isHaveFlag then
        local strArray = ""
        for i, v in pairs(luaInfo) do
            if i == "luaValue" then
                strArray = SplitString(v, "##")
                break
            end
        end
        str = string.format3(langStr, strArray)
    else
        log.Error("ff:服务端数据缺少flag标识符" .. jsonInfo)
        str = langStr
    end
    return str
end

--"{\"flag\":\"1\", \"luaValue\":\"名次"}"
---服务器json结构 "sContent":"{\"flag\":\"1\", \"luaValue\":\"NAME##ABC##285"}"
function GetStrFromFlagJson(jsonInfo)
    if string.IsNullOrEmpty(jsonInfo) then
        return nil
    end
    local luaInfo = json.decode(jsonInfo)
    if not luaInfo then
        return jsonInfo
    end
    local isHaveFlag = false
    if luaInfo["flag"] and (luaInfo["flag"] == 1 or luaInfo["flag"] == "1") then
        isHaveFlag = true
    end

    local str = ""
    if isHaveFlag then
        if luaInfo["luaValue"] and not string.IsNullOrEmpty(luaInfo["luaValue"]) then
            str = SplitString(luaInfo["luaValue"], "##")
        end
    else
        log.Error("ff:服务端数据缺少flag标识符" .. jsonInfo)
    end
    return str
end

---获取服务器中带有多类型flag标识符的字符串,且解析出来应该是个table
---flag=1，是服务端直发的参数
---flag=2，value是langid，直接读取多语言
---flag=3, value是时间戳
---flag=4,luavalue="15##3",guideContentCfgid：1 
function GetStrFromMultiFlagJson(jsonInfo,getLike)
    if string.IsNullOrEmpty(jsonInfo) then
        return nil
    end
    local luaInfo = json.decode(jsonInfo)
    if not luaInfo or type(luaInfo) ~= "table" then
        return jsonInfo
    end

    local str = {}
    for i, v in ipairs(luaInfo) do
        if v["flag"] and v["flag"] > 0 and not string .IsNullOrEmpty(v["luaValue"]) then
            if v["flag"] == 1 then
                --常规
                table.insert(str, v["luaValue"])
            elseif v["flag"] == 2 then
                --langid
                table.insert(str, lang.Get(tonumber(v["luaValue"])))
            elseif v["flag"] == 3 then
                --时间戳，直接转
                local time_util = require "time_util"
                table.insert(str, time_util.ConvertStamp2Time(v["luaValue"]))
            elseif v["flag"] == 4 then
                --flag=4,luavalue="15##3",guideContentCfgid=1 ,desID=10 --攻略邮件的正文需要适配服务端参数
                local tmpStr1 = SplitString(luaInfo["luaValue"], "##")
                if not string.IsNullOrEmpty(v["guideContentCfgid"]) and not string.IsNullOrEmpty(v["desID"]) then
                    local guideContentCfg = game_scheme:GuideContent_0(tonumber(v["guideContentCfgid"]))
                    if guideContentCfg and guideContentCfg.TextID.data then
                        tmpStr1 = TrySet_ServerParamsLang(lang.Get(tonumber(v["desID"])), tmpStr1)
                    else
                        log.Error("ff:攻略配置表id错误：" .. v["guideContentCfgid"])
                    end
                    str[v["guideContentCfgid"] .. "_" .. v["desID"]] = tmpStr1
                end
            elseif v["flag"] == 5 then
                local str_value = v["luaValue"]
                if not str_value or str_value == "" then
                    log.Error("多语言邮件值为空")
                    return ""
                end
                local langList
                if type(str_value) == "table" then
                    -- 如果已经是table，直接使用
                    langList = str_value
                else
                    -- 处理转义字符
                    str_value = string.gsub(str_value, "\\\"", "\"")
                    str_value = string.gsub(str_value, "\\\\", "\\")
                    -- 否则尝试解析JSON
                    langList = json.decode(str_value)
                end

                if not langList or type(langList) ~= "table" then
                    if v["luaValue"] and type(v["luaValue"]) ~= "table" then 
                        log.Error("多语言邮件解析出错" .. v["luaValue"])
                    end
                    return ""
                end
                local ui_setting_data = require "ui_setting_data"
                local languageId = ui_setting_data.GetCurPlatformLanguageId()
                -- 1：中文，2：英文，3：繁体，4：印尼语，5：菲律宾，6：越南，7：泰语，8：葡萄牙语，9：韩语，10：法语
                local matchedItem = nil
                local englishItem = nil

                -- 一次性找匹配选项跟英文选项！！
                for _, item in pairs(langList) do
                    if item.Type == languageId then
                        matchedItem = item.Text or ""
                    end
                    if item.Type == 2 then  -- English
                        englishItem = item.Text or ""
                    end
                end

                local defaultItem = matchedItem or englishItem or ""
                table.insert(str, defaultItem)
            elseif v["flag"] == 6 then
                local str_value = v["luaValue"]
                if str_value and type(str_value) == "table" and getLike then 
                   return str_value.exMailId
                end
            else
                if v["luaValue"] and type(v["luaValue"]) ~= "table" then 
                    log.Error("ff:服务端数据缺少匹配的flag标识符" .. v["luaValue"])
                end
            end
        end
    end

    return str
end

--尝试设置邮件的多语言标题
function TrySetTitleFromMultiJson(jsonInfo,originalTitle)
    if not jsonInfo or jsonInfo == "" then
        return originalTitle
    end
    local luaInfo = json.decode(jsonInfo)
    if not luaInfo or type(luaInfo) ~= "table" then
        return originalTitle
    end

    for i, v in ipairs(luaInfo) do
        if v["flag"] and v["flag"] > 0 and not string.IsNullOrEmpty(v["luaValue"]) then
            if v["flag"] == 5 then
                local str_value = v["luaValue"]
                if not str_value or str_value == "" then
                    return originalTitle
                end
                local langList
                if type(str_value) == "table" then
                    -- 如果已经是table，直接使用
                    langList = str_value
                else
                    -- 处理转义字符
                    str_value = string.gsub(str_value, "\\\"", "\"")
                    str_value = string.gsub(str_value, "\\\\", "\\")
                    -- 否则尝试解析JSON
                    langList = json.decode(str_value)
                end

                if not langList or type(langList) ~= "table" then
                    return originalTitle
                end
                local ui_setting_data = require "ui_setting_data"
                local languageId = ui_setting_data.GetCurPlatformLanguageId()
                local matchedItem = nil
                local englishItem = nil
                
                for _, item in pairs(langList) do
                    if item.Type == languageId then
                        matchedItem = item.Title or ""
                    end
                    if item.Type == 2 then  -- English
                        englishItem = item.Title or ""
                    end
                end
                return matchedItem or englishItem or ""
            end
        end
    end
    return originalTitle
end

--尝试设置邮件正文
function TrySet_ServerParamsLang(cContent, sContent)
    if string.IsNullOrEmpty(sContent) then
        return cContent
    end
    return string.format3(cContent, sContent)--如果有需要解析的服务器标记json，则按规则解析
end

--- 余额展示 18122688 --> 18,122,688.0
function toKeepOneDot(number,keepDot)
    if type(number) ~= "number" then
        return tostring(number)
    end
    local formatted
    if keepDot then
        formatted =  string.format("%.1f", number)
    else
        formatted = tostring(number)
    end
    --local formatted =  string.format("%.1f", number)
    local k = 1
    local times = 10
    while (times > 0) do
        formatted, k = string.gsub(formatted, "^(-?%d+)(%d%d%d)", '%1,%2')
        if (k == 0) then
            break
        end
        times = times - 1
    end
    return formatted
end

function EmojiTruncate(str, maxLen)
    local len = 0
    local i = 1
    local res = {}

    while i <= #str do
        local c = str:sub(i, i)
        if c == '[' then
            -- 找到对应的]
            local closePos = str:find(']', i)
            if not closePos then
                -- 没找到]，按普通字符处理
                if len + 1 > maxLen then break end
                table.insert(res, c)
                len = len + 1
                i = i + 1
            else
                -- 整体当作一个单位长度
                if len + 4 > maxLen then break end
                local emoji = str:sub(i, closePos)
                table.insert(res, emoji)
                len = len + 4
                i = closePos + 1
            end
        else
            -- 普通字符
            if len + 1 > maxLen then break end
            table.insert(res, c)
            len = len + 1
            i = i + 1
        end
    end

    local resultStr = table.concat(res)
    if i <= #str then
        resultStr = resultStr .. "..."
    end
    return resultStr
end

local utf8 = require("utf8")

function GetUTF8_length(str)
    local len = 0
    for _, _ in utf8.codes(str) do
        len = len + 1
    end
    return len
end

function StringToNumberID(str, count)
    local hash = 0
    local len = #str
    for i = 1, len do
        hash = (hash * 131 + str:byte(i)) % 9007199254740991 -- Lua安全整数范围
    end

    if not count or count <= 0 then
        return hash
    end

    local hashStr = tostring(hash) -- 把hash转字符串，方便按位取值
    local hashLen = #hashStr

    local tmpHash = 0
    for i = 1, count do
        -- 随机取hash字符串里某一位数字字符（'0'-'9'）
        local idx = math.random(1, hashLen)
        local digit = tonumber(hashStr:sub(idx, idx)) or 0
        -- 乘10累积，比如 i=1叠加个位，i=2叠加十位等（先乘10相当于左移一位）
        tmpHash = tmpHash * 10 + digit
    end
    return tmpHash
end

--概率显示到小数点后len位（如韩国包保留4位小数）
function GetFloatByChannel(num, len)
    if const.IsKoreaChannel() then
        len = 4
    end
    local str = string.format("%." .. len .. "f%%", num)
    return str
end

-- 把table数据插入到字符串%占位符位置并加颜色
function FormatUnityRichText(baseStr, values, color)
    -- 如果没传颜色，就默认黄色
    color = color or "#FFFFFF"

    -- 先数一数字符串里有多少个%s
    local placeholderCount = select(2, baseStr:gsub("%%s", ""))

    -- 生成一个新的表，用富文本颜色包裹
    local coloredValues = {}
    for i = 1, math.min(placeholderCount, #values) do
        coloredValues[i] = string.format("<color=%s>%s</color>", color, tostring(values[i]))
    end

    -- 用 string.format 把颜色后的值填充到原字符串
    return string.format2(baseStr, unpack(coloredValues))
end

--将table中的元素附上富文本并传出
function TableToRichText(t, color)
    -- 如果颜色没有带 #，自动补上
    if not color:match("^#") then
        color = "#" .. color
    end

    local newTable = {}
    for i, v in ipairs(t) do
        -- tostring 确保各种类型都能转成字符串
        newTable[i] = string.format("<color=%s>%s</color>", color, tostring(v))
    end
    return newTable
end


function FormatTable(str,t)
    local _str = ""
    if type(t) == "table" then
        _str = string.format2(str, unpack(t))
    end
    return _str
end

-- 需要剔除的TMP标签列表（不包括sprite相关）
local tagsToRemove = {
    -- 颜色标签
    '<color[^>]*>', '</color>',
    -- 大小标签  
    '<size[^>]*>', '</size>',
    -- 字体标签
    '<font[^>]*>', '</font>',
    -- 样式标签
    '<b>', '</b>',
    '<i>', '</i>',
    '<u>', '</u>',
    '<s>', '</s>',
    -- 对齐标签
    '<align[^>]*>', '</align>',
    -- 缩进标签
    '<indent[^>]*>', '</indent>',
    -- 行高标签
    '<line%-height[^>]*>', '</line%-height>',
    -- 材质标签
    '<material[^>]*>', '</material>',
    -- 其他常见标签
    '<mark[^>]*>', '</mark>',
    '<sup>', '</sup>',
    '<sub>', '</sub>',
    '<pos[^>]*>',
    '<voffset[^>]*>', '</voffset>',
    '<space[^>]*>',
    '<nobr>', '</nobr>',
    '<br>',
    '<quad[^>]*>',
    '<link[^>]*>', '</link>',
}

-- 剔除TMP富文本格式但保留emoji功能
function StripTMPTagsKeepEmoji(str)
    if not str or type(str) ~= "string" then return str end
    local result = str

    -- 移除所有需要剔除的标签
    for _, pattern in ipairs(tagsToRemove) do
        result = string.gsub(result, pattern, "")
    end

    -- 保留sprite标签（用于emoji显示）
    -- 注意：sprite标签通常不需要处理，因为它们是emoji显示的一部分

    -- 移除零宽字符和控制字符
    result = string.gsub(result, "%z", "")

    -- 去除首尾空白
    result = string.gsub(result, "^%s+", "")
    result = string.gsub(result, "%s+$", "")

    return result
end

-- 替换所有非emoji相关的标签，用<noparse>包裹
local patterns = {
    -- color标签
    { "(<color[^>]*>)", "<noparse>%1</noparse>" },
    { "(</color>)", "<noparse>%1</noparse>" },
    -- size标签
    { "(<size[^>]*>)", "<noparse>%1</noparse>" },
    { "(</size>)", "<noparse>%1</noparse>" },
    -- 其他标签
    { "(<b>)", "<noparse>%1</noparse>" },
    { "(</b>)", "<noparse>%1</noparse>" },
    { "(<i>)", "<noparse>%1</noparse>" },
    { "(</i>)", "<noparse>%1</noparse>" },
    { "(<u>)", "<noparse>%1</noparse>" },
    { "(</u>)", "<noparse>%1</noparse>" },
}

-- 精准包裹非emoji的标签
function EscapeTMPTagsKeepEmoji(str)
    if not str or type(str) ~= "string" then return str end
    for _, pattern in ipairs(patterns) do
        str = string.gsub(str, pattern[1], pattern[2])
    end

    return str
end

function CleanLabel(label)
    if label == nil or label == "" then return "" end
    -- 1. 移除开头的 http:// 或 https://
    -- 2. 捕获直到最后一个路径分隔符之前的部分
    -- 3. 将所有特殊字符替换为下划线
    local pattern = "^https?://(.+)[/\\][^/\\]*$"
    local path = string.match(label, pattern)
    
    if path then
        -- 如果匹配成功，处理路径部分
        return string.gsub(path, "[%./:\\]", "_")
    else
        -- 如果没有路径分隔符，直接处理整个字符串
        local no_protocol = string.gsub(label, "^https?://", "")
        return string.gsub(no_protocol, "[%./:\\]", "_")
    end
end

-- 辅助函数：在字符串指定位置插入文本
function InsertAtStringPosition(text, insertText, charPos)
    if not text then text = "" end
    if not insertText then insertText = "" end
    if charPos < 0 then charPos = 0 end

    -- 将字符索引转换为字节索引
    local bytePos = CharIndexToByteIndex(text, charPos)

    -- 使用字节索引进行插入
    if bytePos <= 0 then
        return table.concat({insertText,text})
    elseif bytePos >= string.len(text) then
        return table.concat({text,insertText})
    else
        local before = string.sub(text, 1, bytePos)
        local after = string.sub(text, bytePos + 1)
        return table.concat({before,insertText,after})
    end
end

-- 修复后的字符索引到字节索引转换函数
function CharIndexToByteIndex(text, charIndex)
    if not text or charIndex <= 0 then
        return 0
    end

    local byteIndex = 1
    local currentCharIndex = 0
    local textLen = string.len(text)

    while byteIndex <= textLen and currentCharIndex < charIndex do
        local byte = string.byte(text, byteIndex)

        -- UTF-8字符长度判断
        local charByteLen = 1
        if byte >= 240 then
            charByteLen = 4  -- 4字节字符（某些emoji）
        elseif byte >= 224 then
            charByteLen = 3  -- 3字节字符（大部分中文）
        elseif byte >= 192 then
            charByteLen = 2  -- 2字节字符
        end

        byteIndex = byteIndex + charByteLen
        currentCharIndex = currentCharIndex + 1
    end

    return byteIndex - 1
end



