local ipairs = ipairs
local pairs = pairs
local os = os
local table = table
local require = require
local string = string
local tonumber = tonumber
local game_scheme = require "game_scheme"
local event = require "event"
local gw_const = require "gw_const"
local red_const = require "red_const"
local red_system = require "red_system"
local skep_mgr = require "skep_mgr"
local log = require "log"
local util = require "util"
local prop_pb = require "prop_pb"
local GWG = GWG
local player_mgr = require "player_mgr"
local string_util = require "string_util"
local new_weapon_combat_crystal_define = require "new_weapon_combat_crystal_define"
local gw_home_building_data = require "gw_home_building_data"
module("new_weapon_combat_crystal_data")

local _M = {}

-- 数据初始化
function Init()
    InitData()
    InitRedDot()
    InitUpdateEvent()
end

-- 初始化刷新事件
function InitUpdateEvent()
    event.Register(event.UPDATE_GOOD_PROP, TriggerRed)
    --event.Register(new_weapon_combat_crystal_define.MSG_DRONESTAR_INFO_RSP,TriggerRed)
end




function InitData()

end

function InitCfg()
    if not _M.isInitCfg then
        _M.isInitCfg = true
        _M.StageCfgList = {}--阶段
        _M.SchemeCfgList = {}--方案
        _M.maxAmplificationLevel = 0
        local cfgNum = game_scheme:MagicWeaponStage_nums()
        for i=0,cfgNum-1 do
            local cfg = game_scheme:MagicWeaponStage(i)
            if not _M.StageCfgList[cfg.stage] then
                _M.StageCfgList[cfg.stage] = {}
            end
            if not _M.SchemeCfgList[cfg.schemeID] then
                _M.SchemeCfgList[cfg.schemeID] = {}
            end
            _M.maxAmplificationLevel = cfg.AmplificationLevel > _M.maxAmplificationLevel and cfg.AmplificationLevel or _M.maxAmplificationLevel
            table.insert(_M.StageCfgList[cfg.stage], cfg)
            table.insert(_M.SchemeCfgList[cfg.schemeID], cfg)
        end
        _M.CommonCombatItemList = {}
        local initCfg = game_scheme:InitBattleProp_0(new_weapon_combat_crystal_define.CommonComBatItemInitID)
        if initCfg then
            local arr = string.split(initCfg.szParamString, ";")
            for i=1,#arr do
                local item = string.split(arr[i], "#")
                _M.CommonCombatItemList[ tonumber(item[1])] = tonumber(item[2])
            end
        else
            log.Error("InitBattleProp cfg is nil",new_weapon_combat_crystal_define.CommonComBatItemInitID)
        end


        _M.combineCrystalData = {}
        _M.crystalGroupData = {}
        cfgNum = game_scheme:MagicWeaponCrystal_nums()
        for i=0,cfgNum-1 do
            local cfg = game_scheme:MagicWeaponCrystal(i)
            if cfg then
                if cfg.PrescriptionID and cfg.PrescriptionID ~= 0 then
                    table.insert(_M.combineCrystalData, cfg)
                end
                if not _M.crystalGroupData[cfg.crystalID] then
                    _M.crystalGroupData[cfg.crystalID] = {}
                end
                table.insert(_M.crystalGroupData[cfg.crystalID], cfg)
            end
        end
        table.sort(_M.crystalGroupData, function(a,b)
            return a.starLevel < b.starLevel
        end)
        table.sort(_M.combineCrystalData, function(a,b)
            if a.rare~= b.rare then
                return a.rare < b.rare
            end
            return a.crystalID < b.crystalID
        end)
    end
end

--获取某一个进阶阶段的所有配置
function GetStageListByStageIndex(stageIndex)
    InitCfg()
    return _M.StageCfgList[stageIndex]
end

--获取当前进阶下一阶段突破等级，nil表示已是最大
function GetNextStageLevel()
    local stage = GetCurrentCombatStage()
    if stage then
        local stageCfgList = GetStageListByStageIndex(stage+1)
        if stageCfgList then
            return stageCfgList[1].stageLevel
        end
    end
    return nil
end

--获取最大增幅等级
function GetMaxAmplificationLevel()
    InitCfg()
    return _M.maxAmplificationLevel
end

--获取所有的进阶阶段
function GetAllStageList()
    InitCfg()
    return _M.StageCfgList
end

--获取最大进阶阶段
function GetMaxCombatStage()
    InitCfg()
    return #_M.StageCfgList
end

--获取最大进阶等级
function GetMaxCombatLevel()
    InitCfg()
    return _M.StageCfgList[#_M.StageCfgList][1].stageLevel
end

--获取通用进阶道具所能提供的进阶经验
function GetCommonCombatItemProvideExpByItemID(itemID)
    InitCfg()
    return _M.CommonCombatItemList[itemID]
end

--获取所有通用进阶道具
function GetAllCommonCombatItem()
    InitCfg()
    return _M.CommonCombatItemList
end

--传入当前等级，加增长的经验，返回可升级到的等级
function CalculateLevel(currentLevel, currentExp, addExp)
    local newExp = currentExp + addExp
    local maxLevel = GetMaxCombatLevel()

    for lvl = currentLevel, maxLevel-1 do
        local cfg = game_scheme:MagicWeaponStage_0(lvl)
        if cfg == nil then
            -- 没有下一级了
            break
        end
        local expForNext = cfg.exp

        if newExp >= expForNext then
            newExp = newExp - expForNext
            currentLevel = lvl + 1
        else
            break
        end
    end

    return currentLevel, newExp
end

function GetExpForNextLevel(curlevel,curLevelExp)
    if curlevel >= GetMaxCombatLevel() then
        return 0
    end
    local cfg = game_scheme:MagicWeaponStage_0(curlevel)
    if cfg then
        return cfg.exp - curLevelExp
    end
    return 0
end

--获取当前进阶阶段
function GetCurrentCombatStage()
    local gw_home_drone_data = require "gw_home_drone_data"
    local curLevel =gw_home_drone_data.GetCombatLevel()
    local cfg = game_scheme:MagicWeaponStage_0(curLevel)
    return cfg and cfg.stage or 0
end

--是否可以再建造队列里显示
function GetCanDisplayInBuildingList()
    return GetCurrentCombatStage() > 0 and GetModuleIsOpen()
end

--获取神兽结晶背包所有sid
function GetCrystalPacketGoods()
    local goods = {}
    local length = 0
    local playerEntity = player_mgr.GetPlayerEntity()
    if playerEntity then
        local skepIDs = skep_mgr.GetSkepIDs(prop_pb.SKEP_TYPE_DRONESTAR)
        if #skepIDs > 0 then
            goods, length = skep_mgr.GetGoodsSidOfSkep(skepIDs[1])
        end
        return goods, length
    end

end

--获取背包中未装备的结晶
function GetUnUsedCrystalPacketGoods()
    local goodsEntityList = {}
    local goods = GetCrystalPacketGoods()
    for k,v in pairs(goods or {}) do
        local entity = player_mgr.GetPacketPartDataBySid(k)
        local props = entity:GetCrystalProps()
        if props.lock and props.lock == 0 then
            table.insert(goodsEntityList, entity)
        end
    end
    return goodsEntityList
end

--获取背包中某功能类型的所有结晶
function GetCrystalGoodsByFeatureType(featureType)
    local goodsEntityList = {}
    local goods = GetCrystalPacketGoods()
    for k,v in pairs(goods or {}) do
        local entity = player_mgr.GetPacketPartDataBySid(k)
        local props = entity:GetCrystalProps()
        local itemID = entity:GetGoodsID()
        local crystalCfg = game_scheme:MagicWeaponCrystal_0(itemID,props.starLv)
        if crystalCfg and crystalCfg.featureType == featureType then
            table.insert(goodsEntityList, entity)
        end
    end
    return goodsEntityList
end

--获取背包中某功能类型的所有未穿戴的结晶
function GetUnUsedCrystalGoodsByFeatureType(featureType)
    local goodsEntityList = {}
    local goods = GetCrystalPacketGoods()
    for k,v in pairs(goods or {}) do
        local entity = player_mgr.GetPacketPartDataBySid(k)
        local props = entity:GetCrystalProps()
        local itemID = entity:GetGoodsID()
        local crystalCfg = game_scheme:MagicWeaponCrystal_0(itemID,props.starLv)
        if props.lock == 0 and crystalCfg and crystalCfg.featureType == featureType then
            table.insert(goodsEntityList, entity)
        end
    end
    return goodsEntityList
end

--获取有哪些结晶宝箱
function GetCrystalBoxData()
    local iniCfg = game_scheme:InitBattleProp_0(new_weapon_combat_crystal_define.CrystalBoxInitID)
    local cfg_util = require "cfg_util"
    return iniCfg and cfg_util.ArrayToLuaArray(iniCfg.szParam)
end

--获取能合成的结晶
function GetCombineCrystalData()
    InitCfg()
    return _M.combineCrystalData
end


--获取该结晶的升星预览配置
function GetCrystalGroupData(crystalID)
    InitCfg()
    return _M.crystalGroupData[crystalID]
end

--设置结晶方案
function SetCrystalSchemeData(msg)
    if _HasField(msg,"droneStar") then
        _M.crystalSchemeData = {}
        for k,schemeInfo in ipairs(msg.droneStar) do
            if schemeInfo.planId ~= 0 then
                local data = {}
                data.planId = schemeInfo.planId
                data.teamIndex = schemeInfo.teamIndex
                if _HasField (schemeInfo,"starPlace") then
                    data.starPlace = {}
                    for k,v in ipairs(schemeInfo.starPlace) do
                        data.starPlace[v.place] = v.StarSid
                    end
                end
                _M.crystalSchemeData[schemeInfo.planId] = data
            end
        end
    end
    event.Trigger(new_weapon_combat_crystal_define.MSG_DRONESTAR_INFO_RSP)
end

--判断该结晶是否被装配，并返回方案planId
function GetCrystalIsInstalledPlanId(sid)
    local planId = 0
    local place = 0
    for _planId,schemeInfo in pairs(_M.crystalSchemeData or {})do
        for k,StarSid in pairs(schemeInfo.starPlace or {})do
            if StarSid == sid then
                planId = _planId
                place = k
                break
            end
        end
    end
    return planId,place
end

--获取结晶方案数据
function GetCrystalSchemeData(planId)
    return _M.crystalSchemeData and _M.crystalSchemeData[planId]
end

--获取当前已解锁的所有结晶方案
function GetAllUnlockCrystalScheme()
    return _M.crystalSchemeData
end

--获取解锁结该晶方案配置
function GetSchemeCfgListByPlanId(planId)
    InitCfg()
    return _M.SchemeCfgList[planId] and _M.SchemeCfgList[planId][1]
end

--获取当前增幅等级
function GetCurAmplificationLevel()
    local gw_home_drone_data = require "gw_home_drone_data"
    local curLevel =gw_home_drone_data.GetCombatLevel()
    local cfg = game_scheme:MagicWeaponStage_0(curLevel)
    if cfg then
        return cfg.AmplificationLevel
    end
    return 0
end

--获取当前阶段的该结晶的增幅战力
function GetAmplificationLevelByCfg(cfg)
    local curStage = GetCurrentCombatStage()
    local stageCfgArr = GetStageListByStageIndex(curStage)
    if not stageCfgArr or not cfg then
        return 0
    end
    local amplificationLevel = stageCfgArr[1].AmplificationLevel
    return cfg.increasePower.data[amplificationLevel] or 0
end

--通过结晶sid获取结晶配置
function GetCrystalCfgBySid(sid)
        local entity = player_mgr.GetPacketPartDataBySid(sid)
        if not entity then
            return
        end
        local props = entity:GetCrystalProps()
        local itemID = entity:GetGoodsID()
        return game_scheme:MagicWeaponCrystal_0(itemID,props.starLv)
end

--通过结晶sid获取增幅战力
function GetCrystalAmplificationPowerBySid(sid)
    local power = 0
    local cfg = GetCrystalCfgBySid(sid)
    if cfg then
        power = GetAmplificationLevelByCfg(cfg)
    end
    return power
end

--通过编队ID获取对应的结晶方案增幅战力
function GetCrystalPowerByPlanID(teamIndex)
    local power = 0
    local planID = GetCrystalPlanIDByTeamIndex(teamIndex)
    if planID == 0 then
        return power
    end
    for place,sid in pairs(_M.crystalSchemeData[planID].starPlace) do
        power = GetCrystalAmplificationPowerBySid(sid) + power
    end
    return power
end


--最大检测星级
local MAX_CRYSTAL_LEVEL = 20
--通过sid获取最大星级配置
function GetMaxCrystalLevelCfgBySid(sid)
    local entity = player_mgr.GetPacketPartDataBySid(sid)
    if not entity then
        return
    end
    local props = entity:GetCrystalProps()
    local itemID = entity:GetGoodsID()
    local level = props.starLv
    for i=level,MAX_CRYSTAL_LEVEL do
        local cfg = game_scheme:MagicWeaponCrystal_0(itemID,i)
        if cfg and cfg.starRisingCost.count == 0 then
            return cfg
        end
    end
end

--通过sid判断该结晶是否可以升星
function IsCanUpgradeCrystal(sid)
    local cfg = GetCrystalCfgBySid(sid)
    if cfg and cfg.starRisingCost.count == 0 then
        return false
    end
    return true
end

--通过结晶sid获取该结晶升星消耗
function GetCrystalUpgradeCostBySid(sid)
    local cost = nil
    local cfg = GetCrystalCfgBySid(sid)
    if cfg and cfg.starRisingCost.count == 3 then
        cost = cfg.starRisingCost
    end
    return cost
end

--通过sidList将结晶ID及星级相同的结晶整合
function GetCrystalListBySidList(sidList)
    local crystalMap = {}
    for k,sid in pairs(sidList or {}) do
        local entity = player_mgr.GetPacketPartDataBySid(sid)
        if entity then
            local itemID = entity:GetGoodsID()
            local props = entity:GetCrystalProps()
            local level = props.starLv   
            if not crystalMap[itemID.."_"..level] then
                crystalMap[itemID.."_"..level] = {crystalID = itemID,level = level,sidList = {}}
            end
            table.insert(crystalMap[itemID.."_"..level].sidList,sid)
        end
    end
    return crystalMap
end


--获取同星级下的同一ItemID的所有未安装结晶
function GetAllCrystalMaterialListByItemIDAndLevel(_itemID,_level)
    local goodsEntityList = {}
    local goods = GetCrystalPacketGoods()
    for k,v in pairs(goods or {}) do
        local entity = player_mgr.GetPacketPartDataBySid(k)
        local itemID = entity:GetGoodsID()
        local props = entity:GetCrystalProps()
        if _itemID == itemID and props.starLv == _level and props.lock and props.lock == 0 then
            table.insert(goodsEntityList, entity)
        end
    end
    return goodsEntityList
end

--获取可以重置的结晶列表（升星且未装备）
function GetCanResetCrystalList() 
    local goodsEntityList = {}
    local goods = GetCrystalPacketGoods()
    for k,v in pairs(goods or {}) do
        local entity = player_mgr.GetPacketPartDataBySid(k)
        local props = entity:GetCrystalProps()
        if props.lock and props.lock == 0  and props.starLv and props.starLv > 0 then
            table.insert(goodsEntityList, entity)
        end
    end
    return goodsEntityList
end

local function decompose(itemID,level, count, depth)
    depth = depth or 0
    if depth > 20 then
        log.Error("递归深度超过限制")
        return count
    end

    local cfg = game_scheme:MagicWeaponCrystal_0(itemID,level-1)
    if cfg == nil then
        return count
    end
    local consumeLevel = cfg.starRisingCost.data[1]
    local consumeCount = cfg.starRisingCost.data[2]

    -- 保护：如果consumeLevel >= level，报错
    if consumeLevel >= level then
        log.Error(string.format("配置错误：当前道具ID %d,等级 %d，消耗等级 %d，导致死递归", itemID,level, consumeLevel))
        return count
    end

    local totalLowerCount = (consumeCount) * count
    return decompose(itemID,consumeLevel, totalLowerCount, depth + 1)
end

--计算该结晶重置后获得的结晶
function GetResetCrystal(sid)
    local entity = player_mgr.GetPacketPartDataBySid(sid)
    if not entity then
        return
    end
    local props = entity:GetCrystalProps()
    local itemID = entity:GetGoodsID()
    local level = props.starLv
    local num = 0
    for i = level , 0 ,-1 do
        local _num = decompose(itemID,i,1)
        num = _num + num
    end
    local cfg = game_scheme:MagicWeaponCrystal_0(itemID,0)
    return num,cfg
end

--获取编队的结晶方案
function GetCrystalPlanIDByTeamIndex(teamIndex)
    if _M.crystalSchemeData then
        for k,v in pairs(_M.crystalSchemeData) do
            if v.teamIndex == teamIndex then
                return k
            end
        end
    end
    return 0
end

--跳转到未建造的编队序号最小的编队
function JumpMinBuildTeamIndex()
    local formationList = {
        [1] = gw_const.enBuildingType.enBuildingType_Formation1,
        [2] = gw_const.enBuildingType.enBuildingType_Formation2,
        [3] = gw_const.enBuildingType.enBuildingType_Formation3,
        [4] = gw_const.enBuildingType.enBuildingType_Formation4,
    }
    for k,typeId in ipairs(formationList) do
        local buildId = GWG.GWHomeMgr.cfg.GetBuildId(typeId)
        local data = GWG.GWHomeMgr.buildingData.GetMinLevelBuildingDataByBuildingID(buildId)
        if data and data.nLevel == 0 then
            local gw_jump_util = require "gw_jump_util"
            gw_jump_util.JumpToBuildAndMenu(typeId.."#2")
            break
        end
    end
end


--当前是否有可制造的结晶，并返回配置
function HasCrystalCanBuild()
    local data = GetCombineCrystalData()

    local cfg_util = require "cfg_util"
    for k,v in ipairs(data) do
        local cfg = game_scheme:Prescription_0(v.PrescriptionID)
        if cfg then
            local isLock = false
            local unlockCondition = nil
            if cfg.UnlockCondition1 ~= "" then
                local condition = string.split(cfg.UnlockCondition1, "#")
                local maxLevel =  gw_home_building_data.GetBuildingDataMaxLevel(tonumber(condition[1]))
                if #condition ~= 2 then
                    log.Error("new_weapon_combat_crystal_data UnlockCondition1 error", v.PrescriptionID)
                else
                    unlockCondition = condition[2]
                    if maxLevel < tonumber(condition[2]) then
                        isLock = true
                    end
                end
            end
            local cost = cfg_util.ArrayToLuaArray(cfg.cost)
            local costnum = cfg_util.ArrayToLuaArray(cfg.costnum)
            if not isLock and cost[1] and costnum[1] then
                local hasNum = player_mgr.GetPlayerOwnNum(cost[1])
                if hasNum >= costnum[1] then
                    return v
                end
            end
        else
            log.Error("Prescription cfg is nil ", v.PrescriptionID)
        end
    end
    return 
end

function GetIncreaseValue(crystalCfg,amplificationLevel)
    local valueTable = {}
    local maxAmplificationLevel = GetMaxAmplificationLevel()
    if crystalCfg and crystalCfg.increaseValue ~= "" then
        local array = string.split(crystalCfg.increaseValue,";")
        for i, v in ipairs(array) do
            local array2 = string.split(v,"#")
            if #array2 < maxAmplificationLevel + 1 then
                log.Error("GetIncreaseValue error ,crystalCfg",crystalCfg.crystalID)
            else
                local value = amplificationLevel > 0 and array2[amplificationLevel + 1] or 0
                if tonumber(array2[1]) == 1 then
                    value = string_util.GetFloatByChannel(tonumber(value/100),2)
                elseif tonumber(array2[1]) == 2 then
                    value = util.NumberWithUnit2(tonumber(value))
                end
                table.insert(valueTable,value)
            end
        end
    end
    return valueTable
end

function GetSkillValue(crystalCfg)
    local valueTable = {}
    if crystalCfg and crystalCfg.skillValue ~= "" then
        local array = string.split(crystalCfg.skillValue,";")
        for i, v in ipairs(array) do
            local array2 = string.split(v,"#")
            local value = 0
            if tonumber(array2[1]) == 1 then
                value = string_util.GetFloatByChannel(tonumber(array2[2]/100),2)
            elseif tonumber(array2[1]) == 2 then
                value = util.NumberWithUnit2(tonumber(array2[2]))
            end
            table.insert(valueTable,value)
        end
    end
    return valueTable
end

--获取结晶升星描述
function GetStarValue(crystalCfg)
    local value = 0

    if crystalCfg and crystalCfg.starValue.count > 1 then
        if crystalCfg.starValue.data[0] == 1 then
            value = string_util.GetFloatByChannel(crystalCfg.starValue.data[1]/100,2)
        elseif crystalCfg.starValue.data[0] == 2 then
            value = util.NumberWithUnit2(crystalCfg.starValue.data[1])
        end
    end
    return value
end

--获取该结晶是否解锁，并返回解锁条件
function GetCrystalMakeIsLock(crystalCfg)
    local cfg = game_scheme:Prescription_0(crystalCfg.PrescriptionID)
    local isLock = false
    local unlockCondition = nil
    if cfg and cfg.UnlockCondition1 ~= "" then
        local condition = string.split(cfg.UnlockCondition1, "#")
        local maxLevel =  gw_home_building_data.GetBuildingDataMaxLevel(tonumber(condition[1]))
        if #condition ~= 2 then
            log.Error("new_weapon_combat_crystal_data UnlockCondition1 error", crystalCfg.PrescriptionID)
        else
            unlockCondition = condition[2]
            if maxLevel < tonumber(condition[2]) then
                isLock = true
            end
        end
    end
    return isLock,unlockCondition
end

--获取结晶技能描述
function GetSkillDesc(crystalCfg)
    if crystalCfg then
        local table_util = require "table_util"
        local lang = require "lang"
        local skillDescValue = GetSkillValue(crystalCfg)
        local skillDescValue1 = GetIncreaseValue(crystalCfg,GetCurAmplificationLevel())
        local _skillDescValue = string_util.TableToRichText(skillDescValue,"#319F38")
        local _skillDescValue1 = string_util.TableToRichText(skillDescValue1,"#DE621E")
        local newTable = table_util.MergeTables(_skillDescValue,_skillDescValue1)
        return string_util.FormatTable(lang.Get(crystalCfg.skillDes),newTable)
    end
    return ""
end

function GetModuleIsOpen()
    local net_module_open = require "net_module_open"
    local moduleOpenPro_pb = require "moduleOpenPro_pb"
    local moduleOpen = net_module_open.CheckModuleOpen(moduleOpenPro_pb.emModuleID_DroneAdvance)
    return moduleOpen
end


-------------------红点相关--------------------
--检测当前装备的结晶是否升星
function CheckCrystalCanUpgrade(sid)
    if sid == 0 then
        return false
    end
    --是否达到最大星级
    if not IsCanUpgradeCrystal(sid) then
        return false
    end
    local cfg = GetCrystalCfgBySid(sid)
    local starRisingCost = cfg.starRisingCost.data
    local list = GetAllCrystalMaterialListByItemIDAndLevel(starRisingCost[0],starRisingCost[1])
    if list and #list >= starRisingCost[2] then
        return true
    end
    return false
end

--检测是否有比当前结晶战力更高且未装备的结晶
function CheckHasHigherPowerAndUnusedCrystal(sid,featureType)
    local entityList = GetUnUsedCrystalGoodsByFeatureType(featureType)
    if sid == 0 and #entityList > 0 then
        return true
    end
    local crystalCfg = GetCrystalCfgBySid(sid)
    if crystalCfg then
        local power = GetAmplificationLevelByCfg(crystalCfg)
        --是否空闲
        if sid == 0 then
            log.Error("#entityList > 0",#entityList)
        end
        for k, v in ipairs(entityList) do
            local _power = GetCrystalAmplificationPowerBySid(v:GetGoodsSid())
            if _power > power then
                return true
            end
        end
    end
    return false
end

--当前有可开启的结晶宝箱
function CheckHasOpenCrystalBox()
    local boxList = GetCrystalBoxData()
    for k, v in ipairs(boxList  or {}) do
        local num = player_mgr.GetPlayerOwnNum(v)
        if num > 0 then
            return true
        end
    end
    return false
end

function InitRedDot()
    red_system.RegisterRedFunc(red_const.Enum.NewMagicWeaponCombatTabRed,GetCombatTabRed)
    red_system.RegisterRedFunc(red_const.Enum.NewMagicWeaponCrystalBoxRed,GetCrystalBoxRed)
    red_system.RegisterRedFunc(red_const.Enum.NewMagicWeaponCrystalTabRed,GetCrystalTabRed)
    red_system.RegisterRedFunc(red_const.Enum.NewMagicWeaponCrystalToggleRed,GetCrystalToggleRed)
    red_system.RegisterRedFunc(red_const.Enum.NewMagicWeaponCrystalRootRed,GetCrystalRootRed)
    red_system.RegisterRedFunc(red_const.Enum.NewMagicWeaponCrystalUpgradeRed,GetCrystalUpgradeRed)
    red_system.RegisterRedFunc(red_const.Enum.NewMagicWeaponCrystalReplaceRed,GetCrystalReplaceRed)
end

function TriggerRed()
    if GetModuleIsOpen() then
        red_system.TriggerRed(red_const.Enum.NewMagicWeaponCrystalReplaceRed)
        red_system.TriggerRed(red_const.Enum.NewMagicWeaponCrystalUpgradeRed)
        red_system.TriggerRed(red_const.Enum.NewMagicWeaponCrystalBoxRed)
    end
end

--神兽战斗进阶切页红点
function GetCombatTabRed()
    return GetCrystalBoxRed()
end


--结晶切页红点
function GetCrystalTabRed()
    local redNum = 0
    local gw_home_drone_data = require "gw_home_drone_data"
    if gw_home_drone_data.GetCrystalIsOpen() then
        local allSchemeData = GetAllUnlockCrystalScheme()
        for panelID,schemeData in pairs(allSchemeData or {}) do
            redNum = redNum + GetCrystalToggleRed(panelID)
        end
        redNum = redNum + GetCrystalBoxRed()
    end
    return redNum
end

--技能晶核宝箱红点
function GetCrystalBoxRed()
    local redNum = 0
    if CheckHasOpenCrystalBox() then
        redNum = 1
    end
    return redNum
end

--结晶方案切页红点
function GetCrystalToggleRed(panelID)
    local redNum = 0
    local schemeData = GetCrystalSchemeData(panelID)
    if not schemeData then
        return 0
    end
    for featureType, sid in ipairs(schemeData.starPlace) do
        redNum = redNum + GetCrystalRootRed(sid,featureType)
    end
    return redNum
end

--结晶方案槽位红点
function GetCrystalRootRed(sid,featureType)
    local redNum = 0
    redNum = redNum + GetCrystalUpgradeRed(sid) + GetCrystalReplaceRed(sid,featureType)
    return redNum
end

--结晶升星红点
function GetCrystalUpgradeRed(sid)
    local redNum = 0
    if CheckCrystalCanUpgrade(sid) then
        redNum = redNum + 1
    end
    return redNum
end

--结晶替换红点
function GetCrystalReplaceRed(sid,featureType)
    local redNum = 0
    if CheckHasHigherPowerAndUnusedCrystal(sid,featureType) then
        redNum = redNum + 1
    end
    return redNum
end
------------------------------------------------
function _HasField(msg, field)
    return msg[field]
end

function GetAllData()
    return _M
end

function Clear()
    InitData()
end

event.Register(event.USER_DATA_RESET, Clear)
Init()