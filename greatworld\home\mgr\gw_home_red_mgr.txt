﻿---
--- Generated by EmmyLua(https://github.com/EmmyLua)
--- Created by hans<PERSON><PERSON>.
--- DateTime: 2024/8/26 17:33
--- Desc: 家园红点管理
local require = require
local pairs = pairs
local tonumber = tonumber
local gw_home_cfg_util = require "gw_home_cfg_util"
local util = require "util"
local string = require "string"
local game_scheme = require "game_scheme"
local red_const = require "red_const"
local red_system = require "red_system"
local GWConst = require "gw_const"
local GWHomeMgr = require "gw_home_mgr"
local gw_home_building_data = require "gw_home_building_data"
local GWG = GWG
module("gw_home_red_mgr")

function Init()
    red_system.RegisterRedFunc(red_const.Enum.BuildList, CheckBuildListRedPoint)
    red_system.RegisterRedFunc(red_const.Enum.BuildListTogEco, CheckBuildListEcoRedPoint)
    red_system.RegisterRedFunc(red_const.Enum.BuildListTogAffair, CheckBuildListMilitaryRedPoint)
    red_system.RegisterRedFunc(red_const.Enum.BuildListTogDecor, CheckBuildListDecorationRedPoint)

    red_system.RegisterRedFunc(red_const.Enum.Survivor, CheckSurvivorRedPoint)
    red_system.RegisterRedFunc(red_const.Enum.TavernTask, CheckTavernTaskRedPoint)
    
    red_system.RegisterRedFunc(red_const.Enum.SPlusBuilding,CheckSPlusBuilding)
    red_system.RegisterRedFunc(red_const.Enum.SBuilding,CheckSBuilding)
    red_system.RegisterRedFunc(red_const.Enum.ABuilding,CheckABuilding)
end

function CheckSPlusBuilding()
    local num = GWG.GWHomeMgr.buildingData.GetRedNumWithQuality(4)
    return num
end

function CheckSBuilding()
    local num = GWG.GWHomeMgr.buildingData.GetRedNumWithQuality(3)
    return num
end

function CheckABuilding()
    local num = GWG.GWHomeMgr.buildingData.GetRedNumWithQuality(2)
    return num
end

function CheckSurvivorRedPoint()
    return #GWG.GWHomeMgr.survivorData.GetOutsideSurvivorList()
end

--入口红点
--      首次登录：显示可派遣 + 可领取 + 可协助
--      点入界面后：只显示可领取
function CheckTavernTaskRedPoint()
    local ui_tavern_mgr = require "ui_tavern_mgr"
    return ui_tavern_mgr.GetMainEnterRedPoint()
end

function CheckBuildListRedPoint()
    return GetTypeAllIndex()
end
function CheckBuildListEcoRedPoint()
    return GetTypeAllIndex(GWConst.BuildType.Economy)
end
function CheckBuildListMilitaryRedPoint()
    return GetTypeAllIndex(GWConst.BuildType.Military)
end
function CheckBuildListDecorationRedPoint()
    return GetTypeAllIndex(GWConst.BuildType.Decoration)
end

function GetTypeAllIndex(type)
    local allIndex = 0
    local buildTypeLen = game_scheme:BuildingType_nums()
    for i = 0, buildTypeLen - 1 do
        local cfg = game_scheme:BuildingType(i)
        if cfg and cfg.BuildingSubtype and cfg.BuildingSubtype > 0 then
            local data = {}
            data.type = cfg.TypeID
            data.buildId = GWHomeMgr.cfg.GetBuildId(cfg.TypeID)
            data.curBuild = GetCurUserBuildNum(data.buildId)
            data.needBuildId = GWHomeMgr.cfg.GetBuildId(cfg.Preconditions)
            data.level = GWHomeMgr.buildingData.GetBuildingDataMaxLevel(data.needBuildId)
            local cfg_buidType = game_scheme:BuildingType_0(data.type)
            local Arr = string.split(cfg_buidType.buildable, ";")
            data.maxBuild = 0
            for _, v in pairs(Arr) do
                local arr = string.split(v, "#")
                if tonumber(arr[1]) > data.level then
                    break
                end
                data.maxBuild = tonumber(arr[2])
            end


            --建筑建造
            if data.curBuild == 0 and data.maxBuild > 0 then
                local isUpgrade = gw_home_cfg_util.IsBuildUpgradeByPreconditions(data.buildId,1)
                if not isUpgrade then
                    data.isNew = false
                    data.maxBuild = 0
                end
            end
            
            local extraBuildNum = gw_home_cfg_util.GetBuildExtraBuildNum(data.type)
            data.maxBuild = data.maxBuild + extraBuildNum
            
            local canShow = true
            if gw_home_building_data.ShowConditionFunc[data.type] then
                if not gw_home_building_data.ShowConditionFunc[data.type]() then
                    canShow = false
                end
            end
            data.isBuildable = data.maxBuild > data.curBuild and canShow
            if data.isBuildable and (not type or cfg_buidType.BuildingSubtype == type) then
                --allIndex = allIndex + 1
                if type == GWConst.BuildType.Decoration then
                    local unlock = GWG.GWHomeMgr.buildingData.IsDecorateBuildUnlock(data.buildId,data.type)
                    if unlock then
                        allIndex = allIndex +1
                    end
                else
                    if cfg.BuildingSubtype == GWConst.BuildType.Decoration then
                        local unlock = GWG.GWHomeMgr.buildingData.IsDecorateBuildUnlock(data.buildId,data.type)
                        if unlock then
                            allIndex = allIndex +1
                        end
                    else
                        allIndex = allIndex +1
                    end
                end
            end
        end
    end
    return allIndex
end

---获取玩家建筑数量
---@param id number 建筑id
---@return number num 建筑数量
function GetCurUserBuildNum(id)
    local buildList = GWHomeMgr.buildingData.GetBuildingDataListByBuildingID(id)
    local index = util.get_len(buildList)
    return index
end