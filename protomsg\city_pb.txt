-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
local error_code_pb=require("error_code_pb")
local common_new_pb=require("common_new_pb")
local prop_pb=require("prop_pb")
local sandbox_pb=require("sandbox_pb")
module('city_pb')


V1M=V(4,"enBuildingState_Broken",0,0)
V2M=V(4,"enBuildingState_Normal",1,1)
V3M=V(4,"enBuildingState_Repairing",2,2)
V4M=V(4,"enBuildingState_Constructing",3,3)
V5M=V(4,"enBuildingState_Upgrading",4,4)
E1M=E(3,"enBuildingState",".CSMsg.enBuildingState")
V6M=V(4,"enCityFlag_BuildingOpenGift",0,2)
V7M=V(4,"enCityFlag_AreaUnlock",1,4)
V8M=V(4,"enCityFlag_AreaCanBuilding",2,8)
V9M=V(4,"enCityFlag_QueueFullOpen",3,16)
V10M=V(4,"enCityFlag_MilitaryRecruit",4,32)
V11M=V(4,"enCityFlag_MilitaryUpgrade",5,64)
V12M=V(4,"enCityFlag_AreaCompleteAll",6,128)
V13M=V(4,"enCityFlag_CityWaitRepair",7,256)
E2M=E(3,"enCityFlag",".CSMsg.enCityFlag")
V14M=V(4,"enCityProp_HospitalCureID",0,0)
V15M=V(4,"enCityProp_LastBuyCityDefendTime",1,1)
V16M=V(4,"enCityProp_Flag",2,4)
V17M=V(4,"enCityProp_Sid",3,5)
V18M=V(4,"enCityProp_TotalPower",4,20)
V19M=V(4,"enCityProp_TotalFreeAccelerateTime",5,21)
V20M=V(4,"enCityProp_TotalBeHelpTimes",6,22)
V21M=V(4,"enCityProp_TotalHospitalCapacity",7,23)
V22M=V(4,"enCityProp_TotalTrainCenterCapacity",8,24)
V23M=V(4,"enCityProp_HeroMaxLevel",9,25)
V24M=V(4,"enCityProp_FreeSummonResetTime",10,26)
V25M=V(4,"enCityProp_MovingSpeedUpN",11,27)
V26M=V(4,"enCityProp_CurDefendValue",12,28)
V27M=V(4,"enCityProp_WallBeginTime",13,29)
V28M=V(4,"enCityProp_WallFireEndTime",14,30)
V29M=V(4,"enCityProp_ResearchSpeedUpRate1",15,31)
V30M=V(4,"enCityProp_ResearchSpeedUpRate2",16,32)
V31M=V(4,"enCityProp_ResearchSpeedUpTime1",17,33)
V32M=V(4,"enCityProp_ResearchSpeedUpTime2",18,34)
V33M=V(4,"enCityProp_MovingSpeedUpN2",19,35)
V34M=V(4,"enCityProp_MovingSpeedUpN3",20,36)
V35M=V(4,"enCityProp_MovingSpeedUpN4",21,37)
V36M=V(4,"enCityProp_TotalFoodProtect",22,38)
V37M=V(4,"enCityProp_TotalIronProtect",23,39)
V38M=V(4,"enCityProp_TotalGoldProtect",24,40)
V39M=V(4,"enCityProp_TotalReduceBuildNeedN",25,41)
V40M=V(4,"enCityProp_TotalCureSpeedUpN",26,42)
V41M=V(4,"enCityProp_ResearchResourceRate1",27,43)
V42M=V(4,"enCityProp_ResearchResourceRate2",28,44)
V43M=V(4,"enCityProp_WorkerFreeSummonCD",29,45)
V44M=V(4,"enCityProp_EquimentMakeSpeedUp",30,46)
V45M=V(4,"enCityProp_EquimentMakeCoinReduce",31,47)
V46M=V(4,"enCityProp_ResourceLimitTimeUp",32,48)
V47M=V(4,"enCityProp_ResourceProduceUpN",33,49)
V48M=V(4,"enCityProp_MaxBuildingPower",34,50)
V49M=V(4,"enCityProp_HiddenQueueNum",35,51)
V50M=V(4,"enCityProp_HiddenRewardNum",36,52)
V51M=V(4,"enCityProp_AllianceHelpAdditionTime",37,53)
V52M=V(4,"enCityProp_AcornPubCanSendTaskCount",38,54)
V53M=V(4,"enCityProp_TotalConfigPower",39,55)
V54M=V(4,"enCityProp_RadarMissionBox",40,56)
V55M=V(4,"enCityProp_RadarMissionBoxMax",41,57)
V56M=V(4,"enCityProp_RadarMissionBoxId",42,58)
V57M=V(4,"enCityProp_CityAddAtk",43,59)
V58M=V(4,"enCityProp_CityAddSoldier",44,60)
V59M=V(4,"enCityProp_Max",45,61)
E3M=E(3,"enCityProp",".CSMsg.enCityProp")
V60M=V(4,"enBuildingType_Main",0,1)
V61M=V(4,"enBuildingType_Farm",1,2)
V62M=V(4,"enBuildingType_Iron",2,3)
V63M=V(4,"enBuildingType_Gold",3,4)
V64M=V(4,"enBuildingType_Exp",4,5)
V65M=V(4,"enBuildingType_Military",5,6)
V66M=V(4,"enBuildingType_TrainCenter",6,7)
V67M=V(4,"enBuildingType_Hospital",7,8)
V68M=V(4,"enBuildingType_Formation1",8,9)
V69M=V(4,"enBuildingType_WorkerHouse",9,10)
V70M=V(4,"enBuildingType_Garden",10,11)
V71M=V(4,"enBuildingType_Summon",11,12)
V72M=V(4,"enBuildingType_Shop",12,13)
V73M=V(4,"enBuildingType_WorkerCenter",13,14)
V74M=V(4,"enBuildingType_Alliance",14,15)
V75M=V(4,"enBuildingType_Wall",15,16)
V76M=V(4,"enBuildingType_Formation2",16,17)
V77M=V(4,"enBuildingType_Formation3",17,18)
V78M=V(4,"enBuildingType_Formation4",18,19)
V79M=V(4,"enBuildingType_Research1",19,20)
V80M=V(4,"enBuildingType_Research2",20,21)
V81M=V(4,"enBuildingType_FarmWareHouse",21,22)
V82M=V(4,"enBuildingType_IronWareHouse",22,23)
V83M=V(4,"enBuildingType_GoldWareHouse",23,24)
V84M=V(4,"enBuildingType_SmelterFactory",24,25)
V85M=V(4,"enBuildingType_Material",25,26)
V86M=V(4,"enBuildingType_Equip",26,27)
V87M=V(4,"enBuildingType_DroneFactory",27,28)
V88M=V(4,"enBuildingType_DroneCenter",28,29)
V89M=V(4,"enBuildingType_Reconnaissance",29,30)
V90M=V(4,"enBuildingType_Radar",30,31)
V91M=V(4,"enBuildingType_TankCenter",31,32)
V92M=V(4,"enBuildingType_MissileCenter",32,33)
V93M=V(4,"enBuildingType_AircraftCenter",33,34)
V94M=V(4,"enBuildingType_RoadA",34,35)
V95M=V(4,"enBuildingType_RoadB",35,36)
V96M=V(4,"enBuildingType_CarriageCenter",36,38)
V97M=V(4,"enBuildingType_ResourceTruck",37,39)
V98M=V(4,"enBuildingType_AirshipStation",38,40)
V99M=V(4,"enBuildingType_OrnamentCollection",39,42)
V100M=V(4,"enBuildingType_AcornPub",40,43)
V101M=V(4,"enBuildingType_FristRecharge",41,44)
V102M=V(4,"enBuildingType_Arena",42,45)
V103M=V(4,"enBuildingType_DroneStar",43,46)
V104M=V(4,"enBuildingType_DogHouse",44,101)
V105M=V(4,"enBuildingType_BronzeCarrier",45,1001)
V106M=V(4,"enBuildingType_BronzeAirplane",46,1002)
V107M=V(4,"enBuildingType_Pacifism",47,1003)
V108M=V(4,"enBuildingType_BronzeTank",48,1004)
V109M=V(4,"enBuildingType_SilverWarrior",49,1011)
V110M=V(4,"enBuildingType_SilverWarplane",50,1012)
V111M=V(4,"enBuildingType_SilverDestroyer",51,1013)
V112M=V(4,"enBuildingType_SilverRocket",52,1014)
V113M=V(4,"enBuildingType_Legend",53,1015)
V114M=V(4,"enBuildingType_Tire",54,1016)
V115M=V(4,"enBuildingType_Pyramid",55,1101)
V116M=V(4,"enBuildingType_GoldenMobileTeam",56,1102)
V117M=V(4,"enBuildingType_GoldenMarshalStatue",57,1103)
V118M=V(4,"enBuildingType_GoldenTank",58,1104)
V119M=V(4,"enBuildingType_GoldenMissileVehicle",59,1105)
V120M=V(4,"enBuildingType_GoldenBomber",60,1106)
V121M=V(4,"enBuildingType_Belfry",61,1107)
V122M=V(4,"enBuildingType_IronTower",62,1108)
V123M=V(4,"enBuildingType_FerrisWheel",63,1109)
V124M=V(4,"enBuildingType_NeonLight",64,1110)
V125M=V(4,"enBuildingType_LadyLiberty",65,1111)
V126M=V(4,"enBuildingType_MonumentWarriors",66,1112)
V127M=V(4,"enBuildingType_MilitaryIndustrial",67,1113)
V128M=V(4,"enBuildingType_VictoryTower",68,1114)
V129M=V(4,"enBuildingType_ThroneBlood",69,1115)
V130M=V(4,"enBuildingType_Max",70,1116)
E4M=E(3,"enBuildingType",".CSMsg.enBuildingType")
V131M=V(4,"enCityMapItemType_Building",0,1)
V132M=V(4,"enCityMapItemType_Event",1,2)
E5M=E(3,"enCityMapItemType",".CSMsg.enCityMapItemType")
V133M=V(4,"enCityBuildingFixCondType_Building",0,1)
V134M=V(4,"enCityBuildingFixCondType_Story",1,2)
V135M=V(4,"enCityBuildingFixCondType_MiniGame",2,3)
V136M=V(4,"enCityBuildingFixCondType_Gift",3,4)
E6M=E(3,"enCityBuildingFixCondType",".CSMsg.enCityBuildingFixCondType")
V137M=V(4,"enCityAreaUnlockType_Building",0,1)
V138M=V(4,"enCityAreaUnlockType_Story",1,2)
E7M=E(3,"enCityAreaUnlockType",".CSMsg.enCityAreaUnlockType")
V139M=V(4,"enSoldierOfflineDataType_InCity",0,1)
V140M=V(4,"enSoldierOfflineDataType_OutCity",1,2)
V141M=V(4,"enSoldierOfflineDataType_Hurt",2,3)
E8M=E(3,"enSoldierOfflineDataType",".CSMsg.enSoldierOfflineDataType")
V142M=V(4,"enRewardType_Survivor",0,1)
E9M=E(3,"enCityRewardType",".CSMsg.enCityRewardType")
V143M=V(4,"enBuildingSubtype_Economy",0,1)
V144M=V(4,"enBuildingSubtype_Military",1,2)
V145M=V(4,"enBuildingSubtype_Decorate",2,3)
E10M=E(3,"enBuildingSubtype",".CSMsg.enBuildingSubtype")
V146M=V(4,"enTeamTroopSubType_City",0,1)
V147M=V(4,"enTeamTroopSubType_Detect",1,2)
E11M=E(3,"enTeamTroopSubType",".CSMsg.enTeamTroopSubType")
F1D=F(2,"nSoldierID",".CSMsg.TCityMilitary.nSoldierID",1,0,2,false,0,5,1)
F2D=F(2,"nCount",".CSMsg.TCityMilitary.nCount",2,1,2,false,0,5,1)
F3D=F(2,"uDoneTime",".CSMsg.TCityMilitary.uDoneTime",3,2,2,false,0,13,3)
F4D=F(2,"uStartTime",".CSMsg.TCityMilitary.uStartTime",4,3,2,false,0,13,3)
M1G=D(1,"TCityMilitary",".CSMsg.TCityMilitary",false,{},{},nil,{})
F5D=F(2,"uLastGetTime",".CSMsg.TCityResource.uLastGetTime",1,0,2,false,0,13,3)
F6D=F(2,"uLastChangeTime",".CSMsg.TCityResource.uLastChangeTime",2,1,2,false,0,13,3)
F7D=F(2,"nResourceCount",".CSMsg.TCityResource.nResourceCount",3,2,2,false,0,13,3)
M2G=D(1,"TCityResource",".CSMsg.TCityResource",false,{},{},nil,{})
F8D=F(2,"uSid",".CSMsg.TCityBuilding.uSid",1,0,2,false,0,13,3)
F9D=F(2,"nBuildingID",".CSMsg.TCityBuilding.nBuildingID",2,1,2,false,0,5,1)
F10D=F(2,"nLevel",".CSMsg.TCityBuilding.nLevel",3,2,2,false,0,5,1)
F11D=F(2,"x",".CSMsg.TCityBuilding.x",4,3,2,false,0,5,1)
F12D=F(2,"y",".CSMsg.TCityBuilding.y",5,4,2,false,0,5,1)
F13D=F(2,"nState",".CSMsg.TCityBuilding.nState",7,5,2,false,0,5,1)
F14D=F(2,"uFlag",".CSMsg.TCityBuilding.uFlag",8,6,2,false,0,13,3)
F15D=F(2,"military",".CSMsg.TCityBuilding.military",9,7,1,false,nil,11,10)
F16D=F(2,"resource",".CSMsg.TCityBuilding.resource",10,8,1,false,nil,11,10)
F17D=F(2,"uMapUnitID",".CSMsg.TCityBuilding.uMapUnitID",11,9,2,false,0,13,3)
M3G=D(1,"TCityBuilding",".CSMsg.TCityBuilding",false,{},{},nil,{})
F18D=F(2,"nID",".CSMsg.TCityArea.nID",1,0,2,false,0,5,1)
F19D=F(2,"uFlag",".CSMsg.TCityArea.uFlag",2,1,2,false,0,13,3)
F20D=F(2,"nDoneEventID",".CSMsg.TCityArea.nDoneEventID",3,2,3,false,{},5,1)
M4G=D(1,"TCityArea",".CSMsg.TCityArea",false,{},{},nil,{})
F21D=F(2,"uFlag",".CSMsg.TCityBuildQueue.uFlag",1,0,2,false,0,13,3)
F22D=F(2,"uOverTime",".CSMsg.TCityBuildQueue.uOverTime",2,1,2,false,0,13,3)
F23D=F(2,"nID",".CSMsg.TCityBuildQueue.nID",3,2,2,false,0,13,3)
F24D=F(2,"uDoneTime",".CSMsg.TCityBuildQueue.uDoneTime",4,3,2,false,0,13,3)
F25D=F(2,"uSid",".CSMsg.TCityBuildQueue.uSid",5,4,2,false,0,13,3)
M5G=D(1,"TCityBuildQueue",".CSMsg.TCityBuildQueue",false,{},{},nil,{})
F26D=F(2,"nSoldierID",".CSMsg.TCityHospitalSoldier.nSoldierID",1,0,2,false,0,5,1)
F27D=F(2,"nCount",".CSMsg.TCityHospitalSoldier.nCount",2,1,2,false,0,5,1)
M6G=D(1,"TCityHospitalSoldier",".CSMsg.TCityHospitalSoldier",false,{},{},nil,{})
F28D=F(2,"uDoneTime",".CSMsg.TCityHospital.uDoneTime",1,0,2,false,0,13,3)
F29D=F(2,"soldiers",".CSMsg.TCityHospital.soldiers",2,1,3,false,{},11,10)
M7G=D(1,"TCityHospital",".CSMsg.TCityHospital",false,{},{},nil,{})
F30D=F(2,"nSoldierID",".CSMsg.TCitySoldier.nSoldierID",1,0,2,false,0,5,1)
F31D=F(2,"nInCount",".CSMsg.TCitySoldier.nInCount",2,1,2,false,0,5,1)
F32D=F(2,"nOutCount",".CSMsg.TCitySoldier.nOutCount",3,2,2,false,0,5,1)
F33D=F(2,"nHurtCount",".CSMsg.TCitySoldier.nHurtCount",4,3,2,false,0,5,1)
M8G=D(1,"TCitySoldier",".CSMsg.TCitySoldier",false,{},{},nil,{})
F34D=F(2,"soldiers",".CSMsg.TCityTrainningCenter.soldiers",2,0,3,false,{},11,10)
M9G=D(1,"TCityTrainningCenter",".CSMsg.TCityTrainningCenter",false,{},{},nil,{})
F35D=F(2,"nID",".CSMsg.TCityWorker.nID",1,0,2,false,0,5,1)
F36D=F(2,"uLevel",".CSMsg.TCityWorker.uLevel",2,1,2,false,0,13,3)
F37D=F(2,"uInSid",".CSMsg.TCityWorker.uInSid",3,2,2,false,0,13,3)
M10G=D(1,"TCityWorker",".CSMsg.TCityWorker",false,{},{},nil,{})
F38D=F(2,"nID",".CSMsg.TRewardQueue.nID",1,0,2,false,0,5,1)
F39D=F(2,"nType",".CSMsg.TRewardQueue.nType",2,1,2,false,0,5,1)
M11G=D(1,"TRewardQueue",".CSMsg.TRewardQueue",false,{},{},nil,{})
M12G=D(1,"TMSG_CITY_GET_ALLINFO_REQ",".CSMsg.TMSG_CITY_GET_ALLINFO_REQ",false,{},{},{},{})
F40D=F(2,"prop",".CSMsg.TMSG_CITY_GET_ALLINFO_RSP.prop",1,0,3,false,{},11,10)
F41D=F(2,"area",".CSMsg.TMSG_CITY_GET_ALLINFO_RSP.area",2,1,3,false,{},11,10)
F42D=F(2,"building",".CSMsg.TMSG_CITY_GET_ALLINFO_RSP.building",3,2,3,false,{},11,10)
F43D=F(2,"buildQueue",".CSMsg.TMSG_CITY_GET_ALLINFO_RSP.buildQueue",4,3,3,false,{},11,10)
F44D=F(2,"trainning",".CSMsg.TMSG_CITY_GET_ALLINFO_RSP.trainning",5,4,1,false,nil,11,10)
F45D=F(2,"hospital",".CSMsg.TMSG_CITY_GET_ALLINFO_RSP.hospital",6,5,1,false,nil,11,10)
F46D=F(2,"worker",".CSMsg.TMSG_CITY_GET_ALLINFO_RSP.worker",7,6,3,false,{},11,10)
F47D=F(2,"reward",".CSMsg.TMSG_CITY_GET_ALLINFO_RSP.reward",8,7,3,false,{},11,10)
M13G=D(1,"TMSG_CITY_GET_ALLINFO_RSP",".CSMsg.TMSG_CITY_GET_ALLINFO_RSP",false,{},{},nil,{})
F48D=F(2,"nBuildingID",".CSMsg.TMSG_CITY_BUILD_A_BUILDING_REQ.nBuildingID",1,0,2,false,0,5,1)
F49D=F(2,"x",".CSMsg.TMSG_CITY_BUILD_A_BUILDING_REQ.x",2,1,2,false,0,5,1)
F50D=F(2,"y",".CSMsg.TMSG_CITY_BUILD_A_BUILDING_REQ.y",3,2,2,false,0,5,1)
M15G=D(1,"TMSG_CITY_BUILD_A_BUILDING_REQ",".CSMsg.TMSG_CITY_BUILD_A_BUILDING_REQ",false,{},{},nil,{})
F51D=F(2,"errCode",".CSMsg.TMSG_CITY_BUILD_A_BUILDING_RSP.errCode",1,0,2,false,nil,14,8)
F52D=F(2,"uSid",".CSMsg.TMSG_CITY_BUILD_A_BUILDING_RSP.uSid",2,1,1,false,0,13,3)
M16G=D(1,"TMSG_CITY_BUILD_A_BUILDING_RSP",".CSMsg.TMSG_CITY_BUILD_A_BUILDING_RSP",false,{},{},nil,{})
F53D=F(2,"uSid",".CSMsg.TMSG_CITY_REPAIR_A_BUILDING_REQ.uSid",1,0,2,false,0,13,3)
F54D=F(2,"bUnusualCreate",".CSMsg.TMSG_CITY_REPAIR_A_BUILDING_REQ.bUnusualCreate",2,1,1,false,false,8,7)
M18G=D(1,"TMSG_CITY_REPAIR_A_BUILDING_REQ",".CSMsg.TMSG_CITY_REPAIR_A_BUILDING_REQ",false,{},{},nil,{})
F55D=F(2,"errCode",".CSMsg.TMSG_CITY_REPAIR_A_BUILDING_RSP.errCode",1,0,2,false,nil,14,8)
F56D=F(2,"uSid",".CSMsg.TMSG_CITY_REPAIR_A_BUILDING_RSP.uSid",2,1,1,false,0,13,3)
M19G=D(1,"TMSG_CITY_REPAIR_A_BUILDING_RSP",".CSMsg.TMSG_CITY_REPAIR_A_BUILDING_RSP",false,{},{},nil,{})
F57D=F(2,"uSid",".CSMsg.TMSG_CITY_GET_BUILDING_OPENGIFT_REQ.uSid",1,0,2,false,0,13,3)
F58D=F(2,"bUnusualCreate",".CSMsg.TMSG_CITY_GET_BUILDING_OPENGIFT_REQ.bUnusualCreate",2,1,1,false,false,8,7)
M20G=D(1,"TMSG_CITY_GET_BUILDING_OPENGIFT_REQ",".CSMsg.TMSG_CITY_GET_BUILDING_OPENGIFT_REQ",false,{},{},nil,{})
F59D=F(2,"errCode",".CSMsg.TMSG_CITY_GET_BUILDING_OPENGIFT_RSP.errCode",1,0,2,false,nil,14,8)
F60D=F(2,"uSid",".CSMsg.TMSG_CITY_GET_BUILDING_OPENGIFT_RSP.uSid",2,1,1,false,0,13,3)
F61D=F(2,"help",".CSMsg.TMSG_CITY_GET_BUILDING_OPENGIFT_RSP.help",3,2,3,false,{},11,10)
M21G=D(1,"TMSG_CITY_GET_BUILDING_OPENGIFT_RSP",".CSMsg.TMSG_CITY_GET_BUILDING_OPENGIFT_RSP",false,{},{},nil,{})
F62D=F(2,"uSid",".CSMsg.TMSG_CITY_UPGRADE_A_BUILDING_REQ.uSid",1,0,2,false,0,13,3)
M23G=D(1,"TMSG_CITY_UPGRADE_A_BUILDING_REQ",".CSMsg.TMSG_CITY_UPGRADE_A_BUILDING_REQ",false,{},{},nil,{})
F63D=F(2,"errCode",".CSMsg.TMSG_CITY_UPGRADE_A_BUILDING_RSP.errCode",1,0,2,false,nil,14,8)
F64D=F(2,"uSid",".CSMsg.TMSG_CITY_UPGRADE_A_BUILDING_RSP.uSid",2,1,1,false,0,13,3)
M24G=D(1,"TMSG_CITY_UPGRADE_A_BUILDING_RSP",".CSMsg.TMSG_CITY_UPGRADE_A_BUILDING_RSP",false,{},{},nil,{})
F65D=F(2,"uSid",".CSMsg.TMSG_CITY_GET_BUILDING_RESOURCE_REQ.uSid",1,0,2,false,0,13,3)
F66D=F(2,"bGetAll",".CSMsg.TMSG_CITY_GET_BUILDING_RESOURCE_REQ.bGetAll",2,1,1,false,false,8,7)
M25G=D(1,"TMSG_CITY_GET_BUILDING_RESOURCE_REQ",".CSMsg.TMSG_CITY_GET_BUILDING_RESOURCE_REQ",false,{},{},nil,{})
F67D=F(2,"errCode",".CSMsg.TMSG_CITY_GET_BUILDING_RESOURCE_RSP.errCode",1,0,2,false,nil,14,8)
F68D=F(2,"uSid",".CSMsg.TMSG_CITY_GET_BUILDING_RESOURCE_RSP.uSid",2,1,3,false,{},13,3)
F69D=F(2,"nResourceID",".CSMsg.TMSG_CITY_GET_BUILDING_RESOURCE_RSP.nResourceID",3,2,1,false,0,5,1)
F70D=F(2,"nCount",".CSMsg.TMSG_CITY_GET_BUILDING_RESOURCE_RSP.nCount",4,3,3,false,{},5,1)
M26G=D(1,"TMSG_CITY_GET_BUILDING_RESOURCE_RSP",".CSMsg.TMSG_CITY_GET_BUILDING_RESOURCE_RSP",false,{},{},nil,{})
F71D=F(2,"uSid",".CSMsg.TMSG_CITY_CHANGE_BUILDING_LOCATION_REQ.uSid",1,0,2,false,0,13,3)
F72D=F(2,"x",".CSMsg.TMSG_CITY_CHANGE_BUILDING_LOCATION_REQ.x",2,1,2,false,0,5,1)
F73D=F(2,"y",".CSMsg.TMSG_CITY_CHANGE_BUILDING_LOCATION_REQ.y",3,2,2,false,0,5,1)
M27G=D(1,"TMSG_CITY_CHANGE_BUILDING_LOCATION_REQ",".CSMsg.TMSG_CITY_CHANGE_BUILDING_LOCATION_REQ",false,{},{},nil,{})
F74D=F(2,"errCode",".CSMsg.TMSG_CITY_CHANGE_BUILDING_LOCATION_RSP.errCode",1,0,2,false,nil,14,8)
F75D=F(2,"uSid",".CSMsg.TMSG_CITY_CHANGE_BUILDING_LOCATION_RSP.uSid",2,1,1,false,0,13,3)
M28G=D(1,"TMSG_CITY_CHANGE_BUILDING_LOCATION_RSP",".CSMsg.TMSG_CITY_CHANGE_BUILDING_LOCATION_RSP",false,{},{},nil,{})
F76D=F(2,"uSid",".CSMsg.TMSG_CITY_MILITARY_RECRUIT_SOLDIER_REQ.uSid",1,0,2,false,0,13,3)
F77D=F(2,"nSoldierID",".CSMsg.TMSG_CITY_MILITARY_RECRUIT_SOLDIER_REQ.nSoldierID",2,1,2,false,0,5,1)
F78D=F(2,"nCount",".CSMsg.TMSG_CITY_MILITARY_RECRUIT_SOLDIER_REQ.nCount",3,2,2,false,0,5,1)
M29G=D(1,"TMSG_CITY_MILITARY_RECRUIT_SOLDIER_REQ",".CSMsg.TMSG_CITY_MILITARY_RECRUIT_SOLDIER_REQ",false,{},{},nil,{})
F79D=F(2,"errCode",".CSMsg.TMSG_CITY_MILITARY_RECRUIT_SOLDIER_RSP.errCode",1,0,2,false,nil,14,8)
F80D=F(2,"uSid",".CSMsg.TMSG_CITY_MILITARY_RECRUIT_SOLDIER_RSP.uSid",2,1,1,false,0,13,3)
M30G=D(1,"TMSG_CITY_MILITARY_RECRUIT_SOLDIER_RSP",".CSMsg.TMSG_CITY_MILITARY_RECRUIT_SOLDIER_RSP",false,{},{},nil,{})
F81D=F(2,"uSid",".CSMsg.TMSG_CITY_MILITARY_GET_SOLDIER_REQ.uSid",1,0,2,false,0,13,3)
M31G=D(1,"TMSG_CITY_MILITARY_GET_SOLDIER_REQ",".CSMsg.TMSG_CITY_MILITARY_GET_SOLDIER_REQ",false,{},{},nil,{})
F82D=F(2,"errCode",".CSMsg.TMSG_CITY_MILITARY_GET_SOLDIER_RSP.errCode",1,0,2,false,nil,14,8)
F83D=F(2,"uSid",".CSMsg.TMSG_CITY_MILITARY_GET_SOLDIER_RSP.uSid",2,1,1,false,0,13,3)
F84D=F(2,"nCount",".CSMsg.TMSG_CITY_MILITARY_GET_SOLDIER_RSP.nCount",3,2,1,false,0,13,3)
M32G=D(1,"TMSG_CITY_MILITARY_GET_SOLDIER_RSP",".CSMsg.TMSG_CITY_MILITARY_GET_SOLDIER_RSP",false,{},{},nil,{})
F85D=F(2,"uSid",".CSMsg.TMSG_CITY_MILITARY_UPGRADE_SOLDIER_REQ.uSid",1,0,2,false,0,13,3)
F86D=F(2,"nFromSoldierID",".CSMsg.TMSG_CITY_MILITARY_UPGRADE_SOLDIER_REQ.nFromSoldierID",2,1,2,false,0,5,1)
F87D=F(2,"nCount",".CSMsg.TMSG_CITY_MILITARY_UPGRADE_SOLDIER_REQ.nCount",3,2,2,false,0,5,1)
F88D=F(2,"nToSoldierID",".CSMsg.TMSG_CITY_MILITARY_UPGRADE_SOLDIER_REQ.nToSoldierID",4,3,2,false,0,5,1)
M33G=D(1,"TMSG_CITY_MILITARY_UPGRADE_SOLDIER_REQ",".CSMsg.TMSG_CITY_MILITARY_UPGRADE_SOLDIER_REQ",false,{},{},nil,{})
F89D=F(2,"errCode",".CSMsg.TMSG_CITY_MILITARY_UPGRADE_SOLDIER_RSP.errCode",1,0,2,false,nil,14,8)
F90D=F(2,"uSid",".CSMsg.TMSG_CITY_MILITARY_UPGRADE_SOLDIER_RSP.uSid",2,1,1,false,0,13,3)
M34G=D(1,"TMSG_CITY_MILITARY_UPGRADE_SOLDIER_RSP",".CSMsg.TMSG_CITY_MILITARY_UPGRADE_SOLDIER_RSP",false,{},{},nil,{})
F91D=F(2,"cureSoldier",".CSMsg.TMSG_CITY_HOSPITAL_CURE_SOLDIER_REQ.cureSoldier",1,0,3,false,{},11,10)
F92D=F(2,"type",".CSMsg.TMSG_CITY_HOSPITAL_CURE_SOLDIER_REQ.type",2,1,1,false,nil,14,8)
M35G=D(1,"TMSG_CITY_HOSPITAL_CURE_SOLDIER_REQ",".CSMsg.TMSG_CITY_HOSPITAL_CURE_SOLDIER_REQ",false,{},{},nil,{})
F93D=F(2,"errCode",".CSMsg.TMSG_CITY_HOSPITAL_CURE_SOLDIER_RSP.errCode",1,0,2,false,nil,14,8)
F94D=F(2,"type",".CSMsg.TMSG_CITY_HOSPITAL_CURE_SOLDIER_RSP.type",2,1,1,false,nil,14,8)
M37G=D(1,"TMSG_CITY_HOSPITAL_CURE_SOLDIER_RSP",".CSMsg.TMSG_CITY_HOSPITAL_CURE_SOLDIER_RSP",false,{},{},nil,{})
F95D=F(2,"type",".CSMsg.TMSG_CITY_HOSPITAL_GET_SOLDIER_REQ.type",1,0,1,false,nil,14,8)
M38G=D(1,"TMSG_CITY_HOSPITAL_GET_SOLDIER_REQ",".CSMsg.TMSG_CITY_HOSPITAL_GET_SOLDIER_REQ",false,{},{},nil,{})
F96D=F(2,"errCode",".CSMsg.TMSG_CITY_HOSPITAL_GET_SOLDIER_RSP.errCode",1,0,2,false,nil,14,8)
F97D=F(2,"uGetSoldierNum",".CSMsg.TMSG_CITY_HOSPITAL_GET_SOLDIER_RSP.uGetSoldierNum",2,1,1,false,0,13,3)
F98D=F(2,"type",".CSMsg.TMSG_CITY_HOSPITAL_GET_SOLDIER_RSP.type",3,2,1,false,nil,14,8)
M39G=D(1,"TMSG_CITY_HOSPITAL_GET_SOLDIER_RSP",".CSMsg.TMSG_CITY_HOSPITAL_GET_SOLDIER_RSP",false,{},{},nil,{})
F99D=F(2,"building",".CSMsg.TMSG_CITY_BUILDING_UPDATE_NTF.building",1,0,3,false,{},11,10)
M40G=D(1,"TMSG_CITY_BUILDING_UPDATE_NTF",".CSMsg.TMSG_CITY_BUILDING_UPDATE_NTF",false,{},{},nil,{})
F100D=F(2,"buildQueue",".CSMsg.TMSG_CITY_BUILDQUEUE_UPDATE_NTF.buildQueue",1,0,3,false,{},11,10)
M41G=D(1,"TMSG_CITY_BUILDQUEUE_UPDATE_NTF",".CSMsg.TMSG_CITY_BUILDQUEUE_UPDATE_NTF",false,{},{},nil,{})
F101D=F(2,"area",".CSMsg.TMSG_CITY_AREA_UPDATE_NTF.area",1,0,3,false,{},11,10)
M42G=D(1,"TMSG_CITY_AREA_UPDATE_NTF",".CSMsg.TMSG_CITY_AREA_UPDATE_NTF",false,{},{},nil,{})
F102D=F(2,"trainning",".CSMsg.TMSG_CITY_TRAINNINGCENTER_UPDATE_NTF.trainning",1,0,2,false,nil,11,10)
F103D=F(2,"type",".CSMsg.TMSG_CITY_TRAINNINGCENTER_UPDATE_NTF.type",2,1,1,false,nil,14,8)
M43G=D(1,"TMSG_CITY_TRAINNINGCENTER_UPDATE_NTF",".CSMsg.TMSG_CITY_TRAINNINGCENTER_UPDATE_NTF",false,{},{},nil,{})
F104D=F(2,"hospital",".CSMsg.TMSG_CITY_HOSPITAL_UPDATE_NTF.hospital",1,0,2,false,nil,11,10)
F105D=F(2,"type",".CSMsg.TMSG_CITY_HOSPITAL_UPDATE_NTF.type",2,1,1,false,nil,14,8)
M44G=D(1,"TMSG_CITY_HOSPITAL_UPDATE_NTF",".CSMsg.TMSG_CITY_HOSPITAL_UPDATE_NTF",false,{},{},nil,{})
F106D=F(2,"uSid",".CSMsg.TMSG_CITY_WALL_FIRE_FIGHTING_REQ.uSid",1,0,2,false,0,13,3)
M45G=D(1,"TMSG_CITY_WALL_FIRE_FIGHTING_REQ",".CSMsg.TMSG_CITY_WALL_FIRE_FIGHTING_REQ",false,{},{},nil,{})
F107D=F(2,"errCode",".CSMsg.TMSG_CITY_WALL_FIRE_FIGHTING_RSP.errCode",1,0,2,false,nil,14,8)
F108D=F(2,"uSid",".CSMsg.TMSG_CITY_WALL_FIRE_FIGHTING_RSP.uSid",2,1,1,false,0,13,3)
M46G=D(1,"TMSG_CITY_WALL_FIRE_FIGHTING_RSP",".CSMsg.TMSG_CITY_WALL_FIRE_FIGHTING_RSP",false,{},{},nil,{})
F109D=F(2,"uSid",".CSMsg.TMSG_CITY_WALL_BUY_CITYDEFENSE_REQ.uSid",1,0,2,false,0,13,3)
M47G=D(1,"TMSG_CITY_WALL_BUY_CITYDEFENSE_REQ",".CSMsg.TMSG_CITY_WALL_BUY_CITYDEFENSE_REQ",false,{},{},nil,{})
F110D=F(2,"errCode",".CSMsg.TMSG_CITY_WALL_BUY_CITYDEFENSE_RSP.errCode",1,0,2,false,0,13,3)
F111D=F(2,"uSid",".CSMsg.TMSG_CITY_WALL_BUY_CITYDEFENSE_RSP.uSid",2,1,1,false,0,13,3)
M48G=D(1,"TMSG_CITY_WALL_BUY_CITYDEFENSE_RSP",".CSMsg.TMSG_CITY_WALL_BUY_CITYDEFENSE_RSP",false,{},{},nil,{})
F112D=F(2,"lineUp",".CSMsg.TCityBattleLineUp.lineUp",1,0,2,false,nil,11,10)
M49G=D(1,"TCityBattleLineUp",".CSMsg.TCityBattleLineUp",false,{},{},nil,{})
F113D=F(2,"teamIndex",".CSMsg.TMSG_CITY_TROOP_IS_IDLE_REQ.teamIndex",1,0,2,false,0,5,1)
M51G=D(1,"TMSG_CITY_TROOP_IS_IDLE_REQ",".CSMsg.TMSG_CITY_TROOP_IS_IDLE_REQ",false,{},{},nil,{})
F114D=F(2,"errCode",".CSMsg.TMSG_CITY_TROOP_IS_IDLE_RSP.errCode",1,0,2,false,0,5,1)
F115D=F(2,"teamIndex",".CSMsg.TMSG_CITY_TROOP_IS_IDLE_RSP.teamIndex",2,1,2,false,0,5,1)
F116D=F(2,"bIdle",".CSMsg.TMSG_CITY_TROOP_IS_IDLE_RSP.bIdle",3,2,1,false,0,5,1)
M52G=D(1,"TMSG_CITY_TROOP_IS_IDLE_RSP",".CSMsg.TMSG_CITY_TROOP_IS_IDLE_RSP",false,{},{},nil,{})
F117D=F(2,"lineUp",".CSMsg.TMSG_CITY_SAVE_TROOP_REQ.lineUp",1,0,2,false,nil,11,10)
F118D=F(2,"teamIndex",".CSMsg.TMSG_CITY_SAVE_TROOP_REQ.teamIndex",2,1,2,false,0,5,1)
M53G=D(1,"TMSG_CITY_SAVE_TROOP_REQ",".CSMsg.TMSG_CITY_SAVE_TROOP_REQ",false,{},{},nil,{})
F119D=F(2,"errCode",".CSMsg.TMSG_CITY_SAVE_TROOP_RSP.errCode",1,0,2,false,0,5,1)
M55G=D(1,"TMSG_CITY_SAVE_TROOP_RSP",".CSMsg.TMSG_CITY_SAVE_TROOP_RSP",false,{},{},nil,{})
F120D=F(2,"teamIndex",".CSMsg.TMSG_CITY_GET_TROOP_REQ.teamIndex",1,0,2,false,0,5,1)
M56G=D(1,"TMSG_CITY_GET_TROOP_REQ",".CSMsg.TMSG_CITY_GET_TROOP_REQ",false,{},{},nil,{})
F121D=F(2,"errCode",".CSMsg.TMSG_CITY_GET_TROOP_RSP.errCode",1,0,2,false,0,5,1)
F122D=F(2,"lineUp",".CSMsg.TMSG_CITY_GET_TROOP_RSP.lineUp",2,1,1,false,nil,11,10)
M57G=D(1,"TMSG_CITY_GET_TROOP_RSP",".CSMsg.TMSG_CITY_GET_TROOP_RSP",false,{},{},nil,{})
F123D=F(2,"sxTeamType",".CSMsg.TMSG_CITY_GET_ALL_TROOP_REQ.sxTeamType",1,0,1,false,0,5,1)
M58G=D(1,"TMSG_CITY_GET_ALL_TROOP_REQ",".CSMsg.TMSG_CITY_GET_ALL_TROOP_REQ",false,{},{},nil,{})
F124D=F(2,"errCode",".CSMsg.TMSG_CITY_GET_ALL_TROOP_RSP.errCode",1,0,2,false,0,5,1)
F125D=F(2,"lineUp",".CSMsg.TMSG_CITY_GET_ALL_TROOP_RSP.lineUp",2,1,3,false,{},11,10)
F126D=F(2,"sxTeamType",".CSMsg.TMSG_CITY_GET_ALL_TROOP_RSP.sxTeamType",3,2,1,false,0,5,1)
M59G=D(1,"TMSG_CITY_GET_ALL_TROOP_RSP",".CSMsg.TMSG_CITY_GET_ALL_TROOP_RSP",false,{},{},nil,{})
F127D=F(2,"orderList",".CSMsg.TMSG_CITY_EXCHANGE_ORDER_REQ.orderList",1,0,3,false,{},5,1)
M60G=D(1,"TMSG_CITY_EXCHANGE_ORDER_REQ",".CSMsg.TMSG_CITY_EXCHANGE_ORDER_REQ",false,{},{},nil,{})
F128D=F(2,"errCode",".CSMsg.TMSG_CITY_EXCHANGE_ORDER_RSP.errCode",1,0,2,false,0,5,1)
M61G=D(1,"TMSG_CITY_EXCHANGE_ORDER_RSP",".CSMsg.TMSG_CITY_EXCHANGE_ORDER_RSP",false,{},{},nil,{})
F129D=F(2,"lineUp",".CSMsg.TMSG_CITY_TEAMINFO_NTF.lineUp",1,0,2,false,nil,11,10)
F130D=F(2,"sxTeamType",".CSMsg.TMSG_CITY_TEAMINFO_NTF.sxTeamType",2,1,1,false,0,5,1)
F131D=F(2,"dataType",".CSMsg.TMSG_CITY_TEAMINFO_NTF.dataType",3,2,1,false,0,5,1)
M62G=D(1,"TMSG_CITY_TEAMINFO_NTF",".CSMsg.TMSG_CITY_TEAMINFO_NTF",false,{},{},nil,{})
F132D=F(2,"uSid",".CSMsg.TMSG_CITY_BUILD_UPGRADE_REQ.uSid",1,0,2,false,0,13,3)
M63G=D(1,"TMSG_CITY_BUILD_UPGRADE_REQ",".CSMsg.TMSG_CITY_BUILD_UPGRADE_REQ",false,{},{},nil,{})
F133D=F(2,"errCode",".CSMsg.TMSG_CITY_BUILD_UPGRADE_RSP.errCode",1,0,2,false,0,5,1)
F134D=F(2,"uSid",".CSMsg.TMSG_CITY_BUILD_UPGRADE_RSP.uSid",2,1,2,false,0,13,3)
F135D=F(2,"uLevel",".CSMsg.TMSG_CITY_BUILD_UPGRADE_RSP.uLevel",3,2,2,false,0,13,3)
M64G=D(1,"TMSG_CITY_BUILD_UPGRADE_RSP",".CSMsg.TMSG_CITY_BUILD_UPGRADE_RSP",false,{},{},nil,{})
F136D=F(2,"uID",".CSMsg.TMSG_CITY_BUILD_DIAMOND_RENT_QUEUE_REQ.uID",1,0,2,false,0,13,3)
M65G=D(1,"TMSG_CITY_BUILD_DIAMOND_RENT_QUEUE_REQ",".CSMsg.TMSG_CITY_BUILD_DIAMOND_RENT_QUEUE_REQ",false,{},{},nil,{})
F137D=F(2,"errCode",".CSMsg.TMSG_CITY_BUILD_DIAMOND_RENT_QUEUE_RSP.errCode",1,0,2,false,0,5,1)
M66G=D(1,"TMSG_CITY_BUILD_DIAMOND_RENT_QUEUE_RSP",".CSMsg.TMSG_CITY_BUILD_DIAMOND_RENT_QUEUE_RSP",false,{},{},nil,{})
F138D=F(2,"prop",".CSMsg.TMSG_CITY_PROP_UPDATE_NTF.prop",1,0,3,false,{},11,10)
M67G=D(1,"TMSG_CITY_PROP_UPDATE_NTF",".CSMsg.TMSG_CITY_PROP_UPDATE_NTF",false,{},{},nil,{})
F139D=F(2,"uSid",".CSMsg.TMSG_CITY_DISPATCH_WORKER_REQ.uSid",1,0,2,false,0,13,3)
F140D=F(2,"uWorkerId",".CSMsg.TMSG_CITY_DISPATCH_WORKER_REQ.uWorkerId",2,1,2,false,0,13,3)
M68G=D(1,"TMSG_CITY_DISPATCH_WORKER_REQ",".CSMsg.TMSG_CITY_DISPATCH_WORKER_REQ",false,{},{},nil,{})
F141D=F(2,"errCode",".CSMsg.TMSG_CITY_DISPATCH_WORKER_RSP.errCode",1,0,2,false,0,5,1)
M69G=D(1,"TMSG_CITY_DISPATCH_WORKER_RSP",".CSMsg.TMSG_CITY_DISPATCH_WORKER_RSP",false,{},{},nil,{})
F142D=F(2,"uSid1",".CSMsg.TMSG_CITY_WORKER_REPLACE_REQ.uSid1",1,0,2,false,0,13,3)
F143D=F(2,"uWorkerId1",".CSMsg.TMSG_CITY_WORKER_REPLACE_REQ.uWorkerId1",2,1,2,false,0,13,3)
F144D=F(2,"uSid2",".CSMsg.TMSG_CITY_WORKER_REPLACE_REQ.uSid2",3,2,2,false,0,13,3)
F145D=F(2,"uWorkerId2",".CSMsg.TMSG_CITY_WORKER_REPLACE_REQ.uWorkerId2",4,3,2,false,0,13,3)
M70G=D(1,"TMSG_CITY_WORKER_REPLACE_REQ",".CSMsg.TMSG_CITY_WORKER_REPLACE_REQ",false,{},{},nil,{})
F146D=F(2,"errCode",".CSMsg.TMSG_CITY_WORKER_REPLACE_RSP.errCode",1,0,2,false,0,5,1)
M71G=D(1,"TMSG_CITY_WORKER_REPLACE_RSP",".CSMsg.TMSG_CITY_WORKER_REPLACE_RSP",false,{},{},nil,{})
F147D=F(2,"uWorkerId",".CSMsg.TMSG_CITY_WORKER_COMPOSITE_REQ.uWorkerId",1,0,2,false,0,13,3)
M72G=D(1,"TMSG_CITY_WORKER_COMPOSITE_REQ",".CSMsg.TMSG_CITY_WORKER_COMPOSITE_REQ",false,{},{},nil,{})
F148D=F(2,"errCode",".CSMsg.TMSG_CITY_WORKER_COMPOSITE_RSP.errCode",1,0,2,false,0,5,1)
M73G=D(1,"TMSG_CITY_WORKER_COMPOSITE_RSP",".CSMsg.TMSG_CITY_WORKER_COMPOSITE_RSP",false,{},{},nil,{})
F149D=F(2,"uWorkerId",".CSMsg.TMSG_CITY_WORKER_LEVELUP_REQ.uWorkerId",1,0,2,false,0,13,3)
M74G=D(1,"TMSG_CITY_WORKER_LEVELUP_REQ",".CSMsg.TMSG_CITY_WORKER_LEVELUP_REQ",false,{},{},nil,{})
F150D=F(2,"errCode",".CSMsg.TMSG_CITY_WORKER_LEVELUP_RSP.errCode",1,0,2,false,0,5,1)
M75G=D(1,"TMSG_CITY_WORKER_LEVELUP_RSP",".CSMsg.TMSG_CITY_WORKER_LEVELUP_RSP",false,{},{},nil,{})
F151D=F(2,"uSid",".CSMsg.TCityDispatch.uSid",1,0,2,false,0,13,3)
F152D=F(2,"uWorkerId",".CSMsg.TCityDispatch.uWorkerId",2,1,3,false,{},13,3)
M76G=D(1,"TCityDispatch",".CSMsg.TCityDispatch",false,{},{},nil,{})
F153D=F(2,"dispatchList",".CSMsg.TMSG_CITY_WORKER_DISPATCH_ONECLICK_REQ.dispatchList",1,0,3,false,{},11,10)
M77G=D(1,"TMSG_CITY_WORKER_DISPATCH_ONECLICK_REQ",".CSMsg.TMSG_CITY_WORKER_DISPATCH_ONECLICK_REQ",false,{},{},nil,{})
F154D=F(2,"errCode",".CSMsg.TMSG_CITY_WORKER_DISPATCH_ONECLICK_RSP.errCode",1,0,2,false,0,5,1)
M78G=D(1,"TMSG_CITY_WORKER_DISPATCH_ONECLICK_RSP",".CSMsg.TMSG_CITY_WORKER_DISPATCH_ONECLICK_RSP",false,{},{},nil,{})
F155D=F(2,"worker",".CSMsg.TMSG_CITY_WORKER_UPDATE_NTF.worker",1,0,3,false,{},11,10)
M79G=D(1,"TMSG_CITY_WORKER_UPDATE_NTF",".CSMsg.TMSG_CITY_WORKER_UPDATE_NTF",false,{},{},nil,{})
F156D=F(2,"uEventId",".CSMsg.TMSG_CITY_GET_EVENT_REWARD_REQ.uEventId",1,0,2,false,0,13,3)
M80G=D(1,"TMSG_CITY_GET_EVENT_REWARD_REQ",".CSMsg.TMSG_CITY_GET_EVENT_REWARD_REQ",false,{},{},nil,{})
F157D=F(2,"errCode",".CSMsg.TMSG_CITY_GET_EVENT_REWARD_RSP.errCode",1,0,2,false,0,5,1)
F158D=F(2,"uRewardId",".CSMsg.TMSG_CITY_GET_EVENT_REWARD_RSP.uRewardId",2,1,1,false,0,13,3)
F159D=F(2,"uEventId",".CSMsg.TMSG_CITY_GET_EVENT_REWARD_RSP.uEventId",3,2,2,false,0,13,3)
M81G=D(1,"TMSG_CITY_GET_EVENT_REWARD_RSP",".CSMsg.TMSG_CITY_GET_EVENT_REWARD_RSP",false,{},{},nil,{})
F160D=F(2,"uAreaId",".CSMsg.TMSG_CITY_CLICK_FULL_OPEN_REQ.uAreaId",1,0,2,false,0,13,3)
M82G=D(1,"TMSG_CITY_CLICK_FULL_OPEN_REQ",".CSMsg.TMSG_CITY_CLICK_FULL_OPEN_REQ",false,{},{},nil,{})
F161D=F(2,"errCode",".CSMsg.TMSG_CITY_CLICK_FULL_OPEN_RSP.errCode",1,0,2,false,0,5,1)
F162D=F(2,"uAreaId",".CSMsg.TMSG_CITY_CLICK_FULL_OPEN_RSP.uAreaId",2,1,2,false,0,13,3)
M83G=D(1,"TMSG_CITY_CLICK_FULL_OPEN_RSP",".CSMsg.TMSG_CITY_CLICK_FULL_OPEN_RSP",false,{},{},nil,{})
F163D=F(2,"area",".CSMsg.TMSG_CITY_AREA_STATE_CHANGE_NTF.area",1,0,3,false,{},11,10)
M84G=D(1,"TMSG_CITY_AREA_STATE_CHANGE_NTF",".CSMsg.TMSG_CITY_AREA_STATE_CHANGE_NTF",false,{},{},nil,{})
F164D=F(2,"uEventId",".CSMsg.TMSG_CITY_COMPLETE_BEGIN_EVENT_REQ.uEventId",1,0,2,false,0,13,3)
M85G=D(1,"TMSG_CITY_COMPLETE_BEGIN_EVENT_REQ",".CSMsg.TMSG_CITY_COMPLETE_BEGIN_EVENT_REQ",false,{},{},nil,{})
F165D=F(2,"errCode",".CSMsg.TMSG_CITY_COMPLETE_BEGIN_EVENT_RSP.errCode",1,0,2,false,0,5,1)
M86G=D(1,"TMSG_CITY_COMPLETE_BEGIN_EVENT_RSP",".CSMsg.TMSG_CITY_COMPLETE_BEGIN_EVENT_RSP",false,{},{},nil,{})
F166D=F(2,"nType",".CSMsg.TMSG_CITY_GET_REWARD_QUEUE_REQ.nType",1,0,2,false,0,5,1)
F167D=F(2,"nId",".CSMsg.TMSG_CITY_GET_REWARD_QUEUE_REQ.nId",2,1,2,false,0,5,1)
M87G=D(1,"TMSG_CITY_GET_REWARD_QUEUE_REQ",".CSMsg.TMSG_CITY_GET_REWARD_QUEUE_REQ",false,{},{},nil,{})
F168D=F(2,"errCode",".CSMsg.TMSG_CITY_GET_REWARD_QUEUE_RSP.errCode",1,0,2,false,0,5,1)
F169D=F(2,"nType",".CSMsg.TMSG_CITY_GET_REWARD_QUEUE_RSP.nType",2,1,1,false,0,5,1)
F170D=F(2,"nId",".CSMsg.TMSG_CITY_GET_REWARD_QUEUE_RSP.nId",3,2,1,false,0,5,1)
M88G=D(1,"TMSG_CITY_GET_REWARD_QUEUE_RSP",".CSMsg.TMSG_CITY_GET_REWARD_QUEUE_RSP",false,{},{},nil,{})
F171D=F(2,"reward",".CSMsg.TMSG_CITY_REWARD_QUEUE_NTF.reward",1,0,3,false,{},11,10)
M89G=D(1,"TMSG_CITY_REWARD_QUEUE_NTF",".CSMsg.TMSG_CITY_REWARD_QUEUE_NTF",false,{},{},nil,{})
F172D=F(2,"uEventId",".CSMsg.TMSG_CITY_CHECK_AREA_EVENT_REQ.uEventId",1,0,2,false,0,13,3)
M90G=D(1,"TMSG_CITY_CHECK_AREA_EVENT_REQ",".CSMsg.TMSG_CITY_CHECK_AREA_EVENT_REQ",false,{},{},nil,{})
F173D=F(2,"errCode",".CSMsg.TMSG_CITY_CHECK_AREA_EVENT_RSP.errCode",1,0,2,false,0,13,3)
M91G=D(1,"TMSG_CITY_CHECK_AREA_EVENT_RSP",".CSMsg.TMSG_CITY_CHECK_AREA_EVENT_RSP",false,{},{},nil,{})

E1M.values = {V1M,V2M,V3M,V4M,V5M}
E2M.values = {V6M,V7M,V8M,V9M,V10M,V11M,V12M,V13M}
E3M.values = {V14M,V15M,V16M,V17M,V18M,V19M,V20M,V21M,V22M,V23M,V24M,V25M,V26M,V27M,V28M,V29M,V30M,V31M,V32M,V33M,V34M,V35M,V36M,V37M,V38M,V39M,V40M,V41M,V42M,V43M,V44M,V45M,V46M,V47M,V48M,V49M,V50M,V51M,V52M,V53M,V54M,V55M,V56M,V57M,V58M,V59M}
E4M.values = {V60M,V61M,V62M,V63M,V64M,V65M,V66M,V67M,V68M,V69M,V70M,V71M,V72M,V73M,V74M,V75M,V76M,V77M,V78M,V79M,V80M,V81M,V82M,V83M,V84M,V85M,V86M,V87M,V88M,V89M,V90M,V91M,V92M,V93M,V94M,V95M,V96M,V97M,V98M,V99M,V100M,V101M,V102M,V103M,V104M,V105M,V106M,V107M,V108M,V109M,V110M,V111M,V112M,V113M,V114M,V115M,V116M,V117M,V118M,V119M,V120M,V121M,V122M,V123M,V124M,V125M,V126M,V127M,V128M,V129M,V130M}
E5M.values = {V131M,V132M}
E6M.values = {V133M,V134M,V135M,V136M}
E7M.values = {V137M,V138M}
E8M.values = {V139M,V140M,V141M}
E9M.values = {V142M}
E10M.values = {V143M,V144M,V145M}
E11M.values = {V146M,V147M}
M1G.fields={F1D, F2D, F3D, F4D}
M2G.fields={F5D, F6D, F7D}
F15D.message_type=M1G
F16D.message_type=M2G
M3G.fields={F8D, F9D, F10D, F11D, F12D, F13D, F14D, F15D, F16D, F17D}
M4G.fields={F18D, F19D, F20D}
M5G.fields={F21D, F22D, F23D, F24D, F25D}
M6G.fields={F26D, F27D}
F29D.message_type=M6G
M7G.fields={F28D, F29D}
M8G.fields={F30D, F31D, F32D, F33D}
F34D.message_type=M8G
M9G.fields={F34D}
M10G.fields={F35D, F36D, F37D}
M11G.fields={F38D, F39D}
F40D.message_type=prop_pb.M9G
F41D.message_type=M4G
F42D.message_type=M3G
F43D.message_type=M5G
F44D.message_type=M9G
F45D.message_type=M7G
F46D.message_type=M10G
F47D.message_type=M11G
M13G.fields={F40D, F41D, F42D, F43D, F44D, F45D, F46D, F47D}
M15G.fields={F48D, F49D, F50D}
F51D.enum_type=error_code_pb.E1M
M16G.fields={F51D, F52D}
M18G.fields={F53D, F54D}
F55D.enum_type=error_code_pb.E1M
M19G.fields={F55D, F56D}
M20G.fields={F57D, F58D}
F59D.enum_type=error_code_pb.E1M
F61D.message_type=common_new_pb.M1G
M21G.fields={F59D, F60D, F61D}
M23G.fields={F62D}
F63D.enum_type=error_code_pb.E1M
M24G.fields={F63D, F64D}
M25G.fields={F65D, F66D}
F67D.enum_type=error_code_pb.E1M
M26G.fields={F67D, F68D, F69D, F70D}
M27G.fields={F71D, F72D, F73D}
F74D.enum_type=error_code_pb.E1M
M28G.fields={F74D, F75D}
M29G.fields={F76D, F77D, F78D}
F79D.enum_type=error_code_pb.E1M
M30G.fields={F79D, F80D}
M31G.fields={F81D}
F82D.enum_type=error_code_pb.E1M
M32G.fields={F82D, F83D, F84D}
M33G.fields={F85D, F86D, F87D, F88D}
F89D.enum_type=error_code_pb.E1M
M34G.fields={F89D, F90D}
F91D.message_type=M6G
F92D.enum_type=sandbox_pb.E1M
M35G.fields={F91D, F92D}
F93D.enum_type=error_code_pb.E1M
F94D.enum_type=sandbox_pb.E1M
M37G.fields={F93D, F94D}
F95D.enum_type=sandbox_pb.E1M
M38G.fields={F95D}
F96D.enum_type=error_code_pb.E1M
F98D.enum_type=sandbox_pb.E1M
M39G.fields={F96D, F97D, F98D}
F99D.message_type=M3G
M40G.fields={F99D}
F100D.message_type=M5G
M41G.fields={F100D}
F101D.message_type=M4G
M42G.fields={F101D}
F102D.message_type=M9G
F103D.enum_type=sandbox_pb.E1M
M43G.fields={F102D, F103D}
F104D.message_type=M7G
F105D.enum_type=sandbox_pb.E1M
M44G.fields={F104D, F105D}
M45G.fields={F106D}
F107D.enum_type=error_code_pb.E1M
M46G.fields={F107D, F108D}
M47G.fields={F109D}
M48G.fields={F110D, F111D}
F112D.message_type=sandbox_pb.M24G
M49G.fields={F112D}
M51G.fields={F113D}
M52G.fields={F114D, F115D, F116D}
F117D.message_type=common_new_pb.M3G
M53G.fields={F117D, F118D}
M55G.fields={F119D}
M56G.fields={F120D}
F122D.message_type=M49G
M57G.fields={F121D, F122D}
M58G.fields={F123D}
F125D.message_type=M49G
M59G.fields={F124D, F125D, F126D}
M60G.fields={F127D}
M61G.fields={F128D}
F129D.message_type=M49G
M62G.fields={F129D, F130D, F131D}
M63G.fields={F132D}
M64G.fields={F133D, F134D, F135D}
M65G.fields={F136D}
M66G.fields={F137D}
F138D.message_type=prop_pb.M9G
M67G.fields={F138D}
M68G.fields={F139D, F140D}
M69G.fields={F141D}
M70G.fields={F142D, F143D, F144D, F145D}
M71G.fields={F146D}
M72G.fields={F147D}
M73G.fields={F148D}
M74G.fields={F149D}
M75G.fields={F150D}
M76G.fields={F151D, F152D}
F153D.message_type=M76G
M77G.fields={F153D}
M78G.fields={F154D}
F155D.message_type=M10G
M79G.fields={F155D}
M80G.fields={F156D}
M81G.fields={F157D, F158D, F159D}
M82G.fields={F160D}
M83G.fields={F161D, F162D}
F163D.message_type=M4G
M84G.fields={F163D}
M85G.fields={F164D}
M86G.fields={F165D}
M87G.fields={F166D, F167D}
M88G.fields={F168D, F169D, F170D}
F171D.message_type=M11G
M89G.fields={F171D}
M90G.fields={F172D}
M91G.fields={F173D}

TCityArea =M(M4G)
TCityBattleLineUp =M(M49G)
TCityBuildQueue =M(M5G)
TCityBuilding =M(M3G)
TCityDispatch =M(M76G)
TCityHospital =M(M7G)
TCityHospitalSoldier =M(M6G)
TCityMilitary =M(M1G)
TCityResource =M(M2G)
TCitySoldier =M(M8G)
TCityTrainningCenter =M(M9G)
TCityWorker =M(M10G)
TMSG_CITY_AREA_STATE_CHANGE_NTF =M(M84G)
TMSG_CITY_AREA_UPDATE_NTF =M(M42G)
TMSG_CITY_BUILDING_UPDATE_NTF =M(M40G)
TMSG_CITY_BUILDQUEUE_UPDATE_NTF =M(M41G)
TMSG_CITY_BUILD_A_BUILDING_REQ =M(M15G)
TMSG_CITY_BUILD_A_BUILDING_RSP =M(M16G)
TMSG_CITY_BUILD_DIAMOND_RENT_QUEUE_REQ =M(M65G)
TMSG_CITY_BUILD_DIAMOND_RENT_QUEUE_RSP =M(M66G)
TMSG_CITY_BUILD_UPGRADE_REQ =M(M63G)
TMSG_CITY_BUILD_UPGRADE_RSP =M(M64G)
TMSG_CITY_CHANGE_BUILDING_LOCATION_REQ =M(M27G)
TMSG_CITY_CHANGE_BUILDING_LOCATION_RSP =M(M28G)
TMSG_CITY_CHECK_AREA_EVENT_REQ =M(M90G)
TMSG_CITY_CHECK_AREA_EVENT_RSP =M(M91G)
TMSG_CITY_CLICK_FULL_OPEN_REQ =M(M82G)
TMSG_CITY_CLICK_FULL_OPEN_RSP =M(M83G)
TMSG_CITY_COMPLETE_BEGIN_EVENT_REQ =M(M85G)
TMSG_CITY_COMPLETE_BEGIN_EVENT_RSP =M(M86G)
TMSG_CITY_DISPATCH_WORKER_REQ =M(M68G)
TMSG_CITY_DISPATCH_WORKER_RSP =M(M69G)
TMSG_CITY_EXCHANGE_ORDER_REQ =M(M60G)
TMSG_CITY_EXCHANGE_ORDER_RSP =M(M61G)
TMSG_CITY_GET_ALLINFO_REQ =M(M12G)
TMSG_CITY_GET_ALLINFO_RSP =M(M13G)
TMSG_CITY_GET_ALL_TROOP_REQ =M(M58G)
TMSG_CITY_GET_ALL_TROOP_RSP =M(M59G)
TMSG_CITY_GET_BUILDING_OPENGIFT_REQ =M(M20G)
TMSG_CITY_GET_BUILDING_OPENGIFT_RSP =M(M21G)
TMSG_CITY_GET_BUILDING_RESOURCE_REQ =M(M25G)
TMSG_CITY_GET_BUILDING_RESOURCE_RSP =M(M26G)
TMSG_CITY_GET_EVENT_REWARD_REQ =M(M80G)
TMSG_CITY_GET_EVENT_REWARD_RSP =M(M81G)
TMSG_CITY_GET_REWARD_QUEUE_REQ =M(M87G)
TMSG_CITY_GET_REWARD_QUEUE_RSP =M(M88G)
TMSG_CITY_GET_TROOP_REQ =M(M56G)
TMSG_CITY_GET_TROOP_RSP =M(M57G)
TMSG_CITY_HOSPITAL_CURE_SOLDIER_REQ =M(M35G)
TMSG_CITY_HOSPITAL_CURE_SOLDIER_RSP =M(M37G)
TMSG_CITY_HOSPITAL_GET_SOLDIER_REQ =M(M38G)
TMSG_CITY_HOSPITAL_GET_SOLDIER_RSP =M(M39G)
TMSG_CITY_HOSPITAL_UPDATE_NTF =M(M44G)
TMSG_CITY_MILITARY_GET_SOLDIER_REQ =M(M31G)
TMSG_CITY_MILITARY_GET_SOLDIER_RSP =M(M32G)
TMSG_CITY_MILITARY_RECRUIT_SOLDIER_REQ =M(M29G)
TMSG_CITY_MILITARY_RECRUIT_SOLDIER_RSP =M(M30G)
TMSG_CITY_MILITARY_UPGRADE_SOLDIER_REQ =M(M33G)
TMSG_CITY_MILITARY_UPGRADE_SOLDIER_RSP =M(M34G)
TMSG_CITY_PROP_UPDATE_NTF =M(M67G)
TMSG_CITY_REPAIR_A_BUILDING_REQ =M(M18G)
TMSG_CITY_REPAIR_A_BUILDING_RSP =M(M19G)
TMSG_CITY_REWARD_QUEUE_NTF =M(M89G)
TMSG_CITY_SAVE_TROOP_REQ =M(M53G)
TMSG_CITY_SAVE_TROOP_RSP =M(M55G)
TMSG_CITY_TEAMINFO_NTF =M(M62G)
TMSG_CITY_TRAINNINGCENTER_UPDATE_NTF =M(M43G)
TMSG_CITY_TROOP_IS_IDLE_REQ =M(M51G)
TMSG_CITY_TROOP_IS_IDLE_RSP =M(M52G)
TMSG_CITY_UPGRADE_A_BUILDING_REQ =M(M23G)
TMSG_CITY_UPGRADE_A_BUILDING_RSP =M(M24G)
TMSG_CITY_WALL_BUY_CITYDEFENSE_REQ =M(M47G)
TMSG_CITY_WALL_BUY_CITYDEFENSE_RSP =M(M48G)
TMSG_CITY_WALL_FIRE_FIGHTING_REQ =M(M45G)
TMSG_CITY_WALL_FIRE_FIGHTING_RSP =M(M46G)
TMSG_CITY_WORKER_COMPOSITE_REQ =M(M72G)
TMSG_CITY_WORKER_COMPOSITE_RSP =M(M73G)
TMSG_CITY_WORKER_DISPATCH_ONECLICK_REQ =M(M77G)
TMSG_CITY_WORKER_DISPATCH_ONECLICK_RSP =M(M78G)
TMSG_CITY_WORKER_LEVELUP_REQ =M(M74G)
TMSG_CITY_WORKER_LEVELUP_RSP =M(M75G)
TMSG_CITY_WORKER_REPLACE_REQ =M(M70G)
TMSG_CITY_WORKER_REPLACE_RSP =M(M71G)
TMSG_CITY_WORKER_UPDATE_NTF =M(M79G)
TRewardQueue =M(M11G)
enBuildingState_Broken = 0
enBuildingState_Constructing = 3
enBuildingState_Normal = 1
enBuildingState_Repairing = 2
enBuildingState_Upgrading = 4
enBuildingSubtype_Decorate = 3
enBuildingSubtype_Economy = 1
enBuildingSubtype_Military = 2
enBuildingType_AcornPub = 43
enBuildingType_AircraftCenter = 34
enBuildingType_AirshipStation = 40
enBuildingType_Alliance = 15
enBuildingType_Arena = 45
enBuildingType_Belfry = 1107
enBuildingType_BronzeAirplane = 1002
enBuildingType_BronzeCarrier = 1001
enBuildingType_BronzeTank = 1004
enBuildingType_CarriageCenter = 38
enBuildingType_DogHouse = 101
enBuildingType_DroneCenter = 29
enBuildingType_DroneFactory = 28
enBuildingType_DroneStar = 46
enBuildingType_Equip = 27
enBuildingType_Exp = 5
enBuildingType_Farm = 2
enBuildingType_FarmWareHouse = 22
enBuildingType_FerrisWheel = 1109
enBuildingType_Formation1 = 9
enBuildingType_Formation2 = 17
enBuildingType_Formation3 = 18
enBuildingType_Formation4 = 19
enBuildingType_FristRecharge = 44
enBuildingType_Garden = 11
enBuildingType_Gold = 4
enBuildingType_GoldWareHouse = 24
enBuildingType_GoldenBomber = 1106
enBuildingType_GoldenMarshalStatue = 1103
enBuildingType_GoldenMissileVehicle = 1105
enBuildingType_GoldenMobileTeam = 1102
enBuildingType_GoldenTank = 1104
enBuildingType_Hospital = 8
enBuildingType_Iron = 3
enBuildingType_IronTower = 1108
enBuildingType_IronWareHouse = 23
enBuildingType_LadyLiberty = 1111
enBuildingType_Legend = 1015
enBuildingType_Main = 1
enBuildingType_Material = 26
enBuildingType_Max = 1116
enBuildingType_Military = 6
enBuildingType_MilitaryIndustrial = 1113
enBuildingType_MissileCenter = 33
enBuildingType_MonumentWarriors = 1112
enBuildingType_NeonLight = 1110
enBuildingType_OrnamentCollection = 42
enBuildingType_Pacifism = 1003
enBuildingType_Pyramid = 1101
enBuildingType_Radar = 31
enBuildingType_Reconnaissance = 30
enBuildingType_Research1 = 20
enBuildingType_Research2 = 21
enBuildingType_ResourceTruck = 39
enBuildingType_RoadA = 35
enBuildingType_RoadB = 36
enBuildingType_Shop = 13
enBuildingType_SilverDestroyer = 1013
enBuildingType_SilverRocket = 1014
enBuildingType_SilverWarplane = 1012
enBuildingType_SilverWarrior = 1011
enBuildingType_SmelterFactory = 25
enBuildingType_Summon = 12
enBuildingType_TankCenter = 32
enBuildingType_ThroneBlood = 1115
enBuildingType_Tire = 1016
enBuildingType_TrainCenter = 7
enBuildingType_VictoryTower = 1114
enBuildingType_Wall = 16
enBuildingType_WorkerCenter = 14
enBuildingType_WorkerHouse = 10
enCityAreaUnlockType_Building = 1
enCityAreaUnlockType_Story = 2
enCityBuildingFixCondType_Building = 1
enCityBuildingFixCondType_Gift = 4
enCityBuildingFixCondType_MiniGame = 3
enCityBuildingFixCondType_Story = 2
enCityFlag_AreaCanBuilding = 8
enCityFlag_AreaCompleteAll = 128
enCityFlag_AreaUnlock = 4
enCityFlag_BuildingOpenGift = 2
enCityFlag_CityWaitRepair = 256
enCityFlag_MilitaryRecruit = 32
enCityFlag_MilitaryUpgrade = 64
enCityFlag_QueueFullOpen = 16
enCityMapItemType_Building = 1
enCityMapItemType_Event = 2
enCityProp_AcornPubCanSendTaskCount = 54
enCityProp_AllianceHelpAdditionTime = 53
enCityProp_CityAddAtk = 59
enCityProp_CityAddSoldier = 60
enCityProp_CurDefendValue = 28
enCityProp_EquimentMakeCoinReduce = 47
enCityProp_EquimentMakeSpeedUp = 46
enCityProp_Flag = 4
enCityProp_FreeSummonResetTime = 26
enCityProp_HeroMaxLevel = 25
enCityProp_HiddenQueueNum = 51
enCityProp_HiddenRewardNum = 52
enCityProp_HospitalCureID = 0
enCityProp_LastBuyCityDefendTime = 1
enCityProp_Max = 61
enCityProp_MaxBuildingPower = 50
enCityProp_MovingSpeedUpN = 27
enCityProp_MovingSpeedUpN2 = 35
enCityProp_MovingSpeedUpN3 = 36
enCityProp_MovingSpeedUpN4 = 37
enCityProp_RadarMissionBox = 56
enCityProp_RadarMissionBoxId = 58
enCityProp_RadarMissionBoxMax = 57
enCityProp_ResearchResourceRate1 = 43
enCityProp_ResearchResourceRate2 = 44
enCityProp_ResearchSpeedUpRate1 = 31
enCityProp_ResearchSpeedUpRate2 = 32
enCityProp_ResearchSpeedUpTime1 = 33
enCityProp_ResearchSpeedUpTime2 = 34
enCityProp_ResourceLimitTimeUp = 48
enCityProp_ResourceProduceUpN = 49
enCityProp_Sid = 5
enCityProp_TotalBeHelpTimes = 22
enCityProp_TotalConfigPower = 55
enCityProp_TotalCureSpeedUpN = 42
enCityProp_TotalFoodProtect = 38
enCityProp_TotalFreeAccelerateTime = 21
enCityProp_TotalGoldProtect = 40
enCityProp_TotalHospitalCapacity = 23
enCityProp_TotalIronProtect = 39
enCityProp_TotalPower = 20
enCityProp_TotalReduceBuildNeedN = 41
enCityProp_TotalTrainCenterCapacity = 24
enCityProp_WallBeginTime = 29
enCityProp_WallFireEndTime = 30
enCityProp_WorkerFreeSummonCD = 45
enRewardType_Survivor = 1
enSoldierOfflineDataType_Hurt = 3
enSoldierOfflineDataType_InCity = 1
enSoldierOfflineDataType_OutCity = 2
enTeamTroopSubType_City = 1
enTeamTroopSubType_Detect = 2

