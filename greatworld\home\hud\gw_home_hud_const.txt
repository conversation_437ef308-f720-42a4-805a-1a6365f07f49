﻿---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON>Lua)
--- Created by ha<PERSON><PERSON><PERSON>.
--- DateTime: 2024/9/13 18:08
--- Desc : HUD常量

local require = require
local GWCompName = GWCompName
local GWSandHudNode = require("gw_sand_hud_node")
module("gw_home_hud_const")
local GWHomeHudConst = {}
---@class GWHomeHudConst
GWHomeHudConst.MoveHud = {
    --事件、 开始战斗
    [GWCompName.gw_home_comp_hud_fight] = {
        parent = GWSandHudNode.UIBaseMidNode(),
    },
    [GWCompName.gw_home_comp_hud_event_lock] = {
        parent = GWSandHudNode.UIBaseMidNode(),
    },
    [GWCompName.gw_home_comp_hud_upgrade] = {
        parent = GWSandHudNode.UIBaseMidNode(),
    },
    [GWCompName.gw_home_comp_hud_get_resource] = {
        parent = GWSandHudNode.UIBaseMidNode(),
    },
    [GWCompName.gw_home_comp_hud_head_bubble] = {
        parent = GWSandHudNode.UIBaseMidNode(),
    },
    [GWCompName.gw_home_comp_hud_lord_bubble] = {
        parent = GWSandHudNode.UIBaseMidNode(),
    },
    [GWCompName.gw_home_comp_hud_stray_dog_bubble] = {
        parent = GWSandHudNode.UIBaseMidNode(),
    },
    [GWCompName.gw_home_comp_hud_arrow] = {
        parent = GWSandHudNode.UIBaseTopNode(),
    },
    [GWCompName.gw_home_comp_hud_xyx] = {
        parent = GWSandHudNode.UIBaseTopNode(),
    },
}

return GWHomeHudConst