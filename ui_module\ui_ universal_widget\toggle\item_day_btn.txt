local require = require
local type = type
local pairs = pairs
local typeof = typeof
local newclass = newclass
local cc = cc

local card_sprite_asset = require "card_sprite_asset"
local log = require "log"
local util = require "util"
local util_widget_table = require "util_widget_table"
local binding = require "item_day_btn_binding"

module("item_day_btn")

---@field 天数枚举类型 table 
DayItemType = {
    UnLock = 1,--已解锁
    Select = 2,--今日 (改为选中)
    Lock = 3,--未解锁
}

local itemView = newclass("item_day_btn")
itemView.widget_table = binding.WidgetTable

function itemView:ctor()
    cc(self):addComponent("base_ui_util"):exportMethods()
    self.isLoaded = false
    self.gameObject = nil
    self.transform = nil
    self.IData = {
        
    }
    self.blueColor = "#365D90"
    self.yellowColor = "#925629"
end

function itemView:Init(prefab)
    if prefab then
        util_widget_table.BindWidgetTableItem(self, prefab, self.widget_table)
        self.isLoaded = true
    else
        log.Error("ui_battlePass_info Init gameObject is nil")
    end
end

function itemView:UpdateData(data)
    --根据类型显隐
    self:RefreshSelectState(data.isLock,data.isSelect)
    self.txt_dayNum.text = data.day  --设置天数文本
    self.clickButtonFunc = data.clickButtonFunc
end

---@public function 刷新选中状态
function itemView:RefreshSelectState(isLock,isSelect)
   
    if not isSelect then
        self.ss_bg:Switch(isLock and 2 or 0)
        self:SetActive(self.rtf_dayTitle0, not isLock)
        self:SetActive(self.rtf_dayTitle2, isLock)
    else
        self.ss_bg:Switch(1)
    end
    self:SetActive(self.rtf_dayTitle1, isSelect)
    self:SetActive(self.txt_dayNum,not isLock)
    self:SetColor(self.txt_dayNum, isSelect and  self.yellowColor or self.blueColor)
end


function itemView:Dispose()
    util_widget_table.DisposeWidgetTableManageEvent(self, self.widget_table)
    self.isLoaded = false
    self.gameObject = nil
    self.transform = nil
    if self.IData then
        for i, v in pairs(self.IData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.IData = nil
end

--region Item Logic
function itemView:OnBtnBgClickedProxy()
    if self.clickButtonFunc then
        self.clickButtonFunc()
    end
end

--endregion

function NewItem(prefab)
    local item = itemView.new()
    item:Init(prefab)
    return item;
end
--return itemView
