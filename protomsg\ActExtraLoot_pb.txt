-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
module('ActExtraLoot_pb')


V1M=V(4,"enRadar",0,1)
V2M=V(4,"enSandBoxMonster",1,2)
V3M=V(4,"enWorldBoss",2,3)
V4M=V(4,"enDarkNightTrial",3,4)
V5M=V(4,"enAllianceBoss",4,5)
E1M=E(3,"EExtraLootAssociatedModule",".CSMsg.EExtraLootAssociatedModule")

E1M.values = {V1M,V2M,V3M,V4M,V5M}

enAllianceBoss = 5
enDarkNightTrial = 4
enRadar = 1
enSandBoxMonster = 2
enWorldBoss = 3

