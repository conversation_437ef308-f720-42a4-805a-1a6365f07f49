-- gray_load_mgr.txt ------------------------------------------
-- author:  ljc
-- date:    2020.11.13
-- ver:     1.0
-- desc:    后台下载
--------------------------------------------------------------

local print = print
local require = require
local pairs = pairs
local table = table
local ipairs = ipairs
local string = string
local unpack = unpack
local getfenv = getfenv

local log = require 'log'
local json = require "dkjson"
local util = require "util"
local event = require "event"
local loader = require "asset_loader"
local ui_window_mgr 	= require "ui_window_mgr"
local download_mgr 	= require "download_mgr"
local ReviewingUtil 	= require "ReviewingUtil"
local const = require "const"
local files_version_mgr = require "files_version_mgr"
local IsReviewing = ReviewingUtil.IsReviewing()

local AssetBundleManager = CS.War.Base.AssetBundleManager
local hashRemote = CS.War.Base.AssetBundleManager.hashRemote
local Time = CS.UnityEngine.Time
local LogHelpBase         = CS.LogHelpBase
local LogHelp       = CS.LogHelp
local newVersion_res_load_mgr = require "newVersion_res_load_mgr"
local OldResVersionManager = CS.War.Base.OldResVersionManager
local PlayerPrefs = CS.UnityEngine.PlayerPrefs

module("gray_load_mgr")
local assetNames = "editorconfig/restag/pack%s.bytes"
--后台下载UI展示类型
local grayLoadShowType =
{
    grayLoad = "grayLoad", --原先旧有的正常的后台下载
    preload = "preload", --新模块预下载
}
local curGrayLoadShowType =  grayLoadShowType.grayLoad
local M = {
    loadAssetlists = {},
    listAssets = {len = 10},
    reslist = {},
}
local _ENV = getfenv(1)
local preload
if const.USE_REFACTOR_PRELOAD_RESOURCES then
    preload = require "preload_resources_main"
else
    preload = require "preload_resources"
end
local isFirstDisEvent = false
function Init()
    -- local cache = {
    --     funcs = {
    --         ,
            
    --         function (  )
    --             local diffs = cache.diffs 
    --             cache.diffs = diffs
    --         end,
    --     },
    --     ind = 0
    -- }
    -- local len = #cache.funcs
    -- util.DelayCallOnce(0,function (  )
    --     cache.ind = cache.ind + 1
    --     local func = cache.funcs[cache.ind]
    --     if func then
    --         return func()
    --     end
    -- end)
    --LoadRes()
    if const.USE_REFACTOR_PRELOAD_RESOURCES then
        local preload_resources_download = preload.preload_resources_download
        preload_resources_download.SetOnZipFinsh(LoadRes)
        preload_resources_download.DownloadPatchPackage(true)
    else
        local files_version_mgr = require "files_version_mgr"
        local isDelay = files_version_mgr.GetIsDelayDownload()
        log.Warning("Gray_load_mgr Init cxq preload_resources isDelay: ", isDelay)
        if isDelay == true then
            log.Warning("Gray_load_mgr Init cxq preload_resources delay start", LoadRes)
            util.DelayCallOnce(120, function()
                log.Warning("Gray_load_mgr Init cxq preload_resources delay 120", LoadRes)
                preload.SetOnZipFinsh(LoadRes)
                preload.DownloadPatchPackage(true)
            end)
        else
            log.Warning("Gray_load_mgr Init cxq preload_resources now", LoadRes)
            preload.SetOnZipFinsh(LoadRes)
            preload.DownloadPatchPackage(true)
        end
    end
end


function LoadRes()
    local checkGrayLoadOver = function()
        CheckLoadOver()
    end

    if CheckLoad() then
        checkGrayLoadOver()
        return
    end

    local diffs = AssetBundleManager.GetDiffList()
    
    if not diffs then
        checkGrayLoadOver()
        return 
    end
    local preload_resources = require "preload_resources"
    if diffs.Length > 0 then
        print("zzd____gray_load_mgr____#diffs.Length=",diffs.Length)
        diffs = preload_resources.ExcludeCollectionDownloadRes(diffs)
        print("zzd____gray_load_mgr____after____#diffs.Length=",diffs.Length)
    end

    local difflist = {}
    for i = 0, diffs.Length-1 do
		local v = diffs[i]
        difflist[v] = v
    end
    difflist.ind = diffs.Length
    M.diffs = difflist
    M.totalSize = 0
    M.loadSize = 0
    M.listAssets.len = 10
    for i=1,10 do
        local res = string.format(assetNames,i)
        local size = hashRemote:GetSize(res)
        if size>0  then
            local l = loader(res):load(
                function ( ld )
                    local text = ld.asset.text
                    local list = string.split(text,"\n")
                    -- print("load",res,#list)
                    table.remove(list,1)
                    M.listAssets[i] = list
                    CheckLoad()
                    ld:Dispose()
                end
            )
            M.loadAssetlists[i] = l
        else
            M.listAssets.len = i-1
            CheckLoad()
            break
        end
    end
end



function CheckLoad()
    -- 小游戏后台下载初始化。生命周期小游戏内自己管理。
    local tiny_entry = require "tiny_entry"
    tiny_entry.InitBGDownload()

    local ui_login_main_mgr = require "ui_login_main_mgr"
    local enter = ui_login_main_mgr.GetOnceLogined()
    -- print("CheckLoad",#M.listAssets,enter)
    local files_version_mgr = require "files_version_mgr"
    local newVerResPreloadBeforeLogin = files_version_mgr.GetNewVerResPreloadBeforeLogin()
    if not enter and not newVerResPreloadBeforeLogin then 
        util.DelayOneCallNoCoroutine("GrayLoad_CheckLoad",function ()
            _ENV.CheckLoad()
        end,3)
        return
    end
    
    if #M.listAssets < M.listAssets.len then
        return
    end
    local bAddMap = nil
    local listA = nil
    if CheckPassLevel() then
        listA = M.listAssets[4]
        M.preload4 = true
        bAddMap = true
        -- print("CheckLoad-preload4")
    end

    if M.start_or_pause then
        SetupLoad(false)
    end
    StartLoad(listA)
    -- if listA then
    -- end
    
    if (not M.totalSize or M.totalSize == 0 or M.loadSize >=M.totalSize) then
        -- print("SetupLoad total size 0")
        return
    end
    if bAddMap then
        Report("DownloadRes_addMaps",{})
    end
    Report("DownloadRes_start",{})
    SetupLoad(true)
    return true
end

function StartLoad(...)
    if IsReviewing then
        return
    end

    -- if M.isLoad then
    --     return 
    -- end
    -- M.isLoad = true
    local reslist = {}
    M.reslist = reslist
    reslist.ind = 1
    
    local prelist = {...}

    -- LogHelpBase.LogMessage("reslist")
    -- print(#M.listAssets,json.encode(M.listAssets))
    local diffs = M.diffs
    local predic = {}

    for i,list in ipairs(prelist) do
        for ii,v in ipairs(list) do
            repeat  
                if predic[v] then break end
                if not diffs[v] then break end
                predic[v] = true
                reslist[reslist.ind] = v
                reslist.ind = reslist.ind + 1
            until true
        end
    end
    for i,list in ipairs(M.listAssets) do
        for ii,v in ipairs(list) do
            repeat  
                if predic[v] then break end
                if not diffs[v] then break end
                predic[v] = true
                reslist[reslist.ind] = v
                reslist.ind = reslist.ind + 1
            until true
        end
    end
    for i,v in pairs(diffs) do
        repeat  
            if predic[v] then break end
            predic[v] = true
            reslist[reslist.ind] = v
            reslist.ind = reslist.ind + 1
        until true
    end
    -- print("reslist xxlen:",reslist.ind)
    -- LogHelpBase.LogMessage("reslist-end")
    M.reslist = reslist
    M.predic = predic
    M.totalSize = CalLen(reslist)

    -- print("totalsize",M.totalSize)
    -- print("totalsize",M.totalSize)
    -- LogHelp.clipboard = json.encode(reslist)
    -- download_mgr.InitDownloadMgr(txtLst,nil,OnDownloadProgressChange,OnFail)
end

function SetupLoad(start_or_pause)
    if IsReviewing then
        return
    end
    -- print("SetupLoad",start_or_pause)
    ResetGrayLoadParam()
    SetGrayLoadShowType()
    if start_or_pause and (not M.totalSize or M.totalSize == 0 or M.loadSize >=M.totalSize) then
        -- print("SetupLoad total size 0",start_or_pause)
        return
    end

    
    if M.start_or_pause == start_or_pause then return end
    M.start_or_pause = start_or_pause
    if start_or_pause then
        if M.IsFail then
            M.IsFail = nil
            Report("DownloadRes_reload",{})
        end
        M.loadSize = 0
        M.startime = M.startime or Time.realtimeSinceStartup
        local networkType = util.GetNetworkType()
        local loadCount = networkType == util.ENETWORK_TYPE.WIFI and 3 or 2
        newVersion_res_load_mgr.OnGrayLoadStart()
        download_mgr.InitDownloadMgr(M.reslist,loadCount,OnDownloadProgressChanged,OnFail,"extra_load",nil,onTaskFinish, true)
    else
        download_mgr.Dispose()
    end
    util.DelayOneCallNoCoroutine("GrayLoad_ui_main_slgCheckGrayLoad",function (  )
        --local ui_main_slg = require "ui_main_slg"
        --ui_main_slg.CheckGrayLoad()
    end,1)
end
function OnDownloadProgressChanged(finishCount, totalCount)
    -- print("OnDownloadProgressChanged",finishCount, totalCount)
    -- 第一次下载完成派发事件
    if finishCount >= 1 then
        if isFirstDisEvent then
            return
        end
        local main_slg_mgr = require "main_slg_mgr"
        --打印
        local isShow = main_slg_mgr.GetDownLoadBtnIsShow()
        log.Warning("OnDownloadProgressChanged", finishCount, totalCount, "isShow", isShow)
        isFirstDisEvent = true
        event.Trigger(event.RefreshMain_Download)
    end
end
function onTaskFinish(ename, abname)
    if _ENV[ename] then
        _ENV[ename](abname)
    end
    -- print("onTaskFinish",abname,M.loadSize, size)
    
end
function on_task_finish(abname)
    -- print("on_task_finish",abname)
    local size = hashRemote:GetSize(abname)
    M.loadSize = M.loadSize + size
    -- add_ab_deal(abname)
end
function on_task_cancel(abname)
    -- local size = hashRemote:GetSize(abname)
    -- M.loadSize = M.loadSize + size
end
function on_task_exist(abname)
    -- print("on_task_exist",abname)
    if M.predic and M.predic[abname] then
        local size = hashRemote:GetSize(abname)
        M.loadSize = M.loadSize + size
    end
    -- add_ab_deal(abname)
end
function on_task_dispose(properties)
    -- M.loadSize = M.totalSize
    -- local miss = {}
    -- local deal = M.deal or {}
    -- for k,v in ipairs(M.reslist) do
    --     if not deal[v] then
    --         miss[v] = true
    --     end
    -- end
    -- print(util.get_len(miss))
    -- print(json.encode(miss))
    M.start_or_pause = false
    if M.loadSize >= M.totalSize then
        Report("DownloadRes_finish",{
        })
        ui_window_mgr:UnloadModule("ui_grayload")
    end
    CheckLoadOver()
end
function add_ab_deal(abname)
    M.deal = M.deal or {}
    M.deal[abname] = true
    -- M.loadSize = M.totalSize
end
function OnFail()
    -- print("GrayLoad OnFail")
    M.IsFail = true
    Report("DownloadRes_fail",{})
end

-- 保存当前版本
function SaveCurrentVer()
    local recordJson = preload.GetPatchRecordJson()
    local ver = files_version_mgr.GetRemoteResourceVersion()
    preload.SavePatchRecord(ver)
end

--检测下载是否完成
function CheckLoadOver()
    if CheckLoadSizeOver() --[[or not M.start_or_pause--]] then
        SaveCurrentVer()

        --后台完整资源下载完成后，创建对应版本的本地模拟HashRemote
        if util.IsCSharpClass(OldResVersionManager) and M.totalSize ~= nil and M.totalSize > 0 then
            local newVersion_res_load_helper = require "newVersion_res_load_helper"
            newVersion_res_load_helper.SaveOldResVerFile("gray_load_mgr", function()
                PlayerPrefs.Save()
                log.Warning("UseOldResVersion CreateHashRemoteVirtualOfCurrentDownLoad by gray_load_mgr")
                newVersion_res_load_mgr.OnGrayLoadOver()
            end)
        else
            newVersion_res_load_mgr.OnGrayLoadOver()
        end
    end
end

--检测下载大小
function CheckLoadSizeOver()
    if not M.totalSize  or not M.predic then
        return false
    end
    
    if M.totalSize == 0 or M.loadSize >=M.totalSize then
        return true
    end

    return false
end

function CalLen( list ,indlist)
    local indexOfInd = 1
    local ind = indlist and indlist[indexOfInd] or list.ind or #list
    local resList = {}
    local total = 0
    for i=1,list.ind or #list do
        local res = list[i]
        if not res then break end
        local size = hashRemote:GetSize(res)
        total = total + size

        if ind == i then
            resList[indexOfInd] = total
            indexOfInd = indexOfInd + 1
            ind = indlist[indexOfInd]
            -- if not ind then break end
        end
    end
    table.insert(resList,total)
    return unpack(resList)
end

function GetLoadParam()
    local grayLoadShowTypes = curGrayLoadShowType
    local loadSize = M.loadSize or 0
    local totalSize = M.totalSize or 0
    local start_or_pause = M.start_or_pause
    if grayLoadShowTypes == grayLoadShowType.preload then
        loadSize = M.PreloadLoadSize or 0
        totalSize = M.PreloadTotalSize or 0
    end

    return loadSize,totalSize,start_or_pause
end


function CheckResetPassLevel()
    if M.preload4 then return end
    util.DelayOneCallNoCoroutine("GrayLoad_CheckLoad",function (  )
        CheckLoad()
    end,2)
end


function CheckPassLevel()
    local bReset = false
	local laymain_data = require "laymain_data"
	local mapLv = laymain_data.GetPassLevel()
	if mapLv > 310 then
        bReset = true 
        -- print("CheckPassLevel-mapLv",mapLv)
        M.markCheckPassLevel = "laymain"
    end
    
    if not bReset then
        local ui_pop_mgr = require "ui_pop_mgr"
        local isOpen = ui_pop_mgr.CheckIsOpen(362,false)

        if isOpen then
		    bReset = true 
            -- print("CheckPassLevel-iChapter",iChapter)
            M.markCheckPassLevel = "peak"
        end
    end

    return bReset
	-- InitDownloadMgr(nil, nil, nil, nil, download_state.background,5)
end
event.Register(event.CHANGE_PASS_LEVEL, CheckResetPassLevel)
event.Register(event.ON_PEAK_SCENE_EVENT, CheckResetPassLevel)



function Report(eName,obj)
    local player_mgr 	= require "player_mgr"
    local playerProp = player_mgr.GetPlayerProp()

    local game_config 	= require "game_config"
	local ui_login_main_mgr = require "ui_login_main_mgr"
    local q1sdk 	= require "q1sdk"
    obj.pid = game_config.CHANNEL_ID
    obj.uuid = q1sdk.GetUUID()
    obj.level = playerProp and playerProp.lv
    obj.seq = M.markCheckPassLevel
    obj.server_id = ui_login_main_mgr.GetLoginServerID()
    obj.role_id=player_mgr.GetPlayerRoleID()
    obj.since_start_time=Time.realtimeSinceStartup
    obj.duration = M.startime and (Time.realtimeSinceStartup - M.startime)
    event.Trigger(event.GAME_EVENT_REPORT,eName, obj)
end

--获取后台加载参数
function GetGrayLoadParam()
    return M
end

--设置后台下载UI展示类型
function SetGrayLoadShowType(grayLoadShowTypes)
    if not grayLoadShowTypes or grayLoadShowTypes == 0 then
        curGrayLoadShowType = grayLoadShowType.grayLoad
    elseif grayLoadShowTypes == 1 then
        curGrayLoadShowType = grayLoadShowType.preload
    end
end

--重置参数
function ResetGrayLoadParam()
    SetNotShowStartPause(false)
    SetGrayLoadBtnShowFinish(false)
    SetGrayLoadBtnCallBack(nil)
end

--设置不显示暂停按钮
function SetNotShowStartPause(notShowStartPause)
    M.notShowStartPause = notShowStartPause
end

--设置后台下载按钮状态显隐当下载完成时
function SetGrayLoadBtnShowFinish(grayBtnNotCloseOnFin)
    M.grayBtnNotCloseOnFin = grayBtnNotCloseOnFin
end

--设置后台下载按钮状态回调函数
function SetGrayLoadBtnCallBack(callBack)
    M.grayBtnCallBack = callBack
end

--当前下载类型是否是预下载
function IsCurGrayLoadPreloadType()
    return curGrayLoadShowType == grayLoadShowType.preload
end

Init()
