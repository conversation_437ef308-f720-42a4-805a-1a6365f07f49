--- Created by fgy.
--- DateTime: 2024/10/30 17:31
--- Des:城际货车场景

local require = require
local pairs = pairs
local typeof = typeof
local os = os
local math = math
local CS = CS
local tostring = tostring
local string = string
local tonumber = tonumber
local Edump = Edump
local next    = next
local RectTransform = CS.UnityEngine.RectTransform
local ipairs = ipairs
local game_scheme 	        = require "game_scheme"
local Quaternion = CS.UnityEngine.Quaternion

local SceneClickItem = typeof(CS.War.Game.SceneClickItem)
local ScrollRectItem = typeof(CS.UI.UGUIExtend.ScrollRectItem)
local truck_scene_base = require "truck_scene_base"
local intercity_trucks_data = require("intercity_trucks_data")
local data_mgr = require("data_mgr")
local event_carriage_define = require("event_carriage_define")
local event         = require "event"
local log = require "log"
local Canvas        = typeof(CS.UnityEngine.Canvas)
local Camera        = typeof(CS.UnityEngine.Camera)
local GameObject = CS.UnityEngine.GameObject
local util = require "util"
local effect_item = require "effect_item"
local face_item_new = require("face_item_new")
local net_carriage_module = require "net_carriage_module"
local ui_window_mgr = require "ui_window_mgr"
local intercity_trucks_enum = require "intercity_trucks_enum"
local flow_text        = require "flow_text"
local lang = require "lang"
local gw_sand_animator_helper = require "gw_sand_animator_helper"
local ImageGray = CS.War.UI.ImageGray
local intercity_trains_data = require "intercity_trains_data"
local intercity_alliance_train_mgr = require "intercity_alliance_train_mgr"
local event_allianceTrain_define = require "event_allianceTrain_define"
local screen_util = require "screen_util"
local alliance_mgr = require "alliance_mgr"
local Edump = Edump
local newclass = newclass
---所有数据存储
local _d = data_mgr:CreateData("intercity_trucks_scene")
---非服务器数据存储
local mc = _d.mde.const
---服务器数据存储
local UIUtil = UIUtil
local defaultLightRota = {x = 40,y = 330,z = 0}
local truckLightRota = {x = 40,y = 300,z = 0}
module("intercity_trucks_scene")

local M = newclass("intercity_trucks_scene",truck_scene_base)
local AB = {
    --白
    "art/greatworld/home/<USER>/carriage/mache_baivariant.prefab",
    --绿
    "art/greatworld/home/<USER>/carriage/mache_lvvariant.prefab",
    --蓝
    "art/greatworld/home/<USER>/carriage/mache_lanvariant.prefab",
    --紫
    "art/greatworld/home/<USER>/carriage/mache_zivariant.prefab",
    --橙
    "art/greatworld/home/<USER>/carriage/mache_chengvariant.prefab",
    --驯鹿车
    "art/greatworld/home/<USER>/carriage/mache_youlingvariant.prefab",
}
local firEff = "art/effects/effects/effect_model_huocheranshao/prefabs/effect_model_huocheranshao.prefab"

function M:ctor()
    self:Init()
end
---@see 初始化
function M:Init()
    truck_scene_base.Init(self)
    mc.hasInitSelf = false
    mc.hasInitOther = false
    mc.curTab = 1  --1是他人，2是自己
    mc.maxOtherTrucks = 15 --最多15辆
    --他人货车点击事件
    mc.listOthersClickItem = {}
    --展示货车品质
    mc.showInfoQuality = {
        [5] = true,
        [6] = true,
    }
    mc.selfScrolls = {}
    mc.hasInitOtherTrucks = false
    mc.hasInitSelfTrucks = false
    mc.selfTruckListItems = {}
    mc.lastInitTab = nil
    mc.addTruckY = 2
    mc.hasLoaded = false
    self.animatorHelper = gw_sand_animator_helper.new()
    self.animatorHelper.animatorState = self.animatorHelper.Walk
    --他盟火车点击事件
    mc.othersTrainClickItem = {}
    mc.hasInitOtherAllianceTrain = false
    mc.hasInitSelfAllianceTrain = false
    mc.otherCount = 1 -- 联盟火车暂时只显示1辆火车
    event.Trigger(event_carriage_define.EVENT_CARRIAGE_UNSELECT_ITEM,mc.curTab)
end

function M:OnGetScreenLightZhu()
    local battle_player = require "battle_player"
    local screenLight = battle_player.GetScreenLightObj()
    if screenLight then
      local zhuLight =  screenLight:Find("UI Light_zhu").transform
        if(zhuLight)then
            return zhuLight
        end
    end
    return nil
end

function M.SetCurTabAndSelectId(tab,select)
    mc.curTab = tab
    mc.curSelectTruck = select
end

---@see 加载完成
function M:OnLoaded()
        --主灯
    if(not mc.zhuLight)then
        mc.zhuLight = self:OnGetScreenLightZhu()
        if(mc.zhuLight)then
            mc.zhuLight.rotation = Quaternion.Euler(truckLightRota)
        end
    end
    truck_scene_base.OnLoaded(self)
    self.selfClickItem = {}
    self.canvasHud = self.transform:Find("CanvasHud"):GetComponent(Canvas)
    self.camera = self.transform:Find("Camera"):GetComponent(Camera)
    self.node1 = self.transform:Find("CanvasHud/rt_other/listPos/1").transform
    self.node2 = self.transform:Find("CanvasHud/rt_other/listPos/2").transform
    self.node3 = self.transform:Find("CanvasHud/rt_other/listPos/3").transform
    self.rt_bottom = self.transform:Find("CanvasHud/rt_other/listPos/bottom").transform

    self.other_item = self.transform:Find("otherTruckTrans/Item").gameObject
    self.other_trans = self.transform:Find("otherTruckTrans")
    self.self_trans = self.transform:Find("selfTruckTrans")
    self.rt_other = self.canvasHud.transform:Find("rt_other"):GetComponent(typeof(RectTransform))
    self.rt_self = self.canvasHud.transform:Find("rt_self")
    self.player_item = self.rt_other:Find("player_item").gameObject
    --self.player_parent = self.rt_other:Find("player_item").gameObject
    self.rt_select = self.transform:Find("selectIcon")
    for i = 1,4 do
        self.selfClickItem[i] = self.self_trans:Find(i.."").gameObject:GetComponent(SceneClickItem)
    end
    self.rt_allianceTrain = self.transform:Find("CanvasHud/rt_other/listPos/allianceTrain").transform
    self.selfAllianceTrainClickItem = self.self_trans:Find("allianceTrain").gameObject:GetComponent(SceneClickItem)
    self.other_allianceTrain = self.transform:Find("otherTruckTrans/allianceTrain"):GetComponent(SceneClickItem)

    --默认切到他人货车界面
    self:ChangeTruckTab(mc.curTab)
    mc.hasLoaded = true
    self.orginIsStretch = screen_util.IsStretched(self.rt_other)
    self:OnResetWidget()
end

function M:RegisterListener()
    self.RefreshSelfTruck = function(name,msg)
        self:OnRefreshSelfTruck(msg)
    end
    event.Register(event_carriage_define.TMSG_CARRIAGE_PRE_TRADE_RSP,self.RefreshSelfTruck)
    
    self.RefreshOtherTrucks = function(name,msg)
        if name == event_carriage_define.TMSG_CARRIAGE_LOOT_DATA_RSP then
            if mc.curTab == 1 then
                ui_window_mgr:UnloadModule("ui_intercity_trucks_lit_tip")
                self.rt_select.gameObject:SetActive(false)
            end
        end
        self:OnRefreshOtherTrucks()
    end
    event.Register(event_carriage_define.TMSG_CARRIAGE_SET_FILTER_RSP,self.RefreshOtherTrucks)
    event.Register(event_carriage_define.TMSG_CARRIAGE_LOOT_DATA_RSP,self.RefreshOtherTrucks)
    
    self.ChangeCarriageTab = function(name,tab)
        self:ChangeTruckTab(tab)
    end
    event.Register(event_carriage_define.EVENT_CARRIAGE_CHANGE_TAB,self.ChangeCarriageTab)
    
    self.TMSG_CARRIAGE_REPART_RSP = function(name,msg)
        self:InitSelfTrucks()
    end
    event.Register(event_carriage_define.TMSG_CARRIAGE_REPART_RSP,self.TMSG_CARRIAGE_REPART_RSP)

    self.TMSG_CARRIAGE_MINE_DATA_RSP = function(name,msg)
        self:RefreshSelfTrucks()
    end
    event.Register(event_carriage_define.TMSG_CARRIAGE_MINE_DATA_RSP,self.TMSG_CARRIAGE_MINE_DATA_RSP)
    
    self.RefreshOtherAllianceTrain = function(name,msg)
        -- log.Error(count,"[InitOtherTrucks] 刷新他人火车信息")
        self:InitOtherTrucks()
        self:OnRefreshOtherAllianceTrain()
    end
    event.Register(event_allianceTrain_define.TMSG_ALLIANCE_TRAIN_REFRESH_RSP,self.RefreshOtherAllianceTrain)

    self.ResetWidget = function()
        self:OnResetWidget()
        util.DelayCallOnce(0,function()
            self:InitOtherTrucks(true)
        end)
        if mc.curTab == 1 then
            ui_window_mgr:UnloadModule("ui_intercity_trucks_lit_tip")
        end
    end
    event.Register(event.SCREEN_SIZE_CHANGED, self.ResetWidget)
    --event.Register(event_carriage_define.TMSG_CARRIAGE_LOOT_DATA_RSP,self.RefreshOtherTrucks)
end

function M:UnregisterListener()
    event.Unregister(event_carriage_define.TMSG_CARRIAGE_PRE_TRADE_RSP,self.RefreshSelfTruck)
    event.Unregister(event_carriage_define.TMSG_CARRIAGE_SET_FILTER_RSP,self.RefreshOtherTrucks)
    event.Unregister(event_carriage_define.TMSG_CARRIAGE_LOOT_DATA_RSP,self.RefreshOtherTrucks)
    event.Unregister(event_carriage_define.EVENT_CARRIAGE_CHANGE_TAB,self.ChangeCarriageTab)
    event.Unregister(event_carriage_define.TMSG_CARRIAGE_REPART_RSP,self.TMSG_CARRIAGE_REPART_RSP)
    event.Unregister(event_carriage_define.TMSG_CARRIAGE_MINE_DATA_RSP,self.TMSG_CARRIAGE_MINE_DATA_RSP)
    event.Unregister(event_allianceTrain_define.TMSG_ALLIANCE_TRAIN_REFRESH_RSP,self.RefreshOtherAllianceTrain)

end

--重叠对齐布局 原始拉伸模式的预制体 在全屏外屏切换时修改对齐模式
function M:OnResetWidget()
    local rt = self.rt_other
    if self:IsValid() and rt and self.orginIsStretch then
        if (screen_util.IsFoldableScreen()) then
            rt.anchorMin = { x = 0.5, y = 0.5 }
            rt.anchorMax = { x = 0.5, y = 0.5 }
            rt.sizeDelta = { x = 720, y = 1280 }
        else
            local prePos = rt.anchoredPosition
            rt.anchorMin = { x = 0, y = 0 }
            rt.anchorMax = { x = 1, y = 1 }
            rt.offsetMin = { x = 0, y = 0 }
            rt.offsetMax = { x = 0, y = 0 }
            rt.anchoredPosition = prePos
        end
    end
end
---@see 他人货车
function M:InitOtherTrucks(needUpdatePos)
    if not self:IsValid() then
        return
    end
    self.itemScale =  self.other_item.transform.localScale
    if not mc.listPos or needUpdatePos then
        self:InitListPosNew()
    end
    mc.hasInitOtherTrucks = true
    local data = intercity_trucks_data.GetOtherTrucksData()
    local count =0
    local allCount = data and #data or 0
    local othertrain = intercity_trains_data.GetOtherTrainData()
    if data then
        for i,v in ipairs(data) do
            count = count + 1
            local curTab =  mc.listOthersClickItem[count] or nil
            if not curTab then
                curTab = {}
                curTab.clickItem = GameObject.Instantiate(self.other_item,self.other_trans):GetComponent(SceneClickItem)
                curTab.trans = curTab.clickItem.transform
                curTab.trans.localScale = self.itemScale
                curTab.effTrans1 = curTab.trans:Find("eff1").transform
                curTab.effTrans2 = curTab.trans:Find("eff2").transform
                
            end
            UIUtil.SetActive(curTab.trans,true)
            if mc.listPos[count] then
                local posz  = {x=mc.listPos[count].x,y=mc.listPos[count].y,z=mc.listPos[count].z}
                if (othertrain and #othertrain>0)then
                    posz.x = posz.x - 0.8
                end
                curTab.trans.position = posz
            else
                log.Error("[InitOtherTrucks] 获取位置信息失败",count)
            end
            curTab.clickItem.iType = v.unitid
            curTab.clickItem.onTouchItem = nil
            curTab.clickItem.onTouchItem = function(t)
                self:onTouchHandle(v,curTab.trans.position)
            end
            if v.quality then
                if  mc.showInfoQuality[v.quality] or intercity_trucks_data.GetIfWanted(v.roleinfo.roleID) then
                    self:InitTruckPlayerInfo(curTab,v)
                else
                    self:DisposeTruckPlayerInfo(curTab)
                end
                self:OnShowTruckEntity(curTab,v,count,allCount)
                local effCount = 0
                curTab.eff = curTab.eff or {}
                --log.Error("Trucks",v.lootcnt)
                if v.lootcnt and v.lootcnt > 0 then
                    for i= 1,v.lootcnt do
                        effCount = effCount + 1
                        curTab.eff[i] = curTab.eff[i] or effect_item:CEffectItem("intercity_trucks_scene"):Init(firEff, curTab["effTrans"..i],10)
                    end
                end
                if effCount <2 then
                    for i = effCount + 1, 2 do
                        if curTab.eff[i] then
                            curTab.eff[i]:Dispose()
                            curTab.eff[i] = nil
                        end
                    end
                end
            else
                curTab.clickItem.onTouchItem = nil
                UIUtil.SetActive(curTab.trans,false)
                self:OnShowTruckEntity(curTab,{quality = -1})
                self:DisposeTruckPlayerInfo(curTab)
            end
            mc.listOthersClickItem[count] = curTab
        end
      
    end
    if mc.listOthersClickItem and #(mc.listOthersClickItem) >count then
        local total =  #mc.listOthersClickItem
        for k = count + 1, total do
            local curTab =  mc.listOthersClickItem[k] or nil
            if curTab then
                curTab.clickItem.onTouchItem = nil
                UIUtil.SetActive(curTab.trans,false)
                self:OnShowTruckEntity(curTab,{quality = -1},count,allCount)
                self:DisposeTruckPlayerInfo(curTab)
            end
        end
    end
end

function M:OnShowTruckEntity(curTab,v,count,allCount)
    if curTab.quality ~= v.quality or  util.IsObjNull(curTab.truckEntity) then
        if curTab.truckEntity then
          
            curTab.truckEntity:Dispose()
            curTab.truckEntity = nil
            if curTab.eff then
                for k,v in pairs(curTab.eff) do
                    v:Dispose()
                end
                curTab.eff = nil
            end
        end
        if AB[v.quality] then
            curTab.truckEntity= self:LoadAB(AB[v.quality],curTab.trans,function(obj)
                obj.transform.localPosition = {x=0,y=0,z= mc.addTruckY}
                local cfg = game_scheme:TruckTrade_0(v.quality)
                local scale = 1 
                if(cfg and cfg.CarriageModelSize)then
                    if(not self.itemScale)then
                        self.itemScale =  self.other_item.transform.localScale
                    end
                    scale =  cfg.CarriageModelSize / 10 * self.itemScale.x
                end
                obj.transform.localScale = {x=1,y=1,z= 1}
                curTab.trans.localScale = {x=scale,y=scale,z= scale}
                self.animatorHelper:AddAnimator(obj.transform:Find("center/mache_Simple").gameObject)
            end)
            curTab.quality = v.quality
        end
    end
end

function M:InitTruckPlayerInfo(curTab,v)
    if not (v and v.roleinfo)then
        log.Error("v or v.roleinfo is nil")
        return
    end
    if not curTab.playerScrollItem then
        curTab.playerScrollItem = GameObject.Instantiate(self.player_item,self.rt_other):GetComponent(ScrollRectItem)
        curTab.faceItem = face_item_new.CFaceItem():Init(curTab.playerScrollItem:Get("transFrame"),nil, 0.9)
    end
    curTab.playerScrollItem.gameObject:SetActive(true)
    curTab.playerScrollItem.transform.position = curTab.trans.position
    local curPos = curTab.playerScrollItem.transform.localPosition
    curTab.playerScrollItem.transform.localPosition = {x= curPos.x ,y = curPos.y-55,z = curPos.z-500}
    curTab.playerScrollItem:Get("trainFrame"):SetActive(false)
    local faceStr = v.roleinfo.faceID
    if v.roleinfo.faceStr and not string.IsNullOrEmpty(v.roleinfo.faceStr) then
        faceStr = v.roleinfo.faceStr
    end
    curTab.faceItem:SetFaceInfo(faceStr,function()
        self:onTouchHandle(v,curTab.trans.position)
    end)
    curTab.faceItem:SetFrameID(v.roleinfo.frameID, true)
    curTab.playerScrollItem:Get("txtName").text = v.roleinfo.name
    curTab.playerScrollItem:Get("txt_union").text = v.roleinfo.leagueShortName or ""
    local wanted = intercity_trucks_data.GetIfWanted(v.roleinfo.roleID)
    curTab.playerScrollItem:Get("rt_steal").gameObject:SetActive(wanted ~= nil)
end

function M:DisposeTruckPlayerInfo(curTab)
    if curTab.playerScrollItem then
         curTab.playerScrollItem.gameObject:SetActive(false)
    end
end

function M:ChangeTruckTab(tab)
    if mc.lastInitTab == tab then
        return
    end
    mc.curTab = tab
    mc.lastInitTab = tab
    ui_window_mgr:UnloadModule("ui_intercity_trucks_lit_tip")
    ui_window_mgr:UnloadModule("ui_alliance_train_lit_tip")
    self.rt_select.gameObject:SetActive(false)
    event.Trigger(event_carriage_define.EVENT_CARRIAGE_UNSELECT_ITEM ,mc.curTab)

    self.rt_other.gameObject:SetActive(mc.curTab == 1)
    self.rt_self.gameObject:SetActive(mc.curTab == 2)
    self.other_trans.gameObject:SetActive(mc.curTab == 1)
    self.self_trans.gameObject:SetActive(mc.curTab == 2)
    if mc.curTab == 1 then
        if not mc.hasInitOtherTrucks then
            util.DelayCallOnce(0,function()
                self:InitOtherTrucks()
            end)
        end
        if not mc.hasInitOtherAllianceTrain then
            intercity_alliance_train_mgr.Log("[wdhc]---------------------其它联盟火车 ChangeTruckTab", mc.hasInitOtherAllianceTrain)
            util.DelayCallOnce(0,function()
                self:InitOtherAllianceTrain()
            end)
        end
    else
        --if not mc.hasInitSelfTrucks then
        self:InitSelfTrucks()
        self:InitSelfAllianceTrain()
        --log.Error("ChangeTruckTab=====InitSelfTrucks======", mc.curSelectTruck)
        --end
        if mc.curSelectTruck then
            --TODO选中图标还要设置
            local data = intercity_trucks_data.GetSelfTruckList()
            if not data then
                log.Error("自己货车数据缺失")
                return
            end
            self:onTouchHandle(data[mc.curSelectTruck] or nil,self.selfTruckPos[mc.curSelectTruck])
            mc.curSelectTruck = nil
        end
    end
end

---@see 自己货车
function M:InitSelfTrucks()
    local data = intercity_trucks_data.GetSelfTruckList()
    local allianceTrainDatas = intercity_trains_data.GetDriveAllianceTrains()
    local count = allianceTrainDatas and util.get_len(allianceTrainDatas) or 0
    if not mc.hasInitSelfTrucks then
        self.selfTruckPos = {}
        for i = 1,4 do
            mc.selfScrolls[i] = self.rt_self:Find(i):GetComponent(ScrollRectItem) 
            mc.selfScrolls[i]:Get("btn_reward").onClick:AddListener(function() self:OnSelfRewardClick(data[i]) end)  
            mc.selfScrolls[i]:Get("btn_click").onClick:AddListener(function() self:OnSelfMoveClick(data[i]) end)
            local originalWorldPos = mc.selfScrolls[i].transform.position
            local cameraToObject = originalWorldPos - self.camera.transform.position
            local t = -self.camera.transform.position.y / cameraToObject.y
            local projectedPos = self.camera.transform.position + cameraToObject * t
            if (count > 0)then
                projectedPos.x = projectedPos.x - 0.8
            end
            --log.Error("设置自己的货车位置:",pX, allianceTrainDatas and util.get_len(allianceTrainDatas))
            self.selfClickItem[i].transform.position = projectedPos
            self.selfTruckPos[i] = projectedPos
            local uiPos = mc.selfScrolls[i].transform.position
            local posX = uiPos.x
            if (count > 0)then
                uiPos.x = uiPos.x - 0.7
            end
            mc.selfScrolls[i].transform.position = uiPos
            -- local posz = mc.selfScrolls[i].transform.position
            -- local screenPos = self.camera:WorldToScreenPoint(posz)
            -- local worldPos =self.camera:ScreenToWorldPoint({x=screenPos.x,y = screenPos.y,z = screenPos.z})
            -- self.selfClickItem[i].transform.position = worldPos
            -- self.selfTruckPos[i] = worldPos
            self.selfClickItem[i].onTouchItem = function(t)
                self:onTouchHandle(data[i] or nil,uiPos)
            end
        end
        mc.hasInitSelfTrucks = true
    end
   
    if data then
        for i = 1,4 do
            self.selfClickItem[i].onTouchItem = nil
            self.selfClickItem[i].onTouchItem = function(t)
                self:onTouchHandle(data[i] or nil,self.selfTruckPos[i])
            end
            local v = data[i]
            if not v then
                log.Error("没有自己货车的初始数据=================truckid = ",i)
                --self:InitSelfTruckBeginStateByIndex(i,v)
            else
                self.selfClickItem[i].gameObject:SetActive(true)
                mc.selfTruckEntity = mc.selfTruckEntity or {}
                local curTab = mc.selfTruckEntity[v.truckid] or {}
                if not mc.selfTruckEntity[v.truckid] and AB[v.quality] then
                    curTab.trans = self.selfClickItem[i].transform
                    curTab.truckEntity = self:LoadAB(AB[v.quality],curTab.trans,function(obj)
                        obj.transform.localPosition = {x=0,y=0,z= mc.addTruckY}
                        local cfg = game_scheme:TruckTrade_0(v.quality)
                        local scale = 1 
                        if(cfg and cfg.CarriageModelSize)then
                            if(not self.itemScale)then
                                self.itemScale =  self.other_item.transform.localScale
                            end
                            scale =  cfg.CarriageModelSize / 10 * self.itemScale.x
                        end
                        obj.transform.localScale = {x=1,y=1,z= 1}
                        curTab.trans.localScale = {x=scale,y=scale,z= scale}
                        self.animatorHelper:AddAnimator(obj.transform:Find("center/mache_Simple").gameObject)
                    end)
                    curTab.quality = v.quality
                else
                    self:OnShowTruckEntity(curTab,v)
                end
                curTab.eff = curTab.eff or {}
                --log.Error("OnShowTruckEntity===",v.lootcnt)
                if v.lootcnt and v.lootcnt > 0 then
                    for t= 1,v.lootcnt do
                        curTab.eff[t] = curTab.eff[t] or effect_item:CEffectItem():Init(firEff,mc.selfScrolls[i]:Get("rt_"..t),10,function(obj)
                            --obj.transform.position =  mc.selfScrolls[v.truckid]:Get("rt"..i).position
                            UIUtil.SetLocalScale(obj.transform,100,100,100)
                        end)
                    end
                    if v.lootcnt < 2 then
                        for t = v.lootcnt + 1, 2 do
                            if curTab.eff[t] then
                                curTab.eff[t]:Dispose()
                                curTab.eff[t] = nil
                            end
                        end
                    end
                else
                    if curTab.eff then
                        for t= 1,2 do
                            if curTab.eff[t] then
                                curTab.eff[t]:Dispose()
                                curTab.eff[t] = nil
                            end
                        end
                    end
                end
              
                mc.selfTruckEntity[v.truckid] = curTab
                mc.selfScrolls[v.truckid]:Get("rt_truck"):SetActive(v and v.finishtm and v.finishtm <= os.server_time())
                self:InitSelfTruckBeginStateByIndex(i,v)
            end
        end
    end
end

---@see 初始化自己的货车状态
function M:InitSelfTruckBeginStateByIndex(index,data)
    local curTab = mc.selfTruckEntity and  mc.selfTruckEntity[index]
    if curTab and data.status == intercity_trucks_enum.CarriageStatus.CARRIAGE_STATUS_FINISH then
        if curTab.truckEntity then
            curTab.truckEntity:Dispose()
        end
        if curTab.eff then
            for k,v in pairs(curTab.eff) do
                v:Dispose()
            end
        end
        mc.selfTruckEntity[index] = nil
    end
    local showModel = M:GetShowModel(data.status)
    self.selfClickItem[index].gameObject:SetActive(showModel)
    mc.selfScrolls[index]:Get("rt_no_truck"):SetActive(not showModel)
    mc.selfScrolls[index]:Get("rt_truck"):SetActive(showModel)
    mc.selfScrolls[index]:Get("rt_add"):SetActive(data.status == intercity_trucks_enum.CarriageStatus.CARRIAGE_STATUS_UNLOCK)
    mc.selfScrolls[index]:Get("rt_reward"):SetActive(data.status == intercity_trucks_enum.CarriageStatus.CARRIAGE_STATUS_REWARD)
    mc.selfScrolls[index]:Get("rt_lock"):SetActive(data.status == intercity_trucks_enum.CarriageStatus.CARRIAGE_STATUS_LOCKED)
    local total,current = intercity_trucks_data.GetDepartureCount()
    mc.selfScrolls[index]:Get("rt_add"):GetComponent(typeof(ImageGray)):SetEnable(current>=total)
end

function M:GetShowModel(status)
   return status == intercity_trucks_enum.CarriageStatus.CARRIAGE_STATUS_DEPART or status == intercity_trucks_enum.CarriageStatus.CARRIAGE_STATUS_REWARD 
end

---@see 他人奖励点击
function M:OnSelfRewardClick(data)
    --local data = intercity_trucks_data.GetSelfTruckList()
    if data and data.truckid then
        net_carriage_module.MSG_CARRIAGE_TRADE_REWARD_REQ(data and data.truckid)
    end
end

---@see 他人移动点击
function M:OnSelfMoveClick( truckData, index)
    local _index = truckData.truckid
    local data = intercity_trucks_data.GetSelfTruckList()
    local total,current = intercity_trucks_data.GetDepartureCount()
    if data[_index] then
        if data[_index].status == intercity_trucks_enum.CarriageStatus.CARRIAGE_STATUS_UNLOCK then
            if current >= total then
                flow_text.Add(lang.Get(658089))
                return
            end
            log.Error(_index)
            ui_window_mgr:ShowModule("ui_intercity_trucks_departure",nil,nil,{curSelectTruckId=_index,truckData =data[_index]})
        elseif data[_index].status == intercity_trucks_enum.CarriageStatus.CARRIAGE_STATUS_LOCKED then
            --TODO 飘字
            flow_text.Add(lang.Get(658087))
        else
            self:ShowLitTip(true,data[_index])
            --ui_window_mgr:ShowModule("ui_intercity_trucks_lit_tip",nil,nil,{isSelf = true,data =data[index]})
        end
    end
end

---@see 刷新他人货车监听服务器回复及
function M:OnRefreshOtherTrucks()
    --ui_window_mgr:UnloadModule("ui_intercity_trucks_lit_tip")
    --self.rt_select.gameObject:SetActive(false)
    if mc.hasLoaded then
        util.DelayCallOnce(0,function()
            self:InitOtherTrucks(true)
        end)
    end
end

---@see 刷新自己的货车单个货车
function M:OnRefreshSelfTruck(msg)
    if msg and msg.truck then
        local curTab = mc.selfTruckEntity[msg.truck.truckid] or {}
        self:OnShowTruckEntity(curTab,msg.truck)
        mc.selfTruckEntity[msg.truck.truckid] = curTab
    end
end

---@see 刷新自己的货车
function M:RefreshSelfTrucks()
    self:InitSelfTrucks()
    if mc.curSelectTruck then
        --TODO选中图标还要设置
        local data = intercity_trucks_data.GetSelfTruckList()
        if not data then
            log.Error("自己货车数据缺失")
            return
        end
        self:onTouchHandle(data[mc.curSelectTruck] or nil,self.selfTruckPos[mc.curSelectTruck])
        mc.curSelectTruck = nil
    end
end

---@see 场景·点击
function M:onTouchHandle(v,pos)
    --当前如果是他人货车
    local delKey = 9
    if mc.curTab == 1 then
        --展示他人货车详情界面
        self:ShowLitTip(false,v)
        --if v.truckid and self.selfClickItem[v.truckid] then
            --self.rt_select.position = {x=pos.x+ delKey*0.05,y=pos.y-delKey*0.606,z=pos.z+delKey - 1}
            self.rt_select.position = {x=pos.x,y=pos.y-0.25,z=pos.z}
            self.rt_select.gameObject:SetActive(true)
            event.Trigger(event_carriage_define.EVENT_CARRIAGE_SELECT_ITEM, mc.curTab)
            event.EventReport("TruckTrade_view_onUI", {
            Truck_quality = v.quality,
            Looted_times = v.Looted_times or 0,
            Is_wanted = intercity_trucks_data.GetIfWanted(v.roleinfo.roleID) and 1 or 0
        })
        --end
    else
        --展示自己货车详情界面
        if v.status == intercity_trucks_enum.CarriageStatus.CARRIAGE_STATUS_UNLOCK then
            local selfTrucks = intercity_trucks_data.GetSelfTruckList()
            --log.Error(v.truckid)
            ui_window_mgr:ShowModule("ui_intercity_trucks_departure",nil,nil,{curSelectTruckId=v.truckid,truckData = selfTrucks[v.truckid]})
        elseif v.status == intercity_trucks_enum.CarriageStatus.CARRIAGE_STATUS_LOCKED then
            --TODO 飘字
            flow_text.Add(lang.Get(658087))
        else
            --if v.quality then
            --    ui_window_mgr:ShowModule("ui_intercity_trucks_lit_tip",nil,nil,{isSelf = true,data= v})
            --end
            if v.status == intercity_trucks_enum.CarriageStatus.CARRIAGE_STATUS_REWARD then
                --ui_window_mgr:ShowModule("uintercity_trucks_detail",nil,nil,v)
                net_carriage_module.MSG_CARRIAGE_TRADE_REWARD_REQ(v.truckid)
            else
                self:ShowLitTip(true,v)
                --self.rt_select.position = {x=pos.x+ delKey*0.05,y=pos.y-delKey*0.606,z=pos.z+delKey}
                self.rt_select.position = {x=pos.x,y=pos.y-0.25,z=pos.z}
                self.rt_select.gameObject:SetActive(true)
                event.Trigger(event_carriage_define.EVENT_CARRIAGE_SELECT_ITEM, mc.curTab)
            end
        end
    end
end

function M:ShowLitTip(isSelf,data)
    if ui_window_mgr:IsModuleShown("ui_intercity_trucks_lit_tip") then
        --local ui_intercity_trucks_lit_tip_controller = require "ui_intercity_trucks_lit_tip_controller"
        --ui_intercity_trucks_lit_tip_controller.OnOutRefreshTruck(data)
        if not data.quality or data.quality == 0 then
            return
        end
        event.Trigger(event_carriage_define.EVENT_CARRIAGE_CHANGE_LIT_TIP,data)
    else
        if not data.quality or data.quality == 0 then
            return
        end
        if ui_window_mgr:IsModuleShown("ui_alliance_train_lit_tip") then
            ui_window_mgr:UnloadModule("ui_alliance_train_lit_tip")
        end
        ui_window_mgr:ShowModule("ui_intercity_trucks_lit_tip",nil,nil,{isSelf = isSelf,data= data})
    end
end

function M:InitListPosNew()
    mc.maxY = self.node1.position.y
    mc.minY = self.rt_bottom.position.y
    local totalHeight = mc.maxY - mc.minY
    mc.addY = (mc.maxY - mc.minY)/3.5
    local rowSpacingMultiplier = 1.15 
    local newAddY = mc.addY * rowSpacingMultiplier
    mc.listPos = {}
    math.randomseed(tonumber(tostring(os.time()):reverse():sub(1, 6)))
    local totalRows = math.ceil(15/3)  
    local totalSpacing = newAddY * (totalRows - 1)
    local topMargin = (totalHeight - totalSpacing) / 2
    
    for i = 1, 15 do
        local randomY = math.random(newAddY*0.15*100, newAddY*0.35*100)
        local row = (i+2)%3 
        local col = math.floor((i-1)/3)  
        local baseZ = mc.minY + topMargin + col * newAddY
        local posZ = baseZ + randomY/100
        
        local posX = self.node1.position.x
        if i % 2 == 0 then posX = self.node2.position.x end
        if i % 3 == 0 then posX = self.node3.position.x end
        mc.listPos[i] = {x = posX, y = 0, z = posZ}
        local screenPos = self.camera:WorldToScreenPoint(mc.listPos[i])
        mc.listPos[i] = self.camera:ScreenToWorldPoint({
            x = screenPos.x,
            y = screenPos.y,
            z = screenPos.z
        })
    end
    util.RandSort(mc.listPos)
end

function M:InitListPos()
    mc.minX,mc.maxY = self.rt_left.position.x,self.rt_left.position.y
    mc.maxX,mc.minY = self.rt_right.position.x,self.rt_right.position.y
    mc.addY = (mc.maxY - mc.minY)/4
    mc.addX = (mc.maxX - mc.minX)/5
    --log.Error("minX",mc.minX,"maxX",mc.maxX,"minY",mc.minY,"maxY",mc.maxY,"addY",mc.addY,"addX",mc.addX)
    mc.listPos = {}
    for i = 1,15 do
        math.randomseed(tonumber(tostring(os.time()):reverse():sub(1, 6) .. (i.."" or "")))
        local randomY = math.random(0, mc.addY*0.3*100) -mc.addY*0.15*100
        local randomX = math.random(0, mc.addX*0.1*100) -mc.addY*0.05*100
        local row = (i+2)%3
        local col = math.floor((i-1)/3)
        mc.listPos[i] = {x = mc.minX+mc.addX*0.6+row*mc.addX + randomX/100, y = mc.minY +col*mc.addY + randomY/100,z = self.rt_right.position.z}
        local screenPos = self.camera:WorldToScreenPoint(mc.listPos[i])
        mc.listPos[i]=self.camera:ScreenToWorldPoint({x=screenPos.x,y = screenPos.y,z = screenPos.z})
    end
    util.RandSort(mc.listPos)
end


---------------------------联盟火车相关 start--------------------------------

---@see 刷新他人联盟火车监听服务器回复
function M:OnRefreshOtherAllianceTrain()
    ui_window_mgr:UnloadModule("ui_alliance_train_lit_tip")
    util.DelayCallOnce(0,function()
        self:InitOtherAllianceTrain()
    end)
end
function M:InitAllianceTrainPos()
    local screenPos = self.camera:WorldToScreenPoint(self.rt_allianceTrain.position)
    mc.listOtherPos = self.camera:ScreenToWorldPoint({x = screenPos.x, y = screenPos.y, z = screenPos.z})
    intercity_alliance_train_mgr.Log("[wdhc]---------------------其它联盟火车 mc.listOtherPos", mc.listOtherPos)
end

function M:InitTrainPlayerInfo(curTab, data,isSelf)
    if not data then
        log.Error("data is nil")
        return
    end
    local transParent = isSelf and self.rt_self or self.rt_other 
    if not curTab.playerScrollItem then
        curTab.playerScrollItem = GameObject.Instantiate(self.player_item, transParent):GetComponent(ScrollRectItem)
        curTab.faceItem = face_item_new.CFaceItem():Init(curTab.playerScrollItem:Get("transFrame"), nil, 0.9)
    end
    curTab.playerScrollItem.gameObject:SetActive(true)
    if not isSelf then
        local curPos = curTab.effTrans1.position
        curTab.playerScrollItem.transform.position =  {x = curPos.x - 0.8 , y = curPos.y + 6, z= curPos.z}
    else
        if not mc.selfAlliance then
            mc.selfAlliance = self.rt_self:Find("allianceTrain"):GetComponent(ScrollRectItem) 
        end
        local curPos =  mc.selfAlliance:Get("rt_"..1).position
        curTab.playerScrollItem.transform.position =  {x = curPos.x - 0.6 , y = curPos.y + 6, z= curPos.z}
    end
    local imgbg = curTab.playerScrollItem:Get("imgBg")
    ---只设置一次
    if not curTab.imgbgPos then
        curTab.imgbgPos = imgbg.transform.localPosition
        local imgPos = curTab.imgbgPos
        if isSelf then
            -- 暂时定一样，看之后是否有区分问题
            imgPos.y = imgPos.y + 80 
        else
            imgPos.y = imgPos.y + 80
        end
        imgbg.transform.localPosition = imgPos
    end
    local trainFrame = curTab.playerScrollItem:Get("trainFrame")
    trainFrame:SetActive(true)

    local faceStr = data.avatarId
    if data.faceStr and not string.IsNullOrEmpty(data.faceStr) then 
        faceStr = data.faceStr
    end
    if isSelf then
        local leaderData = intercity_trains_data.GetTrainCarriageMemberData(data.leader_rold_id)
        or alliance_mgr.GetAllianceRoleInfo(data.leader_rold_id)
        if leaderData then
            faceStr = leaderData.faceStr
            data.avatarFrameId = leaderData.frameID
            data.roleName = leaderData.strName
        end
    end
    curTab.faceItem:SetFaceInfo(faceStr,function()
        self:onTouchHandle(data,curTab.trans.position)
    end)
    curTab.faceItem:SetFrameID(data.avatarFrameId, true)
    curTab.playerScrollItem:Get("txtName").text = data.roleName
    if isSelf then
        curTab.playerScrollItem:Get("txt_union").text = "" 
    else
        curTab.playerScrollItem:Get("txt_union").text = data.allianceShortName or ""
    end
    curTab.playerScrollItem:Get("rt_steal").gameObject:SetActive(false)
end

---@see 他盟火车
function M:InitOtherAllianceTrain()
    if not self:IsValid() then
        return
    end
    if not mc.listOtherPos then
        self:InitAllianceTrainPos()
    end
    mc.hasInitOtherAllianceTrain = true
    local data = intercity_trains_data.GetOtherTrainData()
    if data and #data > 0 then
        --Tip：第一版本策划说只会有一辆其它联盟火车，所以UI界面只按一辆去做(mc.otherCount=1)；但服务器认为日后策划会改成多辆火车，此处数据按多辆存为数组方便日后扩展
        for i = 1, mc.otherCount do
            ---@type TRAIN_OTHER_DATA
            local allianceTrain = data[i]
            intercity_alliance_train_mgr.Log("[wdhc]---------------------其它联盟火车 allianceTrain", Edump(allianceTrain))
            if allianceTrain then
                local curTab =  mc.othersTrainClickItem[i] or nil
                if not curTab then
                    curTab = {}
                    curTab.clickItem = self.other_allianceTrain
                    curTab.trans = curTab.clickItem.transform
                    -- curTab.trans.localScale = self.other_allianceTrain.transform.localScale
                    curTab.effTrans1 = curTab.trans:Find("eff1").transform
                    curTab.effTrans2 = curTab.trans:Find("eff2").transform                
                end
                local posy = mc.listOtherPos
                posy.y = 0
                curTab.trans.position = posy
                -- curTab.clickItem.iType = allianceTrain.unitid
                curTab.clickItem.onTouchItem = function(t)
                    self:onTouchHandleAllianceTrain(allianceTrain, curTab.trans.position)
                end
                if allianceTrain.trainCfgID then
                    self:InitTrainPlayerInfo(curTab, allianceTrain)
                    self:OnShowTrainEntity(curTab, allianceTrain.trainCfgID)
                    local effCount = 0
                    curTab.eff = {}
                    local remainRewardCount = 0
                    for k,v in ipairs(allianceTrain.remainReward) do
                        remainRewardCount = remainRewardCount + 1
                    end
                    local allRewardCount = intercity_alliance_train_mgr.GetAllianceTrainCount(allianceTrain.trainCfgID)
                    if allRewardCount ~= remainRewardCount then
                        curTab.eff[1] = curTab.eff[1] or effect_item:CEffectItem("intercity_trucks_scene"):Init(firEff, curTab["effTrans"..1],10)
                    else
                            if curTab.eff[1] then
                                curTab.eff[1]:Dispose()
                                curTab.eff[1] = nil
                            end
                    end

                    -- 着火
                    --log.Error("Trucks",v.lootcnt)
                    -- if v.lootcnt and v.lootcnt > 0 then
                    --     for i= 1,v.lootcnt do
                    --         effCount = effCount + 1
                    --         curTab.eff[i] = curTab.eff[i] or effect_item:CEffectItem("intercity_trucks_scene"):Init(firEff, curTab["effTrans"..i],10)
                    --     end
                    -- end
                    -- if effCount <2 then
                    --     for i = effCount + 1, 2 do
                    --         if curTab.eff[i] then
                    --             curTab.eff[i]:Dispose()
                    --             curTab.eff[i] = nil
                    --         end
                    --     end
                    -- end
                end
                curTab.trainCfgID = allianceTrain.trainCfgID
                mc.othersTrainClickItem[i] = curTab
            else
                break
            end
        end
    else
        --无火车的时候需要清理火车
        for i,v in pairs(mc.othersTrainClickItem) do
            if v.trainEntity and v.trainEntity.Dispose then
                v.trainEntity:Dispose()
            end
            if v.faceItem then
                v.faceItem:Dispose()
            end
            if v.playerScrollItem then
                v.playerScrollItem.gameObject:SetActive(false)
            end
            if v.eff and v.eff.Dispose then
                v.eff:Dispose()
                v.eff = nil
            end
        end
        mc.othersTrainClickItem = {}
    end
end

---@see 自己联盟火车
function M:InitSelfAllianceTrain()
    local allianceTrainDatas = intercity_trains_data.GetDriveAllianceTrains()
    if not mc.hasInitSelfAllianceTrain then
        mc.selfAlliance = self.rt_self:Find("allianceTrain"):GetComponent(ScrollRectItem) 
        local originalWorldPos = mc.selfAlliance.transform.position
        local cameraToObject = originalWorldPos - self.camera.transform.position
        local t = -self.camera.transform.position.y / cameraToObject.y
        local projectedPos = self.camera.transform.position + cameraToObject * t
        self.selfAllianceTrainClickItem.transform.position = projectedPos
        self.selfAllianceTrainPos = projectedPos
        -- local screenPos = self.camera:WorldToScreenPoint()
        -- local worldPos =self.camera:ScreenToWorldPoint({x = screenPos.x, y = screenPos.y, z = 600})
        -- self.selfAllianceTrainClickItem.transform.position = worldPos
        -- self.selfAllianceTrainPos = worldPos
        mc.hasInitSelfAllianceTrain = true
    end
    --Tip：策划说可能有多辆火车同时在行驶中 但第一版本只会有一辆 未给出多辆火车的显示方式 此处数据按多辆存为数组方便日后扩展 但UI界面仍只按一辆去做 后续有优化改为可行驶多辆火车再修改界面
    if allianceTrainDatas and util.get_len(allianceTrainDatas) > 0 then
        local id = next(allianceTrainDatas)
        ---@type TRAIN_ITEM_DATA
        local allianceTrainData = allianceTrainDatas[id]
        if allianceTrainData then
            self.selfAllianceTrainClickItem.onTouchItem = nil
            self.selfAllianceTrainClickItem.onTouchItem = function(t)
                self:onTouchHandleAllianceTrain(allianceTrainData or nil, self.selfAllianceTrainPos)
            end
            self.selfAllianceTrainClickItem.gameObject:SetActive(true)
            local curTab = mc.selfAllianceTrainEntity or {}
            local cfg = intercity_alliance_train_mgr.GetAllianceTrainCfgByID(allianceTrainData.trainCfgID)
            local model = cfg and cfg.truckModel
            if not model then
                log.Error("@策划 TradeShip.csv未配置火车模型, 火车ID:", allianceTrainData.trainCfgID)
                return
            end

            if not mc.selfAllianceTrainEntity and model then
                curTab.trans = self.selfAllianceTrainClickItem.transform
                curTab.trainEntity = self:LoadAB(cfg.truckModel, curTab.trans, function(obj)
                    obj.transform.localPosition = {x = 0,y = 0,z = mc.addTruckY}
                    -- self.animatorHelper:AddAnimator(obj.transform:Find("center/feichuan_Simple").gameObject)
                end)
                curTab.model = model
                curTab.trainCfgID = allianceTrainData.trainCfgID
                self:InitTrainPlayerInfo(curTab, allianceTrainData,true)
            else
                self:InitTrainPlayerInfo(curTab, allianceTrainData,true)
                self:OnShowTrainEntity(curTab, allianceTrainData.trainCfgID)
            end
            local remainRewardCount =  allianceTrainData.all_remain_list and #allianceTrainData.all_remain_list or 0
            local allRewardCount = intercity_alliance_train_mgr.GetAllianceTrainCount(allianceTrainData.trainCfgID)
            if allRewardCount ~= remainRewardCount then
                curTab.eff = curTab.eff or effect_item:CEffectItem():Init(firEff,mc.selfAlliance:Get("rt_"..1),10,function(obj)
                --obj.transform.position =  mc.selfScrolls[v.truckid]:Get("rt"..i).position
                UIUtil.SetLocalScale(obj.transform,200,200,200)
            end)
            else
                    if curTab.eff then
                        curTab.eff:Dispose()
                        curTab.eff = nil
                    end
            end

            mc.selfAllianceTrainEntity = curTab
        end
    end
end

---@see 刷新联盟火车
function M:OnShowTrainEntity(curTab, trainCfgID)
    if curTab.trainCfgID ~= trainCfgID then
        local cfg = intercity_alliance_train_mgr.GetAllianceTrainCfgByID(trainCfgID)
        local model = cfg and cfg.truckModel
        if not model then
            log.Error("@策划 TradeShip.csv未配置火车模型, 火车ID:", trainCfgID)
            return
        end
        if curTab.model ~= model then
            if curTab.trainEntity then
                if util.IsObjNull(curTab.trainEntity.transform) then
                    -- self.animatorHelper:RemoveAnimator(curTab.trainEntity.transform:Find("center/feichuan_Simple").gameObject)
                end
                curTab.trainEntity:Dispose()
                curTab.trainEntity = nil
            end
            curTab.trainEntity = self:LoadAB(model, curTab.trans, function(obj)
                obj.transform.localPosition = {x = 0, y = 0, z = mc.addTruckY}
                obj.transform.localScale = {x = 1, y = 1, z = 1}
                -- self.animatorHelper:AddAnimator(obj.transform:Find("center/feichuan_Simple").gameObject)
            end)
            curTab.model = model
            curTab.trainCfgID = trainCfgID
        end
    end
end
---@see 场景上点击联盟火车
---@param v TRAIN_ITEM_DATA or TRAIN_OTHER_DATA
function M:onTouchHandleAllianceTrain(v, pos)
    --当前如果是其他联盟货车
    local delKey = 10
    intercity_alliance_train_mgr.Log("[wdhc]---------------------自己的联盟火车 onTouchHandleAllianceTrain mc.curTab: ", mc.curTab, ", mc.curTab == 1: ", mc.curTab == 1, Edump(v))
    self:ShowAllianceTrainLitTip(mc.curTab == 2, v)
    self.rt_select.position = {x = pos.x, y = pos.y - delKey * 0.606, z = pos.z + delKey}
    self.rt_select.gameObject:SetActive(true)
    event.Trigger(event_carriage_define.EVENT_CARRIAGE_SELECT_ITEM, mc.curTab)
end

---@see 显示联盟火车详细信息
---@param data TRAIN_ITEM_DATA or TRAIN_OTHER_DATA
function M:ShowAllianceTrainLitTip(isSelf, data)
    if ui_window_mgr:IsModuleShown("ui_intercity_trucks_lit_tip") then
        ui_window_mgr:UnloadModule("ui_intercity_trucks_lit_tip")
    end
    intercity_alliance_train_mgr.Log("[wdhc]---------------------自己的联盟火车 ShowAllianceTrainLitTip", isSelf , ", data: ", Edump(data))
    ui_window_mgr:ShowModule("ui_alliance_train_lit_tip", nil, nil, {isSelf = isSelf, data = data})
end


---------------------------联盟火车相关 end--------------------------------

---------------------------场景销毁回收--------------------------------

function M:DisposeOtherTruckEntity()
    for i,v in pairs(mc.listOthersClickItem) do
        if v.truckEntity then
            v.truckEntity:Dispose()
        end
        if v.faceItem then
            v.faceItem:Dispose()
        end
        if v.eff then
            for k ,eff in pairs(v.eff) do
                eff:Dispose()
            end
        end
    end
    mc.listOthersClickItem = {}
    for i,v in pairs(mc.othersTrainClickItem) do
        if v.trainEntity then
            v.trainEntity:Dispose()
        end
        if v.faceItem then
            v.faceItem:Dispose()
        end
        if v.eff and v.eff.Dispose then
            v.eff:Dispose()
            v.eff = nil
        end
    end
    mc.othersTrainClickItem = {}
end

function M:DisposeSelfTruckEntity()
    if mc.selfTruckEntity then
        for i,v in pairs(mc.selfTruckEntity) do
            if v.truckEntity then
                v.truckEntity:Dispose()
            end
            if v.eff then
                for k ,eff in pairs(v.eff) do
                    eff:Dispose()
                end
            end
        end
    end
    mc.selfTruckEntity = {}
    if mc.selfAllianceTrainEntity then
        mc.selfAllianceTrainEntity.trainEntity:Dispose()
    end
    mc.selfAllianceTrainEntity = nil
end

---@see 场景·销毁
function M:Dispose()
    M:Init()
    if(mc.zhuLight)then
        mc.zhuLight.rotation = Quaternion.Euler(defaultLightRota)
    end
    mc.zhuLight = nil
    for i = 1,4 do
        if  mc.selfScrolls[i] then
            mc.selfScrolls[i]:Get("btn_reward").onClick:RemoveAllListeners()
            mc.selfScrolls[i]:Get("btn_click").onClick:RemoveAllListeners()
        end
    end
    truck_scene_base.Dispose(self)
    self:DisposeOtherTruckEntity()
    self:DisposeSelfTruckEntity()
end

return M