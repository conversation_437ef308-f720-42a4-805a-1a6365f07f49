--- Created by cxq.
--- DateTime: 2025/07/11
--- Des:r4、r5联盟邀请
local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type
local os = os
local Edump = Edump

local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local alliance_data = require "alliance_data"
local lang = require "lang"
local log = require "log"
local event_alliance_define = require "event_alliance_define"

--region Controller Life
module("ui_alliance_invite_controller")
local controller = nil
local UIController = newClass("ui_alliance_invite_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)    
end

function UIController:OnShow()
    self.__base.OnShow(self)
    self:InitView()
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents() 
    self.refreshInfoView = function()
        self:RefreshBtn()
    end
    self:RegisterEvent(event_alliance_define.REFRESH_SHARE_CD, self.refreshInfoView)
    
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

function UIController:InitView()
    self.CData.userdata = alliance_data.GetUserAllianceData()
    -- log.Warning("[lmyqh] self.CData.userdata", Edump(self.CData.userdata))
    if self.CData.userdata and self.CData.userdata.allianceId then
        local flagData = alliance_data.GetFlagIdData(self.CData.userdata.flag)
        if flagData then
            self.CData.userdata.allianceIconId = flagData.iconID;
        end
        -- self.CData.userdata.r5Info = alliance_user_data.GetBossData(true)
        --刷新联盟主界面数据
        self:TriggerUIEvent("InitUIData", self.CData.userdata)
    end
    self:RefreshBtn()
end

function UIController:RefreshBtn()
    self.CData.refreshTime = alliance_data.GetAllianceShareTime()
    -- log.Warning("[lmyqh] refreshCD", self.CData.refreshTime)
    self:TriggerUIEvent("RefreshBtn", self.CData.refreshTime)
end
--region Controller Logic
function  UIController:OnBtnShareClickedProxy()
    local alliance_pb = require "alliance_pb"
    local pbMsg = alliance_pb.TAllianceBase()
    pbMsg.allianceId = self.CData.userdata.allianceId
    pbMsg.flag = self.CData.userdata.flag
    pbMsg.shortName = self.CData.userdata.shortName
    pbMsg.allianceName = self.CData.userdata.allianceName
    pbMsg.count = self.CData.userdata.count
    pbMsg.language = self.CData.userdata.language
    pbMsg.power = self.CData.userdata.oldPower
    pbMsg.newPower = self.CData.userdata.power
    pbMsg.applySet = self.CData.userdata.applySet
    pbMsg.lvLimit = self.CData.userdata.lvLimit
    pbMsg.ceLimit = self.CData.userdata.ceLimit
    local dataToString = pbMsg:SerializeToString()

    local ShareUtil = require "share_util"
    ShareUtil.OpenAllianceShare(dataToString)
end
function  UIController:OnBtnCdClickedProxy()
end
function  UIController:OnBtnCloseBtnClickedProxy()
	ui_window_mgr:UnloadModule(self.view_name)
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
