--- Created by 农勇智.
--- DateTime: 2025/6/26 10:35
--- Des:狂暴领主类

local pairs = pairs
local ipairs = ipairs
local table = table
local require = require
local data_mgr = require "data_mgr"
local event = require "event"
local game_scheme = require "game_scheme"
local os = os
local util = require "util"
local GWConst = require "gw_const"
local gw_ed = require("gw_ed")
local event_ZombieApocalypse_define = require("event_ZombieApocalypse_define")

---@class GWMadnessLordData
module("gw_madness_lord_data")
local M = {}
local self = M --简化写法，静态类中直接也用Self获取自身
---所有数据存储
local _d = data_mgr:CreateData("gw_madness_lord_data")
---非服务器数据存储
local mc = _d.mde.const

local isInit = false --初始化标记

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
---@see 初始化
function M.Init()
    if isInit then
        return
    end
    isInit = true
    mc.timeTicker = nil;
    mc.activityId = 0 --活动id
    mc.activityData = {
        activityState = GWConst.EnZombieApocalypseState.enZombieApocalypseState_Preview, --活动状态，表面当前活动位于哪个阶段？默认取预告期
        activityRunningState = GWConst.EnZombieApocalypsePointState.enZombieApocalypsePointState_Prepare, --当前据点状态。
        entitySid = -1,--据点实体的sid
        cluePoint = 0,--当前持有的线索点
        maxUnlockDifficulty = 0, --当前最大的解锁难度。
        cooldownTime = 0, --当前的冷却时间
        appointmentTime = 0, --当前预约的时间
        appointmentDifficulty = 0, --当前预约难度
        wave = 0, --当前的波数
        difficulty= 0,
        clockDown = 0, --倒计时
        allDefendNumber = 0,--全部防御数
    };
    mc.chatData = {} --获取到的聊天数据，以聊天标识id为key
    mc.poisonData = {} --获取到的中毒数据，包括当前谁中毒了，中毒层数等等
end

function M.GetActivityData()
    return mc.activityData
end

function M.GetActivityId()
    return mc.activityId
end

function M.GetPoisonData()
    return mc.poisonData
end

function M.SetPoisonData(msg)
    mc.poisonData = {}
    for i,v in ipairs(msg.poisonedInfo) do
        local temp = 
        {
            count = 0,
            playerList = {}
        }
        for j,k in ipairs(v.nHelpList) do
            temp.playerList[k] = true
        end
        temp.count = #v.nHelpList
        mc.poisonData[v.nPoisonedRoleId] = temp
    end
    event.Trigger(event_ZombieApocalypse_define.ZOMBIE_POISON_DATA_UPDATE)
end

function M.SetActivityData(msg)
    mc.activityId = msg.tActivityData.nActivityId
    mc.activityData = {
        activityState = msg.tActivityData.nState or GWConst.EnZombieApocalypseState.enZombieApocalypseState_Preview, --活动状态，表面当前活动位于哪个阶段？默认取预告期
        activityRunningState = msg.tActivityData.nRunningState or GWConst.EnZombieApocalypsePointState.enZombieApocalypsePointState_Prepare, --当前据点状态。
        entitySid = msg.tActivityData.nEntitySid or -1,--据点实体的sid
        cluePoint = msg.tActivityData.nCluePoint or 0,--当前持有的线索点
        maxUnlockDifficulty = msg.tActivityData.nMaxUnlockDifficulty or 1, --当前最大的解锁难度。
        cooldownTime = msg.tActivityData.nCooldownTime or 0, --当前的冷却时间
        appointmentTime = msg.tActivityData.nAppointmentTime or 0, --当前预约的时间
        appointmentDifficulty = msg.tActivityData.nAppointmentDifficulty or 0, --当前预约难度
        difficulty = msg.tActivityData.nDifficulty or 1,--当前开的难度
        wave = msg.tActivityData.nWave or 0, --当前的波数
        clockDown = msg.tActivityData.nCountdown or 0, --倒计时
        allDefendNumber = msg.nAllDefendNumber or 0, --总防御数
    };
    event.Trigger(event_ZombieApocalypse_define.TMSG_ZOMBIEAPOCALYPSE_SET_ACTIVITY_DATA)
end

function M.SetChatData(msg)
    if not mc.chatData[msg.strChat] then
        mc.chatData[msg.strChat] = msg.chatData
    end
    event.Trigger(event_ZombieApocalypse_define.ZOMBIEAPOCALYPSE_CHAT_UPDATE,msg)
end

function M.GetChatData(strChat)
    if mc.chatData[strChat] then
        return mc.chatData[strChat]
    end
    return nil
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
--- 数据清理
function M.Dispose()
    isInit = false
    
    if mc.timeTicker then --销毁广播计时器
        util.RemoveDelayCall(mc.timeTicker)
        mc.timeTicker = nil;
    end
    mc.chatData = {}
    mc.activityData = {}
end
return M