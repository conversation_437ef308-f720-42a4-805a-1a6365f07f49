--- Created by fgy
--- Changed by <PERSON><PERSON>
--- DateTime: 2025/3/5
--- Des: 沙盘实体基类

local require = require
local table = table

local newclass = newclass
local UIUtil = UIUtil
local Edump = Edump

local EntityManager = require "entity_manager"
local MapUtil = require "gw_map_util"
local res_pool = require "res_pool"
local async_entity = require "async_entity"
local log = require "log"
local util = require "util"
local gw_disposable_object = require "gw_disposable_object"
local EntityHybridUtility = CS.Unity.Entities.EntityHybridUtility

module("sand_entity_base")

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

---@class SandEntityBase : GWDisposableObject
local SandEntityBase = newclass("sand_entity_base", gw_disposable_object)

--entity模式
local entityMode = EntityManager.SpriteEcs

function SandEntityBase:ctor()
    gw_disposable_object.ctor(self)
    self:Init()
end

function SandEntityBase:Init()
    ---客户端生成得compId
    self.isShow = true
    self.isLoaded = false
    self.compId = nil
    self.compName = nil

    ---其他数据
    self.entity = nil
    self.hudData = nil
    self.serData = nil
end

function SandEntityBase:InitComp(data)
    if not data.resPath then
        log.Error("data.resPath = nil " .. Edump(data))
    else
        self.isShow = true
        self:InstantiateModelAsync(data.resPath, data.parent);
    end
end

---开始异步实例化物体
---@param path string 模型路径
---@param parent userdata 实例化成功回调（可以设置为空）
function SandEntityBase:InstantiateModelAsync(path, parent)
    self.parent = parent
    self.path = path
    local rootName = ""
    if not util.IsObjNull(parent) and not util.IsObjNull(parent.parent) then
        rootName = parent.parent.gameObject.name
    end

    local bcanvas = rootName == "HudCanvas"
    local bHubCamera = rootName == "HUDLayer"
    local radius
    local serData = self.serData
    local hudData = self.hudData
    if serData and serData.sid then
        radius = MapUtil.GetMap():GetRadiusBySid(serData.sid)
        if serData.dotList_txy and self.compName ~= "gw_comp_march_line" then
            radius = 5
        end
    else
        radius = 10000 -- 无serData 给一个大的半径
    end
    local eIdx = nil
    self.rentity, eIdx = EntityManager.AddEntity(self, bcanvas or bHubCamera, bcanvas, radius)
    local pos = serData and serData.worldPos or hudData.targetPos
    if pos then
        EntityManager.SetPosition(self.rentity, pos)
    end

    if hudData.targetEntity then
        EntityManager.SetParent(self.rentity, hudData.targetEntity)
    end

    self:OnSetLodRange(hudData.minLod, hudData.maxLod, hudData.minInnerLod, hudData.maxInnerLod)
    if self.hudData.loadImmediate or not EntityManager.Streaming then
        self:_InstantiateModelAsync()
    end

    if serData then
        if hudData.strProps then
            serData.strProps:AddListener(hudData.strProps, self.OnPropChanged, self)
        end
        if hudData.resProps then
            serData.resProps:AddListener(hudData.resProps, self.OnPropChanged, self)
        end
        if hudData.props then
            serData.props:AddListener(hudData.props, self.OnPropChanged, self)
        end
    end
end

function SandEntityBase:OnPropChanged(keys)
    if self.hudData and self.hudData.changePrefab then
        self:Dispose(true)
        self.hudData.resPath = self.serData.resProps.ModelRes
        self.path = self.serData.resProps.ModelRes
        self:_InstantiateModelAsync()
    else
        self:OnUpdateData(self.serData)
    end
end

local VecZero = { x = 0, y = 0, z = 0 }
function SandEntityBase:_InstantiateModelAsync()
    if self.entity or self.hybridEntity then
        return
    end
    EntityManager.SetLoading(self.rentity)
    if entityMode and self.hudData.enableEcs then
        self.entityId = async_entity.RequestInstantiate(self.path, function(entity)
            if self.isDisposed then
                entity = nil
                return
            end
            if not entity then
                return
            end
            local attachTrans = EntityManager.SetHybridEntity(self.rentity, entity)
            EntityManager.SetLoaded(self.rentity, attachTrans)
            if attachTrans then
                attachTrans:SetParent(self.parent, false)
                self.hybridTrans = attachTrans
            end
            self.hybridEntity = entity
            self:InstantiateSuccess(entity)
            if EntityManager.VegetationEcs then
                local extentSz = async_entity.GetColliderIntSize(entity, self.path)
                if extentSz and extentSz > 0 then
                    EntityManager.SetColliderExtent(self.rentity, extentSz)
                end
            end

        end, self.hudData.sortingOrder, self.hudData.ZTestAlways)
    else
        self.entity = res_pool.get_res(self.path, function(res_vo1)
            if self.isDisposed then
                res_vo1.cb = nil
                return
            end
            local gameObject = res_vo1.go
            local transform = gameObject.transform
            if self.parent then
                transform:SetParent(self.parent, false)
            end
            transform.localPosition = VecZero

            self:InstantiateSuccess(res_vo1.go)
            self:OnShow(self.isShow)

            EntityManager.SetLoaded(self.rentity, self.transform)
            if EntityManager.VegetationEcs then
                local extentSz = async_entity.GetColliderIntSize(nil, self.path, gameObject, self.collisionScale)
                if extentSz and extentSz > 0 then
                    EntityManager.SetColliderExtent(self.rentity, extentSz)
                end
            end
        end)
    end
end

---实例化成功回调（自动回调
function SandEntityBase:InstantiateSuccess(_obj)
    self:Bind(_obj)
    self:OnLoaded()
    self:RegisterListener()
end

--- 绑定节点
function SandEntityBase:Bind(baseObj)
    local name = self.compName .. "_" .. self.compId
    if self.serData then
        name = name .. "_" .. self.serData.sid
    end
    if not self.hybridEntity then
        self.gameObject = baseObj
        self.transform = baseObj.transform
        self.gameObject.name = name
    end
end

---@public 加载完成回调
function SandEntityBase:OnLoaded()
    self.isLoaded = true
    if self.loadEvent then
        for i = 1, #self.loadEvent do
            self.loadEvent[i](self)
        end
        self.loadEvent = nil
    end
end

local gw_sand_simple_model_util
---@public 注册监听相关
function SandEntityBase:RegisterListener()
    if self.hudData and self.hudData.simpleLevel then
        gw_sand_simple_model_util = gw_sand_simple_model_util or require "gw_sand_simple_model_util"
        gw_sand_simple_model_util.RegisterSimpleLevel(self.compId, self)
        -- 立刻执行一次
        self:OnSimpleLevelChanged(gw_sand_simple_model_util.GetSimpleLevelState())
    end
end

---@public  注销监听相关
function SandEntityBase:UnregisterListener()
    if self.hudData and self.hudData.simpleLevel then
        gw_sand_simple_model_util = gw_sand_simple_model_util or require "gw_sand_simple_model_util"
        gw_sand_simple_model_util.UnRegisterSimpleLevel(self.compId)
    end
end

function SandEntityBase:OnSimpleLevelChanged(level)

end

function SandEntityBase:OnSetLodRange(midLod, maxLod, minInnerLod, maxInnerLod)
    midLod = midLod or self.hudData.midLod
    maxLod = maxLod or self.hudData.maxLod
    minInnerLod = minInnerLod or self.hudData.minInnerLod
    maxInnerLod = maxInnerLod or self.hudData.maxInnerLod
    EntityManager.SetLodRange(self.rentity, midLod, maxLod, minInnerLod, maxInnerLod)
end

---@see override 内部展示隐藏
function SandEntityBase:OnInnerVisible(bo)

end

function SandEntityBase:AddLoadEvent(func)
    if self.isLoaded then
        func(self)
        return
    end
    if not self.loadEvent then
        self.loadEvent = {}
    end
    table.insert(self.loadEvent, func)
end

function SandEntityBase:IsValid()
    local IsValid = gw_disposable_object.IsValid(self)
    if self.hudData and self.hudData.enableEcs then
        return IsValid and self.isLoaded and not self.hybridEntity ~= nil
    else
        return IsValid and self.isLoaded and not util.IsObjNull(self.transform)
    end
end

--- 回收
function SandEntityBase:Recycle()
    self.loadEvent = nil
    self:Init()
    gw_disposable_object.Recycle(self)
end

--- 弃用
function SandEntityBase:Dispose(unloadShowOnly)
    self.loadEvent = nil
    self:UnregisterListener()

    if not unloadShowOnly then
        self:SetScale(1)
        gw_disposable_object.Dispose(self)
    end

    if self.entityId then
        async_entity.Dispose(self.entityId)
        self.hybridEntity = nil
        self.entityId = nil
    end
    -- 销毁节点
    if self.entity then
        res_pool.return_res(self.entity)
        self.entity = nil
    end
    if not unloadShowOnly then
        if self.rentity then
            EntityManager.RemoveEntity(self.rentity)
            self.rentity = nil
        end
        self:Init()
    else
        EntityManager.SetUnLoaded(self.rentity)
        self.isLoaded = false
    end
    self.unloadShowOnly = unloadShowOnly
end

--- @public基类方法设置缩放
---@see override
function SandEntityBase:SetScale(scale)
    if not self.rentity then
        return
    end
    EntityManager.SetScale(self.rentity, scale)
end

function SandEntityBase:ResetPos()
    EntityManager.SetPosition(self.rentity, self.serData.worldPos)
end

function SandEntityBase:UpdatePos(pos)
    EntityManager.SetPosition(self.rentity, pos)
end

--注意不适合场景移动物体，移动物体无worldPos
function SandEntityBase:UpdateChangeTargetPos(pos)
    EntityManager.SetPosition(self.rentity, pos)
end

--注意不适合场景移动物体，移动物体无worldPos
function SandEntityBase:ResetChangeTargetPos()
    if not self.serData then
        log.Error("SandEntityBase:ResetChangeTargetPos serData is nil")
        return
    end
    EntityManager.SetPosition(self.rentity, self.serData.worldPos)
end

function SandEntityBase:OnHide()
    self.isShow = false
    EntityManager.SetActive(self.rentity, false)
    if self.isLoaded then
        if self.hudData.enableEcs then
            EntityHybridUtility.SetActive(self.hybridEntity, self.isShow)
        else
            UIUtil.SetActive(self.transform, self.isShow)
        end
    end
end

function SandEntityBase:OnShow(bool)
    self.isShow = bool or false
    EntityManager.SetActive(self.rentity, self.isShow)
    if self.isLoaded then
        if self.hudData.enableEcs then
            EntityHybridUtility.SetActive(self.hybridEntity, self.isShow)
        else
            UIUtil.SetActive(self.transform, self.isShow)
        end
    end
end

function SandEntityBase:OnUpdateData(serData)
    serData = serData or self.serData
    if not serData then
        return
    end
    local posx, posz, radius = MapUtil.GetMap():GetPosXZRadiusBySid(serData.sid)
    if serData.dotList_txy and self.compName ~= "gw_comp_march_line" then

    else
        EntityManager.SetRadius(self.rentity, radius)
        EntityManager.SetPositionXYZ(self.rentity, posx, 0, posz)
    end
end

function SandEntityBase:GetMoveComp()
    local comp = MapUtil.GetCompBySid(self.serData.sid)
    return comp:GetComponent("gw_comp_movement")
end

function SandEntityBase:SetMoveComp(moveComp)
    self.hudData.targetEntity = moveComp.rentity
    EntityManager.SetParent(self.rentity, moveComp.rentity)
end

function SandEntityBase:GetCurrentPosition()
    local x, y, z = EntityManager.GetPosition(self.rentity)
    return { x = x, y = y, z = z }
end

function SandEntityBase.GetPrefabSpriteRenderer(path, subPath)
    local prefab = async_entity.GetPrefabFromCache(path)
    return prefab or nil
end

return SandEntityBase





