-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
module('mq_common_pb')


V1M=V(4,"EnEndPoint_Platform",0,0)
V2M=V(4,"EnEndPoint_InfinitWar",1,1)
E1M=E(3,"EnEndPoint",".CSMsg.EnEndPoint")
V3M=V(4,"EnSrcType_MQ",0,0)
E2M=E(3,"EnSrcType",".CSMsg.EnSrcType")
V4M=V(4,"EnSubSrcType_GM",0,0)
V5M=V(4,"EnSubSrcType_OperMail",1,1)
V6M=V(4,"EnSubSrcType_Recharge",2,2)
V7M=V(4,"EnSubSrcType_PreventWallow",3,3)
V8M=V(4,"EnSubSrcType_SimulateRecharge",4,4)
V9M=V(4,"EnSubSrcType_Notice",5,5)
V10M=V(4,"EnSubSrcType_RunCmd",6,6)
E3M=E(3,"EnSubSrcType",".CSMsg.EnSubSrcType")
V11M=V(4,"EnDestType_InfinitWar_Unknow",0,0)
V12M=V(4,"EnDestType_InfinitWar_CHN",1,1)
V13M=V(4,"EnDestType_InfinitWar_USAndCAN",2,2)
V14M=V(4,"EnDestType_InfinitWar_SEA",3,3)
E4M=E(3,"EnDestType",".CSMsg.EnDestType")
V15M=V(4,"EnModuleKey_GM",0,0)
V16M=V(4,"EnModuleKey_OperMail",1,1)
V17M=V(4,"EnModuleKey_Recharge",2,2)
V18M=V(4,"EnModuleKey_PreventWallow",3,3)
V19M=V(4,"EnModuleKey_SimulateRecharge",4,4)
V20M=V(4,"EnModuleKey_Notice",5,5)
V21M=V(4,"EnModuleKey_RunCmd",6,6)
V22M=V(4,"EnModuleKey_GMCheckOnline",7,7)
V23M=V(4,"EnModuleKey_GMCloseConversation",8,8)
V24M=V(4,"EnModuleKey_PrivateOperMail",9,9)
V25M=V(4,"EnModuleKey_UpdateNameRsp",10,10)
E5M=E(3,"EnModuleKey",".CSMsg.EnModuleKey")
V26M=V(4,"enCommand_Freeze",0,0)
V27M=V(4,"enCommand_Mute",1,1)
V28M=V(4,"enCommand_Kick",2,2)
V29M=V(4,"enCommand_RemoveFreeze",3,3)
V30M=V(4,"enCommand_RemoveMute",4,4)
V31M=V(4,"enCommand_CancleOperMail",5,5)
V32M=V(4,"enCommand_PrivMute",6,6)
V33M=V(4,"enCommand_RemovePrivMute",7,7)
V34M=V(4,"enCommand_AlterPal",8,8)
V35M=V(4,"enCommand_CustomTitle",9,9)
V36M=V(4,"enCommand_CustomTitleCsvReq",10,10)
V37M=V(4,"enCommand_RoleCustomTitleReq",11,11)
V38M=V(4,"enCommand_SendOperMailRespond",12,12)
V39M=V(4,"enCommand_CancleOperPrivateMail",13,13)
V40M=V(4,"enCommand_CancleSubOperPrivateMail",14,14)
V41M=V(4,"enCommand_ShutDown",15,16)
V42M=V(4,"enCommand_SendOperMail",16,17)
V43M=V(4,"enCommand_ActivityRankReq",17,18)
E6M=E(3,"enCommandType",".CSMsg.enCommandType")
V44M=V(4,"enReportType_Reaction",0,0)
V45M=V(4,"enReportType_Sexy",1,1)
V46M=V(4,"enReportType_Violence",2,2)
V47M=V(4,"enReportType_Else",3,3)
E7M=E(3,"ChatReportType",".CSMsg.ChatReportType")
V48M=V(4,"enSpeak_Text",0,0)
V49M=V(4,"enSpeak_ShareHero",1,1)
V50M=V(4,"enSpeak_ShareGoods",2,2)
V51M=V(4,"enSpeak_ShareEquip",3,3)
V52M=V(4,"enSpeak_ShareBattle",4,4)
V53M=V(4,"enSpeak_SharePitfall",5,5)
V54M=V(4,"enSpeak_OccupyCell",6,6)
V55M=V(4,"enSpeak_GetBossFirstBlood",7,7)
V56M=V(4,"enSpeak_OfficialMark",8,8)
V57M=V(4,"enSpeak_OccupyFortress",9,9)
V58M=V(4,"enSpeak_LangText",10,10)
V59M=V(4,"enSpeak_SendFlower",11,11)
V60M=V(4,"enSpeak_LeagueCompete",12,12)
V61M=V(4,"enSpeak_Mate",13,13)
V62M=V(4,"enSpeak_Voice",14,14)
V63M=V(4,"enSpeak_Like",15,15)
V64M=V(4,"enSpeak_Picture",16,16)
V65M=V(4,"enSpeak_At",17,17)
V66M=V(4,"enSpeak_ShareMultiBattle",18,18)
V67M=V(4,"enSpeak_ShareCell",19,19)
V68M=V(4,"enSpeak_LeagueMutualHelp",20,20)
V69M=V(4,"enSpeak_ShareLottery",21,21)
V70M=V(4,"enSpeak_VoidAreanSuc",22,22)
V71M=V(4,"enSpeak_ShareEquipRecast",23,23)
V72M=V(4,"enSpeak_LeagueRecruit",24,24)
V73M=V(4,"enSpeak_ShareSkin",25,25)
V74M=V(4,"enSpeak_ShareEquipResonace",26,26)
V75M=V(4,"enSpeak_FarmLottery",27,27)
V76M=V(4,"enSpeak_ShareDecorate",28,28)
V77M=V(4,"enSpeak_ShareTreasureRare",29,29)
V78M=V(4,"enSpeak_ChineseRedPacket",30,30)
V79M=V(4,"enSpeak_ShareStarDiamond",31,31)
V80M=V(4,"enSpeak_ShareLabourDayInfo",32,32)
V81M=V(4,"enSpeak_WMTopRaceGuess",33,33)
V82M=V(4,"enSpeak_MakeFoodInfo",34,34)
V83M=V(4,"enSpeak_AnniversaryJackpock",35,35)
V84M=V(4,"enSpeak_DimensionWarAssemble",36,36)
V85M=V(4,"enSpeak_DimensionWarOccupy",37,37)
V86M=V(4,"enSpeak_PickTheRouteShareArchived",38,39)
V87M=V(4,"enSpeak_ChristmasTeamData",39,40)
V88M=V(4,"enSpeak_AllSaintsDay_Pumpkin",40,41)
V89M=V(4,"enSpeak_SpringFestivalBlessing",41,42)
V90M=V(4,"enSpeak_SandboxMarkPos",42,43)
V91M=V(4,"enSpeak_SandboxMass",43,44)
V92M=V(4,"enSpeak_SandboxTreasure",44,45)
V93M=V(4,"enSpeak_SandboxTreasureFinish",45,46)
V94M=V(4,"enSpeak_SandboxKastenboxMultipleReward",46,47)
V95M=V(4,"enSpeak_LeagueAchievement",47,48)
V96M=V(4,"enSpeak_CarriageTruckShare",48,49)
V97M=V(4,"enSpeak_NC_Occupied",49,50)
V98M=V(4,"enSpeak_Announcement",50,51)
V99M=V(4,"enSpeak_UrgentAnnouncement",51,52)
V100M=V(4,"enSpeak_CongressNtf",52,53)
V101M=V(4,"enSpeak_AnnounceForChannel",53,54)
V102M=V(4,"enSpeak_UrgentAnnounceForChannel",54,55)
V103M=V(4,"enSpeak_NC_Abandon",55,56)
V104M=V(4,"enSpeak_AllianceTrain",56,57)
V105M=V(4,"enSpeak_AllianceTrainPush",57,58)
V106M=V(4,"enSpeak_AllianceTrainPlunder",58,59)
V107M=V(4,"enSpeak_AcornPubTreasure",59,60)
V108M=V(4,"enSpeak_GoldenEggs",60,61)
V109M=V(4,"enSpeak_GoldenEggsMaxReward",61,62)
V110M=V(4,"enSpeak_Gathering",62,63)
V111M=V(4,"enSpeak_ReinforcePrivateChat",63,64)
V112M=V(4,"enSpeak_AllianceShare",64,65)
V113M=V(4,"enSpeak_ZombieApocalypseCard",65,66)
V114M=V(4,"enSpeak_ZombieApocalypseList",66,67)
V115M=V(4,"enSpeak_ZombieApocalypseDetail",67,68)
V116M=V(4,"enSpeak_AllianceOutFire",68,69)
V117M=V(4,"enSpeak_PrivateChatLike",69,70)
V118M=V(4,"enSpeak_CityFireShare",70,71)
V119M=V(4,"enSpeak_BattleVicReportShare",71,72)
V120M=V(4,"enSpeak_BattleDefReportShare",72,73)
V121M=V(4,"enSpeak_DetectReportShare",73,74)
V122M=V(4,"enSpeak_ZombieAssist",74,75)
E8M=E(3,"enSpeakType",".CSMsg.enSpeakType")
V123M=V(4,"ChatFlag_None",0,0)
V124M=V(4,"ChatFlag_Recall",1,1)
E9M=E(3,"EMChatFlag",".CSMsg.EMChatFlag")
V125M=V(4,"StallShare",0,1)
V126M=V(4,"InviteTeamShare",1,2)
E10M=E(3,"LabourDayShareInfoType",".CSMsg.LabourDayShareInfoType")
F1D=F(2,"srcType",".CSMsg.TRabbitMQMsgHead.srcType",1,0,2,false,0,13,3)
F2D=F(2,"srcTypeID",".CSMsg.TRabbitMQMsgHead.srcTypeID",2,1,2,false,0,13,3)
F3D=F(2,"srcSubTypeID",".CSMsg.TRabbitMQMsgHead.srcSubTypeID",3,2,2,false,0,13,3)
F4D=F(2,"destType",".CSMsg.TRabbitMQMsgHead.destType",4,3,2,false,0,13,3)
F5D=F(2,"destTypeID",".CSMsg.TRabbitMQMsgHead.destTypeID",5,4,2,false,0,13,3)
F6D=F(2,"destSubTypeID",".CSMsg.TRabbitMQMsgHead.destSubTypeID",6,5,2,false,0,13,3)
F7D=F(2,"destModuleKey",".CSMsg.TRabbitMQMsgHead.destModuleKey",7,6,2,false,nil,14,8)
F8D=F(2,"cmdID",".CSMsg.TRabbitMQMsgHead.cmdID",8,7,2,false,0,13,3)
M1G=D(1,"TRabbitMQMsgHead",".CSMsg.TRabbitMQMsgHead",false,{},{},nil,{})
F9D=F(2,"heroId",".CSMsg.tShareHero.heroId",1,0,2,false,0,5,1)
F10D=F(2,"lv",".CSMsg.tShareHero.lv",2,1,2,false,0,5,1)
F11D=F(2,"advanceLv",".CSMsg.tShareHero.advanceLv",3,2,2,false,0,5,1)
F12D=F(2,"starLv",".CSMsg.tShareHero.starLv",4,3,2,false,0,5,1)
F13D=F(2,"ce",".CSMsg.tShareHero.ce",5,4,2,false,0,5,1)
F14D=F(2,"hp",".CSMsg.tShareHero.hp",6,5,2,false,0,5,1)
F15D=F(2,"attack",".CSMsg.tShareHero.attack",7,6,2,false,0,5,1)
F16D=F(2,"defence",".CSMsg.tShareHero.defence",8,7,2,false,0,5,1)
F17D=F(2,"speed",".CSMsg.tShareHero.speed",9,8,2,false,0,5,1)
F18D=F(2,"equip",".CSMsg.tShareHero.equip",10,9,3,false,{},11,10)
F19D=F(2,"skinId",".CSMsg.tShareHero.skinId",11,10,1,false,0,5,1)
F20D=F(2,"awakeSkillID",".CSMsg.tShareHero.awakeSkillID",12,11,1,false,0,5,1)
F21D=F(2,"awakeSkillLv",".CSMsg.tShareHero.awakeSkillLv",13,12,1,false,0,5,1)
F22D=F(2,"decorate",".CSMsg.tShareHero.decorate",14,13,3,false,{},11,10)
F23D=F(2,"nDecorateAddCE",".CSMsg.tShareHero.nDecorateAddCE",15,14,1,false,0,5,1)
F24D=F(2,"weaponData",".CSMsg.tShareHero.weaponData",16,15,1,false,nil,11,10)
F25D=F(2,"nWeaponAddCE",".CSMsg.tShareHero.nWeaponAddCE",17,16,1,false,0,5,1)
F26D=F(2,"bTrialHero",".CSMsg.tShareHero.bTrialHero",18,17,1,false,false,8,7)
M3G=D(1,"tShareHero",".CSMsg.tShareHero",false,{},{},nil,{})
F27D=F(2,"goodsId",".CSMsg.tShareGoods.goodsId",1,0,2,false,0,5,1)
F28D=F(2,"lv",".CSMsg.tShareGoods.lv",2,1,1,false,0,5,1)
F29D=F(2,"proId",".CSMsg.tShareGoods.proId",3,2,3,false,{},5,1)
F30D=F(2,"resonanceproId",".CSMsg.tShareGoods.resonanceproId",4,3,1,false,0,5,1)
F31D=F(2,"secondArtifactLevel",".CSMsg.tShareGoods.secondArtifactLevel",6,4,1,false,0,5,1)
M4G=D(1,"tShareGoods",".CSMsg.tShareGoods",false,{},{},nil,{})
F32D=F(2,"decorate",".CSMsg.DecorateData.decorate",1,0,1,false,nil,11,10)
F33D=F(2,"lv",".CSMsg.DecorateData.lv",2,1,2,false,0,5,1)
M5G=D(1,"DecorateData",".CSMsg.DecorateData",false,{},{},nil,{})
F34D=F(2,"heroId",".CSMsg.tTopHeroInfo.heroId",1,0,2,false,0,5,1)
F35D=F(2,"heroLv",".CSMsg.tTopHeroInfo.heroLv",2,1,2,false,0,5,1)
F36D=F(2,"starLv",".CSMsg.tTopHeroInfo.starLv",3,2,2,false,0,5,1)
F37D=F(2,"inSoulSlot",".CSMsg.tTopHeroInfo.inSoulSlot",4,3,1,false,false,8,7)
M7G=D(1,"tTopHeroInfo",".CSMsg.tTopHeroInfo",false,{},{},nil,{})
F38D=F(2,"process",".CSMsg.tLeagueMutualHelp.process",1,0,2,false,0,5,1)
F39D=F(2,"helpID",".CSMsg.tLeagueMutualHelp.helpID",2,1,2,false,0,5,1)
F40D=F(2,"flagID",".CSMsg.tLeagueMutualHelp.flagID",3,2,2,false,0,5,1)
M8G=D(1,"tLeagueMutualHelp",".CSMsg.tLeagueMutualHelp",false,{},{},nil,{})
F41D=F(2,"redPacketId",".CSMsg.tLeagueRedPacketInfo.redPacketId",1,0,2,false,0,5,1)
F42D=F(2,"redEnvelopId",".CSMsg.tLeagueRedPacketInfo.redEnvelopId",2,1,2,false,0,5,1)
M9G=D(1,"tLeagueRedPacketInfo",".CSMsg.tLeagueRedPacketInfo",false,{},{},nil,{})
F43D=F(2,"nID",".CSMsg.TShareHeroData.nID",1,0,2,false,0,13,3)
F44D=F(2,"nStar",".CSMsg.TShareHeroData.nStar",2,1,2,false,0,13,3)
M10G=D(1,"TShareHeroData",".CSMsg.TShareHeroData",false,{},{},nil,{})
F45D=F(2,"strName",".CSMsg.tShareLottery.strName",1,0,2,false,"",9,9)
F46D=F(2,"nTime",".CSMsg.tShareLottery.nTime",2,1,2,false,0,13,3)
F47D=F(2,"oHeroData",".CSMsg.tShareLottery.oHeroData",3,2,3,false,{},11,10)
F48D=F(2,"nScore",".CSMsg.tShareLottery.nScore",4,3,2,false,0,13,3)
F49D=F(2,"nLotteryType",".CSMsg.tShareLottery.nLotteryType",5,4,2,false,0,13,3)
M11G=D(1,"tShareLottery",".CSMsg.tShareLottery",false,{},{},nil,{})
F50D=F(2,"heroData",".CSMsg.TFarmLottery.heroData",1,0,2,false,nil,11,10)
M12G=D(1,"TFarmLottery",".CSMsg.TFarmLottery",false,{},{},nil,{})
F51D=F(2,"iLangId",".CSMsg.tLangText.iLangId",1,0,2,false,0,5,1)
F52D=F(2,"arrLangParams",".CSMsg.tLangText.arrLangParams",2,1,3,false,{},9,9)
M13G=D(1,"tLangText",".CSMsg.tLangText",false,{},{},nil,{})
F53D=F(2,"leagueID",".CSMsg.tLeagueRecruit.leagueID",1,0,2,false,0,5,1)
F54D=F(2,"memberCount",".CSMsg.tLeagueRecruit.memberCount",2,1,2,false,0,5,1)
F55D=F(2,"memberLimit",".CSMsg.tLeagueRecruit.memberLimit",3,2,2,false,0,5,1)
F56D=F(2,"icon",".CSMsg.tLeagueRecruit.icon",4,3,2,false,0,5,1)
F57D=F(2,"leagueName",".CSMsg.tLeagueRecruit.leagueName",5,4,2,false,"",9,9)
F58D=F(2,"langtype",".CSMsg.tLeagueRecruit.langtype",6,5,1,false,0,5,1)
F59D=F(2,"titleID",".CSMsg.tLeagueRecruit.titleID",7,6,1,false,0,5,1)
M14G=D(1,"tLeagueRecruit",".CSMsg.tLeagueRecruit",false,{},{},nil,{})
F60D=F(2,"bServerID",".CSMsg.tSandboxMark.bServerID",1,0,2,false,0,13,3)
F61D=F(2,"x",".CSMsg.tSandboxMark.x",2,1,2,false,0,5,1)
F62D=F(2,"y",".CSMsg.tSandboxMark.y",3,2,2,false,0,5,1)
F63D=F(2,"playerName",".CSMsg.tSandboxMark.playerName",4,3,1,false,"",9,9)
F64D=F(2,"sid",".CSMsg.tSandboxMark.sid",5,4,1,false,0,4,4)
F65D=F(2,"quality",".CSMsg.tSandboxMark.quality",6,5,1,false,0,5,1)
F66D=F(2,"context",".CSMsg.tSandboxMark.context",7,6,1,false,"",9,9)
F67D=F(2,"leagueShortName",".CSMsg.tSandboxMark.leagueShortName",8,7,1,false,"",9,9)
F68D=F(2,"titleID",".CSMsg.tSandboxMark.titleID",9,8,1,false,0,5,1)
F69D=F(2,"sandboxSid",".CSMsg.tSandboxMark.sandboxSid",10,9,1,false,0,13,3)
F70D=F(2,"titleDes",".CSMsg.tSandboxMark.titleDes",11,10,1,false,"",9,9)
F71D=F(2,"entityLevel",".CSMsg.tSandboxMark.entityLevel",12,11,1,false,"",9,9)
F72D=F(2,"entityName",".CSMsg.tSandboxMark.entityName",13,12,1,false,0,4,4)
F73D=F(2,"tavernTaskID",".CSMsg.tSandboxMark.tavernTaskID",14,13,1,false,0,5,1)
M15G=D(1,"tSandboxMark",".CSMsg.tSandboxMark",false,{},{},nil,{})
F74D=F(2,"massTeamId",".CSMsg.tSandboxMass.massTeamId",1,0,2,false,0,4,4)
F75D=F(2,"massFinishTime",".CSMsg.tSandboxMass.massFinishTime",2,1,2,false,0,5,1)
F76D=F(2,"targetId",".CSMsg.tSandboxMass.targetId",3,2,2,false,0,5,1)
F77D=F(2,"x",".CSMsg.tSandboxMass.x",4,3,2,false,0,5,1)
F78D=F(2,"y",".CSMsg.tSandboxMass.y",5,4,2,false,0,5,1)
F79D=F(2,"strName",".CSMsg.tSandboxMass.strName",6,5,2,false,"",9,9)
F80D=F(2,"targetSid",".CSMsg.tSandboxMass.targetSid",7,6,2,false,0,5,1)
F81D=F(2,"massWaitingTime",".CSMsg.tSandboxMass.massWaitingTime",8,7,1,false,0,5,1)
M16G=D(1,"tSandboxMass",".CSMsg.tSandboxMass",false,{},{},nil,{})
F82D=F(2,"achID",".CSMsg.tLeagueAchievement.achID",1,0,2,false,0,5,1)
F83D=F(2,"state",".CSMsg.tLeagueAchievement.state",2,1,2,false,0,5,1)
F84D=F(2,"finishCnt",".CSMsg.tLeagueAchievement.finishCnt",3,2,1,false,0,5,1)
F85D=F(2,"reqCnt",".CSMsg.tLeagueAchievement.reqCnt",4,3,1,false,0,5,1)
F86D=F(2,"unlockTime",".CSMsg.tLeagueAchievement.unlockTime",5,4,1,false,0,5,1)
M17G=D(1,"tLeagueAchievement",".CSMsg.tLeagueAchievement",false,{},{},nil,{})
F87D=F(2,"bServerID",".CSMsg.tSandboxTreasure.bServerID",1,0,2,false,0,13,3)
F88D=F(2,"x",".CSMsg.tSandboxTreasure.x",2,1,2,false,0,5,1)
F89D=F(2,"y",".CSMsg.tSandboxTreasure.y",3,2,2,false,0,5,1)
F90D=F(2,"treasureSid",".CSMsg.tSandboxTreasure.treasureSid",4,3,1,false,0,4,4)
F91D=F(2,"context",".CSMsg.tSandboxTreasure.context",5,4,1,false,"",9,9)
M18G=D(1,"tSandboxTreasure",".CSMsg.tSandboxTreasure",false,{},{},nil,{})
F92D=F(2,"bServerID",".CSMsg.tSandboxTreasureFinish.bServerID",1,0,2,false,0,13,3)
F93D=F(2,"x",".CSMsg.tSandboxTreasureFinish.x",2,1,2,false,0,5,1)
F94D=F(2,"y",".CSMsg.tSandboxTreasureFinish.y",3,2,2,false,0,5,1)
F95D=F(2,"treasureSid",".CSMsg.tSandboxTreasureFinish.treasureSid",4,3,2,false,0,4,4)
F96D=F(2,"treasureCfgId",".CSMsg.tSandboxTreasureFinish.treasureCfgId",5,4,2,false,0,13,3)
F97D=F(2,"kastenboxSid",".CSMsg.tSandboxTreasureFinish.kastenboxSid",6,5,2,false,0,4,4)
F98D=F(2,"playerName",".CSMsg.tSandboxTreasureFinish.playerName",7,6,2,false,"",9,9)
F99D=F(2,"context",".CSMsg.tSandboxTreasureFinish.context",8,7,1,false,"",9,9)
M19G=D(1,"tSandboxTreasureFinish",".CSMsg.tSandboxTreasureFinish",false,{},{},nil,{})
F100D=F(2,"dbid",".CSMsg.tSandboxKastenboxMultipleReward.dbid",1,0,2,false,0,13,3)
F101D=F(2,"faceID",".CSMsg.tSandboxKastenboxMultipleReward.faceID",2,1,2,false,0,13,3)
F102D=F(2,"frameID",".CSMsg.tSandboxKastenboxMultipleReward.frameID",3,2,2,false,0,13,3)
F103D=F(2,"playerName",".CSMsg.tSandboxKastenboxMultipleReward.playerName",4,3,2,false,"",9,9)
F104D=F(2,"multipleReward",".CSMsg.tSandboxKastenboxMultipleReward.multipleReward",5,4,2,false,0,5,1)
F105D=F(2,"treasureData",".CSMsg.tSandboxKastenboxMultipleReward.treasureData",6,5,2,false,nil,11,10)
F106D=F(2,"faceStr",".CSMsg.tSandboxKastenboxMultipleReward.faceStr",7,6,1,false,"",9,9)
M20G=D(1,"tSandboxKastenboxMultipleReward",".CSMsg.tSandboxKastenboxMultipleReward",false,{},{},nil,{})
F107D=F(2,"sShort",".CSMsg.tSandboxNCOccupied.sShort",1,0,2,false,"",9,9)
F108D=F(2,"regionID",".CSMsg.tSandboxNCOccupied.regionID",2,1,2,false,0,13,3)
F109D=F(2,"isFirst",".CSMsg.tSandboxNCOccupied.isFirst",3,2,2,false,0,5,1)
F110D=F(2,"sShort0",".CSMsg.tSandboxNCOccupied.sShort0",4,3,1,false,"",9,9)
F111D=F(2,"sandboxsid",".CSMsg.tSandboxNCOccupied.sandboxsid",5,4,1,false,0,5,1)
M21G=D(1,"tSandboxNCOccupied",".CSMsg.tSandboxNCOccupied",false,{},{},nil,{})
F112D=F(2,"iPosIndex",".CSMsg.tLeagueChatDimensionWarInfo.iPosIndex",1,0,2,false,0,13,3)
F113D=F(2,"attacker",".CSMsg.tLeagueChatDimensionWarInfo.attacker",2,1,1,false,"",9,9)
M22G=D(1,"tLeagueChatDimensionWarInfo",".CSMsg.tLeagueChatDimensionWarInfo",false,{},{},nil,{})
F114D=F(2,"levelID",".CSMsg.stPickTheRouteArchived.levelID",1,0,1,false,0,5,1)
F115D=F(2,"eventChooseID",".CSMsg.stPickTheRouteArchived.eventChooseID",2,1,3,false,{},5,1)
F116D=F(2,"activityID",".CSMsg.stPickTheRouteArchived.activityID",3,2,1,false,0,5,1)
M23G=D(1,"stPickTheRouteArchived",".CSMsg.stPickTheRouteArchived",false,{},{},nil,{})
F117D=F(2,"pumpkinId",".CSMsg.tAllSaintsDayPumpkinData.pumpkinId",1,0,2,false,"",9,9)
F118D=F(2,"score",".CSMsg.tAllSaintsDayPumpkinData.score",2,1,2,false,0,5,1)
F119D=F(2,"scoreId",".CSMsg.tAllSaintsDayPumpkinData.scoreId",3,2,1,false,0,5,1)
M24G=D(1,"tAllSaintsDayPumpkinData",".CSMsg.tAllSaintsDayPumpkinData",false,{},{},nil,{})
F120D=F(2,"roleid",".CSMsg.tChatMsg.roleid",1,0,2,false,0,5,1)
F121D=F(2,"faceId",".CSMsg.tChatMsg.faceId",2,1,2,false,0,5,1)
F122D=F(2,"name",".CSMsg.tChatMsg.name",3,2,2,false,"",9,9)
F123D=F(2,"roleLv",".CSMsg.tChatMsg.roleLv",4,3,2,false,0,5,1)
F124D=F(2,"bHideVip",".CSMsg.tChatMsg.bHideVip",5,4,2,false,false,8,7)
F125D=F(2,"nVipLv",".CSMsg.tChatMsg.nVipLv",6,5,2,false,0,5,1)
F126D=F(2,"ce",".CSMsg.tChatMsg.ce",7,6,2,false,0,5,1)
F127D=F(2,"passStage",".CSMsg.tChatMsg.passStage",8,7,2,false,0,5,1)
F128D=F(2,"worldid",".CSMsg.tChatMsg.worldid",9,8,2,false,0,5,1)
F129D=F(2,"guildname",".CSMsg.tChatMsg.guildname",10,9,2,false,"",9,9)
F130D=F(2,"title",".CSMsg.tChatMsg.title",11,10,2,false,"",9,9)
F131D=F(2,"topHero",".CSMsg.tChatMsg.topHero",12,11,3,false,{},11,10)
F132D=F(2,"sType",".CSMsg.tChatMsg.sType",13,12,2,false,nil,14,8)
F133D=F(2,"chatTime",".CSMsg.tChatMsg.chatTime",14,13,2,false,0,5,1)
F134D=F(2,"context",".CSMsg.tChatMsg.context",15,14,1,false,"",9,9)
F135D=F(2,"hero",".CSMsg.tChatMsg.hero",16,15,1,false,nil,11,10)
F136D=F(2,"goods",".CSMsg.tChatMsg.goods",17,16,1,false,nil,11,10)
F137D=F(2,"avatarFrame",".CSMsg.tChatMsg.avatarFrame",18,17,1,false,0,5,1)
F138D=F(2,"nameIcon",".CSMsg.tChatMsg.nameIcon",19,18,1,false,0,5,1)
F139D=F(2,"langText",".CSMsg.tChatMsg.langText",20,19,1,false,nil,11,10)
F140D=F(2,"extendinfo",".CSMsg.tChatMsg.extendinfo",21,20,1,false,"",9,9)
F141D=F(2,"bgm",".CSMsg.tChatMsg.bgm",22,21,1,false,false,8,7)
F142D=F(2,"leagueHelp",".CSMsg.tChatMsg.leagueHelp",23,22,1,false,nil,11,10)
F143D=F(2,"shareLottery",".CSMsg.tChatMsg.shareLottery",24,23,1,false,nil,11,10)
F144D=F(2,"guildid",".CSMsg.tChatMsg.guildid",25,24,1,false,0,5,1)
F145D=F(2,"titleID",".CSMsg.tChatMsg.titleID",26,25,1,false,0,5,1)
F146D=F(2,"voidArenaID",".CSMsg.tChatMsg.voidArenaID",27,26,1,false,0,5,1)
F147D=F(2,"leagueRecruit",".CSMsg.tChatMsg.leagueRecruit",28,27,1,false,nil,11,10)
F148D=F(2,"farmLottery",".CSMsg.tChatMsg.farmLottery",29,28,1,false,nil,11,10)
F149D=F(2,"szChatID",".CSMsg.tChatMsg.szChatID",30,29,1,false,"",9,9)
F150D=F(2,"nFlag",".CSMsg.tChatMsg.nFlag",31,30,1,false,0,13,3)
F151D=F(2,"bPrivateMute",".CSMsg.tChatMsg.bPrivateMute",32,31,1,false,false,8,7)
F152D=F(2,"customtitle",".CSMsg.tChatMsg.customtitle",33,32,1,false,"",9,9)
F153D=F(2,"roomID",".CSMsg.tChatMsg.roomID",39,33,1,false,0,13,3)
F154D=F(2,"redPacketInfo",".CSMsg.tChatMsg.redPacketInfo",40,34,1,false,nil,11,10)
F155D=F(2,"shareInfo",".CSMsg.tChatMsg.shareInfo",41,35,1,false,nil,11,10)
F156D=F(2,"langtype",".CSMsg.tChatMsg.langtype",42,36,1,false,0,5,1)
F157D=F(2,"exchangeInfo",".CSMsg.tChatMsg.exchangeInfo",43,37,1,false,nil,11,10)
F158D=F(2,"dimensionWarInfo",".CSMsg.tChatMsg.dimensionWarInfo",44,38,1,false,nil,11,10)
F159D=F(2,"pickTheRouteArchived",".CSMsg.tChatMsg.pickTheRouteArchived",45,39,1,false,nil,11,10)
F160D=F(2,"nShareState",".CSMsg.tChatMsg.nShareState",46,40,1,false,0,5,1)
F161D=F(2,"pumpkinData",".CSMsg.tChatMsg.pumpkinData",47,41,1,false,nil,11,10)
F162D=F(2,"sandboxmarkData",".CSMsg.tChatMsg.sandboxmarkData",48,42,1,false,nil,11,10)
F163D=F(2,"sandboxmassData",".CSMsg.tChatMsg.sandboxmassData",49,43,1,false,nil,11,10)
F164D=F(2,"sandboxTreasureData",".CSMsg.tChatMsg.sandboxTreasureData",50,44,1,false,nil,11,10)
F165D=F(2,"sandboxTreasureFinishData",".CSMsg.tChatMsg.sandboxTreasureFinishData",51,45,1,false,nil,11,10)
F166D=F(2,"sandboxKastenboxMultipleRewardData",".CSMsg.tChatMsg.sandboxKastenboxMultipleRewardData",52,46,1,false,nil,11,10)
F167D=F(2,"leagueAchievementData",".CSMsg.tChatMsg.leagueAchievementData",53,47,1,false,nil,11,10)
F168D=F(2,"sandboxCarriageTruckShare",".CSMsg.tChatMsg.sandboxCarriageTruckShare",54,48,1,false,nil,11,10)
F169D=F(2,"sandboxNCOccupied",".CSMsg.tChatMsg.sandboxNCOccupied",55,49,1,false,nil,11,10)
F170D=F(2,"sex",".CSMsg.tChatMsg.sex",56,50,1,false,0,5,1)
F171D=F(2,"leaguePostion",".CSMsg.tChatMsg.leaguePostion",57,51,1,false,0,5,1)
F172D=F(2,"postionId",".CSMsg.tChatMsg.postionId",58,52,1,false,0,5,1)
F173D=F(2,"leagueShortName",".CSMsg.tChatMsg.leagueShortName",59,53,1,false,"",9,9)
F174D=F(2,"channelId",".CSMsg.tChatMsg.channelId",60,54,1,false,0,5,1)
F175D=F(2,"extendinfopb",".CSMsg.tChatMsg.extendinfopb",61,55,1,false,"",12,9)
F176D=F(2,"deviceLangType",".CSMsg.tChatMsg.deviceLangType",62,56,1,false,0,5,1)
F177D=F(2,"faceStr",".CSMsg.tChatMsg.faceStr",63,57,1,false,"",9,9)
F178D=F(2,"goldenEggsChat",".CSMsg.tChatMsg.goldenEggsChat",64,58,1,false,nil,11,10)
F179D=F(2,"nLikeCount",".CSMsg.tChatMsg.nLikeCount",65,59,1,false,0,5,1)
F180D=F(2,"isSystemMsg",".CSMsg.tChatMsg.isSystemMsg",66,60,1,false,false,8,7)
F181D=F(2,"nationalFlagID",".CSMsg.tChatMsg.nationalFlagID",67,61,1,false,0,13,3)
M25G=D(1,"tChatMsg",".CSMsg.tChatMsg",false,{},{},nil,{})
F182D=F(2,"nShareType",".CSMsg.tLabourDayShareInfo.nShareType",1,0,2,false,0,13,3)
F183D=F(2,"nHostId",".CSMsg.tLabourDayShareInfo.nHostId",2,1,2,false,0,13,3)
F184D=F(2,"nStallId",".CSMsg.tLabourDayShareInfo.nStallId",3,2,2,false,0,13,3)
F185D=F(2,"nDaySellPrice",".CSMsg.tLabourDayShareInfo.nDaySellPrice",4,3,2,false,0,13,3)
F186D=F(2,"nShareState",".CSMsg.tLabourDayShareInfo.nShareState",5,4,2,false,0,13,3)
F187D=F(2,"nHostAreaId",".CSMsg.tLabourDayShareInfo.nHostAreaId",6,5,2,false,0,13,3)
M27G=D(1,"tLabourDayShareInfo",".CSMsg.tLabourDayShareInfo",false,{},{},nil,{})
F188D=F(2,"originItemId",".CSMsg.tMakeFoodExchangeInfo.originItemId",1,0,2,false,0,13,3)
F189D=F(2,"originItemNum",".CSMsg.tMakeFoodExchangeInfo.originItemNum",2,1,2,false,0,13,3)
F190D=F(2,"targetItemId",".CSMsg.tMakeFoodExchangeInfo.targetItemId",3,2,2,false,0,13,3)
F191D=F(2,"targetItemNum",".CSMsg.tMakeFoodExchangeInfo.targetItemNum",4,3,2,false,0,13,3)
F192D=F(2,"orderId",".CSMsg.tMakeFoodExchangeInfo.orderId",5,4,2,false,"",9,9)
F193D=F(2,"createTime",".CSMsg.tMakeFoodExchangeInfo.createTime",6,5,2,false,0,13,3)
F194D=F(2,"activityID",".CSMsg.tMakeFoodExchangeInfo.activityID",7,6,1,false,0,13,3)
M28G=D(1,"tMakeFoodExchangeInfo",".CSMsg.tMakeFoodExchangeInfo",false,{},{},nil,{})
F195D=F(2,"common",".CSMsg.tCrossSvrChatMsg.common",1,0,2,false,nil,11,10)
F196D=F(2,"toRoleId",".CSMsg.tCrossSvrChatMsg.toRoleId",2,1,1,false,0,5,1)
F197D=F(2,"toWorldid",".CSMsg.tCrossSvrChatMsg.toWorldid",3,2,1,false,0,5,1)
F198D=F(2,"langtype",".CSMsg.tCrossSvrChatMsg.langtype",4,3,1,false,0,5,1)
F199D=F(2,"channelid",".CSMsg.tCrossSvrChatMsg.channelid",5,4,1,false,0,5,1)
M31G=D(1,"tCrossSvrChatMsg",".CSMsg.tCrossSvrChatMsg",false,{},{},nil,{})
F200D=F(2,"gmid",".CSMsg.tChatGmCheckOnlineReq.gmid",1,0,2,false,0,5,1)
F201D=F(2,"roleId",".CSMsg.tChatGmCheckOnlineReq.roleId",2,1,2,false,0,5,1)
F202D=F(2,"worldId",".CSMsg.tChatGmCheckOnlineReq.worldId",3,2,2,false,0,5,1)
F203D=F(2,"extendinfo",".CSMsg.tChatGmCheckOnlineReq.extendinfo",4,3,1,false,"",9,9)
M32G=D(1,"tChatGmCheckOnlineReq",".CSMsg.tChatGmCheckOnlineReq",false,{},{},nil,{})
F204D=F(2,"gmid",".CSMsg.tChatGmCheckOnlineRsp.gmid",1,0,2,false,0,5,1)
F205D=F(2,"roleId",".CSMsg.tChatGmCheckOnlineRsp.roleId",2,1,2,false,0,5,1)
F206D=F(2,"worldId",".CSMsg.tChatGmCheckOnlineRsp.worldId",3,2,2,false,0,5,1)
F207D=F(2,"bOnline",".CSMsg.tChatGmCheckOnlineRsp.bOnline",4,3,2,false,false,8,7)
F208D=F(2,"bCheckOnline",".CSMsg.tChatGmCheckOnlineRsp.bCheckOnline",5,4,1,false,false,8,7)
M33G=D(1,"tChatGmCheckOnlineRsp",".CSMsg.tChatGmCheckOnlineRsp",false,{},{},nil,{})
F209D=F(2,"gmid",".CSMsg.tChatGmCloseConversationNft.gmid",1,0,2,false,0,5,1)
F210D=F(2,"roleId",".CSMsg.tChatGmCloseConversationNft.roleId",2,1,2,false,0,5,1)
F211D=F(2,"worldId",".CSMsg.tChatGmCloseConversationNft.worldId",3,2,2,false,0,5,1)
M34G=D(1,"tChatGmCloseConversationNft",".CSMsg.tChatGmCloseConversationNft",false,{},{},nil,{})
F212D=F(2,"skillID",".CSMsg.TWeaponDiamondSkill.skillID",1,0,1,false,0,5,1)
F213D=F(2,"skillLv",".CSMsg.TWeaponDiamondSkill.skillLv",2,1,1,false,0,5,1)
M35G=D(1,"TWeaponDiamondSkill",".CSMsg.TWeaponDiamondSkill",false,{},{},nil,{})
F214D=F(2,"diamondID",".CSMsg.TWeaponDiamondData.diamondID",1,0,3,false,{},5,1)
F215D=F(2,"skillID",".CSMsg.TWeaponDiamondData.skillID",2,1,3,false,{},11,10)
F216D=F(2,"curLv",".CSMsg.TWeaponDiamondData.curLv",3,2,1,false,0,5,1)
F217D=F(2,"maxLv",".CSMsg.TWeaponDiamondData.maxLv",4,3,1,false,0,5,1)
M6G=D(1,"TWeaponDiamondData",".CSMsg.TWeaponDiamondData",false,{},{},nil,{})
F218D=F(2,"tradeid",".CSMsg.tSandboxCarriageTruckShare.tradeid",1,0,2,false,0,5,1)
F219D=F(2,"quality",".CSMsg.tSandboxCarriageTruckShare.quality",2,1,2,false,0,5,1)
F220D=F(2,"bServerID",".CSMsg.tSandboxCarriageTruckShare.bServerID",3,2,2,false,0,13,3)
F221D=F(2,"lottname",".CSMsg.tSandboxCarriageTruckShare.lottname",4,3,1,false,"",9,9)
F222D=F(2,"lottLeagueShortName",".CSMsg.tSandboxCarriageTruckShare.lottLeagueShortName",5,4,1,false,"",9,9)
F223D=F(2,"titleDes",".CSMsg.tSandboxCarriageTruckShare.titleDes",6,5,1,false,0,5,1)
F224D=F(2,"context",".CSMsg.tSandboxCarriageTruckShare.context",7,6,1,false,"",9,9)
F225D=F(2,"sandboxid",".CSMsg.tSandboxCarriageTruckShare.sandboxid",8,7,1,false,0,5,1)
F226D=F(2,"roleId",".CSMsg.tSandboxCarriageTruckShare.roleId",9,8,1,false,0,5,1)
F227D=F(2,"formats",".CSMsg.tSandboxCarriageTruckShare.formats",10,9,3,false,{},9,9)
F228D=F(2,"contentLang",".CSMsg.tSandboxCarriageTruckShare.contentLang",11,10,1,false,0,5,1)
M29G=D(1,"tSandboxCarriageTruckShare",".CSMsg.tSandboxCarriageTruckShare",false,{},{},nil,{})
F229D=F(2,"redPacketId",".CSMsg.TGoldenEggsChat.redPacketId",1,0,1,false,"",9,9)
F230D=F(2,"goldenEggsId",".CSMsg.TGoldenEggsChat.goldenEggsId",2,1,1,false,0,5,1)
M30G=D(1,"TGoldenEggsChat",".CSMsg.TGoldenEggsChat",false,{},{},nil,{})
F231D=F(2,"playerId",".CSMsg.TGoldenEggsMaxReward.playerId",1,0,1,false,0,5,1)
F232D=F(2,"playerName",".CSMsg.TGoldenEggsMaxReward.playerName",2,1,1,false,"",9,9)
F233D=F(2,"playerFaceStr",".CSMsg.TGoldenEggsMaxReward.playerFaceStr",3,2,1,false,"",9,9)
F234D=F(2,"playerFrameid",".CSMsg.TGoldenEggsMaxReward.playerFrameid",4,3,1,false,0,5,1)
F235D=F(2,"szChatID",".CSMsg.TGoldenEggsMaxReward.szChatID",5,4,1,false,"",9,9)
F236D=F(2,"launchID",".CSMsg.TGoldenEggsMaxReward.launchID",6,5,1,false,0,5,1)
F237D=F(2,"launchName",".CSMsg.TGoldenEggsMaxReward.launchName",7,6,1,false,"",9,9)
F238D=F(2,"redPacketId",".CSMsg.TGoldenEggsMaxReward.redPacketId",8,7,1,false,"",9,9)
F239D=F(2,"goldenEggsId",".CSMsg.TGoldenEggsMaxReward.goldenEggsId",9,8,1,false,0,5,1)
M36G=D(1,"TGoldenEggsMaxReward",".CSMsg.TGoldenEggsMaxReward",false,{},{},nil,{})

E1M.values = {V1M,V2M}
E2M.values = {V3M}
E3M.values = {V4M,V5M,V6M,V7M,V8M,V9M,V10M}
E4M.values = {V11M,V12M,V13M,V14M}
E5M.values = {V15M,V16M,V17M,V18M,V19M,V20M,V21M,V22M,V23M,V24M,V25M}
E6M.values = {V26M,V27M,V28M,V29M,V30M,V31M,V32M,V33M,V34M,V35M,V36M,V37M,V38M,V39M,V40M,V41M,V42M,V43M}
E7M.values = {V44M,V45M,V46M,V47M}
E8M.values = {V48M,V49M,V50M,V51M,V52M,V53M,V54M,V55M,V56M,V57M,V58M,V59M,V60M,V61M,V62M,V63M,V64M,V65M,V66M,V67M,V68M,V69M,V70M,V71M,V72M,V73M,V74M,V75M,V76M,V77M,V78M,V79M,V80M,V81M,V82M,V83M,V84M,V85M,V86M,V87M,V88M,V89M,V90M,V91M,V92M,V93M,V94M,V95M,V96M,V97M,V98M,V99M,V100M,V101M,V102M,V103M,V104M,V105M,V106M,V107M,V108M,V109M,V110M,V111M,V112M,V113M,V114M,V115M,V116M,V117M,V118M,V119M,V120M,V121M,V122M}
E9M.values = {V123M,V124M}
E10M.values = {V125M,V126M}
F7D.enum_type=M2G
M1G.fields={F1D, F2D, F3D, F4D, F5D, F6D, F7D, F8D}
F18D.message_type=M4G
F22D.message_type=M5G
F24D.message_type=M6G
M3G.fields={F9D, F10D, F11D, F12D, F13D, F14D, F15D, F16D, F17D, F18D, F19D, F20D, F21D, F22D, F23D, F24D, F25D, F26D}
M4G.fields={F27D, F28D, F29D, F30D, F31D}
F32D.message_type=M4G
M5G.fields={F32D, F33D}
M7G.fields={F34D, F35D, F36D, F37D}
M8G.fields={F38D, F39D, F40D}
M9G.fields={F41D, F42D}
M10G.fields={F43D, F44D}
F47D.message_type=M10G
M11G.fields={F45D, F46D, F47D, F48D, F49D}
F50D.message_type=M4G
M12G.fields={F50D}
M13G.fields={F51D, F52D}
M14G.fields={F53D, F54D, F55D, F56D, F57D, F58D, F59D}
M15G.fields={F60D, F61D, F62D, F63D, F64D, F65D, F66D, F67D, F68D, F69D, F70D, F71D, F72D, F73D}
M16G.fields={F74D, F75D, F76D, F77D, F78D, F79D, F80D, F81D}
M17G.fields={F82D, F83D, F84D, F85D, F86D}
M18G.fields={F87D, F88D, F89D, F90D, F91D}
M19G.fields={F92D, F93D, F94D, F95D, F96D, F97D, F98D, F99D}
F105D.message_type=M18G
M20G.fields={F100D, F101D, F102D, F103D, F104D, F105D, F106D}
M21G.fields={F107D, F108D, F109D, F110D, F111D}
M22G.fields={F112D, F113D}
M23G.fields={F114D, F115D, F116D}
M24G.fields={F117D, F118D, F119D}
F131D.message_type=M7G
F132D.enum_type=M26G
F135D.message_type=M3G
F136D.message_type=M4G
F139D.message_type=M13G
F142D.message_type=M8G
F143D.message_type=M11G
F147D.message_type=M14G
F148D.message_type=M12G
F154D.message_type=M9G
F155D.message_type=M27G
F157D.message_type=M28G
F158D.message_type=M22G
F159D.message_type=M23G
F161D.message_type=M24G
F162D.message_type=M15G
F163D.message_type=M16G
F164D.message_type=M18G
F165D.message_type=M19G
F166D.message_type=M20G
F167D.message_type=M17G
F168D.message_type=M29G
F169D.message_type=M21G
F178D.message_type=M30G
M25G.fields={F120D, F121D, F122D, F123D, F124D, F125D, F126D, F127D, F128D, F129D, F130D, F131D, F132D, F133D, F134D, F135D, F136D, F137D, F138D, F139D, F140D, F141D, F142D, F143D, F144D, F145D, F146D, F147D, F148D, F149D, F150D, F151D, F152D, F153D, F154D, F155D, F156D, F157D, F158D, F159D, F160D, F161D, F162D, F163D, F164D, F165D, F166D, F167D, F168D, F169D, F170D, F171D, F172D, F173D, F174D, F175D, F176D, F177D, F178D, F179D, F180D, F181D}
M27G.fields={F182D, F183D, F184D, F185D, F186D, F187D}
M28G.fields={F188D, F189D, F190D, F191D, F192D, F193D, F194D}
F195D.message_type=M25G
M31G.fields={F195D, F196D, F197D, F198D, F199D}
M32G.fields={F200D, F201D, F202D, F203D}
M33G.fields={F204D, F205D, F206D, F207D, F208D}
M34G.fields={F209D, F210D, F211D}
M35G.fields={F212D, F213D}
F215D.message_type=M35G
M6G.fields={F214D, F215D, F216D, F217D}
M29G.fields={F218D, F219D, F220D, F221D, F222D, F223D, F224D, F225D, F226D, F227D, F228D}
M30G.fields={F229D, F230D}
M36G.fields={F231D, F232D, F233D, F234D, F235D, F236D, F237D, F238D, F239D}

ChatFlag_None = 0
ChatFlag_Recall = 1
DecorateData =M(M5G)
EnDestType_InfinitWar_CHN = 1
EnDestType_InfinitWar_SEA = 3
EnDestType_InfinitWar_USAndCAN = 2
EnDestType_InfinitWar_Unknow = 0
EnEndPoint_InfinitWar = 1
EnEndPoint_Platform = 0
EnModuleKey_GM = 0
EnModuleKey_GMCheckOnline = 7
EnModuleKey_GMCloseConversation = 8
EnModuleKey_Notice = 5
EnModuleKey_OperMail = 1
EnModuleKey_PreventWallow = 3
EnModuleKey_PrivateOperMail = 9
EnModuleKey_Recharge = 2
EnModuleKey_RunCmd = 6
EnModuleKey_SimulateRecharge = 4
EnModuleKey_UpdateNameRsp = 10
EnSrcType_MQ = 0
EnSubSrcType_GM = 0
EnSubSrcType_Notice = 5
EnSubSrcType_OperMail = 1
EnSubSrcType_PreventWallow = 3
EnSubSrcType_Recharge = 2
EnSubSrcType_RunCmd = 6
EnSubSrcType_SimulateRecharge = 4
InviteTeamShare = 2
StallShare = 1
TFarmLottery =M(M12G)
TGoldenEggsChat =M(M30G)
TGoldenEggsMaxReward =M(M36G)
TRabbitMQMsgHead =M(M1G)
TShareHeroData =M(M10G)
TWeaponDiamondData =M(M6G)
TWeaponDiamondSkill =M(M35G)
enCommand_ActivityRankReq = 18
enCommand_AlterPal = 8
enCommand_CancleOperMail = 5
enCommand_CancleOperPrivateMail = 13
enCommand_CancleSubOperPrivateMail = 14
enCommand_CustomTitle = 9
enCommand_CustomTitleCsvReq = 10
enCommand_Freeze = 0
enCommand_Kick = 2
enCommand_Mute = 1
enCommand_PrivMute = 6
enCommand_RemoveFreeze = 3
enCommand_RemoveMute = 4
enCommand_RemovePrivMute = 7
enCommand_RoleCustomTitleReq = 11
enCommand_SendOperMail = 17
enCommand_SendOperMailRespond = 12
enCommand_ShutDown = 16
enReportType_Else = 3
enReportType_Reaction = 0
enReportType_Sexy = 1
enReportType_Violence = 2
enSpeak_AcornPubTreasure = 60
enSpeak_AllSaintsDay_Pumpkin = 41
enSpeak_AllianceOutFire = 69
enSpeak_AllianceShare = 65
enSpeak_AllianceTrain = 57
enSpeak_AllianceTrainPlunder = 59
enSpeak_AllianceTrainPush = 58
enSpeak_AnniversaryJackpock = 35
enSpeak_AnnounceForChannel = 54
enSpeak_Announcement = 51
enSpeak_At = 17
enSpeak_BattleDefReportShare = 73
enSpeak_BattleVicReportShare = 72
enSpeak_CarriageTruckShare = 49
enSpeak_ChineseRedPacket = 30
enSpeak_ChristmasTeamData = 40
enSpeak_CityFireShare = 71
enSpeak_CongressNtf = 53
enSpeak_DetectReportShare = 74
enSpeak_DimensionWarAssemble = 36
enSpeak_DimensionWarOccupy = 37
enSpeak_FarmLottery = 27
enSpeak_Gathering = 63
enSpeak_GetBossFirstBlood = 7
enSpeak_GoldenEggs = 61
enSpeak_GoldenEggsMaxReward = 62
enSpeak_LangText = 10
enSpeak_LeagueAchievement = 48
enSpeak_LeagueCompete = 12
enSpeak_LeagueMutualHelp = 20
enSpeak_LeagueRecruit = 24
enSpeak_Like = 15
enSpeak_MakeFoodInfo = 34
enSpeak_Mate = 13
enSpeak_NC_Abandon = 56
enSpeak_NC_Occupied = 50
enSpeak_OccupyCell = 6
enSpeak_OccupyFortress = 9
enSpeak_OfficialMark = 8
enSpeak_PickTheRouteShareArchived = 39
enSpeak_Picture = 16
enSpeak_PrivateChatLike = 70
enSpeak_ReinforcePrivateChat = 64
enSpeak_SandboxKastenboxMultipleReward = 47
enSpeak_SandboxMarkPos = 43
enSpeak_SandboxMass = 44
enSpeak_SandboxTreasure = 45
enSpeak_SandboxTreasureFinish = 46
enSpeak_SendFlower = 11
enSpeak_ShareBattle = 4
enSpeak_ShareCell = 19
enSpeak_ShareDecorate = 28
enSpeak_ShareEquip = 3
enSpeak_ShareEquipRecast = 23
enSpeak_ShareEquipResonace = 26
enSpeak_ShareGoods = 2
enSpeak_ShareHero = 1
enSpeak_ShareLabourDayInfo = 32
enSpeak_ShareLottery = 21
enSpeak_ShareMultiBattle = 18
enSpeak_SharePitfall = 5
enSpeak_ShareSkin = 25
enSpeak_ShareStarDiamond = 31
enSpeak_ShareTreasureRare = 29
enSpeak_SpringFestivalBlessing = 42
enSpeak_Text = 0
enSpeak_UrgentAnnounceForChannel = 55
enSpeak_UrgentAnnouncement = 52
enSpeak_Voice = 14
enSpeak_VoidAreanSuc = 22
enSpeak_WMTopRaceGuess = 33
enSpeak_ZombieApocalypseCard = 66
enSpeak_ZombieApocalypseDetail = 68
enSpeak_ZombieApocalypseList = 67
enSpeak_ZombieAssist = 75
stPickTheRouteArchived =M(M23G)
tAllSaintsDayPumpkinData =M(M24G)
tChatGmCheckOnlineReq =M(M32G)
tChatGmCheckOnlineRsp =M(M33G)
tChatGmCloseConversationNft =M(M34G)
tChatMsg =M(M25G)
tCrossSvrChatMsg =M(M31G)
tLabourDayShareInfo =M(M27G)
tLangText =M(M13G)
tLeagueAchievement =M(M17G)
tLeagueChatDimensionWarInfo =M(M22G)
tLeagueMutualHelp =M(M8G)
tLeagueRecruit =M(M14G)
tLeagueRedPacketInfo =M(M9G)
tMakeFoodExchangeInfo =M(M28G)
tSandboxCarriageTruckShare =M(M29G)
tSandboxKastenboxMultipleReward =M(M20G)
tSandboxMark =M(M15G)
tSandboxMass =M(M16G)
tSandboxNCOccupied =M(M21G)
tSandboxTreasure =M(M18G)
tSandboxTreasureFinish =M(M19G)
tShareGoods =M(M4G)
tShareHero =M(M3G)
tShareLottery =M(M11G)
tTopHeroInfo =M(M7G)

