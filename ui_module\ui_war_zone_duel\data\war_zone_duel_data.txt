---
--- Generated by EmmyLua(https://github.com/EmmyLua)
--- Created by du<PERSON><PERSON>.
--- DateTime: 2025/4/1 16:59
---
local require = require
local table = table
local pairs = pairs
local string = string
local ipairs = ipairs
local tonumber = tonumber
local os = os
local math = math
local log = require "log"
local event = require "event"
local cfg_util = require "cfg_util"
local gw_common_util = require "gw_common_util"
local war_zone_duel_define = require "war_zone_duel_define"
local game_scheme = require "game_scheme"
local time_util = require "time_util"
local util = require "util"
---@class war_zone_duel_data
local M = {}-- 开启log日志
M.logger = require("logger").new("war_zone_duel", 4)
M.seasonTimeBegin = 0--赛季开始时间（开始于周一）
M.seasonTimeEnd = 0--赛季结束时间（结束于周日）
M.configPreviewBegin = 0--配置预告开启时间
M.configInPreviewBegin = 0--配置生效时间(应该与赛季开始时间相等, 在活动未开启时, 为客户端提供开启时间)
M.timePoints = {}--各时间节点 enZoneBattleDuelTimeType
M.groups = {}--分组数据（分组模块返回2个分组，分别包含8、4个区服则有2个对象）
M.vsItems = {}--战区1vs1信息/战区对决数据（分组内8个区则有4个，4个区则有2个）
M.rankFirstInfos = {}--战区1vs1信息/排名第一的信息1
M.crossMoveCDTime = 0--下一次可以跨服迁城的时间
M.bSkipReward = false--是否可以领取轮空奖励
M.bSkipRoundWinReward = false--是否可以领取连胜奖励
M.warZonePointsGoodsID = 7052--战区个人积分物品ID(后面要读常量表)
M.playPresInfo = {}--总统信息

M.cfgData = {
    activityServerCross = nil, --跨服活动配置数据
}--活动配置数据

M.WarZoneTaskIDArr = {} --国会争夺个人积分宝箱任务ID

local inStageData = {}--活动阶段
local stageData = {
    [war_zone_duel_define.enZoneBattleDuelStage.None] = true, --未开启状态
}--key war_zone_duel_define.enZoneBattleDuelStage.None value:bool

local curTime = os.server_time()

--region ****************解析数据******************

---战区对决总信息回复(登录请求一次)
function M.CopyFrom_MSG_ZONEBATTLEDUEL_GETINFO_RSP(msg)
    if msg.errCode ~= 0 then
        return
    end
    M.seasonTimeBegin = msg.seasonTimeBegin or 0
    M.seasonTimeEnd = msg.seasonTimeEnd or 0
    M.CopyFrom_ActCrossInfo(msg.actCrossInfo)
    M.CopyFrom_TZoneBattleDuelTime(msg.timePoints)
    M.CopyFrom_TZoneBattleDuelGroup(msg.groups)
    -- 保存客户端预告时间（下面客户端缓存了，但是服务器说信他的）
    M.SetServerPreviewTime(msg.ConfigForeshowBegin, msg.ConfigInForesBegin)
    M.SetPreviewTime()
    M.RefreshStageData()

    M.GetWarZoneTaskID()
end

---活动未开启，尝试初始化阶段信息
function M.ActivityNotOpen(msg)
    M.CopyFrom_ActCrossInfo(msg.actCrossInfo)

    M.SetPreviewTime()
    local isInPreview = M.RefreshStageData_PreviewTime()
    --预告
    stageData[war_zone_duel_define.enZoneBattleDuelStage.PreNotice] = isInPreview
    stageData[war_zone_duel_define.enZoneBattleDuelStage.None] = not isInPreview
    M.RefreshInStageData()
end

--设置刷新阶段时间这里直接被返回了
function M.SetPreviewTime()
    local cfg = M.GetServerCrossCfg()
    if cfg then
        M.configPreviewBegin = cfg.NoticeTime --cfg and time_util.GetTimeStampByTimeCfg(cfg.NoticeTime) or 0
        M.configInPreviewBegin = cfg.TimeStart --cfg and time_util.GetTimeStampByTimeCfg(cfg.TimeStart) or 0
        return M.configPreviewBegin, M.configInPreviewBegin
    end
end

--设置刷新阶段时间这里直接被返回了
function M.SetServerPreviewTime(foreShowBegin, foresBegin)
    M.serverNoticeTime = foreShowBegin
    M.serverTimeStart = foresBegin
end

---获取跨服活动的配置
function M.SetServerCrossCfg()

end

--获取跨服组数据，服务器数量
function M.GetServerCross_ServerCount()
    local cfg = M.GetServerCrossCfg()
    local tmpCount = 0
    if cfg then
        local serverGroup_str = cfg_util.StringToArray(cfg.ServerGroup, ";", "#")
        for m, n in pairs(serverGroup_str) do
            if n then
                if #n == 2 then
                    tmpCount = tonumber(n[2]) - tonumber(n[1]) + 1 + tmpCount
                elseif #n == 1 then
                    tmpCount = tmpCount + 1
                end
            else
                M.logger.Warning("获取跨服活动的配置错误, serverGroup数据错误", 4)
            end
        end
    end
    return tmpCount
end

--获取跨服配置表数据
function M.GetServerCrossCfg()
    --if not M.cfgData.activityServerCross then
    --    M.SetServerCrossCfg()
    --end
    return M.cfgData.activityServerCross
end

---解析每周各时间节点
function M.CopyFrom_TZoneBattleDuelTime(data)
    if data and data.timePoints and #data.timePoints > 0 then
        M.timePoints = {}
        for i, v in ipairs(data.timePoints) do
            table.insert(M.timePoints, v)
        end
    end
end

---解析分组数据（分组模块返回2个分组，分别包含8、4个区服则有2个对象）
function M.CopyFrom_TZoneBattleDuelGroup(data)

    if data then
        M.groups = {
            finalRewarded = data.finalRewarded, --是否已经发放赛季该分组结算奖励
            rounds = {}, --对决轮次数据（8个区的对决会有3轮，4个区的对决会有2轮）
            worlds = {}, --该分组下各个区服整个赛季数据
        }
        if data.rounds and #data.rounds ~= 0 then
            local len_rounds = #data.rounds
            for i = 1, len_rounds do
                table.insert(M.groups.rounds, M.CopyFrom_TZoneBattleDuelRound(data.rounds[i]))
            end
        end
        if data.worlds and #data.worlds ~= 0 then
            local len_worlds = #data.worlds
            for i = 1, len_worlds do
                table.insert(M.groups.worlds, M.CopyFrom_TZoneBattleDuelWorldItem(data.worlds[i]))
            end
        end
    end
end

---每一轮的对决数据
function M.CopyFrom_TZoneBattleDuelRound(data)
    local tmpData = {
        rewarded = data.rewarded, --是否已经补发奖励、进度奖励、排行榜奖励
        vsItems = {}, --战区对决数据（分组内8个区则有4个，4个区则有2个）
    }
    for i = 1, #data.vsItems do
        table.insert(tmpData.vsItems, M.CopyFrom_TZoneBattleDuelVSItem(data.vsItems[i]))
    end

    return tmpData
end

---两个战区对决数据
function M.CopyFrom_TZoneBattleDuelVSItem(data)
    if not data then
        return nil
    end
    local tmpData = {
        worldIDA = data.worldIDA, --世界ID A，比拼排行榜（个人、联盟）定向该沙盘
        worldIDB = data.worldIDB, --世界ID B
        totalScoreA = data.totalScoreA, --比拼总积分 A，该区服所有积分会汇总到该字段
        totalScoreB = data.totalScoreB, --比拼总积分 B，该区服所有积分会汇总到该字段
        atkWorldID = data.atkWorldID, --进攻方世界ID
        atkScore = data.atkScore, --进攻方占领积分（防守方也有积分，谁先达到谁赢，拖到结束时则谁高谁赢，同分守方赢）
        defWorldID = data.defWorldID, --防守方世界ID（争夺积分排行榜定向该沙盘）
        defScore = data.defScore, --进攻方占领积分（防守方也有积分，谁先达到谁赢，拖到结束时则谁高谁赢，同分守方赢）
        winWorldID = data.winWorldID, --争夺胜利的区服
        losWorldID = data.losWorldID, --争夺失败的区服
        isSettle = data.isSettle, --是否已经结算
        occupWorldID = data.occupWorldID, --临时占领国会世界ID（守军队伍被清退后会发生改变）
        occupTime = data.occupTime, --临时占领国会发生时间
        scoreA = M.AnalyzeList(data.scoreA), --int64 特定比拼类型积分 A ，仅统计指定类型 enZoneBattleDuelScoreType
        scoreB = M.AnalyzeList(data.scoreB), --int64 特定比拼类型积分 B，仅统计指定类型 enZoneBattleDuelScoreType
        logs = {}, --事件记录列表（数量限制，比如100条，非当前轮考虑清掉）
    }
    if data.logs then
        for i = 1, #data.logs do
            table.insert(tmpData.logs, M.CopyFrom_TZoneBattleDuelVSLog(data.logs[i]))
        end
    end

    --M.logger.Warning0("战区对决进攻方：", tmpData.atkWorldID, 4)
    --M.logger.Warning0("战区对决防守方：", tmpData.defWorldID, 4)
    return tmpData
end

---解析list数据
function M.AnalyzeList(data)
    local tmpData = {}
    if data then
        for i, v in ipairs(data) do
            table.insert(tmpData, v)
        end
    end
    return tmpData
end

--事件日志记录
function M.CopyFrom_TZoneBattleDuelVSLog(data)
    local tmpData = {
        scoreType = data.scoreType, --比拼类型 enZoneBattleDuelScoreType，个人类型使用A系列字段
        occurTime = data.occurTime, --发生时间
        addScore = data.addScore, --增加积分
        worldIDA = data.worldIDA, --世界ID A
        nameA = data.nameA, --名称 A
        shortNameA = data.shortNameA, --简称 A
        faceStrA = data.faceStrA, --头像 ID A
        frameIDA = data.frameIDA, --头像框 ID A
        power = data.power or 0, --玩家个人战力
        winIsA = data.winIsA or false, --赢方为 A，个人类型不使用
        worldIDB = data.worldIDB or 0, --世界ID B，个人类型不使用
        nameB = data.nameB or "", --名称 B，个人类型不使用
        shortNameB = data.shortNameB or "", --简称 B，个人类型不使用
        faceStrB = data.faceStrB or "", --头像ID B，个人类型不使用
        frameIDB = data.frameIDB or 0, --头像框ID B，个人类型不使用
    }
    return tmpData
end

---整个赛季单个区服数据
function M.CopyFrom_TZoneBattleDuelWorldItem(data)
    local tmpData = {
        worldID = data.worldID, --世界ID
        continueWinTimes = data.continueWinTimes, --连胜次数（战败重置）
        power = data.power, --区服战斗力
        -- required int32 winTimes                   = 3;  //胜利场次
        isSkip = data.isSkip, --是否轮空
    }

    return tmpData
end

---战区1vs1信息回复
function M.CopyFrom_TMSG_ZONEBATTLEDUEL_GET_VSINFO_RSP(msg)

    if msg.errCode ~= 0 then
        return
    end
    if msg.vsItems then
        M.vsItems = M.CopyFrom_TZoneBattleDuelVSItem(msg.vsItems)--战区对决数据（分组内8个区则有4个，4个区则有2个）
    end
    if msg.rankFirstInfos and #msg.rankFirstInfos ~= 0 then
        M.rankFirstInfos = {}
        for i = 1, #msg.rankFirstInfos do
            table.insert(M.rankFirstInfos, M.CopyFrom_RankFirstInfo(msg.rankFirstInfos[i]))--排名第一的信息1
        end
    end
    if msg.worlds and #msg.worlds ~= 0 then
        M.groups.worlds = {}
        for i = 1, #msg.worlds do
            table.insert(M.groups.worlds, M.CopyFrom_TZoneBattleDuelWorldItem(msg.worlds[i]))
        end
    end
    if msg.crossMoveCDTime then
        M.crossMoveCDTime = msg.crossMoveCDTime
    end

    if msg.bSkipReward then
        M.bSkipReward = msg.bSkipReward
    end

    if msg.bSkipRoundWinReward then
        M.bSkipRoundWinReward = msg.bSkipRoundWinReward
    end
    M.RefreshStageData()
end

--排名第一的信息1
function M.CopyFrom_RankFirstInfo(data)
    local tmpData = {
        worldID = data.worldID, --世界ID
        playerBasic = M.CopyFrom_PlayerBasicInfo(data.playerBasic), --玩家基本信息
        PlayerScore = data.PlayerScore, --玩家积分
        allianceBasic = M.CopyFrom_AllianceBasicInfo(data.allianceBasic), --联盟基本信息
        AllianceScore = data.AllianceScore, --联盟积分
    }

    return tmpData
end

--玩家基本信息
function M.CopyFrom_PlayerBasicInfo(data)
    if not data then
        return { }
    end
    local tmpData = {
        roleID = data.roleID, --玩家角色ID
        faceID = data.faceID, --头像ID
        level = data.level, --玩家等级
        name = data.name, --名字
        logoutTime = data.logoutTime, --离线时间，0为在线
        frameID = data.frameID, --头像框
        robotID = data.robotID, --机器人ID
        nationalFlagID = data.nationalFlagID, --国旗ID
        worldId = data.worldId, --区服id
        leagueid = data.leagueid, --联盟ID
        leagueName = data.leagueName, --联盟名字
        leagueShortName = data.leagueShortName, --联盟简称
        leagueFlag = data.leagueFlag, --联盟旗帜
        maxSoldierLv = data.maxSoldierLv, --最高士兵等级
        power = data.power, --战斗力
        positionID = data.positionID, --官职ID
        leaguePosition = data.leaguePosition, --联盟职位
        faceStr = data.faceStr, --头像字符串
    }
    return tmpData
end

--联盟基本信息
function M.CopyFrom_AllianceBasicInfo(data)
    local tmpData = {
        leagueid = data.leagueid, --联盟ID
        leagueName = data.leagueName, --联盟名字
        leagueShortName = data.leagueShortName, --联盟简称
        leagueFlag = data.leagueFlag, --联盟旗帜
    }
    return tmpData
end

--活动状态变动时服务器数据下推
function M.CopyFrom_MSG_ZONE_BATTLE_DUEL_CHANGE_NTF(msg)
    M.seasonTimeBegin = (msg.seasonTimeBegin and msg.seasonTimeBegin ~= 0) and msg.seasonTimeBegin or M.seasonTimeBegin
    M.seasonTimeEnd = (msg.seasonTimeEnd and msg.seasonTimeEnd ~= 0) and msg.seasonTimeEnd or M.seasonTimeEnd

    M.CopyFrom_TZoneBattleDuelTime(msg.timePoints)
    if msg.vsItems and #msg.vsItems ~= 0 then
        M.vsItems = M.CopyFrom_TZoneBattleDuelVSItem(msg.vsItems)
    end
    M.CopyFrom_TZoneBattleDuelGroup(msg.groups)

    M.RefreshStageData()
    M.SetStageData(msg.ZoneBattleType, true)--强制保证前后端当前阶段数据一致
end

--刷新在当前的阶段
function M.RefreshInStageData()
    inStageData = {}
    for i, v in pairs(stageData) do
        if v then
            inStageData[i] = true
        end
    end
end

--获取当前阶段的数据
function M.GetInStageData()
    return inStageData
end

function M.CopyFrom_TZoneBattleDuelTeamInfo(data)
    local tmpData = {
        roleName = data.roleName, --角色名称
        frameID = data.frameID, --头像框ID
        faceStr = data.faceStr, --头像字符串
        simpleTroopStr = data.simpleTroopStr, --部队字符串
    }
    return tmpData
end


--设置总统信息
function M.CopyFrom_MSG_ZONEBATTLEDUEL_PRESIDENT_GET_INFO(data)
    if not data.arrPresidentInfo or #data.arrPresidentInfo == 0 then
        return
    end
    local len = #data.arrPresidentInfo
    for i = 1, len do
        local presInfo = M.CopyFrom_PlayerBasicInfo(data.arrPresidentInfo[i])
        M.playPresInfo[presInfo.worldId] = presInfo --将总统信息存储到表中
    end
end

--获取总统信息
function M.GetPlayPresInfo(data)
    if not M.playPresInfo or util.get_len(M.playPresInfo) == 0 then
        return
    end
    local tmpData = {}
    for i, v in pairs(data) do
        local presInfo = M.playPresInfo[v]
        if presInfo then
            table.insert(tmpData, presInfo)
        end
    end
    return tmpData
end

function M.CopyFrom_ActCrossInfo(data)
    if data then
        local player_mgr = require "player_mgr"
        local worldId = player_mgr.GetCreateRoleWorldID()
        if not worldId then
            return nil
        end

        M.RefreshTmpCurTime()
        --local serverGroup_str = cfg_util.StringToArray(data.ServerGroup, ";", "#")
        --for m, n in pairs(serverGroup_str) do
        --    if n then
        --        if #n == 2 and worldId >= tonumber(n[1]) and worldId <= tonumber(n[2]) or (#n == 1 and worldId == tonumber(n[1])) then
        --            table.insert(tmpCfgData, { cfg = cfg, noticeTime = time_util.GetTimeStampByTimeCfg(data.nNoticeTime), endTime = time_util.GetTimeStampByTimeCfg(data.nEndTime) })
        --        end
        --    else
        --        M.logger.Warning("获取跨服活动的配置错误, serverGroup数据错误", 4)
        --    end
        --end
        local serverCrossData = {
            AtyID = data.nAtyId,
            ServerGroup = data.ServerGroup,
            NoticeTime = data.nNoticeTime,
            TimeStart = data.nStartTime,
            TimeEnd = data.nEndTime
        }
        M.cfgData.activityServerCross = serverCrossData
    end
end

---清空数据
function M.ClearData()
    M.seasonTimeBegin = 0--赛季开始时间（开始于周一）
    M.seasonTimeEnd = 0--赛季结束时间（结束于周日）
    M.timePoints = {}--各时间节点 enZoneBattleDuelTimeType
    M.groups = {}--分组数据（分组模块返回2个分组，分别包含8、4个区服则有2个对象）
    M.vsItems = {}--战区1vs1信息/战区对决数据（分组内8个区则有4个，4个区则有2个）
    M.rankFirstInfos = {}--战区1vs1信息/排名第一的信息1
    M.configPreviewBegin = 0
    M.configInPreviewBegin = 0
    M.bSkipReward = false --是否可以领取轮空奖励
    M.bSkipRoundWinReward = false --是否可以领取连胜奖励
    M.playPresInfo = {} --玩家总统信息
    M.WarZoneTaskIDArr = {} --缓存的玩家任务数据
    M.ClearStageData()
end

--endregion

--region *****************数据处理函数*****************

function M.RefreshTmpCurTime()
    curTime = os.server_time()
end

---@param stageType:war_zone_duel_define.enZoneBattleDuelStage
---@return boolean 是否处于当前阶段
---判断是否处于当前阶段
function M.IsInStage(stageType)
    return stageData[stageType] or false
end

---@param stageType:war_zone_duel_define.enZoneBattleDuelStage
---@return boolean 是否处于当前阶段
---判断是否大于等于某个阶段
function M.IsOverStage(stageType)
    local isSuccess = false
    local len = util.get_len(stageData)
    for i = stageType, len do
        if stageData[i] then
            isSuccess = true
            break
        end
    end
    return isSuccess
end

---@param stageType:war_zone_duel_define.enZoneBattleDuelStage
---@return boolean 是否处于当前阶段
---判断是否小于等于某个阶段
function M.IsLessStage(stageType)
    local isSuccess = false
    local len = math.min(stageType, util.get_len(stageData))
    for i = 0, len do
        if stageData[i] then
            isSuccess = true
            break
        end
    end
    return isSuccess
end

---自动刷新阶段数据（登录刷新，阶段转换NTF时刷新）
function M.RefreshStageData()
    curTime = os.server_time()
    if not M.configPreviewBegin or M.configPreviewBegin == 0 or not M.configInPreviewBegin or M.configInPreviewBegin == 0
            or not M.seasonTimeBegin or not M.seasonTimeEnd or curTime < M.seasonTimeBegin or curTime > M.seasonTimeEnd or not M.timePoints or #M.timePoints == 0 then
        stageData[war_zone_duel_define.enZoneBattleDuelStage.None] = true
        M.ClearStageData()
        return
    end

    stageData[war_zone_duel_define.enZoneBattleDuelStage.None] = false

    --预告
    stageData[war_zone_duel_define.enZoneBattleDuelStage.PreNotice] = M.RefreshStageData_PreviewTime()

    local IsInActivity = curTime >= M.configInPreviewBegin and curTime >= M.seasonTimeBegin
    stageData[war_zone_duel_define.enZoneBattleDuelStage.AtyStart] = IsInActivity and curTime >= M.GetTimeTypePoint(war_zone_duel_define.enZoneBattleDuelTimeType.ScoreTimeBegin) and curTime < M.GetTimeTypePoint(war_zone_duel_define.enZoneBattleDuelTimeType.ScoreTimeEnd)
    stageData[war_zone_duel_define.enZoneBattleDuelStage.BattleEnd] = IsInActivity and curTime >= M.GetTimeTypePoint(war_zone_duel_define.enZoneBattleDuelTimeType.ScoreTimeEnd) and curTime < M.GetTimeTypePoint(war_zone_duel_define.enZoneBattleDuelTimeType.CrossMoveBegin)
    stageData[war_zone_duel_define.enZoneBattleDuelStage.CrossStart] = IsInActivity and curTime >= M.GetTimeTypePoint(war_zone_duel_define.enZoneBattleDuelTimeType.CrossMoveBegin) and curTime < M.GetTimeTypePoint(war_zone_duel_define.enZoneBattleDuelTimeType.CrossMoveEnd)
    stageData[war_zone_duel_define.enZoneBattleDuelStage.CongressBattleStart] = IsInActivity and curTime >= M.GetTimeTypePoint(war_zone_duel_define.enZoneBattleDuelTimeType.CongressAtkBegin) and curTime < M.GetTimeTypePoint(war_zone_duel_define.enZoneBattleDuelTimeType.CongressAtkEnd)
    stageData[war_zone_duel_define.enZoneBattleDuelStage.CongressBattleSettlement] = IsInActivity and M.RefreshStageData_CongressBattleSettlement()
    stageData[war_zone_duel_define.enZoneBattleDuelStage.CongressBattleEnd] = IsInActivity and curTime >= M.GetTimeTypePoint(war_zone_duel_define.enZoneBattleDuelTimeType.CongressAtkEnd) and curTime < M.GetTimeTypePoint(war_zone_duel_define.enZoneBattleDuelTimeType.RoundTimeEnd)
    stageData[war_zone_duel_define.enZoneBattleDuelStage.CrossEnd] = IsInActivity and curTime >= M.GetTimeTypePoint(war_zone_duel_define.enZoneBattleDuelTimeType.CongressAtkEnd) and curTime < M.GetTimeTypePoint(war_zone_duel_define.enZoneBattleDuelTimeType.RoundTimeEnd)
    stageData[war_zone_duel_define.enZoneBattleDuelStage.CrossTotalEnd] = IsInActivity and curTime >= M.GetTimeTypePoint(war_zone_duel_define.enZoneBattleDuelTimeType.CongressAtkEnd) and M.GetTimeTypePoint(war_zone_duel_define.enZoneBattleDuelTimeType.RoundTimeEnd) == M.seasonTimeEnd
    stageData[war_zone_duel_define.enZoneBattleDuelStage.AtyEnd] = IsInActivity and curTime >= M.GetTimeTypePoint(war_zone_duel_define.enZoneBattleDuelTimeType.RoundTimeEnd)
    M.RefreshInStageData()
end

---预告阶段
function M.RefreshStageData_PreviewTime()
    return curTime >= M.configPreviewBegin and curTime < M.configInPreviewBegin
end

---判断是否处于国会争夺结算阶段
function M.RefreshStageData_CongressBattleSettlement()
    if curTime < M.GetTimeTypePoint(war_zone_duel_define.enZoneBattleDuelTimeType.RoundTimeEnd) then
        if M.IsCurSelfDuelSkip() then
            ---轮空状态-判断是否已结算（true）时间小于活动结束
            return true
        elseif M.vsItems.isSettle then
            ---非轮空状态-判断是否已结算（true）
            return true
        end
    end

    return false
end

---设置阶段数据
function M.SetStageData(stageType, state)
    stageData[stageType] = state
end

---清空当前阶段数据
function M.ClearStageData()
    if stageData then
        for i, v in pairs(stageData) do
            stageData[i] = false
        end
        stageData = {
            [war_zone_duel_define.enZoneBattleDuelStage.None] = true, --未开启状态
        }
    end
end

---@param eTimeType:war_zone_duel_define.enZoneBattleDuelTimeType
---@return number
---根据枚举类型获取时间戳
function M.GetTimeTypePoint(eTimeType)
    if M.timePoints[eTimeType] then
        return M.timePoints[eTimeType]
    end
    M.logger.Warning0("GetTimeTypePoint", "没有对应的时间节点", eTimeType)
    return 0
end

---获取最大轮次
function M.GetMaxMatchNum()
    if M.groups and M.groups.matchNum then
        return M.groups.matchNum
    end
    return 0
end

---获取整个赛季单个区服数据
---@param worldID number
function M.GetWorldItemByWorldID(worldID)
    if M.groups and M.groups.worlds then
        for i = 1, #M.groups.worlds do
            if M.groups.worlds[i].worldID == worldID then
                return M.groups.worlds[i]
            end
        end
    end
    return nil
end

---获取当前(最新)轮次（周次）的对决数据
function M.GetLastRound()
    if M.groups and M.groups.rounds then
        local count = #M.groups.rounds
        return M.groups.rounds[count]
    end
end

---获取当前(最新)轮次（周次）的对决数据
function M.GetRoundByIndex(index)
    if M.groups and M.groups.rounds then
        local maxCount = #M.groups.rounds
        index = math.min(index, maxCount)
        return M.groups.rounds[index]
    end
end

---根据轮次获取已排序（胜场>战力）的区服id
function M.GetIndexSortedWorldsByPower(index)
    if not index then
        return nil
    end

    local tmpWorlds = {}
    index = math.min(index, #M.groups.rounds)
    if M.groups and M.groups.rounds and #M.groups.rounds ~= 0 and M.groups.rounds[index].vsItems and #M.groups.rounds[index].vsItems ~= 0 then
        tmpWorlds = M.GetCurTempBattleDuelWorldData()
        for m = 1, index do
            for i, v in pairs(M.groups.rounds[m].vsItems) do
                local tmpWorldData = M.GetItemByParams(tmpWorlds, "worldID", v.winWorldID)
                if v.winWorldID ~= 0 and tmpWorldData then
                    tmpWorldData.winNum = tmpWorldData.winNum + 1
                end
            end
        end

        M.SortedWorlds(tmpWorlds)
    else
        M.logger.Warning0("GetIndexSortedWorldsByPower", "没有对应的索引轮次数据", index)
    end
    return tmpWorlds
end

---获取战区对决的所有区服id
function M.GetCurTempBattleDuelWorldData()
    local tmpData = {}
    if M.groups.worlds and #M.groups.worlds > 0 then
        for i, v in pairs(M.groups.worlds) do
            table.insert(tmpData, {
                worldID = v.worldID,
                winNum = 0,
                power = v.power,
            })
        end
    end

    return tmpData
end

---根据参数获取table中的数据
function M.GetItemByParams(tmpTable, parKey, parValue)
    if tmpTable then
        for i = 1, #tmpTable do
            if tmpTable[i][parKey] == parValue then
                return tmpTable[i]
            end
        end
    end
    return nil
end

---将单个区服数据按战斗力降序排序
function M.SortedWorlds(tmpWorlds)
    if tmpWorlds and #tmpWorlds ~= 0 then
        table.sort(tmpWorlds, function(a, b)
            if a.winNum and b.winNum and a.winNum ~= b.winNum then
                return a.winNum > b.winNum
            end

            if a.power and a.power then
                return a.power > b.power
            end
        end)
    end
end

---获取自己战区对决战斗双方的sid
function M.GetSelfZoneDuelWorldIDs()
    local worldIDs = nil
    if M.vsItems and M.vsItems.worldIDA and M.vsItems.worldIDA ~= 0 and M.vsItems.worldIDB and M.vsItems.worldIDB ~= 0 then
        worldIDs = {}
        worldIDs.worldIDA = M.vsItems.worldIDA
        worldIDs.worldIDB = M.vsItems.worldIDB
    end
    return worldIDs
end

---获取战区对决1V1攻击方worldid
function M.GetSelfZoneDuelAtkWorldID()
    if M.vsItems and M.vsItems.atkWorldID and M.vsItems.atkWorldID ~= 0 then
        return M.vsItems.atkWorldID
    end
    return 0
end

---获取战区对决1V1攻击方worldid
function M.GetSelfZoneDuelDefWorldID()
    if M.vsItems and M.vsItems.defWorldID and M.vsItems.defWorldID ~= 0 then
        return M.vsItems.defWorldID
    end
    return 0
end

--当前轮次自己是否轮空
function M.IsCurSelfDuelSkip()
    if M.vsItems and (M.vsItems.worldIDA == -1 or M.vsItems.worldIDB == -1) then
        return true
    end
    return false
end

---获取战区对决活动id
function M.GetActivityID()
    M.GetServerCrossCfg()

    return M.cfgData.activityServerCross and M.cfgData.activityServerCross.AtyID or 601
end

--下一次可以跨服迁城的时间
function M.GetCrossMoveCDTime()
    return M.crossMoveCDTime
end

--endregion

--region ****************提供给View层的接口******************

---@public function 获取当前轮次索引
---@return number 轮次索引
function M.GetCurRoundIndex()
    if M.groups and M.groups.rounds then
        local count = #M.groups.rounds
        return count
    end
    return 0
end

---@public function 获取当前可以获得积分时间
function M.GetGainPointsTime()
    --local serverCross = M.GetServerCrossCfg()
    --local actID = serverCross.AtyID
    if M.configInPreviewBegin then
        local next_saturday, next_sunday = time_util.GetNextWeekDay(M.configInPreviewBegin)
        return next_saturday
    end
    return 0
end

---@public function 获取战区对决任务id
function M.GetWarZoneTaskID()
    local id = M.GetActivityID()
    local showDownCfg = game_scheme:SrverShowdown_1(id)

    if showDownCfg then
        local boxCount = showDownCfg.ProgressRewards.count
        local boxDataArr = showDownCfg.ProgressRewards.data

        for i = 0, boxCount - 1, 2 do
            local tempData = {}
            local taskID = boxDataArr[i]
            local taskCfg = game_scheme:TaskMain_0(taskID)
            local points = taskCfg.ConditionValue1
            tempData.taskID = taskID
            tempData.points = points
            table.insert(M.WarZoneTaskIDArr, tempData)
        end
    end
end

---@public function 获取自己战区对决国会争夺个人积分
function M.GetSelfCapitalPoints()
    local player_mgr = require "player_mgr"
    local sid = player_mgr.GetGoodsSidById(M.warZonePointsGoodsID)
    local itemCount = 0
    if sid and sid > 0 then
        itemCount = player_mgr.GetGoodsNumberBySid(sid)
    end
    return itemCount
end

---@public function 刷新领奖红点
function M.RefreshReceiveRed()
    M.bSkipReward = 0
    local red_system = require "red_system"
    local red_const = require "red_const"
    red_system.TriggerRed(red_const.Enum.WarZoneRewardBtn)

    local festival_activity_mgr = require "festival_activity_mgr"
    local festival_activity_cfg = require "festival_activity_cfg"
    local gw_event_activity_define = require "gw_event_activity_define"
    local activityID = festival_activity_mgr.GetActivityIdByCodeType(festival_activity_cfg.ActivityCodeType.WarZoneDuel)
    event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE, activityID)
end

---@public function 获取双方MVP数据
---@return table,table 自己的数据,对方的数据
function M.GetRankFirstData()
    local player_mgr = require "player_mgr"
    local selfWorldID = player_mgr.GetCreateRoleWorldID()
    local enemyWorldID = 0
    local selfFirstData = {}
    local enemyFirstData = {}
    for _, firstData in ipairs(M.rankFirstInfos) do
        if firstData.worldID == selfWorldID then
            M.SetRankFirstData(selfFirstData, firstData)
        else
            enemyWorldID = firstData.worldID
            M.SetRankFirstData(enemyFirstData, firstData)
        end
    end
    return selfFirstData, enemyFirstData
end
---@public function 设置战区第一个的联盟、玩家信息
---@param rankData table 要写入的数据
---@param rankFirstMsg table 传过来的网络数据
function M.SetRankFirstData(rankData, rankFirstMsg)
    if not rankFirstMsg then
        return nil
    end
    rankData = rankData or {}
    if rankFirstMsg.AllianceScore == 0 and rankFirstMsg.PlayerScore > 0 then
        rankData.roleID = rankFirstMsg.playerBasic.roleID --玩家roleId
        rankData.faceID = rankFirstMsg.playerBasic.faceID --头像id
        rankData.faceStr = rankFirstMsg.playerBasic.faceStr --头像字符串
        rankData.frameID = rankFirstMsg.playerBasic.frameID --头像框
        rankData.playerName = rankFirstMsg.playerBasic.name --玩家名
        rankData.playerScore = rankFirstMsg.PlayerScore  --玩家积分
        rankData.playerShortName = rankFirstMsg.playerBasic.leagueShortName --联盟简称
    elseif rankFirstMsg.PlayerScore == 0 and rankFirstMsg.AllianceScore > 0 then
        rankData.flag = rankFirstMsg.allianceBasic.leagueFlag--联盟旗帜
        rankData.shortName = rankFirstMsg.allianceBasic.leagueShortName --联盟简称
        rankData.allianceName = rankFirstMsg.allianceBasic.leagueName --联盟名
        rankData.alliancePoints = rankFirstMsg.AllianceScore  --联盟积分
    end
end

--设置刷新阶段时间这里直接被返回了
function M.GetServerPreviewTime()
    if M.serverNoticeTime and M.serverTimeStart then
        return M.serverNoticeTime, M.serverTimeStart
    end
    return 0, 0
end

-- 获得服务器的预告时间戳
function M.GetRankRewardCfg()
    local cfg = require "war_zone_duel_cfg"
    local rankRewardCfg = cfg.rankRewardCfg

end
--endregion

return M