local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tonumber = tonumber
local type = type
local Common_Util = CS.Common_Util.UIUtil

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local class = require "class"
local game_scheme = require "game_scheme"
local binding = require "ui_city_siege_leaderboard_binding"
local GWTaskMgr = require "gw_task_mgr"
local goods_item = require "goods_item_new"
local iui_item_detail = require "iui_item_detail"
local item_data = require "item_data"
local city_siege_activity_data = require "city_siege_activity_data"
local card_sprite_asset = require "card_sprite_asset"
local alliance_user_data = require "alliance_user_data"
local gw_task_const = require "gw_task_const"

--region View Life
module("ui_city_siege_leaderboard")
local ui_path = binding.UIPath
local window = nil
local UIView = {}
local sprite_asset = nil

UIView.widget_table = binding.WidgetTable

RankingEnum =
{
    [1] = {r=0.6313726, g=0.4117647, b=0.1529412, a=1},
    [2] = {r = 0.3058824,g=0.3843137,b=0.4862745,a=1},
    [3] = {r=0.654902,g=0.345098,b=0.2588235,a=1},
    [4] = {r=0.4117647,g=0.4352941,b=0.4666667,a=1},
}

function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)
    self:InitScrollRectTable()
    self.VData = {}
    sprite_asset = sprite_asset or card_sprite_asset.CreateLeagueAsset()
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base:OnHide()
end

function UIView:Close()   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    if self.srt_targetPage then
        self.srt_targetPage:ItemsDispose()
    end
    if self.srt_rankPage then
        self.srt_rankPage:ItemsDispose()
    end
    if self.srt_rewardPage then
        self.srt_rewardPage:ItemsDispose()
    end
    self.VData = nil
    if sprite_asset then
        sprite_asset:Dispose()
    end
    self.__base.Close(self)
end
--endregion

--region View Logic

--初始化列表
function UIView:InitScrollRectTable()
    self.srt_targetPage.onItemRender = OnItemRender
    self.srt_targetPage.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data then
            --rect_table Dispose时 Item上相关的资源是否需要Dispose 
            if scroll_rect_item.data[3] then
                for i,v in pairs(scroll_rect_item.data[3]) do
                    v:Dispose()
                end
            end
        end
    end
    self.srt_rankPage.onItemRender = OnItemRender2
    self.srt_rankPage.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data then
            --rect_table Dispose时 Item上相关的资源是否需要Dispose           
        end
    end
    self.srt_rewardPage.onItemRender = OnItemRender3
    self.srt_rewardPage.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data then
            if scroll_rect_item.data[3] then
                for i,v in pairs(scroll_rect_item.data[3]) do
                    v:Dispose()
                end
            end          
        end
    end
    self.srt_Content.onItemRender = OnItemRender4
end

function OnItemRender(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    if scroll_rect_item.data[3] then
        for i,v in pairs(scroll_rect_item.data[3]) do
            v:Dispose()
        end
    end
    scroll_rect_item.data[3] = {}
    --开始查找item 内部的属性 并进行相应的设置

    local titleStr = {string.format2(lang.Get(dataItem.data.TaskLang),dataItem.data.ConditionValue1),"(",dataItem.rate,"/",dataItem.data.ConditionValue1,")"}
    
    scroll_rect_item:Get("title").text = table.concat(titleStr)--string.format2(lang.Get(dataItem.data.TaskLang),dataItem.data.ConditionValue1)
    local GetRewardBtn = scroll_rect_item:Get("GetReward")
    GetRewardBtn.onClick:RemoveAllListeners()
    if dataItem.isLoop then
        Common_Util.SetActive(scroll_rect_item:Get("Mark"),dataItem.isGet)
        if not dataItem.isGet then
            Common_Util.SetActive(GetRewardBtn,true)
            local BtnGray = scroll_rect_item:Get("BtnGray")
            local TextGray = scroll_rect_item:Get("TextGray")
            BtnGray:SetEnable(not dataItem.status)
            TextGray:SetEnable(not dataItem.status)
            GetRewardBtn.onClick:AddListener(function()
                GWTaskMgr.ReceiveTaskReward(dataItem.data.TaskID,dataItem.actId,false)
            end)
        else
            Common_Util.SetActive(GetRewardBtn,false)
        end
    else
        Common_Util.SetActive(GetRewardBtn,false)
        Common_Util.SetActive(scroll_rect_item:Get("Mark"),dataItem.status)
    end

    local rewardTrans = scroll_rect_item:Get("reward")
    local rewardItemList = {}
    local rewardCfg = game_scheme:Reward_0(dataItem.data.TaskReward)
    if rewardCfg then
        if rewardCfg.iRewardType == 5 then --5表示多个奖励
            for i=1,10 do
                local value = rewardCfg["RewardParam"..i]
                if value and value.count > 0 then
                    local temp =
                    {
                        type = tonumber(value.data[0]),
                        index = tonumber(value.data[1]),
                        count = tonumber(value.data[2]),
                    }
                    table.insert(rewardItemList,temp)
                end
            end
        else
            local temp =
            {
                type = rewardCfg.iRewardType,
                index = rewardCfg.arrParam[0],
                count = rewardCfg.arrParam[1],
            }
            table.insert(rewardItemList,temp)
        end
        for i,v in ipairs(rewardItemList) do
            local item = goods_item.CGoodsItem():Init(rewardTrans, function(p)
                if not p then
                    return
                end
                p:DisplayInfo()
            end, 0.65)
            if item then
                item:SetGoods(nil,v.index,v.count,function()
                    iui_item_detail.Show(v.index, nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil)
                end)
                table.insert(scroll_rect_item.data[3],item)
            end
        end
    end

end

function OnItemRender2(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    --开始查找item 内部的属性 并进行相应的设置

    local bg = scroll_rect_item:Get("bg")
    local rankTxt = scroll_rect_item:Get("rankTxt")
    local rankIcon = scroll_rect_item:Get("rankIcon")
    local flag = scroll_rect_item:Get("flag")
    local AllianceName = scroll_rect_item:Get("AllianceName")
    local PlayerName = scroll_rect_item:Get("PlayerName")
    local Point = scroll_rect_item:Get("Point")
    
    Common_Util.SetActive(rankTxt,dataItem.rank >= 4)
    Common_Util.SetActive(rankIcon,dataItem.rank < 4)
    AllianceName.text = dataItem.alliranceName
    local alliance_data = require("alliance_data")
    local flagData = alliance_data.GetFlagIdData(dataItem.flag)
    if sprite_asset then
        sprite_asset:GetSprite("qizhi" .. flagData.iconID, function(sprite)
            if sprite then
                flag.sprite = sprite
            end
        end)
    end
    PlayerName.text = dataItem.ceoName;
    Point.text = dataItem.point;
    if dataItem.rank >= 4 then
        rankTxt.text = dataItem.rank;
        bg:Switch(3);
    else
        bg:Switch(dataItem.rank - 1);
        rankIcon:Switch(dataItem.rank - 1);
    end
end

function OnItemRender3(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    --开始查找item 内部的属性 并进行相应的设置
    if scroll_rect_item.data[3] then
        for i,v in pairs(scroll_rect_item.data[3]) do
            v:Dispose()
        end
    end
    scroll_rect_item.data[3] = {}
    local rank = dataItem.conditionalparameters.data[0]
    local rewardCfg = game_scheme:Reward_0(dataItem.reward)
    if rank <= 3 then
        scroll_rect_item:Get("title").text = string.format2(lang.Get(667126),rank)
        scroll_rect_item:Get("TitleBg"):Switch(rank - 1)
        scroll_rect_item:Get("titleOutline").effectColor = RankingEnum[rank]
    else
        scroll_rect_item:Get("title").text = string.format2(lang.Get(667126),dataItem.conditionalparameters.data[0].."-"..dataItem.conditionalparameters.data[1])
        scroll_rect_item:Get("TitleBg"):Switch(3)
        scroll_rect_item:Get("titleOutline").effectColor = RankingEnum[4]
    end
    local itemParent = scroll_rect_item:Get("rewardParent")
    local rewardItemList = {}
    if rewardCfg.iRewardType == 5 then --5表示多个奖励
        for i=1,10 do
            local value = rewardCfg["RewardParam"..i]
            if value and value.count > 0 then
                local temp =
                {
                    type = tonumber(value.data[0]),
                    index = tonumber(value.data[1]),
                    count = tonumber(value.data[2]),
                }
                table.insert(rewardItemList,temp)
            end
        end
    else
        local temp =
        {
            type = rewardCfg.iRewardType,
            index = rewardCfg.arrParam[0],
            count = rewardCfg.arrParam[1],
        }
        table.insert(rewardItemList,temp)
    end
    for i,v in ipairs(rewardItemList) do
        local item = goods_item.CGoodsItem():Init(itemParent, function(p)
            if not p then
                return
            end
            p:DisplayInfo()
        end, 0.6)
        if item then
            item:SetGoods(nil,v.index,v.count,function()
                iui_item_detail.Show(v.index, nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil)
            end)
            table.insert(scroll_rect_item.data[3],item)
        end
    end
end

function OnItemRender4(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    local bg = scroll_rect_item:Get("colorBg");
    local name = scroll_rect_item:Get("name");
    local points = scroll_rect_item:Get("points");

    Common_Util.SetActive(bg,index % 2 == 0);
    name.text = dataItem.title;
    points.text = dataItem.point;
end

function UIView:UpdatePanel(id)
    if self.isLoopBattle then --循环时才刷新
        self:SetPanel(id)
    end
end

function UIView:SetPanel(id)
    local cfg = game_scheme:Sandmapcompetition_0(id)
    local taskList = {}
    self.isLoopBattle = false
    self.activityID = id
    if cfg then
        if cfg.stagetype ~= 4 then--rankingrewards ~= 0 then
            local rankRewardList = city_siege_activity_data.GetRankReward()
            local rankItem = {}
            local rankType = cfg.stagetype
            if rankType > 3 then
                rankType = 3
            end
            for i,v in pairs(rankRewardList) do
                if v.subtype == rankType then
                    local temp =
                    {
                        conditionalparameters = v.conditionalparameters,
                        --title = v.Title,
                        reward = v.Reward,
                        --rewardID = v.RewardID,
                    }
                    table.insert(rankItem,temp)
                end
            end
            self.srt_rewardPage.data = rankItem
            self.srt_rewardPage:Refresh(0,-1)
            Common_Util.SetActive(self.obj_toggleGroup,true)
            Common_Util.SetActive(self.btn_Tips,false)
            self.isLoopBattle = false
        else
            self.txt_des.text = lang.Get(cfg.CompetitivePointsDescription)
            Common_Util.SetActive(self.obj_toggleGroup,false)
            Common_Util.SetActive(self.btn_Tips,true)
            self.isLoopBattle = true
        end
        for i=0,cfg.taskID.count do
            local taskCfg = game_scheme:TaskMain_0(cfg.taskID.data[i])
            if taskCfg then
                local taskData = GWTaskMgr.GetTaskData(cfg.taskID.data[i])
                if taskData then
                    local temp =
                    {
                        data = taskCfg,
                        status = taskData and taskData.rate>=taskCfg.ConditionValue1 or false,
                        rate = taskData.rate,
                        isGet = taskData.status,
                        isLoop = self.isLoopBattle,
                        actId = self.activityID,
                    }
                    table.insert(taskList,temp)
                end
            end
        end
        self.srt_targetPage.data = taskList
        self.srt_targetPage:Refresh(0,-1)
        Common_Util.SetActive(self.tog_target,cfg.taskID.count > 0)
        Common_Util.SetActive(self.tog_rank,cfg.rankingrewards and cfg.rankingrewards ~= 0)
        Common_Util.SetActive(self.tog_reward,cfg.rankingrewards and cfg.rankingrewards ~= 0)
        if cfg.taskID.count > 0 then
            self:ShowTargetPage()
        else
            self:ShowRankPage()
        end
    end
    
    local alliance_data = require("alliance_data")
    local userAllianceData = alliance_data.GetUserAllianceData()
    if not userAllianceData or userAllianceData.allianceId == nil or userAllianceData.allianceId == 0  then
        Common_Util.SetActive(self.obj_AllianceData,false)
        Common_Util.SetActive(self.obj_Empty,true)
    else
        Common_Util.SetActive(self.obj_AllianceData,true)
        Common_Util.SetActive(self.obj_Empty,false)
        self.txt_myAllianceName.text = string.format2("[{%s1}]{%s2}", alliance_data.GetUserAllianceShortName(), alliance_data.GetUserAllianceName())
        self.txt_myPlayerName.text = alliance_user_data.GetBossData(true).strName or ""
        local flagData = alliance_data.GetFlagIdData(userAllianceData.flag)
        if flagData then
            local allianceIconId = flagData.iconID;
            if sprite_asset then
                sprite_asset:GetSprite("qizhi" .. allianceIconId, function(sprite)
                    if sprite then
                        self.img_myFlag.sprite = sprite
                    end
                end)
            end
        end
    end
    if not self.isInit then
        self.isInit = true
        local taskLen = game_scheme:TaskMain_nums()
        local citySiegeList = {}
        for i = 0,taskLen - 1 do
            local temp = game_scheme:TaskMain(i)
            if temp.ModuleID == gw_task_const.TaskModuleType.CitySiege and temp.PointsRewardID.count > 0 then
                local tempTask =
                {
                    title = string.format2(lang.Get(temp.TaskLang),temp.ConditionValue1,temp.ConditionValue2.data[0]),
                    point = temp.PointsRewardID.data[1]
                }
                table.insert(citySiegeList,tempTask)
            end
        end
        self.srt_Content.data = citySiegeList
        self.srt_Content:Refresh(0,-1)
    end
    
end

function UIView:SetEmptyRank()
    Common_Util.SetActive(self.obj_RecordEmpty,true)
    Common_Util.SetActive(self.obj_rankScrollView,false)
    local alliance_data = require("alliance_data")
    local userAllianceData = alliance_data.GetUserAllianceData()
    if userAllianceData and userAllianceData.allianceId ~= nil then
        Common_Util.SetActive(self.txt_myRank,true)
        Common_Util.SetActive(self.ss_myRankIcon,false)
        self.txt_myPoint.text = lang.Get(661007);
        self.txt_myRank.text = lang.Get(661007);
    end
end

function UIView:SetRankInfo(msg)
    local rankList = {}
    local len = 0
    for i,v in ipairs(msg.rankInfo) do
        local temp = {}
        temp.alliranceName = string.format("[%s]%s", v.leagueShortName, v.leagueName)
        temp.rank = v.rank;
        temp.flag = v.leagueFlag;
        temp.point = v.score;
        temp.ceoName = v.ceoname;
        table.insert(rankList,temp)
        len = len + 1
    end
    Common_Util.SetActive(self.obj_RecordEmpty,len <= 0)
    Common_Util.SetActive(self.obj_rankScrollView,len > 0)
    self.srt_rankPage.data = rankList;
    self.srt_rankPage:Refresh(0,-1)
    local alliance_data = require("alliance_data")
    local userAllianceData = alliance_data.GetUserAllianceData()
    if userAllianceData and userAllianceData.allianceId ~= nil then
        if msg.selfRank then
            Common_Util.SetActive(self.txt_myRank,msg.selfRank.rank >= 4)
            Common_Util.SetActive(self.ss_myRankIcon,msg.selfRank.rank < 4)
            if msg.selfRank.score > 0 then
                self.txt_myPoint.text = msg.selfRank.score;
            else
                self.txt_myPoint.text = lang.Get(661007);
            end

            if msg.selfRank.rank >= 4 then
                self.txt_myRank.text = msg.selfRank.rank;
                self.ss_myRankBg:Switch(3);
            else
                self.ss_myRankBg:Switch(msg.selfRank.rank - 1);
                self.ss_myRankIcon:Switch(msg.selfRank.rank - 1);
            end
        else
            Common_Util.SetActive(self.txt_myRank,true)
            Common_Util.SetActive(self.ss_myRankIcon,false)
            self.txt_myPoint.text = lang.Get(661007);
            self.txt_myRank.text = lang.Get(661007);
        end

    end
end

function UIView:ShowTargetPage()
    Common_Util.SetActive(self.obj_TargetPage,true)
    Common_Util.SetActive(self.obj_RankPage,false)
    Common_Util.SetActive(self.obj_RewardPage,false)
    Common_Util.SetActive(self.obj_target_sel_txt,true)
    Common_Util.SetActive(self.obj_target_txt,false)
    Common_Util.SetActive(self.obj_rank_txt,true)
    Common_Util.SetActive(self.obj_rank_sel_txt,false)
    Common_Util.SetActive(self.obj_reward_txt,true)
    Common_Util.SetActive(self.obj_reward_sel_txt,false)
end

function UIView:ShowRankPage()
    Common_Util.SetActive(self.obj_TargetPage,false)
    Common_Util.SetActive(self.obj_RankPage,true)
    Common_Util.SetActive(self.obj_RewardPage,false)
    Common_Util.SetActive(self.obj_target_sel_txt,false)
    Common_Util.SetActive(self.obj_target_txt,true)
    Common_Util.SetActive(self.obj_rank_txt,false)
    Common_Util.SetActive(self.obj_rank_sel_txt,true)
    Common_Util.SetActive(self.obj_reward_txt,true)
    Common_Util.SetActive(self.obj_reward_sel_txt,false)
end

function UIView:ShowRewardPage()
    Common_Util.SetActive(self.obj_TargetPage,false)
    Common_Util.SetActive(self.obj_RankPage,false)
    Common_Util.SetActive(self.obj_RewardPage,true)
    Common_Util.SetActive(self.obj_target_sel_txt,false)
    Common_Util.SetActive(self.obj_target_txt,true)
    Common_Util.SetActive(self.obj_rank_txt,true)
    Common_Util.SetActive(self.obj_rank_sel_txt,false)
    Common_Util.SetActive(self.obj_reward_txt,false)
    Common_Util.SetActive(self.obj_reward_sel_txt,true)
    --查看个人任务奖励 上报
    city_siege_activity_data.EventReport("SandMapCompetition_TaskReward", {})
end

function UIView:ShowTipsPanel()
    Common_Util.SetActive(self.obj_TipsPanel,true)
end

function UIView:HideTipsPanel()
    Common_Util.SetActive(self.obj_TipsPanel,false)
end
--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil)
        else
			window:LoadUIResource(ui_path, nil, nil, nil)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
