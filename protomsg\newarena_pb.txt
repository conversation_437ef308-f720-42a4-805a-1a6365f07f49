-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
local common_new_pb=require("common_new_pb")
module('newarena_pb')


V1M=V(4,"enNewArenaType_Invalid",0,0)
V2M=V(4,"enNewArenaType_StormArena_Middle",1,1)
V3M=V(4,"enNewArenaType_StormArena_High",2,2)
V4M=V(4,"enNewArenaType_Max",3,3)
E1M=E(3,"EnNewArenaType",".CSMsg.EnNewArenaType")
V5M=V(4,"enArenaChallengeStatus_None",0,0)
V6M=V(4,"enArenaChallengeStatus_Lose",1,1)
V7M=V(4,"enArenaChallengeStatus_Win",2,2)
E2M=E(3,"EnArenaChallengeStatus",".CSMsg.EnArenaChallengeStatus")
V8M=V(4,"enArenaTeamNum_1V1",0,1)
V9M=V(4,"enArenaTeamNum_3V3",1,3)
E3M=E(3,"EnArenaTeamNum",".CSMsg.EnArenaTeamNum")
F1D=F(2,"nArenaID",".CSMsg.TMSG_NEW_ARENA_SET_DEFENCE_LINEUP_REQ.nArenaID",1,0,1,false,0,5,1)
F2D=F(2,"arrLineup",".CSMsg.TMSG_NEW_ARENA_SET_DEFENCE_LINEUP_REQ.arrLineup",2,1,3,false,{},11,10)
M1G=D(1,"TMSG_NEW_ARENA_SET_DEFENCE_LINEUP_REQ",".CSMsg.TMSG_NEW_ARENA_SET_DEFENCE_LINEUP_REQ",false,{},{},nil,{})
F3D=F(2,"nArenaID",".CSMsg.TMSG_NEW_ARENA_SET_DEFENCE_LINEUP_RSP.nArenaID",1,0,1,false,0,5,1)
F4D=F(2,"errorCode",".CSMsg.TMSG_NEW_ARENA_SET_DEFENCE_LINEUP_RSP.errorCode",2,1,1,false,0,5,1)
M3G=D(1,"TMSG_NEW_ARENA_SET_DEFENCE_LINEUP_RSP",".CSMsg.TMSG_NEW_ARENA_SET_DEFENCE_LINEUP_RSP",false,{},{},nil,{})
F5D=F(2,"nArenaID",".CSMsg.TMSG_NEW_ARENA_GET_DEFENCE_LINEUP_REQ.nArenaID",1,0,1,false,0,5,1)
F6D=F(2,"nTargetRoleID",".CSMsg.TMSG_NEW_ARENA_GET_DEFENCE_LINEUP_REQ.nTargetRoleID",2,1,1,false,0,13,3)
M4G=D(1,"TMSG_NEW_ARENA_GET_DEFENCE_LINEUP_REQ",".CSMsg.TMSG_NEW_ARENA_GET_DEFENCE_LINEUP_REQ",false,{},{},nil,{})
F7D=F(2,"nArenaID",".CSMsg.TMSG_NEW_ARENA_GET_DEFENCE_LINEUP_RSP.nArenaID",1,0,1,false,0,5,1)
F8D=F(2,"nTargetRoleID",".CSMsg.TMSG_NEW_ARENA_GET_DEFENCE_LINEUP_RSP.nTargetRoleID",2,1,1,false,0,13,3)
F9D=F(2,"arrTroopData",".CSMsg.TMSG_NEW_ARENA_GET_DEFENCE_LINEUP_RSP.arrTroopData",3,2,3,false,{},9,9)
F10D=F(2,"errorCode",".CSMsg.TMSG_NEW_ARENA_GET_DEFENCE_LINEUP_RSP.errorCode",4,3,1,false,0,5,1)
M5G=D(1,"TMSG_NEW_ARENA_GET_DEFENCE_LINEUP_RSP",".CSMsg.TMSG_NEW_ARENA_GET_DEFENCE_LINEUP_RSP",false,{},{},nil,{})
F11D=F(2,"nArenaID",".CSMsg.TMSG_NEW_ARENA_GET_RANK_INFO_REQ.nArenaID",1,0,1,false,0,5,1)
F12D=F(2,"nPageID",".CSMsg.TMSG_NEW_ARENA_GET_RANK_INFO_REQ.nPageID",2,1,1,false,0,5,1)
F13D=F(2,"nArenaType",".CSMsg.TMSG_NEW_ARENA_GET_RANK_INFO_REQ.nArenaType",3,2,1,false,0,5,1)
M6G=D(1,"TMSG_NEW_ARENA_GET_RANK_INFO_REQ",".CSMsg.TMSG_NEW_ARENA_GET_RANK_INFO_REQ",false,{},{},nil,{})
F14D=F(2,"nRoleID",".CSMsg.TNewArenaRankInfo.nRoleID",1,0,1,false,0,13,3)
F15D=F(2,"nScore",".CSMsg.TNewArenaRankInfo.nScore",2,1,1,false,0,5,1)
F16D=F(2,"nRank",".CSMsg.TNewArenaRankInfo.nRank",3,2,1,false,0,5,1)
F17D=F(2,"nDefenseLineUpPower",".CSMsg.TNewArenaRankInfo.nDefenseLineUpPower",4,3,1,false,0,13,3)
F18D=F(2,"tRoleInfo",".CSMsg.TNewArenaRankInfo.tRoleInfo",5,4,1,false,nil,11,10)
M7G=D(1,"TNewArenaRankInfo",".CSMsg.TNewArenaRankInfo",false,{},{},nil,{})
F19D=F(2,"nArenaID",".CSMsg.TMSG_NEW_ARENA_GET_RANK_INFO_RSP.nArenaID",1,0,1,false,0,5,1)
F20D=F(2,"errorCode",".CSMsg.TMSG_NEW_ARENA_GET_RANK_INFO_RSP.errorCode",2,1,1,false,0,5,1)
F21D=F(2,"tSelfRankInfo",".CSMsg.TMSG_NEW_ARENA_GET_RANK_INFO_RSP.tSelfRankInfo",3,2,1,false,nil,11,10)
F22D=F(2,"lsTopNRoleArenaRankInfo",".CSMsg.TMSG_NEW_ARENA_GET_RANK_INFO_RSP.lsTopNRoleArenaRankInfo",4,3,3,false,{},11,10)
F23D=F(2,"lsRoleArenaRankInfo",".CSMsg.TMSG_NEW_ARENA_GET_RANK_INFO_RSP.lsRoleArenaRankInfo",5,4,3,false,{},11,10)
F24D=F(2,"nPageID",".CSMsg.TMSG_NEW_ARENA_GET_RANK_INFO_RSP.nPageID",6,5,1,false,0,5,1)
F25D=F(2,"nChallengedNum",".CSMsg.TMSG_NEW_ARENA_GET_RANK_INFO_RSP.nChallengedNum",7,6,1,false,0,5,1)
F26D=F(2,"nTotalChallengedNum",".CSMsg.TMSG_NEW_ARENA_GET_RANK_INFO_RSP.nTotalChallengedNum",8,7,1,false,0,5,1)
F27D=F(2,"nLastRank",".CSMsg.TMSG_NEW_ARENA_GET_RANK_INFO_RSP.nLastRank",9,8,1,false,0,5,1)
F28D=F(2,"nActivityStartTime",".CSMsg.TMSG_NEW_ARENA_GET_RANK_INFO_RSP.nActivityStartTime",10,9,1,false,0,13,3)
F29D=F(2,"nActivityEndTime",".CSMsg.TMSG_NEW_ARENA_GET_RANK_INFO_RSP.nActivityEndTime",11,10,1,false,0,13,3)
F30D=F(2,"nArenaType",".CSMsg.TMSG_NEW_ARENA_GET_RANK_INFO_RSP.nArenaType",12,11,1,false,0,5,1)
F31D=F(2,"lsWorldIDs",".CSMsg.TMSG_NEW_ARENA_GET_RANK_INFO_RSP.lsWorldIDs",13,12,3,false,{},5,1)
F32D=F(2,"bCanUpgrade",".CSMsg.TMSG_NEW_ARENA_GET_RANK_INFO_RSP.bCanUpgrade",14,13,1,false,false,8,7)
M9G=D(1,"TMSG_NEW_ARENA_GET_RANK_INFO_RSP",".CSMsg.TMSG_NEW_ARENA_GET_RANK_INFO_RSP",false,{},{},nil,{})
F33D=F(2,"nRivalRoleID",".CSMsg.TArenaChallengeInfo.nRivalRoleID",1,0,1,false,0,13,3)
F34D=F(2,"nStatus",".CSMsg.TArenaChallengeInfo.nStatus",2,1,1,false,0,5,1)
F35D=F(2,"nDefenseLineUpPower",".CSMsg.TArenaChallengeInfo.nDefenseLineUpPower",3,2,1,false,0,5,1)
F36D=F(2,"tRoleInfo",".CSMsg.TArenaChallengeInfo.tRoleInfo",4,3,1,false,nil,11,10)
F37D=F(2,"nRank",".CSMsg.TArenaChallengeInfo.nRank",5,4,1,false,0,5,1)
F38D=F(2,"nRivalIndex",".CSMsg.TArenaChallengeInfo.nRivalIndex",6,5,1,false,0,5,1)
M10G=D(1,"TArenaChallengeInfo",".CSMsg.TArenaChallengeInfo",false,{},{},nil,{})
F39D=F(2,"nArenaID",".CSMsg.TMSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_REQ.nArenaID",1,0,1,false,0,5,1)
F40D=F(2,"bRefresh",".CSMsg.TMSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_REQ.bRefresh",2,1,1,false,false,8,7)
M11G=D(1,"TMSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_REQ",".CSMsg.TMSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_REQ",false,{},{},nil,{})
F41D=F(2,"nArenaID",".CSMsg.TMSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_RSP.nArenaID",1,0,1,false,0,5,1)
F42D=F(2,"bRefresh",".CSMsg.TMSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_RSP.bRefresh",2,1,1,false,false,8,7)
F43D=F(2,"errorCode",".CSMsg.TMSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_RSP.errorCode",3,2,1,false,0,5,1)
F44D=F(2,"arrChallengeInfo",".CSMsg.TMSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_RSP.arrChallengeInfo",4,3,3,false,{},11,10)
F45D=F(2,"nRefreshedNum",".CSMsg.TMSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_RSP.nRefreshedNum",5,4,1,false,0,5,1)
F46D=F(2,"nChallengedNum",".CSMsg.TMSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_RSP.nChallengedNum",6,5,1,false,0,5,1)
F47D=F(2,"nTotalChallengedNum",".CSMsg.TMSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_RSP.nTotalChallengedNum",7,6,1,false,0,5,1)
M12G=D(1,"TMSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_RSP",".CSMsg.TMSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_RSP",false,{},{},nil,{})
F48D=F(2,"nArenaID",".CSMsg.TMSG_NEW_ARENA_ENTER_BATTLE_REQ.nArenaID",1,0,1,false,0,5,1)
F49D=F(2,"nRivalRoleID",".CSMsg.TMSG_NEW_ARENA_ENTER_BATTLE_REQ.nRivalRoleID",2,1,1,false,0,13,3)
F50D=F(2,"arrLineUp",".CSMsg.TMSG_NEW_ARENA_ENTER_BATTLE_REQ.arrLineUp",3,2,3,false,{},11,10)
M13G=D(1,"TMSG_NEW_ARENA_ENTER_BATTLE_REQ",".CSMsg.TMSG_NEW_ARENA_ENTER_BATTLE_REQ",false,{},{},nil,{})
F51D=F(2,"nArenaID",".CSMsg.TMSG_NEW_ARENA_ENTER_BATTLE_RSP.nArenaID",1,0,1,false,0,5,1)
F52D=F(2,"nRivalRoleID",".CSMsg.TMSG_NEW_ARENA_ENTER_BATTLE_RSP.nRivalRoleID",2,1,1,false,0,13,3)
F53D=F(2,"nOldScore",".CSMsg.TMSG_NEW_ARENA_ENTER_BATTLE_RSP.nOldScore",3,2,1,false,0,5,1)
F54D=F(2,"nNewScore",".CSMsg.TMSG_NEW_ARENA_ENTER_BATTLE_RSP.nNewScore",4,3,1,false,0,5,1)
F55D=F(2,"nRivalOldScore",".CSMsg.TMSG_NEW_ARENA_ENTER_BATTLE_RSP.nRivalOldScore",5,4,1,false,0,5,1)
F56D=F(2,"nRivalNewScore",".CSMsg.TMSG_NEW_ARENA_ENTER_BATTLE_RSP.nRivalNewScore",6,5,1,false,0,5,1)
F57D=F(2,"nOldRank",".CSMsg.TMSG_NEW_ARENA_ENTER_BATTLE_RSP.nOldRank",7,6,1,false,0,5,1)
F58D=F(2,"nNewRank",".CSMsg.TMSG_NEW_ARENA_ENTER_BATTLE_RSP.nNewRank",8,7,1,false,0,5,1)
M14G=D(1,"TMSG_NEW_ARENA_ENTER_BATTLE_RSP",".CSMsg.TMSG_NEW_ARENA_ENTER_BATTLE_RSP",false,{},{},nil,{})
F59D=F(2,"nArenaID",".CSMsg.TMSG_NEW_ARENA_GET_BATTLE_RECORDS_REQ.nArenaID",1,0,1,false,0,5,1)
F60D=F(2,"nPageID",".CSMsg.TMSG_NEW_ARENA_GET_BATTLE_RECORDS_REQ.nPageID",2,1,1,false,0,5,1)
M15G=D(1,"TMSG_NEW_ARENA_GET_BATTLE_RECORDS_REQ",".CSMsg.TMSG_NEW_ARENA_GET_BATTLE_RECORDS_REQ",false,{},{},nil,{})
F61D=F(2,"nArenaID",".CSMsg.TMSG_NEW_ARENA_GET_BATTLE_RECORDS_RSP.nArenaID",1,0,1,false,0,5,1)
F62D=F(2,"nPageID",".CSMsg.TMSG_NEW_ARENA_GET_BATTLE_RECORDS_RSP.nPageID",2,1,1,false,0,5,1)
F63D=F(2,"arrBattleReportInfo",".CSMsg.TMSG_NEW_ARENA_GET_BATTLE_RECORDS_RSP.arrBattleReportInfo",3,2,3,false,{},9,9)
M16G=D(1,"TMSG_NEW_ARENA_GET_BATTLE_RECORDS_RSP",".CSMsg.TMSG_NEW_ARENA_GET_BATTLE_RECORDS_RSP",false,{},{},nil,{})
F64D=F(2,"nOldArenaID",".CSMsg.TMSG_NEW_ARENA_CHANGE_ARENA_REQ.nOldArenaID",1,0,1,false,0,5,1)
F65D=F(2,"nNewArenaID",".CSMsg.TMSG_NEW_ARENA_CHANGE_ARENA_REQ.nNewArenaID",2,1,1,false,0,5,1)
M17G=D(1,"TMSG_NEW_ARENA_CHANGE_ARENA_REQ",".CSMsg.TMSG_NEW_ARENA_CHANGE_ARENA_REQ",false,{},{},nil,{})
F66D=F(2,"nOldArenaID",".CSMsg.TMSG_NEW_ARENA_CHANGE_ARENA_RSP.nOldArenaID",1,0,1,false,0,5,1)
F67D=F(2,"nNewArenaID",".CSMsg.TMSG_NEW_ARENA_CHANGE_ARENA_RSP.nNewArenaID",2,1,1,false,0,5,1)
F68D=F(2,"errorCode",".CSMsg.TMSG_NEW_ARENA_CHANGE_ARENA_RSP.errorCode",3,2,1,false,0,5,1)
M18G=D(1,"TMSG_NEW_ARENA_CHANGE_ARENA_RSP",".CSMsg.TMSG_NEW_ARENA_CHANGE_ARENA_RSP",false,{},{},nil,{})
F69D=F(2,"nNewArenaID",".CSMsg.TMSG_NEW_ARENA_ENTER_ARENA_NTF.nNewArenaID",1,0,1,false,0,5,1)
F70D=F(2,"nOldArenaID",".CSMsg.TMSG_NEW_ARENA_ENTER_ARENA_NTF.nOldArenaID",2,1,1,false,0,5,1)
M19G=D(1,"TMSG_NEW_ARENA_ENTER_ARENA_NTF",".CSMsg.TMSG_NEW_ARENA_ENTER_ARENA_NTF",false,{},{},nil,{})
F71D=F(2,"nArenaID",".CSMsg.TMSG_NEW_ARENA_RANK_UPDATE_NTF.nArenaID",1,0,1,false,0,5,1)
F72D=F(2,"nOldRank",".CSMsg.TMSG_NEW_ARENA_RANK_UPDATE_NTF.nOldRank",2,1,1,false,0,5,1)
F73D=F(2,"nNewRank",".CSMsg.TMSG_NEW_ARENA_RANK_UPDATE_NTF.nNewRank",3,2,1,false,0,5,1)
M20G=D(1,"TMSG_NEW_ARENA_RANK_UPDATE_NTF",".CSMsg.TMSG_NEW_ARENA_RANK_UPDATE_NTF",false,{},{},nil,{})

E1M.values = {V1M,V2M,V3M,V4M}
E2M.values = {V5M,V6M,V7M}
E3M.values = {V8M,V9M}
F2D.message_type=common_new_pb.M3G
M1G.fields={F1D, F2D}
M3G.fields={F3D, F4D}
M4G.fields={F5D, F6D}
M5G.fields={F7D, F8D, F9D, F10D}
M6G.fields={F11D, F12D, F13D}
F18D.message_type=common_new_pb.M1G
M7G.fields={F14D, F15D, F16D, F17D, F18D}
F21D.message_type=M7G
F22D.message_type=M7G
F23D.message_type=M7G
M9G.fields={F19D, F20D, F21D, F22D, F23D, F24D, F25D, F26D, F27D, F28D, F29D, F30D, F31D, F32D}
F36D.message_type=common_new_pb.M1G
M10G.fields={F33D, F34D, F35D, F36D, F37D, F38D}
M11G.fields={F39D, F40D}
F44D.message_type=M10G
M12G.fields={F41D, F42D, F43D, F44D, F45D, F46D, F47D}
F50D.message_type=common_new_pb.M3G
M13G.fields={F48D, F49D, F50D}
M14G.fields={F51D, F52D, F53D, F54D, F55D, F56D, F57D, F58D}
M15G.fields={F59D, F60D}
M16G.fields={F61D, F62D, F63D}
M17G.fields={F64D, F65D}
M18G.fields={F66D, F67D, F68D}
M19G.fields={F69D, F70D}
M20G.fields={F71D, F72D, F73D}

TArenaChallengeInfo =M(M10G)
TMSG_NEW_ARENA_CHANGE_ARENA_REQ =M(M17G)
TMSG_NEW_ARENA_CHANGE_ARENA_RSP =M(M18G)
TMSG_NEW_ARENA_ENTER_ARENA_NTF =M(M19G)
TMSG_NEW_ARENA_ENTER_BATTLE_REQ =M(M13G)
TMSG_NEW_ARENA_ENTER_BATTLE_RSP =M(M14G)
TMSG_NEW_ARENA_GET_BATTLE_RECORDS_REQ =M(M15G)
TMSG_NEW_ARENA_GET_BATTLE_RECORDS_RSP =M(M16G)
TMSG_NEW_ARENA_GET_DEFENCE_LINEUP_REQ =M(M4G)
TMSG_NEW_ARENA_GET_DEFENCE_LINEUP_RSP =M(M5G)
TMSG_NEW_ARENA_GET_RANK_INFO_REQ =M(M6G)
TMSG_NEW_ARENA_GET_RANK_INFO_RSP =M(M9G)
TMSG_NEW_ARENA_RANK_UPDATE_NTF =M(M20G)
TMSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_REQ =M(M11G)
TMSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_RSP =M(M12G)
TMSG_NEW_ARENA_SET_DEFENCE_LINEUP_REQ =M(M1G)
TMSG_NEW_ARENA_SET_DEFENCE_LINEUP_RSP =M(M3G)
TNewArenaRankInfo =M(M7G)
enArenaChallengeStatus_Lose = 1
enArenaChallengeStatus_None = 0
enArenaChallengeStatus_Win = 2
enArenaTeamNum_1V1 = 1
enArenaTeamNum_3V3 = 3
enNewArenaType_Invalid = 0
enNewArenaType_Max = 3
enNewArenaType_StormArena_High = 2
enNewArenaType_StormArena_Middle = 1

