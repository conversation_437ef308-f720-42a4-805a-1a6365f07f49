﻿local require = require


module("server_info")

servers = {
    {name = "成鸿震1360_41", worldid = 1360, {name = "成鸿震", ip = "*************", port = 9101}},
		{name = "成鸿震1361_41", worldid = 1361, {name = "成鸿震", ip = "*************", port = 9101}},
		{name = "成鸿震1362_41", worldid = 1362, {name = "成鸿震", ip = "*************", port = 9101}},
		{name = "成鸿震1363_41", worldid = 1363, {name = "成鸿震", ip = "*************", port = 9101}},
		{name = "成鸿震1364_41", worldid = 1364, {name = "成鸿震", ip = "*************", port = 9101}},
		{name = "成鸿震1365_41", worldid = 1365, {name = "成鸿震", ip = "*************", port = 9101}},
		{name = "成鸿震1366_41", worldid = 1366, {name = "成鸿震", ip = "*************", port = 9101}},
		{name = "成鸿震1367_41", worldid = 1367, {name = "成鸿震", ip = "*************", port = 9101}},
		--{name = "成鸿震1360_35", worldid = 1360, {name = "成鸿震", ip = "*************", port = 9101}},
		--{name = "成鸿震1361_35", worldid = 1361, {name = "成鸿震", ip = "*************", port = 9101}},
		--{name = "成鸿震1362_35", worldid = 1362, {name = "成鸿震", ip = "*************", port = 9101}},
		--{name = "成鸿震1363_35", worldid = 1363, {name = "成鸿震", ip = "*************", port = 9101}},
    {name = "XMan主干0713", worldid = 1453, {name = "XMan主干0713", ip = "**************", port = 9101--[[,udp_port = 9101--]]}},
    {name = "吉凯私服", worldid = 1409, {name = "吉凯", ip = "*************", port = 9101}},
	{name = "樊改艳私服", worldid = 3823, {name = "樊改艳", ip = "*************", port = 9101}},
    {name = "XMan压力测试", worldid = 1455, {name = "XMan压力测试", ip = "**************", port = 9101}},
    {name = "陶子钊主干", worldid = 1215, {name = "陶子钊", ip = "*************", port = 9101}},
    {name = "曾俊铭私服", worldid = 1425, {name = "曾俊铭", ip = "*************6", port = 9101}},
    {name = "彭雨龙私服", worldid = 1888, {name = "彭雨龙", ip = "*************", port = 9101}},
    {name = "彭雨龙私服跨服", worldid = 1887, {name = "彭雨龙", ip = "*************", port = 9101}},
    {name = "江正东私服", worldid = 3070, {name = "江正东", ip = "**************", port = 9101}},
	{name = "江正东私服跨服", worldid = 3071, {name = "江正东", ip = "**************", port = 9101}},
    {name = "仇龙私服", worldid = 1213, {name = "测试9005", ip = "*************", port = 9101}},
    {name = "孙加亮私服", worldid = 2555, {name = "孙加亮私服", ip = "**************", port = 9101}},
    {name = "孙加亮DACS私服", worldid = 2553, {name = "孙加亮DACS私服", ip = "***********", port = 9101}},
    {name = "孙加亮私服2", worldid = 3554, {name = "孙加亮私服2", ip = "**************", port = 9102}},
    {name = "唐其文私服", worldid = 2552, {name = "唐其文私服", ip = "**************", port = 9101}},
    --{name = "唐其文DACS私服", worldid = 2552, {name = "唐其文DACS私服", ip = "***********", port = 9101}},
    {name = "张盛堡私服", worldid = 3556, {name = "张盛堡私服", ip = "*************", port = 9101}},
    {name = "张盛堡私服2", worldid = 4557, {name = "张盛堡私服", ip = "*************", port = 9101}},
    {name = "张盛堡DACS私服", worldid = 2556, {name = "张盛堡私服", ip = "***********", port = 9101}},
    {name = "张盛堡DACS私服2", worldid = 4556, {name = "张盛堡私服", ip = "***********", port = 9101}},
    {name = "赵忠管私服", worldid = 1235, {name = "赵忠管私服", ip = "**************", port = 9101}},
    {name = "赵忠管DACS私服", worldid = 1234, {name = "赵忠管DACS私服", ip = "***********", port = 9101}},
    {name = "孙彬彬私服", worldid = 2521, {name = "孙彬彬私服", ip = "*************", port = 9101}},
    {name = "孙彬彬DACS私服", worldid = 2522, {name = "孙彬彬DACS私服", ip = "***********", port = 9101}},
    {name = "姜冉博私服", worldid = 1997, {name = "姜冉博私服", ip = "*************", port = 9101}},
    {name = "温冠然私服", worldid = 1229, {name = "温冠然私服", ip = "**************", port = 9101}},
    {name = "温冠然DACS私服", worldid = 1228, {name = "温冠然DACS私服", ip = "***********", port = 9101}},
    {name = "赵旭东私服", worldid = 10544, {name = "赵旭东私服", ip = "**************", port = 9101}},
    {name = "跨服测试服赵旭东", worldid = 1511, {name = "跨服测试服赵旭东", ip = "**************", port = 9101}},
    {name = "蔡贵如私服", worldid = 2557, {name = "蔡贵如私服", ip = "**************", port = 9101}},
    {name = "蔡贵如DACS私服", worldid = 2557, {name = "蔡贵如DACS私服", ip = "***********", port = 9101}},
    {name = "杨晓伟私服1399", worldid = 1399, {name = "杨晓伟私服1399", ip = "*************", port = 9101}},
    {name = "杨晓伟私服1502跨服", worldid = 1502, {name = "杨晓伟私服1502", ip = "*************", port = 9101}},
    {name = "黄恩温私服", worldid = 2563, {name = "黄恩温私服", ip = "**************", port = 9101}},
    {name = "黄恩温DACS私服", worldid = 2560, {name = "黄恩温DACS私服", ip = "***********", port = 9101}},
    {name = "周广生DACS私服", worldid = 2561, {name = "周广生DACS私服", ip = "***********", port = 9101}},
    {name = "周广生私服", worldid = 2561, {name = "周广生私服", ip = "**************", port = 9101}},
    {name = "金家华私服", worldid = 2558, {name = "金家华私服", ip = "*************", port = 9101}},
    {name = "钟远珍DACS私服", worldid = 2571, {name = "钟远珍DACS私服", ip = "***********", port = 9101}},
    {name = "钟远珍私服", worldid = 2559, {name = "钟远珍私服", ip = "*************", port = 9101}},
    {name = "钟远珍沙漠风暴私服", worldid = 2570, {name = "钟远珍沙漠风暴私服", ip = "**************", port = 9101}},
    {name = "黄雄南私服", worldid = 1108, {name = "黄雄南私服", ip = "*************", port = 9101}},
    {name = "金凯私服", worldid = 1314, {name = "金凯私服", ip = "**************", port = 9101}},
    {name = "马宇驰私服", worldid = 6958, {name = "马宇驰私服", ip = "**************", port = 9101}},
    {name = "唐培程私服", worldid = 1400, {name = "唐培程私服", ip = "*************", port = 9101}},
    {name = "版署服", worldid = 1500, {name = "版署服1500", ip = "*************", port = 9101}},
    {name = "唐培程私服2", worldid = 1401, {name = "唐培程私服2", ip = "*************", port = 9101}},
    {name = "黄晓玟私服", worldid = 3251, {name = "黄晓玟私服", ip = "*************", port = 9101}},
    {name = "黄晓玟私服2", worldid = 3252, {name = "黄晓玟私服", ip = "*************", port = 9101}},
    {name = "黄浩私服", worldid = 6956, {name = "黄浩私服", ip = "*************", port = 9101}},
    {name = "陈财宇私服", worldid = 1819, {name = "陈财宇私服", ip = "*************", port = 9101}},
    {name = "刘艳阳私服", worldid = 2590, {name = "刘艳阳私服", ip = "*************", port = 9101}},
    {name = "刘艳阳DACS私服", worldid = 2590, {name = "刘艳阳DACS私服", ip = "***********", port = 9101}},
    {name = "农勇智私服", worldid = 3001, {name = "农勇智", ip = "*************", port = 9101}},
    {name = "刘志红DACS私服", worldid = 2889, {name = "刘志红", ip = "***********", port = 9101}},
    {name = "刘志红私服", worldid = 2888, {name = "刘志红", ip = "*************", port = 9101}},
    {name = "刘苏鹏私有云", worldid = 1212,{name = "测试1", ip = "*************", port = 9101}},
    {name = "苏天鸿私服", worldid = 6957, {name = "苏天鸿", ip = "*************", port = 9101}},
    {name = "刘梓孝私服", worldid = 3003, {name = "刘梓孝", ip = "*************", port = 9101}},
    {name = "黄颖媚私服", worldid = 3002, {name = "黄颖媚私服", ip = "*************", port = 9101}},
    {name = "陈伍宏私服", worldid = 6959, {name = "陈伍宏", ip = "*************", port = 9101}},
    {name = "陈航私服", worldid = 6960, {name = "陈航", ip = "*************", port = 9101}},
	 {name = "常贺翔私服", worldid = 6961, {name = "常贺翔", ip = "**************", port = 9101}},
    {name = "龙二辉测试私服", worldid = 2502, {name = "龙二辉测试私服", ip = "*************", port = 9101}},
	{name = "杨兆良私服", worldid = 9527, {name = "杨兆良私服", ip = "***********", port = 9101}},
	{name = "蓝琚成私服", worldid = 9528, {name = "蓝琚成私服", ip = "***********", port = 9101}},
	{name = "蓝琚成私服2", worldid = 9532, {name = "蓝琚成私服2", ip = "***********", port = 9101}},
	{name = "黄如意开发服", worldid = 9529, {name = "黄如意开发服", ip = "***********", port = 9101}},
    {name = "黄如意私服", worldid = 9550, {name = "黄如意私服", ip = "*************", port = 9101}},
	{name = "郑雷私服", worldid = 9530, {name = "郑雷私服", ip = "***********", port = 9101}},
	{name = "郑雷私服a", worldid = 9531, {name = "郑雷私服a", ip = "***********", port = 9101}},
	{name = "许海薇私服", worldid = 2222, {name = "许海薇私服", ip = "**************", port = 9101}},
	{name = "许海薇DACS私服", worldid = 2223, {name = "许海薇私服", ip = "***********", port = 9101}},
	{name = "许海薇私服2", worldid = 2224, {name = "许海薇私服2", ip = "**************", port = 9101}},
	{name = "张宁辉私服", worldid = 2800, {name = "张宁辉私服", ip = "**************", port = 9101}},
	{name = "张宁辉开发服", worldid = 2810, {name = "张宁辉开发服", ip = "***********", port = 9101}},
	{name = "魏广成私服", worldid = 3065, {name = "魏广成私服", ip = "*************", port = 9101}},
	{name = "魏广成私服1", worldid = 3266, {name = "魏广成私服", ip = "*************", port = 9101}},
	{name = "魏广成私服2", worldid = 3067, {name = "魏广成私服", ip = "**************", port = 9101}},
    {name = "魏广成私服11", worldid = 3266, {name = "魏广成私服", ip = "*************", port = 9101}},
    {name = "唐子棉私服", worldid = 1021, {name = "唐子棉私服", ip = "*************", port = 9101}},
    {name = "李玫私服", worldid = 1318, {name = "李玫私服", ip = "*************", port = 9101}},
    {name = "王莞天私服", worldid = 3777 ,{name = "王莞天私服", ip = "**************", port = 9101}},
	{name = "刘海平私服", worldid = 7777, {name = "刘海平私服", ip = "*************", port = 9101}},
	{name = "刘海平私服1", worldid = 7778, {name = "刘海平私服1", ip = "*************", port = 9101}},
	{name = "吴星鹏1255", worldid = 1255, {name = "吴星鹏", ip = "*************", port = 9101}},
	{name = "梁海燕1315", worldid = 1315, {name = "梁海燕", ip = "*************", port = 9101}},   
	{name = "向涛10030", worldid = 10030 ,{name = "向涛私服", ip = "**************", port = 9101}},
   	{name = "杨智圆", worldid = 1145 ,{name = "杨智圆私服", ip = "*************", port = 9101}},
	{name = "杨智圆1", worldid = 1150 ,{name = "杨智圆私服1", ip = "*************", port = 9101}},
	{name = "黄贺群2572", worldid = 2572 ,{name = "黄贺群私服", ip = "***********", port = 9101}},
	{name = "黄贺群2573", worldid = 2573 ,{name = "黄贺群DACS私服", ip = "*************", port = 9101}},
	{name = "黄振伟私服", worldid = 5020 ,{name = "黄振伟私服", ip = "*************", port = 9101}},
	{name = "黄振伟DACS私服", worldid = 5021, {name = "黄振伟DACS私服", ip = "***********", port = 9101}},
   	 {name = "张梓航私服", worldid = 3333 ,{name = "张梓航私服", ip = "***********", port = 9101}},
   	 {name = "张梓航测试服", worldid = 3334 ,{name = "张梓航测试服", ip = "*************", port = 9101}},
	{name = "王聪私服", worldid = 3069, {name = "王聪私服", ip = "*************", port = 9101}},
	{name = "谭磊私服", worldid = 9049, {name = "谭磊私服", ip = "*************", port = 9101}},
}

--在没有设置端口的时候，游客默认是以gates_tcp =2登录
-- tcp网关端口
local _gates_tcp = {
    [0] = { -- 外网网关组
    { ip = "1808wg01.cbb.szgla.com", port = 9101, desc = "描述", },
    { ip = "1808wg01.cbb.szgla.com", port = 9102, desc = "描述", },
    { ip = "1808wg02.cbb.szgla.com", port = 9103, desc = "描述", },	
    { ip = "1808wg02.cbb.szgla.com", port = 9104, desc = "描述", },	
    },

    [1] = { -- 预演服网关组
    { ip = "**************", port = 9101, desc = "描述", },
    { ip = "**************", port = 9102, desc = "描述", },
    },
	
    [2] = { -- 38网关组
    { ip = "*************", port = 9101, desc = "描述", },
    { ip = "*************", port = 9102, desc = "描述", },
    },
	
    [3] = { -- 47网关组
    { ip = "*************", port = 9101, desc = "描述", },
    { ip = "*************", port = 9102, desc = "描述", },
    },	

}
	
--[[
	
-- udp网关端口
local _gates_udp = {
    [0] = { -- 外网网关组
    { ip = "1808wg01.cbb.szgla.com", port = 9101, desc = "描述", },
    { ip = "1808wg01.cbb.szgla.com", port = 9102, desc = "描述", },
    { ip = "1808wg02.cbb.szgla.com", port = 9103, desc = "描述", },	
    { ip = "1808wg02.cbb.szgla.com", port = 9104, desc = "描述", },	
    },

    [1] = { -- 预演服网关组
    { ip = "**************", port = 9101, desc = "描述", },
    { ip = "**************", port = 9102, desc = "描述", },
    },
	
    [2] = { -- 38网关组
    { ip = "*************", port = 9101, desc = "描述", },
    { ip = "*************", port = 9102, desc = "描述", },
    },
	
    [3] = { -- 47网关组
    { ip = "*************", port = 9101, desc = "描述", },
    { ip = "*************", port = 9102, desc = "描述", },
    },	

}

--]]

local game_config = require "game_config"
gates_tcp = _gates_tcp[game_config.GAME_GATEWAY_GROUP] or {}
-- gates_udp = _gates_udp[game_config.GAME_GATEWAY_GROUP] or {}
