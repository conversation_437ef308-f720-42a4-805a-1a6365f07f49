--
--版权所有:冰川网络
-- Author:hlg
-- Date: 2024-08-19 16:12:34
--des 新增维护需要动到的
--
local red_const = {}
red_const.Tree2Children = nil --[父节点] = {子节点1，子节点2，子节点3}
--样式枚举
red_const.Type = {
    Default = "ui/prefabs/gw/gw_common/red/uicommonred.prefab", --小圆点，不带数字
    Num = "ui/prefabs/gw/gw_common/red/uicommonrednum.prefab", --带数字的（支持变宽）
    New = "ui/prefabs/gw/gw_common/red/uicommonrednew.prefab", --new
    Other = "ui/prefabs/gw/gw_common/red/UICommonrednum.prefab", --特殊的预留，后续都往下新增
    Mark = "ui/prefabs/gw/gw_common/red/uicommonredmark.prefab", --感叹号
    GM = "ui/prefabs/gw/gw_common/red/uicommonredgm.prefab", --感叹号
}
--红点枚举
red_const.Enum = {

    MainGW = "MainGW", --城建总红点
    MainStand = "MainStand", --沙盘总红点

    --测试用
    Panel1 = "Panel1",
    Panel1SkillItem = "Panel1SkillItem",
    Panel2 = "Panel2",
    Panel2SkillItem = "Panel2SkillItem",


    --英雄
    HeroAllMain = "HeroAllMain",
    HeroItemMain = "HeroItemMain",
    HeroItemLevel = "HeroItemLevel",
    HeroItemBtn = "HeroItemBtn",

    HeroItemSkill = "HeroItemSkill",
    HeroItemSkillIndex = "HeroItemSkillIndex",
    HeroItemStar = "HeroItemStar",

    HeroStoryMain = "HeroStoryMain", --英雄传记 main
    HeroStoryToggle1 = "HeroStoryToggle1", --英雄传记分页1
    HeroStoryToggle2 = "HeroStoryToggle2", --英雄传记分页2
    HeroStoryToggle3 = "HeroStoryToggle3", ----英雄传记分页3

    --背包
    BagMain = "BagMain",
    BagPage = "BagPage",
    BagItem = "BagItem",

    --建筑 列表
    BuildList = "BuildList",
    BuildListTogEco = "BuildListTogEco",
    BuildListTogAffair = "BuildListTogAffair",
    BuildListTogDecor = "BuildListTogDecor",

    --装饰建筑红点相关
    SPlusBuilding = "SPlusBuilding",
    SBuilding = "SBuilding",
    ABuilding = "ABuilding",

    --建筑队列
    BuildQueue = "BuildQueue",
    
    --科研列表
    TechnologyList = "TechnologyList",

    --增援
    ReinforceMain = "ReinforceMain",
    --体力
    StaminaMain = "StaminaMain",
    --集结
    GatherMain = "GatherMain",

    --英雄招募
    HeroRecruitMain = "HeroRecruitMain",
    HeroRecruitType = "HeroRecruitType",
    HeroRecruitItemBtn = "HeroRecruitItemBtn",

    --雷达
    RadarMain = "RadarMain",
    RadarItem = "RadarItem",

    --活动中心和入口红点
    ActivityEntrance = "ActivityEntrance", --入口
    ActivityCenterTab = "ActivityCenterTab", --分页

    --先锋目标
    PioneerTargetMain = "PioneerTargetMain",
    PioneerTargetDayItem = "PioneerTargetDayItem",
    PioneerTargetTaskTypeItem = "PioneerTargetTaskTypeItem",
    PioneerTargetTaskItem = "PioneerTargetTaskItem",
    PioneerScoreReward = "PioneerScoreReward",
    PioneerScoreRewardSingle = "PioneerScoreRewardSingle", 

    --装备
    EuqipHeroItem = "EuqipHeroItem", --装备单项
    EuqipHeroItemLevel = "EuqipHeroItemLevel", --装备升级
    EuqipHeroItemWear = "EuqipHeroItemWear", --装备穿戴
    EuqipHeroWearAll = "EuqipHeroWearAll", --一件穿戴

    EuqipHeroItemWearItem = "EuqipHeroItemWearItem", --穿戴界面红点

    --世界boss
    WorldBoss = "WorldBoss", --世界bos
    WorldBossHaveAttack = "WorldBossHaveAttack", --世界boss有攻击
    WorldBossAchievement = "WorldBossAchievement", --世界boss的可领取成就
    WorldBossSingleAchievement = "WorldBossSingleAchievement", --世界boss的单独可领取成就

    --丧尸宝藏
    ZombieTreasure = "ZombieTreasure", --丧尸宝藏
    ZombieTreasureReward = "ZombieTreasureReward", --丧尸宝藏领取按钮红点
    
    --同盟boss
    AllianceBoss = "AllianceBoss", --同盟boss活动主界面tab
    AllianceBossChallenge = "AllianceBossChallenge", --同盟boss挑战按钮红点

    --任务界面
    TaskMain = "TaskMain", --任务主界面红点
    TaskDay = "TaskDay", --任务日常
    TaskChapter = "TaskChapter", --任务章节
    TaskMainLine = "TaskMainLine", --任务主线
    TaskDayActivity = "TaskDayActivity", --任务日常活动

    --邮件
    MailMain = "MailMain", --邮件主界面红点
    MailItem = "MailItem", --邮件一级界面列表红点
    MailEntry = "MailEntry", --邮件二级界面条目红点

    --解救人质
    BomberMan = "BomberMan",

    CarriageMain = "CarriageMain", --货车主界面红点
    AllianceTrain = "AllianceTrain", --火车主界面红点

    CarriageOthers = "CarriageOthers", --货车他人红点
    CarriageMine = "CarriageMine", --货车自己红点

    --VIP
    VipFreeExp = "VipFreeExp",

    --全面备战
    FullyMain = "FullyMain",
    FullyExchangeShop = "FullyExchangeShop",
    FullyProgressReward = "FullyProgressReward",

    --荣誉墙
    HonorWallTankUpgrade = "HonorWallTankUpgrade",
    HonorWallPlanUpgrade = "HonorWallPlanUpgrade",
    HonorWallCannonUpgrade = "HonorWallCannonUpgrade",


    --试炼主界面Item红点提示
    ExperienceMain_Item = "ExperienceMain_Item",

    --阵营试炼

    --主界面底部图标
    CampTrial_Main_Icon = "CampTrial_Main_Icon",
    --阵营试炼成就宝箱
    CampTrial_Achievement = "CampTrial_Achievement",
    --阵营试炼主界面入口提示选择难度
    CampTrial_SelectDifficulty = "CampTrial_SelectDifficulty",
    --阵营试炼重要物品可购买
    CampTrial_ImportantItem = "CampTrial_ImportantItem",
    --新手竞技场红点
    ArenaNew_ChallengeTip = "ArenaNew_ChallengeTip",
    --巅峰竞技场红点
    ArenaWeekend_ChallengeTip = "ArenaWeekend_ChallengeTip",
    --3V3竞技场红点
    Arena3v3_ChallengeTip = "Arena3v3_ChallengeTip",
    
    --试炼界面Item红点New提示
    ExperienceItem_New = "ExperienceItem_New",
    
    
    --幸运转盘
    LuckyWheelGift = "LuckyWheelGift", --幸运转盘礼包

    --幸存者红点
    Survivor = "Survivor", --拜访者与幸存者

    --同盟对决红点
    AllianceDuel = "AllianceDuel",
    DuelExplain = "DuelExplain", --对决说明红点

    --将军试炼领取礼物
    GeneralTrailGiftItem = "GeneralTrailGiftItem", --礼物item
    GeneralTrailGiftEntrance = "GeneralTrailGiftEntrance", -- 礼物入口
    GeneralTrailGiftIndex = "GeneralTrailGiftIndex", --礼物页签01
    GeneralTrailGiftIndex1 = "GeneralTrailGiftIndex1", --礼物页签01
    GeneralTrailGiftIndex02 = "GeneralTrailGiftIndex02", --礼物页签02
    GeneralTrailGiftIndex03 = "GeneralTrailGiftIndex03", --礼物页签03
    GeneralTrailGiftIndex04 = "GeneralTrailGiftIndex04", --礼物页签04
    GeneralTrailGiftIndex05 = "GeneralTrailGiftIndex05", --礼物页签05
    GeneralTrailGiftIndex06 = "GeneralTrailGiftIndex06", --礼物页签06
    GeneralTrailGiftIndex07 = "GeneralTrailGiftIndex07", --礼物页签07
    GeneralTrailGiftIndex08 = "GeneralTrailGiftIndex08", --礼物页签08
    GeneralTrailGiftIndex09 = "GeneralTrailGiftIndex09", --礼物页签09
    GeneralTrailLeagueItem = "GeneralTrailLeagueItem", --联盟试炼item

    -- 活动通用模块红点，战令红点
    BattlePassToggleRed = "BattlePassRewardToggleRed",

    --周卡
    WeekendCardEveryDay = "WeekendCardEveryDay",
    WeekendCardTypeOfCard = "WeekendCardTypeOfCard",
    --月卡
    monthCardEveryDay = "monthCardEveryDay",
    --通用任务红点
    ComTaskItem = "ComTaskItem",
    --最强领主
    BestLeaderStage = "BestLeaderStage",
    BestLeaderBigReward = "BestLeaderBigReward",

    --国会红点
    Congress = "Congress",

    --region 联盟红点相关
    AllianceMain = "AllianceMain", --联盟主界面入口红点
    AllianceAchievement = "AllianceAchievement", --联盟成就按钮红点
    AllianceInvite = "AllianceInvite", --联盟邀请分页红点  

    AllianceTechnology = "AllianceTechnology", --联盟科技按钮红点
    
    AllianceGift = "AllianceGift", --联盟礼包红点
    AllianceBoxPage = "AllianceBoxPage", --联盟宝箱红点
    AllianceGiftPage = "AllianceGiftPage", --联盟礼包红点
    AllianceMedal = "AllianceMedal", --联盟徽章红点
    
    AllianceApply = "AllianceApply",--联盟申请列表红点

    AllianceMainMassBtn = "AllianceMainMassBtn",--联盟主界面集结按钮
    AllianceSendMass = "AllianceSendMass",--联盟集结集合按钮
    AllianceQuickMoveCity = "AllianceQuickMoveCity",--联盟快速迁城按钮
    AllianceInviteBtnRed = "AllianceInviteBtnRed",--联盟邀请按钮

    AllianceMainWarBtn = "AllianceMainWarBtn",--联盟主界面战争按钮
    SinglePersonWarRed = "SinglePersonWarRed",--单人战争红点
    TeamWarRed = "TeamWarRed",--组队战争红点
    --endregion

    --region 登录好礼红点
    LoginGiftEntrance = "LoginGiftEntrance", --登录好礼入口红点
    LoginGiftCanGetReward = "LoginGiftCanGetReward", --登录好礼可领取红点
    --endregion

    --排行榜
    RankingFaceRed = "RankingFaceRed",--头像红点 --TODO 后续变量名需要修改，头像红点不只有排行榜逻辑
    RankingMain = "RankingMain", --排行榜入口红点
    RankingMainItem = "RankingMainItem", --排行榜主界面红点
    RankingReward = "RankingReward", --排行榜详情红点

    --个性化
    Personalised = "Personalised", --个性化入口红点
    PersonalisedPerTag = "PersonalisedPerTag", --个性化页签红点

    PersonalisedProfileTag = "PersonalisedProfileTag", --个性化头像页签红点
    PersonalisedFrameTag = "PersonalisedFrameTag", --个性化边框页签红点
    PersonalisedSchlossTag = "PersonalisedSchlossTag", --个性化城堡页签红点
    PersonalisedNamePlateTag = "PersonalisedNamePlateTag", --个性化名牌页签红点
    PersonalisedAnimalTag = "PersonalisedAnimalTag", --个性化神兽页签红点
    PersonalisedEffectTag = "PersonalisedEffectTag", --个性化特效页签红点

    PersonalisedPerTagItem = "PersonalisedPerTagItem", --个性化页签内item红点

    --聊天红点
    ChatMain= "ChatMain", --聊天主界面红点
    
    ChatWorld = "ChatWorld", --世界聊天页签
    ChatSelfServer = "ChatSelfServer", --本服聊天子页签
    ChatLanguage = "ChatLanguage", --语言聊天子页签

    ChatAllianceMain = "ChatAllianceMain", --联盟聊天页签
    ChatAlliance = "ChatAlliance", --联盟聊天子页签
    ChatR4R5 = "ChatR4R5", --R4R5聊天子页签
    
    --每日特惠
    DaliyGift = "DaliyGift",
    
    --主界面小游戏
    MiniGame_Main_Icon = "MiniGame_Main_Icon",
    MiniGame_Tab_Red = "MiniGame_Tab_Red",--小游戏切页红点
    MiniGame_Level_Red = "MiniGame_Level_Red",--游戏关卡红点

    
    --冒险之旅挂机宝箱
    AdventureTripBox = "AdventureTripBox",

    ZombiesAttackShop = "ZombiesAttackShop",--丧尸来袭兑换商店
    ZombiesAttackShopItem = "ZombiesAttackShopItem",--丧尸来袭兑换商店列表

    Questionnaire = "Questionnaire", --问卷调查红点
    DownLoadRed = "DownLoadRed", --下载红点
    ChangePackageRed = "ChangePackageRed", --换包入口红点
    JumpDownloadUrlRed = "JumpDownloadUrlRed", --换包界面 跳转下载链接按钮红点

    shiningPrograssReward = "shiningPrograssReward",--闪金集市进度奖励
    shiningEveryDayGift = "shiningEveryDayGift",--闪金集市每日礼包 (礼包页面使用)
    shiningBtnEveryDayGift = "shiningBtnEveryDayGift",--闪金集市每日礼包 (按钮使用)

    cardDrawActivityTaskReward = "cardDrawActivityTaskReward", -- 召唤活动奖励红点
    cardDrawEntrance = "cardDrawEntrance",

    --神迹宝箱
    MiracleBoxFreeCall = "miracleBoxFreeCall", --神迹宝箱免费抽奖红点
    MiracleBoxProgressReward = "miracleProgressReward", --神迹进度奖励红点
    MiracleBoxFreeGift = "miracleBoxFreeGift", --神迹宝箱每日礼包免费红点
    
    --酒馆任务
    TavernTask = "TavernTask", --入口红点
    tavernMineTask = "tavernMineTask", --个人任务页签红点
    tavernLeagueTask = "tavernLeagueTask",--盟友任务页签红点
    tavernTreasure = "tavernTreasure",--酒馆隐秘宝藏红点（可挖掘）
    
    --集结大作战
    warRally = "warRally", -- 奖励按钮红点

    --怪物攻城
    MonsterAttackCity = "MonsterAttackCity", -- 主页面怪物攻城红点    
    --x新版怪物攻城主界面红点
    MonsterApprochingEnterRed = "MonsterApprochingEnterRed", -- 主页面怪物攻城入口红点
    MonsterApprochingTaskItem = "MonsterApprochingTaskItem", -- 怪物攻城任务界面红点
    
    --待办日程
    ScheduleEntrance = "ScheduleEntrance", --待办日程入口红点
    
        --末日降临
    DoomsdayToggleRed = "DoomsdayToggleRed", -- toggle按钮红点
    DoomsdayRewardRed = "DoomsdayRewardRed",
    
    --惊喜盒
    SurpriseBagEnterRed =  "SurpriseBagEnterRed",--入口按钮红点
    SurpriseBagAddBtnRed = "SurpriseBagAddBtnRed",--聊天+号按钮红点
    -- 寻宝大作战
    FindTreasureGetRewardRed = "FindTreasureGetRewardRed", -- 领取奖励入口红点

    --自选周卡
    OptionalWeekCardGetReward = "OptionalWeekCardGetReward", --自选周卡领取红点

    --首充红点
    FirstRechargeEntrance = "FirstRechargeEntrance",
    FirstRechargeDayToggle = "FirstRechargeDayToggle",
    FirstRechargeGetRreward = "FirstRechargeGetRreward",

    --七日登录红点
    SevenDayLogin = "SevenDayLogin",
    SevenDayLoginItem = "SevenDayLoginItem",
    --AI客服红点
    AICustomerService = "AICustomerService",

    --region 战区对决红点
    WarZoneMain = "WarZoneMain",--战区对决入口红点
    WarZoneCapitol = "WarZoneCapitol",--战区对决国会争夺页签红点
    WarZoneCapitolClickBtn = "WarZoneCapitolClickBtn",--战区对决国会争夺按钮
    WarZoneRewardBtn = "WarZoneRewardBtn",--战区对决连胜奖励按钮红点
    --endregion
    
    --风暴营救奖励红点
    StormRescueReward = "StormRescueReward",
    
    --个性换新红点
    FaceTransplantReward = "FaceTransplantReward",

    --个人主页点赞记录红点
    PersonalLikeRecord = "PersonalLikeRecord",

    --月月超值领取红点
    MonthGiftGetReward = "MonthGiftGetReward",

    --region 英雄副本红点
    HeroFirstChargeActivity = "HeroFirstChargeActivity", --英雄副本入口红点

    HeroFirstChallengePage = "HeroFirstChallengePage", --挑战页签红点
    HeroFirstNormalModel = "HeroFirstNormalModel", --普通模式红点
    HeroFirstEndlessModel = "HeroFirstHardModel", --困难模式红点
    
    HeroFirstNormalStartBtn = "HeroFirstNormalStartBtn", --普通模式挑战按钮红点 
    HeroFirstEndlessStartBtn = "HeroFirstEndlessStartBtn", --无尽模式挑战按钮红点
    
    HeroFirstCultivate = "HeroFirstChallengeReward", --养成页签红点
    HeroFirstEveryDayBox = "HeroFirstEveryDayBox", --每日宝箱红点
    HeroFirstUpgradeStar = "HeroFirstCanUpgrade", --可升星红点
    HeroFirstCultivateTaskRed = "HeroFirstTaskRed", --英雄养成任务红点

    HeroFirstRewardRed = "HeroFirstRewardRed", --英雄副本奖励红点
    HeroFirstEndlessRewardPage = "HeroFirstEndlessRewardPage", --英雄副本无尽模式奖励页签红点
    HeroFirstCultivateRewardRed = "HeroFirstCultivateRewardRed", --英雄副本养成奖励红点 
    HeroFirstOneClickBtn = "HeroFirstOneClickBtn", --英雄副本无尽模式一键领取按钮红点
    --endregion
    
    --region 神兽bingo
    MythicalBeastBingoMain = "MythicalBeastBingoMain", --神兽bingo主
    MythicalBeastBingoReward  = "MythicalBeastBingoBoxReward", --神兽bingo任务
    --endregion
    
    --region 神兽签到
    MythicalBeastSignIn = "MythicalBeastSignIn", --神兽签到
    MythicalBeastSignInReward  = "MythicalBeastSignInReward", --神兽签到奖励
    --endregion
    
    --region 狗屋
    DogHouseReward = "DogHouseReward", --狗屋奖励
    --endregion

    --region 万圣节
    HalloweenEntry = "HalloweenEntry", --万圣节入口
    HalloweenSignIn = "HalloweenSignIn", --万圣节签到
    HalloweenSignInReward = "HalloweenSignInReward", --万圣节签到奖励
    HalloweenSlotMachine = "HalloweenSlotMachine", --万圣节老虎机
    HalloweenSlotMachineReward = "HalloweenSlotMachineReward", --万圣节老虎机奖励
    --endregion
    
    NewMagicWeaponCombatTabRed = "NewMagicWeaponCombatTabRed",--神兽战斗进阶切页红点
    NewMagicWeaponCrystalBoxRed = "NewMagicWeaponCrystalBoxRed",--神兽技能晶核宝箱红点
    NewMagicWeaponCrystalTabRed = "NewMagicWeaponCrystalTabRed",--神兽结晶切页红点
    NewMagicWeaponCrystalToggleRed = "NewMagicWeaponCrystalToggleRed",--神兽结晶方案切页红点
    NewMagicWeaponCrystalRootRed = "NewMagicWeaponCrystalRootRed",--神兽结晶方案槽位红点
    NewMagicWeaponCrystalUpgradeRed = "NewMagicWeaponCrystalUpgradeRed",--神兽结晶升星红点
    NewMagicWeaponCrystalReplaceRed = "NewMagicWeaponCrystalReplaceRed",--神兽结晶替换红点
}

red_const.Tree = {}

--神兽
red_const.Tree.NewMagicWeapon = {
    [red_const.Enum.NewMagicWeaponCombatTabRed] = {
        [red_const.Enum.NewMagicWeaponCrystalBoxRed] = {},
    },
    [red_const.Enum.NewMagicWeaponCrystalTabRed] = {
        [red_const.Enum.NewMagicWeaponCrystalToggleRed] = {
            [red_const.Enum.NewMagicWeaponCrystalRootRed] = {
                [red_const.Enum.NewMagicWeaponCrystalUpgradeRed] = {},
                [red_const.Enum.NewMagicWeaponCrystalReplaceRed] = {},
            },
        },
    },
}

--背包
red_const.Tree.Bag = {
    [red_const.Enum.BagMain] = {
        [red_const.Enum.BagPage] = {
            [red_const.Enum.BagItem] = {},
        },
    }
}
--英雄
red_const.Tree.Hero = {
    [red_const.Enum.HeroAllMain] = {
        [red_const.Enum.HeroItemMain] = {
            [red_const.Enum.HeroItemLevel] = {
                [red_const.Enum.HeroItemBtn] = {}, --英雄经验
                [red_const.Enum.EuqipHeroItem] = {
                    [red_const.Enum.EuqipHeroItemLevel] = {}, --装备升级
                    [red_const.Enum.EuqipHeroItemWear] = {}, --装备替换
                }, --单个英雄装备
                [red_const.Enum.EuqipHeroWearAll] = {

                    [red_const.Enum.EuqipHeroItemWear] = {}, --装备替换
                }, --一件穿戴
            },
            [red_const.Enum.HeroItemSkill] = {
                [red_const.Enum.HeroItemSkillIndex] = {},
            },
            [red_const.Enum.HeroItemStar] = {},
        },
    },
    [red_const.Enum.HeroStoryMain] = {
        [red_const.Enum.HeroStoryToggle1] = {},
        [red_const.Enum.HeroStoryToggle2] = {},
        [red_const.Enum.HeroStoryToggle3] = {},
    }
}
--建筑
red_const.Tree.Build = {
    [red_const.Enum.BuildList] = {
        [red_const.Enum.BuildListTogEco] = {},
        [red_const.Enum.BuildListTogAffair] = {},
        [red_const.Enum.BuildListTogDecor] = {},
    }
}

red_const.Tree.BuildQueue = {
    [red_const.Enum.BuildQueue] = {

    }
}

--招募
red_const.Tree.Recruit = {
    [red_const.Enum.HeroRecruitMain] = {
        [red_const.Enum.HeroRecruitType] = {
            [red_const.Enum.HeroRecruitItemBtn] = {},
        }
    }
}

--幸存者与拜访者
red_const.Tree.Survivor = {
    [red_const.Enum.Survivor] = {

    }
}

--酒馆任务
red_const.Tree.TavernTask = {
    [red_const.Enum.TavernTask] = {

    }
}

--雷达
red_const.Tree.Radar = {
    [red_const.Enum.RadarMain] = {
        [red_const.Enum.RadarItem] = {},
    }
}
--任务
red_const.Tree.Task = {
    [red_const.Enum.TaskMain] = {
        [red_const.Enum.TaskDay] = { [red_const.Enum.TaskDayActivity] = {} },
        [red_const.Enum.TaskChapter] = {},
        [red_const.Enum.TaskMainLine] = {},
    }
}

-- 主页头像 - 排行榜 
red_const.Tree.Ranking = {
    [red_const.Enum.RankingFaceRed] = {
        [red_const.Enum.RankingMain] = {
            [red_const.Enum.RankingMainItem] = {
                [red_const.Enum.RankingReward] = {},
            },
        },

        [red_const.Enum.Personalised] = {
            [red_const.Enum.PersonalisedPerTag] = {
                [red_const.Enum.PersonalisedPerTagItem] = {},
            },
            [red_const.Enum.PersonalisedProfileTag] = {},
            [red_const.Enum.PersonalisedFrameTag] = {},
            [red_const.Enum.PersonalisedSchlossTag] = {},
            [red_const.Enum.PersonalisedNamePlateTag] = {},
            [red_const.Enum.PersonalisedAnimalTag] = {},
            [red_const.Enum.PersonalisedEffectTag] = {},
        },
    }
}

--末日降临
red_const.Tree.Doomsday = {
    [red_const.Enum.DoomsdayRewardRed] = {
        [red_const.Enum.DoomsdayToggleRed] = {

        }
    }
}
--科研
red_const.Tree.Technology = {
    [red_const.Enum.TechnologyList] = {},
}

--国会
red_const.Tree.Congress = {
    [red_const.Enum.Congress] = {}
}

--世界boss
red_const.Tree.WorldBoss = {
    [red_const.Enum.WorldBossHaveAttack] = {},
    [red_const.Enum.WorldBossAchievement] = {}
}
--同盟boss
red_const.Tree.AllianceBoss = {
    [red_const.Enum.AllianceBossChallenge] = {},
}
--邮件
red_const.Tree.Mail = {
    [red_const.Enum.MailMain] = {
        [red_const.Enum.MailItem] = {
            [red_const.Enum.MailEntry] = {},
        },
    }
}

--货车
red_const.Tree.Carriage = {
    [red_const.Enum.CarriageMain] = {},
    [red_const.Enum.CarriageOthers] = {},
    [red_const.Enum.CarriageMine] = {},
}

--阵营试炼
red_const.Tree.CampTrial = {
    [red_const.Enum.ExperienceMain_Item] = {
        [red_const.Enum.CampTrial_Main_Icon] = {
            [red_const.Enum.CampTrial_Achievement] = {},
            [red_const.Enum.CampTrial_SelectDifficulty] = {},
            [red_const.Enum.CampTrial_ImportantItem] = {},
            [red_const.Enum.ArenaNew_ChallengeTip] = {},
            [red_const.Enum.ArenaWeekend_ChallengeTip] = {},
            [red_const.Enum.Arena3v3_ChallengeTip] = {},
            [red_const.Enum.ExperienceItem_New] = {},
        },
    }
}

red_const.Tree.Alliance = {
    [red_const.Enum.AllianceMain] = {
        [red_const.Enum.AllianceInvite] = {},
        [red_const.Enum.AllianceTechnology] = {},
        [red_const.Enum.AllianceGift] = {
            [red_const.Enum.AllianceBoxPage] = {},
            [red_const.Enum.AllianceGiftPage] = {},
        },
        [red_const.Enum.AllianceMedal] = {},
        [red_const.Enum.AllianceAchievement] = {},
        [red_const.Enum.AllianceApply] = {},
        
        [red_const.Enum.AllianceMainMassBtn] = {
            [red_const.Enum.AllianceSendMass] = {},
            [red_const.Enum.AllianceQuickMoveCity] = {},
        },
        [red_const.Enum.AllianceInviteBtnRed] = {},
        
        [red_const.Enum.AllianceMainWarBtn] = {
            [red_const.Enum.SinglePersonWarRed] = {},
            [red_const.Enum.TeamWarRed] = {},
        }
    },
}

--聊天红点
red_const.Tree.Chat = {
    [red_const.Enum.ChatMain] = {
        [red_const.Enum.ChatWorld] = {
            [red_const.Enum.ChatSelfServer] = {},
            [red_const.Enum.ChatLanguage] = {},
        },

        [red_const.Enum.ChatAllianceMain] = {
            [red_const.Enum.ChatAlliance] = {},
            [red_const.Enum.ChatR4R5] = {},
        },
    },
}

--排行榜
red_const.Tree.ZombiesAttack = {
    [red_const.Enum.ZombiesAttackShop] = {
        [red_const.Enum.ZombiesAttackShopItem] = {
        },
    }
}

--战区对决
red_const.Tree.WarZoneDuel = {
    [red_const.Enum.WarZoneMain] = {
        [red_const.Enum.WarZoneCapitol] = {
            [red_const.Enum.WarZoneRewardBtn] = {
            },
        },
    }
}

--暴风营救
red_const.Tree.StormRescue = {
    [red_const.Enum.StormRescueReward] = {
    }
}

--个性换新
red_const.Tree.FaceTransplant = {
    [red_const.Enum.FaceTransplantReward] = {
    }
}

--英雄副本
red_const.Tree.HeroFirstChallenge = {
    [red_const.Enum.HeroFirstChargeActivity] = {
        [red_const.Enum.HeroFirstChallengePage] = {
            [red_const.Enum.HeroFirstNormalModel] = {
                [red_const.Enum.HeroFirstNormalStartBtn] = {},
            },
            [red_const.Enum.HeroFirstEndlessModel] = {
                [red_const.Enum.HeroFirstEndlessStartBtn] = {},
            },
            --无尽模式奖励红红点
            [red_const.Enum.HeroFirstRewardRed] = {
                [red_const.Enum.HeroFirstEndlessRewardPage] = {
                    [red_const.Enum.HeroFirstOneClickBtn] = {},
                },
                [red_const.Enum.HeroFirstCultivateRewardRed] = {
                    [red_const.Enum.HeroFirstCultivateTaskRed] = {},
                },
            },
        },
        [red_const.Enum.HeroFirstCultivate] = {
            [red_const.Enum.HeroFirstEveryDayBox] = {},
            [red_const.Enum.HeroFirstCultivateTaskRed] = {},
            [red_const.Enum.HeroFirstUpgradeStar] = {},
        },
    }
}

red_const.Enum.MythicalBeastBingoMain = {
    [red_const.Enum.MythicalBeastBingoReward] = {
    }
}

--红点关系树  {[子节点] = {父节点1，父节点2}}
-- red_const.ParentTree = {
--     --
--     --英雄
--     [red_const.Enum.Panel1SkillItem] = {red_const.Enum.Panel1},
--     [red_const.Enum.Panel2SkillItem] = {red_const.Enum.Panel2},
--     --建筑列表
--     [red_const.Enum.BuildListTogEco] = {red_const.Enum.BuildList},
--     [red_const.Enum.BuildListTogAffair] = {red_const.Enum.BuildList},
--     [red_const.Enum.BuildListTogDecor] = {red_const.Enum.BuildList},

--     --英雄列表
--     [red_const.Enum.HeroItemMain] = {red_const.Enum.HeroAllMain},
--     [red_const.Enum.HeroItemLevel] = {red_const.Enum.HeroItemMain},
--     [red_const.Enum.HeroItemBtn] = {red_const.Enum.HeroItemLevel},
--     [red_const.Enum.HeroItemSkill] = {red_const.Enum.HeroItemMain},
--     [red_const.Enum.HeroItemSkillIndex] = {red_const.Enum.HeroItemSkill},
--     [red_const.Enum.HeroItemStar] = {red_const.Enum.HeroItemMain},
--     --背包
--     [red_const.Enum.BagItem] = {red_const.Enum.BagPage },
--     [red_const.Enum.BagPage] = { red_const.Enum.BagMain},
-- }

--动态的，比如升级材料，是配置的不可知的
red_const.GoodsRedMap = {}
function red_const.AddGoodsRed(costId, redId, func)
    if not red_const.GoodsRedMap[costId] then
        red_const.GoodsRedMap[costId] = {}
    end
    if not red_const.GoodsRedMap[costId][redId] then
        red_const.GoodsRedMap[costId][redId] = func
    end
end

--刷新红点，现在主界面先打开，背包数据很晚才回来
function red_const.FreshGoodsRed()
    for costId,list in pairs(red_const.GoodsRedMap or {}) do
        for redId,func in pairs(list) do
            func()
        end
    end
end

--注册功能开放刷新
red_const.FunctionOpeRedMap = {}
function red_const.AddFunctionOpenRed(openId, redId, func)
    if not red_const.FunctionOpeRedMap[openId] then
        red_const.GoodsRedMap[openId] = {}
    end
    if not red_const.GoodsRedMap[openId][redId] then
        red_const.GoodsRedMap[openId][redId] = func
    end
end

--静态的，就是已知什么物品变动会影响什么红点
red_const.ConstGoodsRedMap = {}
function red_const.GetConstGoodsRedMap()
    if not next(red_const.ConstGoodsRedMap) then
        local skep_mgr = require "skep_mgr"
        red_const.ConstGoodsRedMap = {
            [skep_mgr.const_id.heroExp] = { red_const.Enum.HeroItemBtn }, --英雄经验
        }
    end

    return red_const.ConstGoodsRedMap
end

function red_const.Clear()
    red_const.FunctionOpeRedMap = {}
end

return red_const