---
--- Generated by Emmy<PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by hans<PERSON><PERSON>.
--- DateTime: 2024/6/28 10:23
--- DeSC : 联盟礼物数据
---
local require = require
local ipairs = ipairs
local pairs = pairs
local table = table
local print = print
local type = type
local string = string
local lang = require "lang"
local flow_text = require "flow_text"
local red_const = require "red_const"
local red_system = require "red_system"
local log = require "log"
local event = require "event"
local event_alliance_define = require "event_alliance_define"
local game_scheme = require "game_scheme"
local net_login_module = require "net_login_module"
local time_util = require "time_util"
local alliance_pb = require "alliance_pb"
local topic_pb = require "topic_pb"
local net_prop_module = require "net_prop_module"
local util = require "util"
local prop_pb = require "prop_pb"
local player_mgr = require "player_mgr"
module("alliance_gift_data")

local curGiftLevel = 0; --当前联盟礼物等级
local curGiftExp = 0; --当前联盟礼物经验
local boxCanGetNums = 0; --宝箱可领取次数
local giftCanGetNums = 0; --赠礼可领取次数
local BoxData = {};--宝箱奖励
local BoxSortData = {};--宝箱排序
local GiftData = {};--礼包奖励
local GiftSortData = {};--礼包排序
local giftLevelExp = {} --联盟礼物等级经验 
local giftLevelMaxLevel = 1
local anonymousSate = 0; --匿名状态
local maxCount = 200; --最大存储量

--topic 数据转换
local function OnTopicEvent(data)
    SetAnonymousTopic(data)
end
local function OnLoginEvent(data)
    local taskData = prop_pb.TSubjectPart()
    taskData:ParseFromString(data)
    if taskData.topicData then
        for i, value in ipairs(taskData.topicData) do
            if value.topicID == topic_pb.TOPICNAME_ALLIANCE_GIFT_TOPICDATA then
                local info = {}
                info.topicKey = 0
                info.value = value.data[1] or 0
                SetAnonymousTopic(info)
            end
        end
    end
end

function Init()
    
end

--获取战利品宝箱敌人Id
function GetLookChestEnemyId()

end
--刷新表数据
local function RefreshCfgData(data)
    local level = 0
    if data.boxType and data.boxType ~= alliance_pb.EAllianceGiftBoxType_Present then
        level = data.giftLv
    end
    local cfg = game_scheme:SpoilsChest_0(data.spoilsID, level)
    if cfg then
        data.BoxType = cfg.BoxType
        data.AllianceGiftLevel = cfg.AllianceGiftLevel
        data.levelRewardItemID = cfg.levelRewardItemID
        data.TextConfiguration = cfg.TextConfiguration
        data.GiftRetentionTime = cfg.GiftRetentionTime
        data.GiftExperience = cfg.GiftExperience
    else
        log.Error("RefreshCfgData error id = " .. data.spoilsID .. " level = " .. level)
    end
    return data;
end
local function RefreshGiftBaseData(info)
    local data = {}
    if info:HasField("roleId") then
        data.roleId = info.roleId
    end
    if info:HasField("roleName") then
        data.roleName = info.roleName
    end
    if info:HasField("createTime") then
        data.createTime = info.createTim
    end
    if info:HasField("expireTime") then
        data.expireTime = info.expireTime
    end
    if info:HasField("boxId") then
        data.boxId = info.boxId
    end
    if info:HasField("spoilsID") then
        data.spoilsID = info.spoilsID
    end
    if info:HasField("boxType") then
        data.boxType = info.boxType
    end
    if info:HasField("experience") then
        data.experience = info.experience
    end
    if info:HasField("anonymous") then
        data.anonymous = info.anonymous
    end
    if info:HasField("boxChooseId") then
        data.boxChooseId = info.boxChooseId
    end
    if info:HasField("rewardId") then
        data.rewardId = info.rewardId
    end
    if info:HasField("giftLv") then
        data.giftLv = info.giftLv
    end
    if info:HasField("rechargeID") then
        data.rechargeID = info.rechargeID
    end
    
    data.time = data.expireTime - net_login_module.GetServerTime()
    return data
end

--region 初始化 联盟礼物数据
-- 自定义排序函数
local function compare(a, b)
    -- 首先根据 rewardId 排序，已领取的在后
    if (a.rewardId ~= nil) and (b.rewardId == nil) then
        return false
    elseif (a.rewardId == nil) and (b.rewardId ~= nil) then
        return true
    end
    -- 如果 rewardId 相同（即都为 nil 或都不为 nil），则按照时间排序
    return a.createTime > b.createTime
end
--刷新查看宝箱
local function InitBoxData(msg)
    BoxData = {}
    BoxSortData = {}
    if msg.arrLootBox then
        local index = 0
        for i, v in ipairs(msg.arrLootBox) do
            local info = {}
            local tempInfo = RefreshGiftBaseData(v)
            info = RefreshCfgData(tempInfo)
            if not info.rewardId then
                index = index + 1
            end
            if not BoxData[v.boxId] then
                local sortData = {}
                sortData.boxId = v.boxId
                sortData.createTime = v.createTime
                sortData.rewardId = info.rewardId
                table.insert(BoxSortData, sortData)
                BoxData[v.boxId] = info
            end
        end
        table.sort(BoxSortData, compare)
        boxCanGetNums = index
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_BOX_DATA)
    end
end
--刷新礼包
local function InitGiftData(msg)
    GiftData = {}
    GiftSortData = {}
    if msg.arrAllyBox then
        local index = 0
        for i, v in ipairs(msg.arrAllyBox) do
            local info = {}
            tempInfo = RefreshGiftBaseData(v)
            info = RefreshCfgData(tempInfo)
            if not info.rewardId then
                index = index + 1
            end
            if not GiftData[v.boxId] then
                local sortData = {}
                sortData.boxId = v.boxId
                sortData.createTime = v.createTime
                sortData.rewardId = info.rewardId
                table.insert(GiftSortData, sortData)
                GiftData[v.boxId] = info
            end
        end
        table.sort(GiftSortData, compare)
        giftCanGetNums = index
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_GIFT_DATA)
    end
end
--初始化 联盟礼物数据
function InitNTF(msg)
    InitBoxData(msg)
    InitGiftData(msg)
    --red_system.TriggerRed(red_const.Enum.AllianceGift)
    red_system.TriggerRed(red_const.Enum.AllianceBoxPage)
    red_system.TriggerRed(red_const.Enum.AllianceGiftPage)
end
--endregion

--region 产生联盟礼物数据

local function RefreshBoxData(msg)
    if msg.arrLootBox and #msg.arrLootBox > 0 then
        local index = 0
        local isMarauder = false
        for i, v in ipairs(msg.arrLootBox) do
            local info = {}
            info = RefreshGiftBaseData(v)
            info = RefreshCfgData(info)
            BoxData[v.boxId] = info
            local sortData = {}
            sortData.boxId = v.boxId
            sortData.createTime = v.createTime
            sortData.rewardId = info.rewardId
            if not info.rewardId then
                index = index + 1
            end
            table.insert(BoxSortData, 1, sortData)

            if v.boxType == alliance_pb.EAllianceGiftBoxType_Marauder then
                isMarauder = true
            end
        end
        if isMarauder then
            flow_text.Add(lang.Get(600582))
        end
        
        if #BoxSortData > maxCount then
            for i = #BoxSortData, maxCount + 1, -1 do
                BoxSortData[i] = nil
                table.remove(BoxSortData, i)
            end
        end

        boxCanGetNums = boxCanGetNums + index
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_BOX_DATA)
    end
end

local function RefreshBaseData(msg)
    if msg.arrAllyBox and #msg.arrAllyBox > 0 then
        local index = 0
        for i, v in ipairs(msg.arrAllyBox) do
            local info = {}
            info = RefreshGiftBaseData(v)
            info = RefreshCfgData(info)
            GiftData[v.boxId] = info
            local sortData = {}
            sortData.boxId = v.boxId
            sortData.createTime = v.createTime
            sortData.rewardId = info.rewardId
            if not info.rewardId then
                index = index + 1
            end
            table.insert(GiftSortData, 1, sortData)
        end
        if #GiftSortData > maxCount then
            for i = #GiftSortData, maxCount + 1, -1 do
                GiftSortData[i] = nil
                table.remove(GiftSortData, i)
            end
        end
        giftCanGetNums = giftCanGetNums + index
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_GIFT_DATA)
    end
end

--产生联盟礼物数据
function RefreshNTF(msg)
    RefreshBoxData(msg)
    RefreshBaseData(msg)

    red_system.TriggerRed(red_const.Enum.AllianceBoxPage)
    red_system.TriggerRed(red_const.Enum.AllianceGiftPage)
end
--endregion

--region 领取 联盟礼物
--领取盟友赠礼
local function ReceiveGiftData(msg)
    local rewardData = {}
    if msg.oneKey and msg.boxReward then
        for i, v in ipairs(msg.boxReward) do
            table.insert(rewardData, v.rewardIds)
            if GiftData[v.boxId] then
                GiftData[v.boxId].rewardId = v.rewardIds
                giftCanGetNums = giftCanGetNums - 1
            end
        end
        if msg.oneKey == alliance_pb.emAllianceOneKey_Yes then
            giftCanGetNums = 0
            ShowRewardDataPanel(rewardData)
        else
            local selectData = nil
            --查询当前选中的数据
            for i, v in ipairs(GiftSortData) do
                if selectData then
                    break
                end
                for _, value in ipairs(msg.boxReward) do
                    if v.boxId == value.boxId then
                        v.rewardId = value.rewardIds  -- 更新当前的 rewardId
                        selectData = v  -- 保存选择的数据
                        table.remove(GiftSortData, i)  -- 在原表中移除当前元素
                        break
                    end
                end
            end
            --插入
            if selectData then
                local insertIndex = nil  -- 使用 nil 作为表示未找到的标识
                for i, v in ipairs(GiftSortData) do
                    if v.rewardId and v.rewardId ~= 0 then
                        insertIndex = i  -- 记录应插入的位置
                        break
                    end
                end
                if insertIndex then
                    table.insert(GiftSortData, insertIndex, selectData)  -- 插入 selectData
                else
                    table.insert(GiftSortData, selectData)  -- 直接添加到末尾
                end
            end
        end
        if giftCanGetNums < 0 then
            giftCanGetNums = 0
        end
   
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_GIFT_DATA)
    end
end
--领取宝箱
local function ReceiveBoxData(msg)
    local rewardData = {}
    if msg.oneKey and msg.boxReward then
        for i, v in ipairs(msg.boxReward) do
            table.insert(rewardData, v.rewardIds)
            if BoxData[v.boxId] then
                BoxData[v.boxId].rewardId = v.rewardIds
                boxCanGetNums = boxCanGetNums - 1
            end

        end
        if msg.oneKey == alliance_pb.emAllianceOneKey_Yes then
            boxCanGetNums = 0
            ShowRewardDataPanel(rewardData)
        else
            local selectData = nil
            for i, v in ipairs(BoxSortData) do
                if selectData then
                    break ;
                end
                for key, value in ipairs(msg.boxReward) do
                    if v.boxId == value.boxId then
                        BoxSortData[i].rewardId = value.rewardIds
                        selectData = BoxSortData[i]
                        table.remove(BoxSortData, i)
                        break ;
                    end
                end
            end
            if selectData then
                local insertIndex = 0
                for i, v in ipairs(BoxSortData) do
                    if v.rewardId and v.rewardId ~= 0 then
                        -- 追加当前的 `v` 到新表
                        insertIndex = i
                        break ;
                    end
                end
                if insertIndex ~= 0 then
                    table.insert(BoxSortData, insertIndex, selectData)
                else
                    table.insert(BoxSortData, selectData)
                end
            end
        end
        if boxCanGetNums < 0 then
            boxCanGetNums = 0
        end
      
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_BOX_DATA)
    end
end
--领取 
function ReceiveDataRsp(msg)
    if msg.giftType then
        if msg.giftType == alliance_pb.EAllianceGiftType_Loot then
            --领取战利品
            ReceiveBoxData(msg)
            
        elseif msg.giftType == alliance_pb.EAllianceGiftType_Ally then
            --领取联盟礼物
            ReceiveGiftData(msg)
        end

        local giftNum = #msg.boxReward
        local giftIDArr = {}
        local rewardIDArr = {}
        for _, v in ipairs(msg.boxReward) do

            if not giftIDArr[v.boxId] then
                giftIDArr[v.boxId] = 1
            else
                giftIDArr[v.boxId] = giftIDArr[v.boxId] + 1
            end

            if not rewardIDArr[v.rewardIds] then
                rewardIDArr[v.rewardIds] = 1
            else
                rewardIDArr[v.rewardIds] = rewardIDArr[v.rewardIds] + 1
            end
        end

        local giftIDStr = ""
        local rewardIDStr = ""
        for k, v in pairs(giftIDArr) do
            --giftIDStr = giftIDStr .. k .. "#" .. v .. ";"
            giftIDStr =string.format("%s#%s;",k,v)
        end
        for k, v in pairs(rewardIDArr) do
            --rewardIDStr = rewardIDStr .. k .. "#" .. v .. ";"
            rewardIDStr =string.format("%s#%s;",k,v)
        end

        local alliance_ui_util = require "alliance_ui_util"
        local reportMsg =
        {
            QuantityClaimed = giftNum, -- 领取数量
            GiftID = giftIDStr, --(礼物ID#数量;itemID#数量)
            GiftRewardID = rewardIDStr, --itemID#数量;itemID#数量
        }
        alliance_ui_util.EventReport("league_ReceiveLeagueGift", reportMsg)
        
    end
    if msg.giftLv and msg.giftExp then
        RefreshLevelData(msg.giftLv, msg.giftExp)
    end

    red_system.TriggerRed(red_const.Enum.AllianceGiftPage)
    red_system.TriggerRed(red_const.Enum.AllianceBoxPage)
end
--领取战利品 展示
function ShowRewardDataPanel(exhibitData)
    local rewardData = {}
    --if exhibitData and #exhibitData == 1 then
    --    local reward_mgr = require "reward_mgr"
    --    for i, v in ipairs(exhibitData) do
    --        local data = reward_mgr.GetRewardGoods(v)
    --        local flow_good = require "flow_good"
    --        flow_good.Add({ { id = data.id, num = data.num, nType = data.nType } })
    --    end
    --    return
    --end
    --for i, v in ipairs(exhibitData) do
    --    local reward_mgr = require "reward_mgr"
    --    table.insert(rewardData, reward_mgr.GetRewardGoods(v))
    --end
    --if rewardData and #rewardData > 1 then
    --    local listData = { title = "", dataList = rewardData }
    --    local showData = {}
    --    table.insert(showData, listData)
    --    local ui_reward_result = require "ui_reward_result"
    --    ui_reward_result.SetInputParam(showData, function()
    --        print("#### 111 ####")
    --    end, nil, nil, nil, false)
    --    local windowMgr = require "ui_window_mgr"
    --    windowMgr:ShowModule("ui_reward_result")
    --end
    --region 新版奖励显示
    local reward_mgr = require "reward_mgr"
    --local rewardData = {}

    
    rewardData =  reward_mgr.GetRewardGoodsMergers(exhibitData)
    --for i, v in ipairs(msg.rewardIds) do
    --    rewardData =  reward_mgr.GetRewardGoodsMergers(v,rewardData)
    --end
    if rewardData and #rewardData > 0 then
        local listData = { title = "", dataList = rewardData }
        local showData = {}
        table.insert(showData, listData)
        local ui_reward_result = require "ui_reward_result_new"
        ui_reward_result.SetInputParam(showData)
        local windowMgr = require "ui_window_mgr"
        windowMgr:ShowModule("ui_reward_result_new")
    end
    --endregion
    
end
--endregion

--region 联盟礼物经验 基础面板展示
--联盟礼物经验
local function InitGiftLevelExp()
    local cfgLevel = game_scheme:InitBattleProp_0(8026)
    local cfgExp = game_scheme:InitBattleProp_0(8027)
    if cfgExp and cfgLevel then
        if cfgExp.szParam.data and cfgLevel.szParam.data then
            local len = #cfgLevel.szParam.data;
            for i = 0, len do
                local expLevel = {}
                expLevel.level = cfgLevel.szParam.data[i]
                giftLevelMaxLevel = expLevel.level
                expLevel.exp = cfgExp.szParam.data[i]
                giftLevelExp[expLevel.level] = {}
                giftLevelExp[expLevel.level] = expLevel
            end
        end
    end
end
local function GetGiftLevelAndExp(level)
    if giftLevelMaxLevel == 1 then
        InitGiftLevelExp()
    end
    if level <= 0 then
        level = 1
    end
    if level > giftLevelMaxLevel then
        level = giftLevelMaxLevel
    end
    return giftLevelExp[level]
end
--获取经验基础数据
function GetGiftLevelData()
    local data = {}
    if type(curGiftLevel) == "number" and curGiftLevel >= 0 then
        local levelData = GetGiftLevelAndExp(curGiftLevel)
        if levelData then
            data.allianceGiftMaxExp = levelData.exp
            data.allianceGiftLV = levelData.level
        end
        data.allianceGiftExp = curGiftExp
        data.oneClickReceiveNum = boxCanGetNums
        data.giftOneClickReceiveNum = giftCanGetNums
    end
    return data
end
--endregion

--region 礼物奖励展示
--获取reward面板介绍LangId
local function GetRewardLang(index)
    local cfg = game_scheme:InitBattleProp_0(8041)
    if cfg and cfg.szParam.data then
        local len = #cfg.szParam.data
        if index <= len + 1 then
            return cfg.szParam.data[index]
        end
    end
end
-- 获取奖励概率列表 获取宝箱奖励面板数据
function GetRewardProbability(proId)
    local rewardList = {}
    local totalRate = 0;

    local cfg = game_scheme:BoxChoose_0(proId)
    if cfg and cfg.ShowType.data then
        local len = cfg.ShowType.count;
        for i = 0, len-1 do
            local index = cfg.ShowType.data[i];
            local reward = rewardList[index];
            if not reward then
                reward = {}
                reward.rate = 0
                reward.items = {}
                reward.langId = GetRewardLang(index);
            end
            local rate = cfg.arrProbability.data[i]
            reward.rate = reward.rate + rate
            totalRate = totalRate + rate
            if cfg.reward.data[i] then
                local reward_mgr = require "reward_mgr"
                local itemData = reward_mgr.GetRewardGoods(cfg.reward.data[i])
                table.insert(reward.items, itemData)
            end
            if not rewardList[index] then
                rewardList[index] = {};
            end
            rewardList[index] = reward;
        end
    end
    for i, v in ipairs(rewardList) do
        if v.rate then
            v.rate = (v.rate / totalRate * 100)
            local itemLen = util.get_len(v.items)
            local indexRate = (v.rate / itemLen)
            for key, value in pairs(v.items) do
                v.items[key].rate = indexRate
            end
        end
    end
    return rewardList
    
end
--endregion
--region 匿名状态
--设置Topic值
function SetAnonymousTopic(topicData)
    if topicData.topicKey == topic_pb.ETopicKey_Gift_Anonymous then
        anonymousSate = topicData.value
    end
end
--检测当前是否符合匿名升级条件
function CheckGifiReceiveAllState()
    if type(curGiftLevel) ~= "number" or not curGiftLevel then
        curGiftLevel = 0
    end
    return curGiftLevel >= 15;
end
--获取匿名状态
function GetAnonymousSate()
    return anonymousSate
end
--更新匿名状态
function UpdateAnonymousData(msg)
    if msg.switchOpt then
        anonymousSate = msg.switchOpt
        event.Trigger(event_alliance_define.UPDATE_ALLIANCE_ANONYMOUS_DATA)
    end
end
--endregion

--region 过期盒子
--检测过期box数据 删除 
function CheckTimeBoxData()
    local ExpiredData = {}
    -- 映射过期盒子的索引
    local expiredIndexes = {}
    -- 遍历 BoxSortData，获取过期的盒子
    for i, v in ipairs(BoxSortData) do
        local boxData = BoxData[v.boxId]
        if boxData then
            local time = boxData.expireTime - net_login_module.GetServerTime()
            if time <= 0 then
                table.insert(ExpiredData, v.boxId)
                expiredIndexes[i] = true -- 标记为过期
            end
        end
    end
    -- 从 BoxSortData 中移除过期的盒子
    for i = #BoxSortData, 1, -1 do
        -- 从后往前遍历，避免索引变化问题
        if expiredIndexes[i] then
            BoxData[BoxSortData[i].boxId] = nil  -- 同时清理 BoxData
            table.remove(BoxSortData, i)
        end
    end
end
function CheckTimeGiftData()
    local ExpiredData = {}
    -- 映射过期盒子的索引
    local expiredIndexes = {}
    -- 遍历 BoxSortData，获取过期的盒子
    for i, v in ipairs(GiftSortData) do
        local boxData = GiftData[v.boxId]
        if boxData then
            local time = boxData.expireTime - net_login_module.GetServerTime()
            if time <= 0 then
                table.insert(ExpiredData, v.boxId)
                expiredIndexes[i] = true -- 标记为过期
            end
        end
    end
    -- 从 GiftSortData 中移除过期的盒子
    for i = #GiftSortData, 1, -1 do
        -- 从后往前遍历，避免索引变化问题
        if expiredIndexes[i] then
            GiftData[GiftSortData[i].boxId] = nil  -- 同时清理 BoxData
            table.remove(GiftSortData, i)
        end
    end
end

--endregion


function GetBoxIndexData()
    return BoxSortData
end
function GetBoxInfoData(id)
    return BoxData[id]
end
function GetGiftIndexData()
    return GiftSortData
end
function GetGiftInfoData(id)
    return GiftData[id]
end

---@public 获取宝箱可领取次数
function GetBoxReceiveNum()
    return boxCanGetNums 
end

---@public 获取赠礼可领取次数
function GetGiftReceiveNum()
    return giftCanGetNums
end

--刷新联盟礼物经验
function RefreshLevelData(lv, exp)
    curGiftLevel = lv
    curGiftExp = exp
    event.Trigger(event_alliance_define.UPDATE_ALLIANCE_GIFT_BASE_DATA)
end
--获取倒计时
function GetExpireTime(time, giftRetentionTime)
    --产生时间
    local proTime = net_login_module.GetServerTime() - time;
    local expireTime = giftRetentionTime - proTime
    return time_util.GetCountDown(expireTime);
end
--TODO清除数据 上下要对应
function ClearUserData()
    curGiftLevel = 0; --当前联盟礼物等级
    curGiftExp = 0; --当前联盟礼物经验
    boxCanGetNums = 0; --可领取次数
    giftCanGetNums = 0
    BoxData = {};--宝箱奖励
    BoxSortData = {};--宝箱奖励排序
    GiftData = {};--礼包奖励
    GiftSortData = {};--礼包奖励排序
    giftLevelExp = {} --联盟礼物等级经验
    anonymousSate = 0; --匿名状态
    
    red_system.TriggerRed(red_const.Enum.AllianceGiftPage)
    red_system.TriggerRed(red_const.Enum.AllianceBoxPage)
end
event.Register(event.USER_DATA_RESET, ClearUserData)
event.Register(event_alliance_define.EXIT_ALLIANCE, ClearUserData)
net_prop_module.AddTopicsEvent(topic_pb.TOPICNAME_ALLIANCE_GIFT_TOPICDATA, OnTopicEvent)
player_mgr.AddInitEvent(prop_pb.PERSONPART_SUBJECT, OnLoginEvent)