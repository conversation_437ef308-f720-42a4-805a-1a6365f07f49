local print = print
local require = require
local pairs = pairs
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil
local typeof = typeof
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_storm_mini_map_binding"
local gw_storm_mgr = require "gw_storm_mgr"
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local GameObject = CS.UnityEngine.GameObject
local Image = CS.UnityEngine.UI.Image
local Vector2 = CS.UnityEngine.Vector2
local rectTransformUtility = CS.UnityEngine.RectTransformUtility
local math = math
local vector2 = CS.UnityEngine.Vector2
local gwSandMgr = require "gw_sand_mgr"
local game_scheme = require "game_scheme"
local event_DesertStrom_define = require "event_DesertStrom_define"
local render_tool = require "render_tool"
local log = require "log"
local event = require "event"
--region View Life
module("ui_storm_mini_map")
local ui_path = binding.UIPath
local window = nil
local UIView = {}
local effectPath = "art/effects/effects/effect_ui_desert_biangeng/prefabs/effect_ui_desert_biangeng.prefab"

UIView.widget_table = binding.WidgetTable
function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
    self.showImgPool = {}
    self.hideImgPool = {}
    self.minimapData = {}
    self:InitEffect()
    self.lastStateMap = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self:DisposeEffectRaw()
    self.VData = nil
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

function UIView:SetRemainTop(value)
    UIUtil.SetActive(self.img_Bg, value)
    UIUtil.SetActive(self.btn_battleMap, value)
end

function UIView:GetItemBySid(sid, buildType)
    if not self:IsValid() then
        return
    end
    
    if not self.showImgPool then
        self.showImgPool = {}
    end

    if not self.hideImgPool then
        self.hideImgPool = {}
    end
    
    local item
    if not self.showImgPool[sid] then
        if self.hideImgPool[1] then
            item = self.hideImgPool[1]
            item.gameObject:SetActive(true)
            table.remove(self.hideImgPool, 1)
        else
            local cloneObj = buildType and buildType == 8 and self.obj_Build_Oil or self.obj_Build
            local obj = GameObject.Instantiate(cloneObj, cloneObj.transform.parent)
            if obj then
                obj.gameObject:SetActive(true)
                item = obj:GetComponent(typeof(ScrollRectItem))
            end
        end
        item.gameObject.name = sid
        self.showImgPool[sid] = item
    end
    item = self.showImgPool[sid]
    return item
end
function UIView:DestoryImgBySid(sid)
    if not self:IsValid() then
        return
    end
    
    local item = self.showImgPool[sid]
    if item then
        item.gameObject:SetActive(false)
        table.insert(self.hideImgPool, item)
        self.showImgPool[sid] = nil
    end
end

--更新位置
function UIView:UpdateEntityPos(entity)
    if entity and entity.sid and not entity.MarchEntityInScene then
        local item = self:GetItemBySid(entity.sid, entity.cfg and entity.cfg.BuildingTypes)
        if item then
            local rtf = item:Get("rtfImg")
            if rtf then
                rtf.transform.anchoredPosition = self:GetImagePos(entity.pos)
            end

            local btnBuild = item:Get("btnBuild", true)
            if btnBuild then
                self:AddOnClick(btnBuild.onClick, function()
                    self:OnBuildClick(entity)
                end)
            end
        end
    end
end

function UIView:OnBuildClick(entity)
    gwSandMgr.JumpToTargetGrid({ x = entity.pos.x, y = entity.pos.y })
    local sandbox_ui_mgr = require "sandbox_ui_mgr"
    sandbox_ui_mgr.OnGW_CLICK_EMPTY()
end

--世界坐標转为小地图上的坐标（目前因为世界是200，UI也是200，正好不用转换）
function UIView:GetImagePos(pos)
    if pos and pos.x and pos.y then
        return pos
    else
        pos = { x = 0, y = 0 }
    end
end

--更新状态
function UIView:UpdateEntityState(entity)
    if not self:IsValid() then
        return
    end
    
    if entity and not entity.MarchEntityInScene then
        local item = self:GetItemBySid(entity.sid)
        if item then
            local img = item:Get("img")
            if img then
                if entity.GetIfBase and entity.GetIfSelf and entity:GetIfBase() and entity:GetIfSelf() then
                    --自己
                    local resCfg = game_scheme:SandMapModelResource_0(101)
                    if resCfg and resCfg.minIcon3 then
                        self:CreateSubSprite("CreateStormMiniMapAsset", img, resCfg.minIcon3, function()
                            local imgColor = self:GetColorByState(event_DesertStrom_define.Storm_Own_State.Lock)
                            img.color = imgColor
                            img:SetNativeSize()
                        end)
                    end
                else
                    local miniMap = gw_storm_mgr.GetStormCfg().GetStormMiniMapImg(entity.cfg.Building)
                    if miniMap and miniMap.img then
                        local state = event_DesertStrom_define.Storm_Own_State.Lock
                        local isOpen = gw_storm_mgr.CheckBuiildOpen(entity)
                        if isOpen then
                            state = entity:GetStormBuildStage()
                        end

                        if miniMap.img[1] then
                            self:CreateSubSprite("CreateStormMiniMapAsset", img, miniMap.img[1], function()
                                local imgColor = self:GetColorByState(state, entity)
                                img.color = imgColor
                                img:SetNativeSize()
                            end)
                        end
                        if self.lastStateMap[entity.sid] then
                            --当前状态被占领，播放特效
                            if self.lastStateMap[entity.sid] ~= state and (state == event_DesertStrom_define.Storm_Own_State.Friend or state == event_DesertStrom_define.Storm_Own_State.Enemy) then
                                self:ShowEffect(item, entity)
                            end
                        end
                        self.lastStateMap[entity.sid] = state
                    end
                end
            end
        end
    end
end

function UIView:GetColorByState(state, entity)
    local color_palette = require "color_palette"
    if entity and entity.cfg and entity.cfg.BuildingTypes == event_DesertStrom_define.STORM_BUILDING_TYPE.OilWell then
        --油井固定显示绿色
        return color_palette.HexToColor("6bca65")
    end
    local color = event_DesertStrom_define.MINIMAP_COLOR.Grey
    if state == event_DesertStrom_define.Storm_Own_State.Lock then
        color = event_DesertStrom_define.MINIMAP_COLOR.Grey
    elseif state == event_DesertStrom_define.Storm_Own_State.Alone then
        color = event_DesertStrom_define.MINIMAP_COLOR.Yellow
    elseif state == event_DesertStrom_define.Storm_Own_State.Friend then
        color = event_DesertStrom_define.MINIMAP_COLOR.Blue
    elseif state == event_DesertStrom_define.Storm_Own_State.Enemy then
        color = event_DesertStrom_define.MINIMAP_COLOR.Red
    end
    local imgColor = color_palette.HexToColor(color.img)
    return imgColor
end

-- 设置相机位置和坐标
function UIView:SetCameraPositionAndSize(cameraPos, cameraSize, level)
    if (cameraPos and self.minimapData.cPosition ~= cameraPos) then
        self.minimapData.cPosition = cameraPos
        self.rtf_Area.anchoredPosition = cameraPos
    end

    if (cameraSize and self.minimapData.cSize ~= cameraSize) then
        self.minimapData.cSize = cameraSize
        local sizeDelta = Vector2(cameraSize.w, cameraSize.h)
        self.rtf_Area.sizeDelta = sizeDelta
    end
end

function UIView:JumpToWorldGrid(screenPoint, camera)
    local rectTransform = self.rtf_Click
    local success, localPoint2 = rectTransformUtility.ScreenPointToLocalPointInRectangle(rectTransform, vector2(screenPoint.x, screenPoint.y), camera)
    if success then
        local gridX = math.floor(localPoint2.x + rectTransform.sizeDelta.x / 2 + 0.5)
        local gridY = math.floor(localPoint2.y + rectTransform.sizeDelta.y / 2 + 0.5)
        gwSandMgr.JumpToTargetGrid({ x = gridX, y = gridY })
    end
end

function UIView:ShowEffect(item, entity)
    self:InitTween(entity)
    local effect = item:Get("effect")
    if effect then
        UIUtil.SetActive(effect, false)
        UIUtil.SetActive(effect, true)
    end
    self.tweenMap[entity.sid] = util.DelayCallOnce(2, function()
        if effect then
            UIUtil.SetActive(effect, false)
        end
        self.tweenMap[entity.sid] = nil
    end)
end

function UIView:InitTween(entity)
    if self.tweenMap[entity.sid] then
        util.RemoveDelayCall(self.tweenMap[entity.sid])
        self.tweenMap[entity.sid] = nil
    end
end

function UIView:InitEffect()
    self.tweenMap = {}
end

function UIView:DisposeEffectRaw()
    for k, v in pairs(self.tweenMap) do
        util.RemoveDelayCall(v)
    end
    self.tweenMap = {}
end
--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
            window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
            window:LoadUIResource(ui_path, nil, nil, nil, nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
