-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
local sandbox_pb=require("sandbox_pb")
module('sandboxdb_pb')


F1D=F(2,"enType",".CSMsg.TDBSXPropInt.enType",1,0,2,false,nil,14,8)
F2D=F(2,"value",".CSMsg.TDBSXPropInt.value",2,1,2,false,0,3,2)
M1G=D(1,"TDBSXPropInt",".CSMsg.TDBSXPropInt",false,{},{},nil,{})
F3D=F(2,"enType",".CSMsg.TDBSXPropStr.enType",1,0,2,false,nil,14,8)
F4D=F(2,"value",".CSMsg.TDBSXPropStr.value",2,1,2,false,"",9,9)
M3G=D(1,"TDBSXPropStr",".CSMsg.TDBSXPropStr",false,{},{},nil,{})
F5D=F(2,"x",".CSMsg.TDBSXPos.x",1,0,2,false,0,5,1)
F6D=F(2,"y",".CSMsg.TDBSXPos.y",2,1,2,false,0,5,1)
M5G=D(1,"TDBSXPos",".CSMsg.TDBSXPos",false,{},{},nil,{})
F7D=F(2,"sid",".CSMsg.TDBSXEntityBase.sid",1,0,2,false,0,4,4)
F8D=F(2,"pos",".CSMsg.TDBSXEntityBase.pos",2,1,2,false,nil,11,10)
F9D=F(2,"propInt",".CSMsg.TDBSXEntityBase.propInt",3,2,3,false,{},11,10)
F10D=F(2,"propStr",".CSMsg.TDBSXEntityBase.propStr",4,3,3,false,{},11,10)
F11D=F(2,"type",".CSMsg.TDBSXEntityBase.type",5,4,2,false,nil,14,8)
F12D=F(2,"size",".CSMsg.TDBSXEntityBase.size",6,5,2,false,0,13,3)
M6G=D(1,"TDBSXEntityBase",".CSMsg.TDBSXEntityBase",false,{},{},nil,{})
F13D=F(2,"base",".CSMsg.TDBSXRoleCity.base",1,0,2,false,nil,11,10)
M8G=D(1,"TDBSXRoleCity",".CSMsg.TDBSXRoleCity",false,{},{},nil,{})
F14D=F(2,"base",".CSMsg.TDBSXNeutralCity.base",1,0,2,false,nil,11,10)
M9G=D(1,"TDBSXNeutralCity",".CSMsg.TDBSXNeutralCity",false,{},{},nil,{})
F15D=F(2,"base",".CSMsg.TDBSXFixedMonster.base",1,0,2,false,nil,11,10)
F16D=F(2,"monsterType",".CSMsg.TDBSXFixedMonster.monsterType",2,1,2,false,0,13,3)
F17D=F(2,"lineDbid",".CSMsg.TDBSXFixedMonster.lineDbid",4,2,2,false,0,13,3)
M10G=D(1,"TDBSXFixedMonster",".CSMsg.TDBSXFixedMonster",false,{},{},nil,{})
F18D=F(2,"base",".CSMsg.TDBSXResource.base",1,0,2,false,nil,11,10)
F19D=F(2,"resType",".CSMsg.TDBSXResource.resType",2,1,2,false,0,13,3)
F20D=F(2,"resCurCount",".CSMsg.TDBSXResource.resCurCount",3,2,2,false,0,13,3)
F21D=F(2,"collectBtime",".CSMsg.TDBSXResource.collectBtime",4,3,2,false,0,13,3)
F22D=F(2,"collectEtime",".CSMsg.TDBSXResource.collectEtime",5,4,2,false,0,13,3)
F23D=F(2,"collectTeamIdx",".CSMsg.TDBSXResource.collectTeamIdx",6,5,2,false,0,13,3)
M11G=D(1,"TDBSXResource",".CSMsg.TDBSXResource",false,{},{},nil,{})
F24D=F(2,"base",".CSMsg.TDBSXCommonEntity.base",1,0,2,false,nil,11,10)
M12G=D(1,"TDBSXCommonEntity",".CSMsg.TDBSXCommonEntity",false,{},{},nil,{})
F25D=F(2,"allianceId",".CSMsg.TDBSXAllianceColor.allianceId",1,0,2,false,0,13,3)
F26D=F(2,"colorIdx",".CSMsg.TDBSXAllianceColor.colorIdx",2,1,2,false,0,13,3)
M13G=D(1,"TDBSXAllianceColor",".CSMsg.TDBSXAllianceColor",false,{},{},nil,{})
F27D=F(2,"sandBoxSid",".CSMsg.TDBSXSandBox.sandBoxSid",1,0,2,false,0,5,1)
F28D=F(2,"width",".CSMsg.TDBSXSandBox.width",2,1,2,false,0,5,1)
F29D=F(2,"height",".CSMsg.TDBSXSandBox.height",3,2,2,false,0,5,1)
F30D=F(2,"roleCityList",".CSMsg.TDBSXSandBox.roleCityList",4,3,3,false,{},11,10)
F31D=F(2,"neutralCityList",".CSMsg.TDBSXSandBox.neutralCityList",5,4,3,false,{},11,10)
F32D=F(2,"fixedMonsterList",".CSMsg.TDBSXSandBox.fixedMonsterList",6,5,3,false,{},11,10)
F33D=F(2,"resourceList",".CSMsg.TDBSXSandBox.resourceList",7,6,3,false,{},11,10)
F34D=F(2,"sidoffset",".CSMsg.TDBSXSandBox.sidoffset",8,7,2,false,0,4,4)
F35D=F(2,"allianceColorList",".CSMsg.TDBSXSandBox.allianceColorList",9,8,3,false,{},11,10)
F36D=F(2,"commonEntityList",".CSMsg.TDBSXSandBox.commonEntityList",10,9,3,false,{},11,10)
M14G=D(1,"TDBSXSandBox",".CSMsg.TDBSXSandBox",false,{},{},nil,{})

F1D.enum_type=sandbox_pb.E13M
M1G.fields={F1D, F2D}
F3D.enum_type=sandbox_pb.E14M
M3G.fields={F3D, F4D}
M5G.fields={F5D, F6D}
F8D.message_type=M5G
F9D.message_type=M1G
F10D.message_type=M3G
F11D.enum_type=sandbox_pb.E8M
M6G.fields={F7D, F8D, F9D, F10D, F11D, F12D}
F13D.message_type=M6G
M8G.fields={F13D}
F14D.message_type=M6G
M9G.fields={F14D}
F15D.message_type=M6G
M10G.fields={F15D, F16D, F17D}
F18D.message_type=M6G
M11G.fields={F18D, F19D, F20D, F21D, F22D, F23D}
F24D.message_type=M6G
M12G.fields={F24D}
M13G.fields={F25D, F26D}
F30D.message_type=M8G
F31D.message_type=M9G
F32D.message_type=M10G
F33D.message_type=M11G
F35D.message_type=M13G
F36D.message_type=M12G
M14G.fields={F27D, F28D, F29D, F30D, F31D, F32D, F33D, F34D, F35D, F36D}

TDBSXAllianceColor =M(M13G)
TDBSXCommonEntity =M(M12G)
TDBSXEntityBase =M(M6G)
TDBSXFixedMonster =M(M10G)
TDBSXNeutralCity =M(M9G)
TDBSXPos =M(M5G)
TDBSXPropInt =M(M1G)
TDBSXPropStr =M(M3G)
TDBSXResource =M(M11G)
TDBSXRoleCity =M(M8G)
TDBSXSandBox =M(M14G)

