-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
local common_new_pb=require("common_new_pb")
module('socket_pb')


V1M=V(4,"CommandType_Unknow",0,0)
V2M=V(4,"CommandType_HandShake",1,1)
V3M=V(4,"CommandType_HandShakeResponse",2,2)
V4M=V(4,"CommandType_Keepalive",3,3)
V5M=V(4,"CommandType_KeepaliveResponse",4,4)
V6M=V(4,"CommandType_SendData",5,5)
V7M=V(4,"CommandType_ReportWorld",6,6)
V8M=V(4,"CommandType_GMCommand",7,7)
V9M=V(4,"CommandType_GMCommandResponse",8,8)
V10M=V(4,"CommandType_HuoDongManager",9,9)
V11M=V(4,"CommandType_PortraitVerifyResult",10,10)
V12M=V(4,"CommandType_BroadcastMsg",11,11)
V13M=V(4,"CommandType_OperationEmail",12,12)
V14M=V(4,"CommandType_OperationEmailResponse",13,13)
V15M=V(4,"CommandType_RunLuaScript",14,14)
V16M=V(4,"CommandType_UdpateActorNameResponse",15,15)
V17M=V(4,"CommandType_OperationEmailResponseMultiple",16,16)
V18M=V(4,"CommandType_DBHttpSubAction_Notity_AlbumVerify",17,17)
V19M=V(4,"CommandType_PushGameConfig",18,18)
V20M=V(4,"CommandType_PushGameConfigResponse",19,19)
V21M=V(4,"CommandType_PushGameConfigRequest",20,20)
V22M=V(4,"CommandType_OperationTitle",21,21)
V23M=V(4,"CommandType_OperationTitleResponse",22,22)
V24M=V(4,"CommandType_DeleteUserEmail",23,23)
V25M=V(4,"CommandType_OpUserGoods",24,25)
V26M=V(4,"CommandType_OpUserGoodsResponse",25,26)
E1M=E(3,"CommandType",".GPMsg.CommandType")
V27M=V(4,"DataType_Unknow",0,0)
V28M=V(4,"DataType_Chat",1,1)
V29M=V(4,"DataType_LoginXLogout",2,2)
V30M=V(4,"DataType_ClanShow",3,3)
V31M=V(4,"DataType_ReportMsg",4,4)
V32M=V(4,"DataType_ClanData",5,5)
V33M=V(4,"DataType_VoiceRoomData",6,6)
V34M=V(4,"DataType_AnchorData",7,7)
V35M=V(4,"DataType_CountryUnion",8,8)
V36M=V(4,"DataType_CountryRankings",9,9)
V37M=V(4,"DataType_GMChatData",10,10)
V38M=V(4,"DataType_GMMuteData",11,11)
V39M=V(4,"DataType_GameData",12,12)
E2M=E(3,"DataType",".GPMsg.DataType")
V40M=V(4,"Normal",0,0)
V41M=V(4,"Porn",1,100)
V42M=V(4,"Ad",2,200)
V43M=V(4,"Spam",3,300)
V44M=V(4,"Contraband",4,400)
V45M=V(4,"Politics",5,500)
V46M=V(4,"Abuse",6,600)
V47M=V(4,"Flood",7,700)
V48M=V(4,"Terrorism",8,800)
V49M=V(4,"Meaningless",9,900)
V50M=V(4,"Customized",10,1000)
E3M=E(3,"MsgLabel",".GPMsg.MsgLabel")
V51M=V(4,"eHuoDongManagerCommandType_Init",0,0)
V52M=V(4,"eHuoDongManagerCommandType_Create",1,1)
V53M=V(4,"eHuoDongManagerCommandType_Delete",2,2)
V54M=V(4,"eHuoDongManagerCommandType_Query",3,3)
V55M=V(4,"eHuoDongManagerCommandType_CreateResponse",4,4)
V56M=V(4,"eHuoDongManagerCommandType_DeleteResponse",5,5)
V57M=V(4,"eHuoDongManagerCommandType_QueryResponse",6,6)
V58M=V(4,"eHuoDongManagerCommandType_Update",7,7)
V59M=V(4,"eHuoDongManagerCommandType_UpdateResponse",8,8)
V60M=V(4,"eHuoDongManagerCommandType_CloseBriefly",9,9)
V61M=V(4,"eHuoDongManagerCommandType_CloseBrieflyResponse",10,10)
E4M=E(3,"eHuoDongManagerCommandType",".GPMsg.eHuoDongManagerCommandType")
V62M=V(4,"eHuoDongManagerTimeType_Init",0,0)
V63M=V(4,"eHuoDongManagerTimeType_Date",1,1)
V64M=V(4,"eHuoDongManagerTimeType_Day",2,2)
V65M=V(4,"eHuoDongManagerTimeType_DateAndDay",3,3)
E5M=E(3,"eHuoDongManagerTimeType",".GPMsg.eHuoDongManagerTimeType")
V66M=V(4,"eHuodongManagerStatus_Init",0,0)
V67M=V(4,"eHuodongManagerStatus_Created",1,1)
V68M=V(4,"eHuodongManagerStatus_Runing",2,2)
V69M=V(4,"eHuodongManagerStatus_Closed",3,3)
E6M=E(3,"eHuodongManagerStatus",".GPMsg.eHuodongManagerStatus")
V70M=V(4,"eHuodongManagerErrCode_NoError",0,0)
V71M=V(4,"eHuodongManagerErrCode_DataLen",1,1)
V72M=V(4,"eHuodongManagerErrCode_Execute",2,2)
E7M=E(3,"eHuodongManagerErrCode",".GPMsg.eHuodongManagerErrCode")
V73M=V(4,"eGMCommandType_Freeze",0,0)
V74M=V(4,"eGMCommandType_Mute",1,1)
V75M=V(4,"eGMCommandType_Kick",2,2)
V76M=V(4,"eGMCommandType_RemoveFreeze",3,3)
V77M=V(4,"eGMCommandType_RemoveMute",4,4)
V78M=V(4,"eGMCommandType_CancleOperMail",5,5)
V79M=V(4,"eGMCommandType_ClanNotice",6,6)
V80M=V(4,"eGMCommandType_ClanShaikh",7,7)
V81M=V(4,"eGMCommandType_RemoveClan",8,8)
V82M=V(4,"eGMCommandType_GMVoiceKick",9,9)
V83M=V(4,"eGMCommandType_GMServiceNotice",10,10)
V84M=V(4,"eGMCommandType_GMUpdPwd",11,11)
V85M=V(4,"eGMCommandType_ModifyTeamName",12,12)
V86M=V(4,"eGMCommandType_ReportUnionData",13,13)
V87M=V(4,"eGMCommandType_ModifyUnionData",14,14)
V88M=V(4,"eGMCommandType_ModifyCountryRankings",15,15)
V89M=V(4,"eGMCommandType_AddUnionData",16,16)
V90M=V(4,"eGMCommandType_ChatRecall",17,17)
V91M=V(4,"eGMCommandType_ReportCountryRankings",18,18)
V92M=V(4,"eGMCommandType_UpdateActorName",19,19)
V93M=V(4,"eGMCommandType_GMReportLog",20,20)
V94M=V(4,"eGMCommandType_GMFramePush",21,21)
V95M=V(4,"eGMCommandType_KinApprove",22,22)
V96M=V(4,"eGMCommandType_ClanNoticeStatus",23,23)
V97M=V(4,"eGMCommandType_ChatConfig",24,24)
V98M=V(4,"eGMCommandType_Questionnaire",25,25)
V99M=V(4,"eGMCommandType_NFreeze",26,26)
V100M=V(4,"eGMCommandType_QuestionNotice",27,27)
V101M=V(4,"eGMCommandType_ClanName",28,28)
V102M=V(4,"eGMCommandType_ActorHeroBack",29,29)
V103M=V(4,"eGMCommandType_ClanWX",30,30)
V104M=V(4,"eGMCommandType_CSNotify",31,31)
V105M=V(4,"eGMCommandType_MailRecall",32,32)
V106M=V(4,"eGMCommandType_RoleTransfer",33,33)
V107M=V(4,"eGMCommandType_ReportResult",34,34)
V108M=V(4,"eGMCommandType_ImageShare",35,35)
E8M=E(3,"eGMCommandType",".GPMsg.eGMCommandType")
V109M=V(4,"eGMCommandErrCode_NoError",0,0)
V110M=V(4,"eGMCommandErrCode_PublicWorld",1,1)
V111M=V(4,"eGMCommandErrCode_NoFindID",2,2)
V112M=V(4,"eGMCommandErrCode_DataLen",3,3)
V113M=V(4,"eGMCommandErrCode_ValueNum",4,4)
V114M=V(4,"eGMCommandErrCode_Execute",5,5)
V115M=V(4,"eGMCommandErrCode_TimeError",6,6)
V116M=V(4,"eGMCommandErrCode_ChannelError",7,7)
V117M=V(4,"eGMCommandErrCode_ClanNoFind",8,8)
V118M=V(4,"eGMCommandErrCode_RecallFailed",9,9)
V119M=V(4,"eGMCommandErrCode_UpdateActorNameFailed",10,10)
V120M=V(4,"eGMCommandErrCode_ClanNameDuplicate",11,11)
V121M=V(4,"eGMCommandErrCode_ActorHeroBack",12,12)
E9M=E(3,"eGMCommandErrCode",".GPMsg.eGMCommandErrCode")
V122M=V(4,"ePortraitVerifyResultCode_OK",0,0)
V123M=V(4,"ePortraitVerifyResultCode_Fail",1,1)
E10M=E(3,"ePortraitVerifyResultCode",".GPMsg.ePortraitVerifyResultCode")
V124M=V(4,"ChatFilterChannel_Unknow",0,0)
V125M=V(4,"ChatFilterChannel_Langue",1,1)
V126M=V(4,"ChatFilterChannel_World",2,2)
V127M=V(4,"ChatFilterChannel_Guild",3,3)
V128M=V(4,"ChatFilterChannel_Recruit",4,4)
V129M=V(4,"ChatFilterChannel_Private",5,5)
V130M=V(4,"ChatFilterChannel_All",6,6)
E11M=E(3,"tChatFilterChannel",".GPMsg.tChatFilterChannel")
V131M=V(4,"eGameDataCommandType_GameLoginWorld",0,0)
V132M=V(4,"eGameDataCommandType_ChatConfigReq",1,1)
V133M=V(4,"eGameDataCommandType_ClanLog",2,3)
E12M=E(3,"eGameDataCommandType",".GPMsg.eGameDataCommandType")
V134M=V(4,"GMOPUSERTYPE_DELGOODS",0,0)
V135M=V(4,"GMOPUSERTYPE_REDUCEVIPLV",1,1)
E13M=E(3,"EGMOpUserType",".GPMsg.EGMOpUserType")
F1D=F(2,"gameID",".GPMsg.TMSG_HEADER.gameID",1,0,2,false,0,13,3)
F2D=F(2,"worldID",".GPMsg.TMSG_HEADER.worldID",2,1,2,false,0,13,3)
F3D=F(2,"Command",".GPMsg.TMSG_HEADER.Command",3,2,2,false,nil,14,8)
F4D=F(2,"SubCode",".GPMsg.TMSG_HEADER.SubCode",4,3,1,false,0,13,3)
M1G=D(1,"TMSG_HEADER",".GPMsg.TMSG_HEADER",false,{},{},nil,{})
F5D=F(2,"gameID",".GPMsg.TMSG_HANDSHAKE_REQ.gameID",1,0,2,false,0,13,3)
F6D=F(2,"worldIDs",".GPMsg.TMSG_HANDSHAKE_REQ.worldIDs",2,1,3,false,{},13,3)
M3G=D(1,"TMSG_HANDSHAKE_REQ",".GPMsg.TMSG_HANDSHAKE_REQ",false,{},{},nil,{})
F7D=F(2,"gameID",".GPMsg.TMSG_HANDSHAKE_RSP.gameID",1,0,2,false,0,13,3)
F8D=F(2,"sessionIndex",".GPMsg.TMSG_HANDSHAKE_RSP.sessionIndex",2,1,2,false,0,13,3)
M4G=D(1,"TMSG_HANDSHAKE_RSP",".GPMsg.TMSG_HANDSHAKE_RSP",false,{},{},nil,{})
F9D=F(2,"timestamp",".GPMsg.TMSG_KEEPALIVE_RSP.timestamp",1,0,2,false,0,4,4)
M5G=D(1,"TMSG_KEEPALIVE_RSP",".GPMsg.TMSG_KEEPALIVE_RSP",false,{},{},nil,{})
F10D=F(2,"senderID",".GPMsg.TMSG_CHAT_REQ.senderID",1,0,2,false,0,13,3)
F11D=F(2,"senderName",".GPMsg.TMSG_CHAT_REQ.senderName",2,1,2,false,"",9,9)
F12D=F(2,"receiverID",".GPMsg.TMSG_CHAT_REQ.receiverID",3,2,2,false,0,13,3)
F13D=F(2,"receiverName",".GPMsg.TMSG_CHAT_REQ.receiverName",4,3,2,false,"",9,9)
F14D=F(2,"channel",".GPMsg.TMSG_CHAT_REQ.channel",5,4,2,false,0,13,3)
F15D=F(2,"sendTime",".GPMsg.TMSG_CHAT_REQ.sendTime",6,5,2,false,0,13,3)
F16D=F(2,"content",".GPMsg.TMSG_CHAT_REQ.content",7,6,2,false,"",9,9)
F17D=F(2,"level",".GPMsg.TMSG_CHAT_REQ.level",8,7,2,false,0,13,3)
F18D=F(2,"chatID",".GPMsg.TMSG_CHAT_REQ.chatID",9,8,2,false,0,4,4)
F19D=F(2,"worldid",".GPMsg.TMSG_CHAT_REQ.worldid",10,9,2,false,0,13,3)
F20D=F(2,"muterule",".GPMsg.TMSG_CHAT_REQ.muterule",11,10,1,false,0,13,3)
F21D=F(2,"RoleRecharge",".GPMsg.TMSG_CHAT_REQ.RoleRecharge",12,11,2,false,0,4,4)
F22D=F(2,"UserId",".GPMsg.TMSG_CHAT_REQ.UserId",13,12,2,false,0,13,3)
F23D=F(2,"vipLevel",".GPMsg.TMSG_CHAT_REQ.vipLevel",14,13,2,false,"",9,9)
F24D=F(2,"NationName",".GPMsg.TMSG_CHAT_REQ.NationName",15,14,2,false,"",9,9)
F25D=F(2,"PId",".GPMsg.TMSG_CHAT_REQ.PId",16,15,2,false,0,13,3)
F26D=F(2,"langtype",".GPMsg.TMSG_CHAT_REQ.langtype",17,16,1,false,0,5,1)
F27D=F(2,"szChatID",".GPMsg.TMSG_CHAT_REQ.szChatID",18,17,1,false,"",9,9)
F28D=F(2,"toWorldID",".GPMsg.TMSG_CHAT_REQ.toWorldID",19,18,1,false,0,5,1)
F29D=F(2,"leageID",".GPMsg.TMSG_CHAT_REQ.leageID",20,19,1,false,0,5,1)
F30D=F(2,"channelUserId",".GPMsg.TMSG_CHAT_REQ.channelUserId",21,20,1,false,"",9,9)
F31D=F(2,"leageName",".GPMsg.TMSG_CHAT_REQ.leageName",22,21,1,false,"",9,9)
F32D=F(2,"ip",".GPMsg.TMSG_CHAT_REQ.ip",23,22,2,false,"",9,9)
F33D=F(2,"senderIdStr",".GPMsg.TMSG_CHAT_REQ.senderIdStr",24,23,1,false,"",9,9)
F34D=F(2,"userIdStr",".GPMsg.TMSG_CHAT_REQ.userIdStr",25,24,1,false,"",9,9)
F35D=F(2,"receiverIdStr",".GPMsg.TMSG_CHAT_REQ.receiverIdStr",26,25,1,false,"",9,9)
F36D=F(2,"channelStr",".GPMsg.TMSG_CHAT_REQ.channelStr",27,26,1,false,"",9,9)
F37D=F(2,"filterTextType",".GPMsg.TMSG_CHAT_REQ.filterTextType",28,27,1,false,0,5,1)
F38D=F(2,"langtypeStr",".GPMsg.TMSG_CHAT_REQ.langtypeStr",29,28,1,false,"",9,9)
F39D=F(2,"leageIDStr",".GPMsg.TMSG_CHAT_REQ.leageIDStr",30,29,1,false,"",9,9)
F40D=F(2,"messageState",".GPMsg.TMSG_CHAT_REQ.messageState",31,30,1,false,"",9,9)
F41D=F(2,"json_data",".GPMsg.TMSG_CHAT_REQ.json_data",32,31,1,false,"",9,9)
M6G=D(1,"TMSG_CHAT_REQ",".GPMsg.TMSG_CHAT_REQ",false,{},{},nil,{})
F42D=F(2,"senderID",".GPMsg.TChatData_REQ.senderID",1,0,2,false,0,13,3)
F43D=F(2,"senderName",".GPMsg.TChatData_REQ.senderName",2,1,2,false,"",9,9)
F44D=F(2,"receiverID",".GPMsg.TChatData_REQ.receiverID",3,2,2,false,0,13,3)
F45D=F(2,"receiverName",".GPMsg.TChatData_REQ.receiverName",4,3,2,false,"",9,9)
F46D=F(2,"channel",".GPMsg.TChatData_REQ.channel",5,4,2,false,0,13,3)
F47D=F(2,"sendTime",".GPMsg.TChatData_REQ.sendTime",6,5,2,false,0,13,3)
F48D=F(2,"content",".GPMsg.TChatData_REQ.content",7,6,2,false,"",9,9)
F49D=F(2,"level",".GPMsg.TChatData_REQ.level",8,7,2,false,0,13,3)
F50D=F(2,"chatID",".GPMsg.TChatData_REQ.chatID",9,8,2,false,0,4,4)
F51D=F(2,"Action",".GPMsg.TChatData_REQ.Action",10,9,2,false,0,13,3)
F52D=F(2,"Label",".GPMsg.TChatData_REQ.Label",11,10,2,false,nil,14,8)
F53D=F(2,"LanguageType",".GPMsg.TChatData_REQ.LanguageType",12,11,1,false,0,13,3)
F54D=F(2,"senderIdStr",".GPMsg.TChatData_REQ.senderIdStr",13,12,1,false,"",9,9)
F55D=F(2,"userIdStr",".GPMsg.TChatData_REQ.userIdStr",14,13,1,false,"",9,9)
F56D=F(2,"muterule",".GPMsg.TChatData_REQ.muterule",15,14,1,false,0,13,3)
F57D=F(2,"RoleRecharge",".GPMsg.TChatData_REQ.RoleRecharge",16,15,2,false,0,4,4)
F58D=F(2,"vipLevel",".GPMsg.TChatData_REQ.vipLevel",17,16,2,false,"",9,9)
F59D=F(2,"NationName",".GPMsg.TChatData_REQ.NationName",18,17,2,false,"",9,9)
F60D=F(2,"PId",".GPMsg.TChatData_REQ.PId",19,18,2,false,0,13,3)
F61D=F(2,"langtype",".GPMsg.TChatData_REQ.langtype",20,19,1,false,0,5,1)
F62D=F(2,"szChatID",".GPMsg.TChatData_REQ.szChatID",21,20,1,false,"",9,9)
F63D=F(2,"toWorldID",".GPMsg.TChatData_REQ.toWorldID",22,21,1,false,0,5,1)
F64D=F(2,"leageID",".GPMsg.TChatData_REQ.leageID",23,22,1,false,0,5,1)
F65D=F(2,"channelUserId",".GPMsg.TChatData_REQ.channelUserId",24,23,1,false,"",9,9)
F66D=F(2,"leageName",".GPMsg.TChatData_REQ.leageName",25,24,1,false,"",9,9)
F67D=F(2,"ip",".GPMsg.TChatData_REQ.ip",26,25,2,false,"",9,9)
F68D=F(2,"oldContent",".GPMsg.TChatData_REQ.oldContent",27,26,1,false,"",9,9)
F69D=F(2,"receiverIdStr",".GPMsg.TChatData_REQ.receiverIdStr",28,27,1,false,"",9,9)
F70D=F(2,"channelStr",".GPMsg.TChatData_REQ.channelStr",29,28,1,false,"",9,9)
F71D=F(2,"filterTextType",".GPMsg.TChatData_REQ.filterTextType",30,29,1,false,0,5,1)
F72D=F(2,"worldid",".GPMsg.TChatData_REQ.worldid",31,30,2,false,0,13,3)
M7G=D(1,"TChatData_REQ",".GPMsg.TChatData_REQ",false,{},{},nil,{})
F73D=F(2,"senderID",".GPMsg.TMSG_CHATDATA_REQ.senderID",1,0,2,false,0,13,3)
F74D=F(2,"senderName",".GPMsg.TMSG_CHATDATA_REQ.senderName",2,1,2,false,"",9,9)
F75D=F(2,"receiverID",".GPMsg.TMSG_CHATDATA_REQ.receiverID",3,2,2,false,0,13,3)
F76D=F(2,"receiverName",".GPMsg.TMSG_CHATDATA_REQ.receiverName",4,3,2,false,"",9,9)
F77D=F(2,"chatType",".GPMsg.TMSG_CHATDATA_REQ.chatType",5,4,2,false,0,13,3)
F78D=F(2,"sendTime",".GPMsg.TMSG_CHATDATA_REQ.sendTime",6,5,2,false,0,13,3)
F79D=F(2,"content",".GPMsg.TMSG_CHATDATA_REQ.content",7,6,2,false,"",9,9)
F80D=F(2,"level",".GPMsg.TMSG_CHATDATA_REQ.level",8,7,2,false,0,13,3)
F81D=F(2,"worldid",".GPMsg.TMSG_CHATDATA_REQ.worldid",9,8,2,false,0,13,3)
F82D=F(2,"RoleRecharge",".GPMsg.TMSG_CHATDATA_REQ.RoleRecharge",10,9,2,false,0,4,4)
F83D=F(2,"UserId",".GPMsg.TMSG_CHATDATA_REQ.UserId",11,10,2,false,0,13,3)
F84D=F(2,"langtype",".GPMsg.TMSG_CHATDATA_REQ.langtype",12,11,1,false,0,5,1)
F85D=F(2,"senderIdStr",".GPMsg.TMSG_CHATDATA_REQ.senderIdStr",13,12,1,false,"",9,9)
F86D=F(2,"receiverIdStr",".GPMsg.TMSG_CHATDATA_REQ.receiverIdStr",14,13,1,false,"",9,9)
F87D=F(2,"UserIdStr",".GPMsg.TMSG_CHATDATA_REQ.UserIdStr",15,14,1,false,"",9,9)
M9G=D(1,"TMSG_CHATDATA_REQ",".GPMsg.TMSG_CHATDATA_REQ",false,{},{},nil,{})
F88D=F(2,"senderID",".GPMsg.TGMChatData_REQ.senderID",1,0,2,false,0,13,3)
F89D=F(2,"senderName",".GPMsg.TGMChatData_REQ.senderName",2,1,2,false,"",9,9)
F90D=F(2,"receiverID",".GPMsg.TGMChatData_REQ.receiverID",3,2,2,false,0,13,3)
F91D=F(2,"receiverName",".GPMsg.TGMChatData_REQ.receiverName",4,3,2,false,"",9,9)
F92D=F(2,"worldID",".GPMsg.TGMChatData_REQ.worldID",5,4,2,false,0,13,3)
F93D=F(2,"chatType",".GPMsg.TGMChatData_REQ.chatType",6,5,2,false,0,13,3)
F94D=F(2,"sendTime",".GPMsg.TGMChatData_REQ.sendTime",7,6,2,false,0,13,3)
F95D=F(2,"content",".GPMsg.TGMChatData_REQ.content",8,7,2,false,"",9,9)
F96D=F(2,"level",".GPMsg.TGMChatData_REQ.level",9,8,2,false,0,13,3)
F97D=F(2,"isEnd",".GPMsg.TGMChatData_REQ.isEnd",10,9,2,false,0,13,3)
F98D=F(2,"LanguageType",".GPMsg.TGMChatData_REQ.LanguageType",11,10,1,false,0,13,3)
F99D=F(2,"senderIdStr",".GPMsg.TGMChatData_REQ.senderIdStr",12,11,1,false,"",9,9)
F100D=F(2,"receiverIdStr",".GPMsg.TGMChatData_REQ.receiverIdStr",13,12,1,false,"",9,9)
M10G=D(1,"TGMChatData_REQ",".GPMsg.TGMChatData_REQ",false,{},{},nil,{})
F101D=F(2,"roleID",".GPMsg.TMSG_LOGINXLOGOUT_REQ.roleID",1,0,2,false,0,13,3)
F102D=F(2,"mode",".GPMsg.TMSG_LOGINXLOGOUT_REQ.mode",2,1,2,false,0,13,3)
F103D=F(2,"userID",".GPMsg.TMSG_LOGINXLOGOUT_REQ.userID",3,2,1,false,0,13,3)
F104D=F(2,"account",".GPMsg.TMSG_LOGINXLOGOUT_REQ.account",4,3,1,false,"",9,9)
F105D=F(2,"channelID",".GPMsg.TMSG_LOGINXLOGOUT_REQ.channelID",5,4,1,false,0,13,3)
F106D=F(2,"ip",".GPMsg.TMSG_LOGINXLOGOUT_REQ.ip",6,5,1,false,"",9,9)
F107D=F(2,"device",".GPMsg.TMSG_LOGINXLOGOUT_REQ.device",7,6,1,false,"",9,9)
F108D=F(2,"senderIdStr",".GPMsg.TMSG_LOGINXLOGOUT_REQ.senderIdStr",8,7,1,false,"",9,9)
F109D=F(2,"userIdStr",".GPMsg.TMSG_LOGINXLOGOUT_REQ.userIdStr",9,8,1,false,"",9,9)
F110D=F(2,"remark",".GPMsg.TMSG_LOGINXLOGOUT_REQ.remark",10,9,1,false,"",9,9)
F111D=F(2,"oaId",".GPMsg.TMSG_LOGINXLOGOUT_REQ.oaId",11,10,1,false,"",9,9)
M11G=D(1,"TMSG_LOGINXLOGOUT_REQ",".GPMsg.TMSG_LOGINXLOGOUT_REQ",false,{},{},nil,{})
F112D=F(2,"nClanID",".GPMsg.TMSG_CLANSHOW_REQ.nClanID",1,0,2,false,0,5,1)
F113D=F(2,"clanName",".GPMsg.TMSG_CLANSHOW_REQ.clanName",2,1,2,false,"",9,9)
F114D=F(2,"nDBID",".GPMsg.TMSG_CLANSHOW_REQ.nDBID",3,2,2,false,0,5,1)
F115D=F(2,"Name",".GPMsg.TMSG_CLANSHOW_REQ.Name",4,3,2,false,"",9,9)
F116D=F(2,"content",".GPMsg.TMSG_CLANSHOW_REQ.content",5,4,2,false,"",9,9)
F117D=F(2,"nClanIdStr",".GPMsg.TMSG_CLANSHOW_REQ.nClanIdStr",6,5,1,false,"",9,9)
M12G=D(1,"TMSG_CLANSHOW_REQ",".GPMsg.TMSG_CLANSHOW_REQ",false,{},{},nil,{})
F118D=F(2,"ID",".GPMsg.TMSG_COUNTRYUNION_REQ.ID",1,0,2,false,0,5,1)
F119D=F(2,"nationalUnionIDs",".GPMsg.TMSG_COUNTRYUNION_REQ.nationalUnionIDs",2,1,2,false,"",9,9)
F120D=F(2,"nationalUnionName",".GPMsg.TMSG_COUNTRYUNION_REQ.nationalUnionName",3,2,2,false,"",9,9)
F121D=F(2,"nFlag",".GPMsg.TMSG_COUNTRYUNION_REQ.nFlag",4,3,2,false,0,5,1)
F122D=F(2,"StartTime",".GPMsg.TMSG_COUNTRYUNION_REQ.StartTime",5,4,2,false,0,5,1)
F123D=F(2,"EndTime",".GPMsg.TMSG_COUNTRYUNION_REQ.EndTime",6,5,2,false,0,5,1)
F124D=F(2,"worldID",".GPMsg.TMSG_COUNTRYUNION_REQ.worldID",7,6,2,false,0,5,1)
F125D=F(2,"validlist",".GPMsg.TMSG_COUNTRYUNION_REQ.validlist",8,7,2,false,"",9,9)
F126D=F(2,"groupID",".GPMsg.TMSG_COUNTRYUNION_REQ.groupID",9,8,2,false,0,5,1)
M13G=D(1,"TMSG_COUNTRYUNION_REQ",".GPMsg.TMSG_COUNTRYUNION_REQ",false,{},{},nil,{})
F127D=F(2,"worldID",".GPMsg.TMSG_COUNTRYRANKINGS_REQ.worldID",1,0,2,false,0,5,1)
F128D=F(2,"nationalID",".GPMsg.TMSG_COUNTRYRANKINGS_REQ.nationalID",2,1,2,false,"",9,9)
F129D=F(2,"nationalName",".GPMsg.TMSG_COUNTRYRANKINGS_REQ.nationalName",3,2,2,false,"",9,9)
F130D=F(2,"nationalPower",".GPMsg.TMSG_COUNTRYRANKINGS_REQ.nationalPower",4,3,2,false,0,5,1)
F131D=F(2,"order",".GPMsg.TMSG_COUNTRYRANKINGS_REQ.order",5,4,2,false,0,5,1)
F132D=F(2,"groupID",".GPMsg.TMSG_COUNTRYRANKINGS_REQ.groupID",6,5,2,false,0,5,1)
M14G=D(1,"TMSG_COUNTRYRANKINGS_REQ",".GPMsg.TMSG_COUNTRYRANKINGS_REQ",false,{},{},nil,{})
F133D=F(2,"gameID",".GPMsg.TMSG_REPORTWORLD_REQ.gameID",1,0,2,false,0,13,3)
F134D=F(2,"worldIDs",".GPMsg.TMSG_REPORTWORLD_REQ.worldIDs",2,1,3,false,{},13,3)
M15G=D(1,"TMSG_REPORTWORLD_REQ",".GPMsg.TMSG_REPORTWORLD_REQ",false,{},{},nil,{})
F135D=F(2,"roleID",".GPMsg.TMSG_ROLEMUTEDDATA_REQ.roleID",1,0,2,false,0,5,1)
F136D=F(2,"nTime",".GPMsg.TMSG_ROLEMUTEDDATA_REQ.nTime",2,1,2,false,0,5,1)
F137D=F(2,"reason",".GPMsg.TMSG_ROLEMUTEDDATA_REQ.reason",3,2,2,false,"",9,9)
F138D=F(2,"roleName",".GPMsg.TMSG_ROLEMUTEDDATA_REQ.roleName",4,3,2,false,"",9,9)
F139D=F(2,"worldID",".GPMsg.TMSG_ROLEMUTEDDATA_REQ.worldID",5,4,2,false,0,5,1)
F140D=F(2,"startTime",".GPMsg.TMSG_ROLEMUTEDDATA_REQ.startTime",6,5,2,false,0,5,1)
F141D=F(2,"endTime",".GPMsg.TMSG_ROLEMUTEDDATA_REQ.endTime",7,6,2,false,0,5,1)
F142D=F(2,"roleIdStr",".GPMsg.TMSG_ROLEMUTEDDATA_REQ.roleIdStr",8,7,1,false,"",9,9)
M16G=D(1,"TMSG_ROLEMUTEDDATA_REQ",".GPMsg.TMSG_ROLEMUTEDDATA_REQ",false,{},{},nil,{})
F143D=F(2,"beginDate",".GPMsg.THuodongManagerSpecificDate.beginDate",1,0,2,false,0,13,3)
F144D=F(2,"endDate",".GPMsg.THuodongManagerSpecificDate.endDate",2,1,2,false,0,13,3)
F145D=F(2,"endDelayTime",".GPMsg.THuodongManagerSpecificDate.endDelayTime",3,2,1,false,0,13,3)
F146D=F(2,"originalBeginDate",".GPMsg.THuodongManagerSpecificDate.originalBeginDate",4,3,2,false,0,13,3)
M17G=D(1,"THuodongManagerSpecificDate",".GPMsg.THuodongManagerSpecificDate",false,{},{},nil,{})
F147D=F(2,"beginDays",".GPMsg.THuodongManagerRelativeDate.beginDays",1,0,2,false,0,5,1)
F148D=F(2,"beginTime",".GPMsg.THuodongManagerRelativeDate.beginTime",2,1,2,false,0,5,1)
F149D=F(2,"endDays",".GPMsg.THuodongManagerRelativeDate.endDays",3,2,2,false,0,5,1)
F150D=F(2,"endTime",".GPMsg.THuodongManagerRelativeDate.endTime",4,3,2,false,0,5,1)
F151D=F(2,"endDelayTime",".GPMsg.THuodongManagerRelativeDate.endDelayTime",5,4,1,false,0,13,3)
M18G=D(1,"THuodongManagerRelativeDate",".GPMsg.THuodongManagerRelativeDate",false,{},{},nil,{})
F152D=F(2,"nStatus",".GPMsg.THuodongManagerCloseBrieflyDate.nStatus",1,0,2,false,0,13,3)
F153D=F(2,"beginDate",".GPMsg.THuodongManagerCloseBrieflyDate.beginDate",2,1,2,false,0,13,3)
F154D=F(2,"endDate",".GPMsg.THuodongManagerCloseBrieflyDate.endDate",3,2,2,false,0,13,3)
F155D=F(2,"nInheritance",".GPMsg.THuodongManagerCloseBrieflyDate.nInheritance",4,3,2,false,0,13,3)
M19G=D(1,"THuodongManagerCloseBrieflyDate",".GPMsg.THuodongManagerCloseBrieflyDate",false,{},{},nil,{})
F156D=F(2,"nHuoDongID",".GPMsg.TMSG_HUODONGMANAGER_PACKAGE.nHuoDongID",1,0,2,false,0,13,3)
F157D=F(2,"nTemplateID",".GPMsg.TMSG_HUODONGMANAGER_PACKAGE.nTemplateID",2,1,2,false,0,13,3)
F158D=F(2,"HuoDongTitle",".GPMsg.TMSG_HUODONGMANAGER_PACKAGE.HuoDongTitle",3,2,2,false,"",9,9)
F159D=F(2,"TimeType",".GPMsg.TMSG_HUODONGMANAGER_PACKAGE.TimeType",4,3,2,false,nil,14,8)
F160D=F(2,"SpecificDate",".GPMsg.TMSG_HUODONGMANAGER_PACKAGE.SpecificDate",5,4,1,false,nil,11,10)
F161D=F(2,"RelativeDate",".GPMsg.TMSG_HUODONGMANAGER_PACKAGE.RelativeDate",6,5,1,false,nil,11,10)
F162D=F(2,"RewardConfig",".GPMsg.TMSG_HUODONGMANAGER_PACKAGE.RewardConfig",7,6,2,false,"",9,9)
F163D=F(2,"Status",".GPMsg.TMSG_HUODONGMANAGER_PACKAGE.Status",8,7,1,false,nil,14,8)
F164D=F(2,"CloseBrieflyDate",".GPMsg.TMSG_HUODONGMANAGER_PACKAGE.CloseBrieflyDate",9,8,1,false,nil,11,10)
M20G=D(1,"TMSG_HUODONGMANAGER_PACKAGE",".GPMsg.TMSG_HUODONGMANAGER_PACKAGE",false,{},{},nil,{})
F165D=F(2,"huodongInfo",".GPMsg.TMSG_HUODONGMANAGER_CREATE_REQ.huodongInfo",1,0,2,false,nil,11,10)
F166D=F(2,"relativeID",".GPMsg.TMSG_HUODONGMANAGER_CREATE_REQ.relativeID",2,1,1,false,"",9,9)
M23G=D(1,"TMSG_HUODONGMANAGER_CREATE_REQ",".GPMsg.TMSG_HUODONGMANAGER_CREATE_REQ",false,{},{},nil,{})
F167D=F(2,"errorCode",".GPMsg.TMSG_HUODONGMANAGER_CREATE_RSP.errorCode",1,0,2,false,nil,14,8)
F168D=F(2,"relativeID",".GPMsg.TMSG_HUODONGMANAGER_CREATE_RSP.relativeID",2,1,1,false,"",9,9)
F169D=F(2,"desc",".GPMsg.TMSG_HUODONGMANAGER_CREATE_RSP.desc",3,2,2,false,"",9,9)
M24G=D(1,"TMSG_HUODONGMANAGER_CREATE_RSP",".GPMsg.TMSG_HUODONGMANAGER_CREATE_RSP",false,{},{},nil,{})
F170D=F(2,"nHuoDongID",".GPMsg.TMSG_HUODONGMANAGER_DELETE_REQ.nHuoDongID",1,0,2,false,0,13,3)
F171D=F(2,"relativeID",".GPMsg.TMSG_HUODONGMANAGER_DELETE_REQ.relativeID",2,1,1,false,"",9,9)
F172D=F(2,"bForced",".GPMsg.TMSG_HUODONGMANAGER_DELETE_REQ.bForced",3,2,2,false,false,8,7)
M26G=D(1,"TMSG_HUODONGMANAGER_DELETE_REQ",".GPMsg.TMSG_HUODONGMANAGER_DELETE_REQ",false,{},{},nil,{})
F173D=F(2,"errorCode",".GPMsg.TMSG_HUODONGMANAGER_DELETE_RSP.errorCode",1,0,2,false,nil,14,8)
F174D=F(2,"relativeID",".GPMsg.TMSG_HUODONGMANAGER_DELETE_RSP.relativeID",2,1,1,false,"",9,9)
F175D=F(2,"desc",".GPMsg.TMSG_HUODONGMANAGER_DELETE_RSP.desc",3,2,2,false,"",9,9)
M27G=D(1,"TMSG_HUODONGMANAGER_DELETE_RSP",".GPMsg.TMSG_HUODONGMANAGER_DELETE_RSP",false,{},{},nil,{})
F176D=F(2,"nHuoDongID",".GPMsg.TMSG_HUODONGMANAGER_QUERY_REQ.nHuoDongID",1,0,2,false,0,13,3)
F177D=F(2,"relativeID",".GPMsg.TMSG_HUODONGMANAGER_QUERY_REQ.relativeID",2,1,1,false,"",9,9)
F178D=F(2,"openTime",".GPMsg.TMSG_HUODONGMANAGER_QUERY_REQ.openTime",3,2,1,false,0,3,2)
F179D=F(2,"nSearchEndTime",".GPMsg.TMSG_HUODONGMANAGER_QUERY_REQ.nSearchEndTime",4,3,1,false,0,3,2)
M28G=D(1,"TMSG_HUODONGMANAGER_QUERY_REQ",".GPMsg.TMSG_HUODONGMANAGER_QUERY_REQ",false,{},{},nil,{})
F180D=F(2,"data",".GPMsg.TMSG_HUODONGMANAGER_QUERY_RSP.data",1,0,3,false,{},11,10)
F181D=F(2,"relativeID",".GPMsg.TMSG_HUODONGMANAGER_QUERY_RSP.relativeID",2,1,1,false,"",9,9)
M29G=D(1,"TMSG_HUODONGMANAGER_QUERY_RSP",".GPMsg.TMSG_HUODONGMANAGER_QUERY_RSP",false,{},{},nil,{})
F182D=F(2,"huodongInfo",".GPMsg.TMSG_HUODONGMANAGER_UPDATE_REQ.huodongInfo",1,0,2,false,nil,11,10)
F183D=F(2,"relativeID",".GPMsg.TMSG_HUODONGMANAGER_UPDATE_REQ.relativeID",2,1,1,false,"",9,9)
M30G=D(1,"TMSG_HUODONGMANAGER_UPDATE_REQ",".GPMsg.TMSG_HUODONGMANAGER_UPDATE_REQ",false,{},{},nil,{})
F184D=F(2,"errorCode",".GPMsg.TMSG_HUODONGMANAGER_UPDATE_RSP.errorCode",1,0,2,false,nil,14,8)
F185D=F(2,"relativeID",".GPMsg.TMSG_HUODONGMANAGER_UPDATE_RSP.relativeID",2,1,1,false,"",9,9)
F186D=F(2,"desc",".GPMsg.TMSG_HUODONGMANAGER_UPDATE_RSP.desc",3,2,2,false,"",9,9)
M31G=D(1,"TMSG_HUODONGMANAGER_UPDATE_RSP",".GPMsg.TMSG_HUODONGMANAGER_UPDATE_RSP",false,{},{},nil,{})
F187D=F(2,"nHuoDongID",".GPMsg.TMSG_HUODONGMANAGER_CLOSEBRIEFLY_REQ.nHuoDongID",1,0,2,false,0,13,3)
F188D=F(2,"relativeID",".GPMsg.TMSG_HUODONGMANAGER_CLOSEBRIEFLY_REQ.relativeID",2,1,1,false,"",9,9)
F189D=F(2,"CloseBrieflyDate",".GPMsg.TMSG_HUODONGMANAGER_CLOSEBRIEFLY_REQ.CloseBrieflyDate",3,2,2,false,nil,11,10)
M32G=D(1,"TMSG_HUODONGMANAGER_CLOSEBRIEFLY_REQ",".GPMsg.TMSG_HUODONGMANAGER_CLOSEBRIEFLY_REQ",false,{},{},nil,{})
F190D=F(2,"errorCode",".GPMsg.TMSG_HUODONGMANAGER_CLOSEBRIEFLY_RSP.errorCode",1,0,2,false,nil,14,8)
F191D=F(2,"relativeID",".GPMsg.TMSG_HUODONGMANAGER_CLOSEBRIEFLY_RSP.relativeID",2,1,1,false,"",9,9)
F192D=F(2,"desc",".GPMsg.TMSG_HUODONGMANAGER_CLOSEBRIEFLY_RSP.desc",3,2,2,false,"",9,9)
M33G=D(1,"TMSG_HUODONGMANAGER_CLOSEBRIEFLY_RSP",".GPMsg.TMSG_HUODONGMANAGER_CLOSEBRIEFLY_RSP",false,{},{},nil,{})
F193D=F(2,"nDBID",".GPMsg.TGMFreeze.nDBID",1,0,2,false,0,5,1)
F194D=F(2,"nTime",".GPMsg.TGMFreeze.nTime",2,1,2,false,0,5,1)
F195D=F(2,"nGMID",".GPMsg.TGMFreeze.nGMID",3,2,2,false,0,5,1)
F196D=F(2,"reason",".GPMsg.TGMFreeze.reason",4,3,2,false,"",9,9)
F197D=F(2,"reasonShow",".GPMsg.TGMFreeze.reasonShow",5,4,2,false,"",9,9)
F198D=F(2,"GMname",".GPMsg.TGMFreeze.GMname",6,5,2,false,"",9,9)
F199D=F(2,"bFreeze",".GPMsg.TGMFreeze.bFreeze",7,6,2,false,false,8,7)
M34G=D(1,"TGMFreeze",".GPMsg.TGMFreeze",false,{},{},nil,{})
F200D=F(2,"freezeID",".GPMsg.TGMFreezeNew.freezeID",1,0,2,false,0,5,1)
F201D=F(2,"freezeValue",".GPMsg.TGMFreezeNew.freezeValue",2,1,2,false,"",9,9)
F202D=F(2,"timeLen",".GPMsg.TGMFreezeNew.timeLen",3,2,2,false,0,5,1)
F203D=F(2,"gMID",".GPMsg.TGMFreezeNew.gMID",4,3,2,false,0,5,1)
F204D=F(2,"reason",".GPMsg.TGMFreezeNew.reason",5,4,2,false,"",9,9)
F205D=F(2,"reasonShow",".GPMsg.TGMFreezeNew.reasonShow",6,5,2,false,"",9,9)
F206D=F(2,"gMName",".GPMsg.TGMFreezeNew.gMName",7,6,2,false,"",9,9)
M35G=D(1,"TGMFreezeNew",".GPMsg.TGMFreezeNew",false,{},{},nil,{})
F207D=F(2,"nDBID",".GPMsg.TGMKick.nDBID",1,0,2,false,0,5,1)
F208D=F(2,"ActorName",".GPMsg.TGMKick.ActorName",2,1,2,false,"",9,9)
F209D=F(2,"reason",".GPMsg.TGMKick.reason",3,2,2,false,"",9,9)
F210D=F(2,"nWorldID",".GPMsg.TGMKick.nWorldID",4,3,2,false,0,5,1)
F211D=F(2,"nDBIdStr",".GPMsg.TGMKick.nDBIdStr",5,4,1,false,"",9,9)
F212D=F(2,"stFreeze",".GPMsg.TGMKick.stFreeze",6,5,1,false,nil,11,10)
M36G=D(1,"TGMKick",".GPMsg.TGMKick",false,{},{},nil,{})
F213D=F(2,"nDBID",".GPMsg.TGMVoiceKick.nDBID",1,0,2,false,0,5,1)
F214D=F(2,"ActorName",".GPMsg.TGMVoiceKick.ActorName",2,1,2,false,"",9,9)
F215D=F(2,"reason",".GPMsg.TGMVoiceKick.reason",3,2,2,false,"",9,9)
F216D=F(2,"nWorldID",".GPMsg.TGMVoiceKick.nWorldID",4,3,2,false,0,5,1)
F217D=F(2,"RoomID",".GPMsg.TGMVoiceKick.RoomID",5,4,2,false,"",9,9)
F218D=F(2,"nDBIdStr",".GPMsg.TGMVoiceKick.nDBIdStr",6,5,1,false,"",9,9)
M37G=D(1,"TGMVoiceKick",".GPMsg.TGMVoiceKick",false,{},{},nil,{})
F219D=F(2,"nClanID",".GPMsg.TGMRemoveClan.nClanID",1,0,2,false,0,5,1)
F220D=F(2,"reason",".GPMsg.TGMRemoveClan.reason",2,1,2,false,"",9,9)
F221D=F(2,"nClanIdStr",".GPMsg.TGMRemoveClan.nClanIdStr",3,2,1,false,"",9,9)
M38G=D(1,"TGMRemoveClan",".GPMsg.TGMRemoveClan",false,{},{},nil,{})
F222D=F(2,"nDBID",".GPMsg.TGMMute.nDBID",1,0,2,false,0,5,1)
F223D=F(2,"nTime",".GPMsg.TGMMute.nTime",2,1,2,false,0,5,1)
F224D=F(2,"channel",".GPMsg.TGMMute.channel",3,2,2,false,0,5,1)
F225D=F(2,"reason",".GPMsg.TGMMute.reason",4,3,2,false,"",9,9)
F226D=F(2,"bMute",".GPMsg.TGMMute.bMute",5,4,2,false,false,8,7)
F227D=F(2,"Name",".GPMsg.TGMMute.Name",6,5,2,false,"",9,9)
F228D=F(2,"nWorldID",".GPMsg.TGMMute.nWorldID",7,6,2,false,0,5,1)
F229D=F(2,"UUID",".GPMsg.TGMMute.UUID",8,7,2,false,"",9,9)
F230D=F(2,"nDBIdStr",".GPMsg.TGMMute.nDBIdStr",9,8,1,false,"",9,9)
F231D=F(2,"channelStr",".GPMsg.TGMMute.channelStr",10,9,1,false,"",9,9)
F232D=F(2,"nMuteType",".GPMsg.TGMMute.nMuteType",11,10,1,false,0,5,1)
M39G=D(1,"TGMMute",".GPMsg.TGMMute",false,{},{},nil,{})
F233D=F(2,"strOperMailBatchID",".GPMsg.TGMOperMail.strOperMailBatchID",1,0,2,false,"",9,9)
M40G=D(1,"TGMOperMail",".GPMsg.TGMOperMail",false,{},{},nil,{})
F234D=F(2,"nClanID",".GPMsg.TGMClanNotice.nClanID",1,0,2,false,0,5,1)
F235D=F(2,"ClanNotice",".GPMsg.TGMClanNotice.ClanNotice",2,1,2,false,"",9,9)
F236D=F(2,"nHour",".GPMsg.TGMClanNotice.nHour",3,2,1,false,0,5,1)
F237D=F(2,"nClanIdStr",".GPMsg.TGMClanNotice.nClanIdStr",4,3,1,false,"",9,9)
F238D=F(2,"NType",".GPMsg.TGMClanNotice.NType",5,4,1,false,0,5,1)
M41G=D(1,"TGMClanNotice",".GPMsg.TGMClanNotice",false,{},{},nil,{})
F239D=F(2,"nClanID",".GPMsg.TGMClanShaikh.nClanID",1,0,2,false,0,5,1)
F240D=F(2,"nDBID",".GPMsg.TGMClanShaikh.nDBID",2,1,2,false,0,5,1)
F241D=F(2,"nIdentity",".GPMsg.TGMClanShaikh.nIdentity",3,2,2,false,0,5,1)
F242D=F(2,"nClanIdStr",".GPMsg.TGMClanShaikh.nClanIdStr",4,3,1,false,"",9,9)
F243D=F(2,"nDBIdStr",".GPMsg.TGMClanShaikh.nDBIdStr",5,4,1,false,"",9,9)
M42G=D(1,"TGMClanShaikh",".GPMsg.TGMClanShaikh",false,{},{},nil,{})
F244D=F(2,"nClanID",".GPMsg.TGMClanName.nClanID",1,0,2,false,0,5,1)
F245D=F(2,"nClanName",".GPMsg.TGMClanName.nClanName",2,1,2,false,"",9,9)
F246D=F(2,"NClanIDStr",".GPMsg.TGMClanName.NClanIDStr",3,2,1,false,"",9,9)
F247D=F(2,"NType",".GPMsg.TGMClanName.NType",4,3,1,false,0,5,1)
M43G=D(1,"TGMClanName",".GPMsg.TGMClanName",false,{},{},nil,{})
F248D=F(2,"nClanID",".GPMsg.TGMClanWX.nClanID",1,0,2,false,"",9,9)
F249D=F(2,"sClanWX",".GPMsg.TGMClanWX.sClanWX",2,1,2,false,"",9,9)
M44G=D(1,"TGMClanWX",".GPMsg.TGMClanWX",false,{},{},nil,{})
F250D=F(2,"nActorID",".GPMsg.TGMCSNotify.nActorID",1,0,2,false,0,3,2)
F251D=F(2,"nContent",".GPMsg.TGMCSNotify.nContent",2,1,1,false,"",9,9)
M45G=D(1,"TGMCSNotify",".GPMsg.TGMCSNotify",false,{},{},nil,{})
F252D=F(2,"commandType",".GPMsg.TMSG_GM_COMMAND_NTF.commandType",1,0,2,false,nil,14,8)
F253D=F(2,"relativeID",".GPMsg.TMSG_GM_COMMAND_NTF.relativeID",2,1,1,false,"",9,9)
F254D=F(2,"stGMKick",".GPMsg.TMSG_GM_COMMAND_NTF.stGMKick",3,2,1,false,nil,11,10)
F255D=F(2,"stGMMute",".GPMsg.TMSG_GM_COMMAND_NTF.stGMMute",4,3,1,false,nil,11,10)
F256D=F(2,"stGMOperMail",".GPMsg.TMSG_GM_COMMAND_NTF.stGMOperMail",5,4,1,false,nil,11,10)
F257D=F(2,"stGMFreeze",".GPMsg.TMSG_GM_COMMAND_NTF.stGMFreeze",6,5,1,false,nil,11,10)
F258D=F(2,"stGMClanNotice",".GPMsg.TMSG_GM_COMMAND_NTF.stGMClanNotice",7,6,1,false,nil,11,10)
F259D=F(2,"stGMClanShaikh",".GPMsg.TMSG_GM_COMMAND_NTF.stGMClanShaikh",8,7,1,false,nil,11,10)
F260D=F(2,"stTGMRemoveClan",".GPMsg.TMSG_GM_COMMAND_NTF.stTGMRemoveClan",9,8,1,false,nil,11,10)
F261D=F(2,"stTGMVoiceKick",".GPMsg.TMSG_GM_COMMAND_NTF.stTGMVoiceKick",10,9,1,false,nil,11,10)
F262D=F(2,"stTGMOperPwd",".GPMsg.TMSG_GM_COMMAND_NTF.stTGMOperPwd",11,10,1,false,nil,11,10)
F263D=F(2,"stModifyTeamName",".GPMsg.TMSG_GM_COMMAND_NTF.stModifyTeamName",12,11,1,false,nil,11,10)
F264D=F(2,"stReportData",".GPMsg.TMSG_GM_COMMAND_NTF.stReportData",13,12,1,false,nil,11,10)
F265D=F(2,"stCreateCountryUnion",".GPMsg.TMSG_GM_COMMAND_NTF.stCreateCountryUnion",14,13,1,false,nil,11,10)
F266D=F(2,"stRemoveCountryUnion",".GPMsg.TMSG_GM_COMMAND_NTF.stRemoveCountryUnion",15,14,1,false,nil,11,10)
F267D=F(2,"stModifyCountryRankings",".GPMsg.TMSG_GM_COMMAND_NTF.stModifyCountryRankings",16,15,1,false,nil,11,10)
F268D=F(2,"ChatRecall",".GPMsg.TMSG_GM_COMMAND_NTF.ChatRecall",17,16,1,false,nil,11,10)
F269D=F(2,"UpdateActorName",".GPMsg.TMSG_GM_COMMAND_NTF.UpdateActorName",18,17,1,false,nil,11,10)
F270D=F(2,"GMReportLog",".GPMsg.TMSG_GM_COMMAND_NTF.GMReportLog",19,18,1,false,nil,11,10)
F271D=F(2,"FramePush",".GPMsg.TMSG_GM_COMMAND_NTF.FramePush",20,19,1,false,nil,11,10)
F272D=F(2,"stKinApprove",".GPMsg.TMSG_GM_COMMAND_NTF.stKinApprove",21,20,1,false,nil,11,10)
F273D=F(2,"stClanNoticeStatus",".GPMsg.TMSG_GM_COMMAND_NTF.stClanNoticeStatus",22,21,1,false,nil,11,10)
F274D=F(2,"stChatConfigData",".GPMsg.TMSG_GM_COMMAND_NTF.stChatConfigData",23,22,1,false,nil,11,10)
F275D=F(2,"stQuestionnaire",".GPMsg.TMSG_GM_COMMAND_NTF.stQuestionnaire",24,23,1,false,nil,11,10)
F276D=F(2,"stQuestionNotice",".GPMsg.TMSG_GM_COMMAND_NTF.stQuestionNotice",25,24,1,false,nil,11,10)
F277D=F(2,"stGMFreezeNew",".GPMsg.TMSG_GM_COMMAND_NTF.stGMFreezeNew",26,25,1,false,nil,11,10)
F278D=F(2,"stGMClanName",".GPMsg.TMSG_GM_COMMAND_NTF.stGMClanName",27,26,1,false,nil,11,10)
F279D=F(2,"stGMAlterCard",".GPMsg.TMSG_GM_COMMAND_NTF.stGMAlterCard",28,27,1,false,nil,11,10)
F280D=F(2,"stGMClanWX",".GPMsg.TMSG_GM_COMMAND_NTF.stGMClanWX",29,28,1,false,nil,11,10)
F281D=F(2,"stGMCSNotify",".GPMsg.TMSG_GM_COMMAND_NTF.stGMCSNotify",30,29,1,false,nil,11,10)
F282D=F(2,"stMailRecall",".GPMsg.TMSG_GM_COMMAND_NTF.stMailRecall",31,30,1,false,nil,11,10)
F283D=F(2,"stRoleTransfer",".GPMsg.TMSG_GM_COMMAND_NTF.stRoleTransfer",32,31,1,false,nil,11,10)
F284D=F(2,"stReportResult",".GPMsg.TMSG_GM_COMMAND_NTF.stReportResult",33,32,1,false,nil,11,10)
F285D=F(2,"stImageShare",".GPMsg.TMSG_GM_COMMAND_NTF.stImageShare",34,33,1,false,nil,11,10)
M46G=D(1,"TMSG_GM_COMMAND_NTF",".GPMsg.TMSG_GM_COMMAND_NTF",false,{},{},nil,{})
F286D=F(2,"commandType",".GPMsg.TMSG_GM_COMMAND_RSP.commandType",1,0,2,false,nil,14,8)
F287D=F(2,"errorCode",".GPMsg.TMSG_GM_COMMAND_RSP.errorCode",2,1,2,false,nil,14,8)
F288D=F(2,"desc",".GPMsg.TMSG_GM_COMMAND_RSP.desc",3,2,2,false,"",9,9)
F289D=F(2,"relativeID",".GPMsg.TMSG_GM_COMMAND_RSP.relativeID",4,3,1,false,"",9,9)
M68G=D(1,"TMSG_GM_COMMAND_RSP",".GPMsg.TMSG_GM_COMMAND_RSP",false,{},{},nil,{})
F290D=F(2,"nPDBID",".GPMsg.TMSG_FACE_VERIFY_RESULT.nPDBID",1,0,2,false,0,5,1)
F291D=F(2,"nResult",".GPMsg.TMSG_FACE_VERIFY_RESULT.nResult",2,1,2,false,nil,14,8)
F292D=F(2,"strPortraitUrl",".GPMsg.TMSG_FACE_VERIFY_RESULT.strPortraitUrl",3,2,2,false,"",9,9)
F293D=F(2,"strErrDesc",".GPMsg.TMSG_FACE_VERIFY_RESULT.strErrDesc",4,3,1,false,"",9,9)
F294D=F(2,"relativeID",".GPMsg.TMSG_FACE_VERIFY_RESULT.relativeID",5,4,1,false,"",9,9)
M70G=D(1,"TMSG_FACE_VERIFY_RESULT",".GPMsg.TMSG_FACE_VERIFY_RESULT",false,{},{},nil,{})
F295D=F(2,"nPDBID",".GPMsg.TMSG_FACE_VERIFY_RESULT_RSP.nPDBID",1,0,2,false,0,5,1)
F296D=F(2,"nResult",".GPMsg.TMSG_FACE_VERIFY_RESULT_RSP.nResult",2,1,2,false,nil,14,8)
F297D=F(2,"strErrDesc",".GPMsg.TMSG_FACE_VERIFY_RESULT_RSP.strErrDesc",3,2,1,false,"",9,9)
F298D=F(2,"relativeID",".GPMsg.TMSG_FACE_VERIFY_RESULT_RSP.relativeID",4,3,1,false,"",9,9)
M72G=D(1,"TMSG_FACE_VERIFY_RESULT_RSP",".GPMsg.TMSG_FACE_VERIFY_RESULT_RSP",false,{},{},nil,{})
F299D=F(2,"reportedID",".GPMsg.TMSG_CHAT_REPORT_MSG_REQ.reportedID",1,0,2,false,0,13,3)
F300D=F(2,"reportedName",".GPMsg.TMSG_CHAT_REPORT_MSG_REQ.reportedName",2,1,2,false,"",9,9)
F301D=F(2,"bereportedID",".GPMsg.TMSG_CHAT_REPORT_MSG_REQ.bereportedID",3,2,2,false,0,13,3)
F302D=F(2,"bereportedName",".GPMsg.TMSG_CHAT_REPORT_MSG_REQ.bereportedName",4,3,2,false,"",9,9)
F303D=F(2,"channel",".GPMsg.TMSG_CHAT_REPORT_MSG_REQ.channel",5,4,2,false,0,13,3)
F304D=F(2,"sendTime",".GPMsg.TMSG_CHAT_REPORT_MSG_REQ.sendTime",6,5,2,false,0,13,3)
F305D=F(2,"reason",".GPMsg.TMSG_CHAT_REPORT_MSG_REQ.reason",7,6,3,false,{},14,8)
F306D=F(2,"content",".GPMsg.TMSG_CHAT_REPORT_MSG_REQ.content",8,7,1,false,"",9,9)
F307D=F(2,"level",".GPMsg.TMSG_CHAT_REPORT_MSG_REQ.level",9,8,2,false,0,13,3)
F308D=F(2,"remark",".GPMsg.TMSG_CHAT_REPORT_MSG_REQ.remark",10,9,1,false,"",9,9)
F309D=F(2,"RoleRecharge",".GPMsg.TMSG_CHAT_REPORT_MSG_REQ.RoleRecharge",11,10,2,false,0,13,3)
F310D=F(2,"UserId",".GPMsg.TMSG_CHAT_REPORT_MSG_REQ.UserId",12,11,2,false,0,13,3)
F311D=F(2,"reportedIdStr",".GPMsg.TMSG_CHAT_REPORT_MSG_REQ.reportedIdStr",13,12,1,false,"",9,9)
F312D=F(2,"bereportedIdStr",".GPMsg.TMSG_CHAT_REPORT_MSG_REQ.bereportedIdStr",14,13,1,false,"",9,9)
F313D=F(2,"userIdStr",".GPMsg.TMSG_CHAT_REPORT_MSG_REQ.userIdStr",15,14,1,false,"",9,9)
F314D=F(2,"channelStr",".GPMsg.TMSG_CHAT_REPORT_MSG_REQ.channelStr",16,15,1,false,"",9,9)
F315D=F(2,"reportedPid",".GPMsg.TMSG_CHAT_REPORT_MSG_REQ.reportedPid",17,16,1,false,"",9,9)
F316D=F(2,"bereportedPid",".GPMsg.TMSG_CHAT_REPORT_MSG_REQ.bereportedPid",18,17,1,false,"",9,9)
F317D=F(2,"bereportedWorldId",".GPMsg.TMSG_CHAT_REPORT_MSG_REQ.bereportedWorldId",19,18,1,false,0,13,3)
F318D=F(2,"source",".GPMsg.TMSG_CHAT_REPORT_MSG_REQ.source",20,19,1,false,"",9,9)
F319D=F(2,"beImageURL",".GPMsg.TMSG_CHAT_REPORT_MSG_REQ.beImageURL",21,20,1,false,"",9,9)
F320D=F(2,"beImageId",".GPMsg.TMSG_CHAT_REPORT_MSG_REQ.beImageId",22,21,1,false,"",9,9)
M73G=D(1,"TMSG_CHAT_REPORT_MSG_REQ",".GPMsg.TMSG_CHAT_REPORT_MSG_REQ",false,{},{},nil,{})
F321D=F(2,"ClanID",".GPMsg.TMSG_CLANDATA_REQ.ClanID",1,0,2,false,0,5,1)
F322D=F(2,"ClanName",".GPMsg.TMSG_CLANDATA_REQ.ClanName",2,1,2,false,"",9,9)
F323D=F(2,"nShaikhDBID",".GPMsg.TMSG_CLANDATA_REQ.nShaikhDBID",3,2,2,false,0,5,1)
F324D=F(2,"strShaikhName",".GPMsg.TMSG_CLANDATA_REQ.strShaikhName",4,3,2,false,"",9,9)
F325D=F(2,"szSlogan",".GPMsg.TMSG_CLANDATA_REQ.szSlogan",5,4,2,false,"",9,9)
F326D=F(2,"Level",".GPMsg.TMSG_CLANDATA_REQ.Level",6,5,2,false,0,5,1)
F327D=F(2,"State",".GPMsg.TMSG_CLANDATA_REQ.State",7,6,2,false,0,5,1)
F328D=F(2,"ClanIdentity",".GPMsg.TMSG_CLANDATA_REQ.ClanIdentity",8,7,2,false,0,5,1)
F329D=F(2,"szNotice",".GPMsg.TMSG_CLANDATA_REQ.szNotice",12,8,2,false,"",9,9)
F330D=F(2,"ActorID",".GPMsg.TMSG_CLANDATA_REQ.ActorID",13,9,2,false,0,5,1)
F331D=F(2,"ActorName",".GPMsg.TMSG_CLANDATA_REQ.ActorName",14,10,2,false,"",9,9)
F332D=F(2,"UserID",".GPMsg.TMSG_CLANDATA_REQ.UserID",15,11,2,false,0,5,1)
F333D=F(2,"Nation",".GPMsg.TMSG_CLANDATA_REQ.Nation",16,12,2,false,0,5,1)
F334D=F(2,"nOperateState",".GPMsg.TMSG_CLANDATA_REQ.nOperateState",17,13,2,false,0,5,1)
F335D=F(2,"nMaxCount",".GPMsg.TMSG_CLANDATA_REQ.nMaxCount",18,14,2,false,0,5,1)
F336D=F(2,"nCurCount",".GPMsg.TMSG_CLANDATA_REQ.nCurCount",19,15,2,false,0,5,1)
F337D=F(2,"nOperateActorID",".GPMsg.TMSG_CLANDATA_REQ.nOperateActorID",20,16,1,false,0,5,1)
F338D=F(2,"RoleRecharge",".GPMsg.TMSG_CLANDATA_REQ.RoleRecharge",21,17,2,false,0,13,3)
F339D=F(2,"bGMOnceKinApprove",".GPMsg.TMSG_CLANDATA_REQ.bGMOnceKinApprove",22,18,1,false,false,8,7)
F340D=F(2,"bApproveSuccess",".GPMsg.TMSG_CLANDATA_REQ.bApproveSuccess",23,19,1,false,false,8,7)
F341D=F(2,"nLuanchTime",".GPMsg.TMSG_CLANDATA_REQ.nLuanchTime",24,20,1,false,0,4,4)
F342D=F(2,"nNeedApproveDays",".GPMsg.TMSG_CLANDATA_REQ.nNeedApproveDays",25,21,1,false,0,5,1)
F343D=F(2,"clanIdStr",".GPMsg.TMSG_CLANDATA_REQ.clanIdStr",26,22,1,false,"",9,9)
F344D=F(2,"nShaikhDBIdStr",".GPMsg.TMSG_CLANDATA_REQ.nShaikhDBIdStr",27,23,1,false,"",9,9)
F345D=F(2,"actorIdStr",".GPMsg.TMSG_CLANDATA_REQ.actorIdStr",28,24,1,false,"",9,9)
F346D=F(2,"userIdStr",".GPMsg.TMSG_CLANDATA_REQ.userIdStr",29,25,1,false,"",9,9)
F347D=F(2,"powerValue",".GPMsg.TMSG_CLANDATA_REQ.powerValue",30,26,1,false,0,3,2)
F348D=F(2,"order",".GPMsg.TMSG_CLANDATA_REQ.order",31,27,1,false,0,13,3)
F349D=F(2,"nOperateActorIDStr",".GPMsg.TMSG_CLANDATA_REQ.nOperateActorIDStr",32,28,1,false,"",9,9)
F350D=F(2,"shaikhWX",".GPMsg.TMSG_CLANDATA_REQ.shaikhWX",33,29,1,false,"",9,9)
M75G=D(1,"TMSG_CLANDATA_REQ",".GPMsg.TMSG_CLANDATA_REQ",false,{},{},nil,{})
F351D=F(2,"infoPos",".GPMsg.TMSG_BROADCAST_INFORMATION.infoPos",1,0,2,false,0,13,3)
F352D=F(2,"channelList",".GPMsg.TMSG_BROADCAST_INFORMATION.channelList",2,1,2,false,"",9,9)
F353D=F(2,"broadcastMsg",".GPMsg.TMSG_BROADCAST_INFORMATION.broadcastMsg",3,2,2,false,"",9,9)
F354D=F(2,"mutiLanguageMsg",".GPMsg.TMSG_BROADCAST_INFORMATION.mutiLanguageMsg",4,3,2,false,"",9,9)
M76G=D(1,"TMSG_BROADCAST_INFORMATION",".GPMsg.TMSG_BROADCAST_INFORMATION",false,{},{},nil,{})
F355D=F(2,"DBID",".GPMsg.TMSG_NOTITY_ALBUMVERIFY_INFORMATION.DBID",1,0,2,false,0,13,3)
F356D=F(2,"worldID",".GPMsg.TMSG_NOTITY_ALBUMVERIFY_INFORMATION.worldID",2,1,2,false,0,13,3)
F357D=F(2,"count",".GPMsg.TMSG_NOTITY_ALBUMVERIFY_INFORMATION.count",3,2,2,false,0,13,3)
F358D=F(2,"imageinfo",".GPMsg.TMSG_NOTITY_ALBUMVERIFY_INFORMATION.imageinfo",4,3,3,false,{},11,10)
M77G=D(1,"TMSG_NOTITY_ALBUMVERIFY_INFORMATION",".GPMsg.TMSG_NOTITY_ALBUMVERIFY_INFORMATION",false,{},{},nil,{})
F359D=F(2,"imageId",".GPMsg.TMSG_IMAGE_INFORMATION.imageId",1,0,2,false,"",9,9)
F360D=F(2,"status",".GPMsg.TMSG_IMAGE_INFORMATION.status",2,1,2,false,0,13,3)
M78G=D(1,"TMSG_IMAGE_INFORMATION",".GPMsg.TMSG_IMAGE_INFORMATION",false,{},{},nil,{})
F361D=F(2,"goodsId",".GPMsg.TMSG_OPERATION_EMAIL_ATTACHMENT_PACKAGE.goodsId",1,0,2,false,0,13,3)
F362D=F(2,"goodsNum",".GPMsg.TMSG_OPERATION_EMAIL_ATTACHMENT_PACKAGE.goodsNum",2,1,2,false,0,13,3)
M79G=D(1,"TMSG_OPERATION_EMAIL_ATTACHMENT_PACKAGE",".GPMsg.TMSG_OPERATION_EMAIL_ATTACHMENT_PACKAGE",false,{},{},nil,{})
F363D=F(2,"emailId",".GPMsg.TMSG_OPERATION_EMAIL_REQ.emailId",1,0,2,false,"",9,9)
F364D=F(2,"channels",".GPMsg.TMSG_OPERATION_EMAIL_REQ.channels",2,1,3,false,{},13,3)
F365D=F(2,"addresseeType",".GPMsg.TMSG_OPERATION_EMAIL_REQ.addresseeType",3,2,2,false,0,13,3)
F366D=F(2,"addressees",".GPMsg.TMSG_OPERATION_EMAIL_REQ.addressees",4,3,3,false,{},4,4)
F367D=F(2,"emailTopic",".GPMsg.TMSG_OPERATION_EMAIL_REQ.emailTopic",5,4,2,false,"",9,9)
F368D=F(2,"emailText",".GPMsg.TMSG_OPERATION_EMAIL_REQ.emailText",6,5,2,false,"",9,9)
F369D=F(2,"attachments",".GPMsg.TMSG_OPERATION_EMAIL_REQ.attachments",7,6,3,false,{},11,10)
F370D=F(2,"persistDays",".GPMsg.TMSG_OPERATION_EMAIL_REQ.persistDays",8,7,2,false,0,13,3)
F371D=F(2,"creator",".GPMsg.TMSG_OPERATION_EMAIL_REQ.creator",9,8,2,false,"",9,9)
F372D=F(2,"createTime",".GPMsg.TMSG_OPERATION_EMAIL_REQ.createTime",10,9,2,false,0,4,4)
F373D=F(2,"expireTime",".GPMsg.TMSG_OPERATION_EMAIL_REQ.expireTime",11,10,1,false,0,4,4)
F374D=F(2,"mutilanguageText",".GPMsg.TMSG_OPERATION_EMAIL_REQ.mutilanguageText",12,11,1,false,"",9,9)
F375D=F(2,"convertMutilanguageText",".GPMsg.TMSG_OPERATION_EMAIL_REQ.convertMutilanguageText",13,12,1,false,"",9,9)
F376D=F(2,"condition",".GPMsg.TMSG_OPERATION_EMAIL_REQ.condition",14,13,1,false,"",9,9)
M80G=D(1,"TMSG_OPERATION_EMAIL_REQ",".GPMsg.TMSG_OPERATION_EMAIL_REQ",false,{},{},nil,{})
F377D=F(2,"emailId",".GPMsg.TMSG_OPERATION_EMAIL_RSP.emailId",1,0,1,false,"",9,9)
F378D=F(2,"actorId",".GPMsg.TMSG_OPERATION_EMAIL_RSP.actorId",2,1,2,false,0,4,4)
F379D=F(2,"actorName",".GPMsg.TMSG_OPERATION_EMAIL_RSP.actorName",3,2,2,false,"",9,9)
F380D=F(2,"status",".GPMsg.TMSG_OPERATION_EMAIL_RSP.status",4,3,2,false,0,13,3)
F381D=F(2,"relationId",".GPMsg.TMSG_OPERATION_EMAIL_RSP.relationId",5,4,1,false,"",9,9)
M81G=D(1,"TMSG_OPERATION_EMAIL_RSP",".GPMsg.TMSG_OPERATION_EMAIL_RSP",false,{},{},nil,{})
F382D=F(2,"emailId",".GPMsg.TMSG_OPERATION_EMAIL_RSP_MULTI.emailId",1,0,1,false,"",9,9)
F383D=F(2,"status",".GPMsg.TMSG_OPERATION_EMAIL_RSP_MULTI.status",2,1,2,false,0,13,3)
F384D=F(2,"actors",".GPMsg.TMSG_OPERATION_EMAIL_RSP_MULTI.actors",3,2,3,false,{},11,10)
M82G=D(1,"TMSG_OPERATION_EMAIL_RSP_MULTI",".GPMsg.TMSG_OPERATION_EMAIL_RSP_MULTI",false,{},{},nil,{})
F385D=F(2,"actorId",".GPMsg.TMSG_OPERATION_EMAIL_RSP_ACTOR.actorId",1,0,2,false,0,4,4)
F386D=F(2,"actorName",".GPMsg.TMSG_OPERATION_EMAIL_RSP_ACTOR.actorName",2,1,2,false,"",9,9)
F387D=F(2,"relationId",".GPMsg.TMSG_OPERATION_EMAIL_RSP_ACTOR.relationId",3,2,1,false,"",9,9)
M83G=D(1,"TMSG_OPERATION_EMAIL_RSP_ACTOR",".GPMsg.TMSG_OPERATION_EMAIL_RSP_ACTOR",false,{},{},nil,{})
F388D=F(2,"OperateType",".GPMsg.TMSG_MIC_INFORMATION.OperateType",1,0,2,false,0,13,3)
F389D=F(2,"OperateID",".GPMsg.TMSG_MIC_INFORMATION.OperateID",2,1,2,false,0,13,3)
F390D=F(2,"OperateWorldID",".GPMsg.TMSG_MIC_INFORMATION.OperateWorldID",3,2,2,false,0,13,3)
F391D=F(2,"KinID",".GPMsg.TMSG_MIC_INFORMATION.KinID",4,3,2,false,0,13,3)
F392D=F(2,"CountryID",".GPMsg.TMSG_MIC_INFORMATION.CountryID",5,4,2,false,0,13,3)
F393D=F(2,"OperateName",".GPMsg.TMSG_MIC_INFORMATION.OperateName",6,5,2,false,"",9,9)
F394D=F(2,"RoomName",".GPMsg.TMSG_MIC_INFORMATION.RoomName",7,6,2,false,"",9,9)
F395D=F(2,"KinName",".GPMsg.TMSG_MIC_INFORMATION.KinName",8,7,2,false,"",9,9)
F396D=F(2,"CountryName",".GPMsg.TMSG_MIC_INFORMATION.CountryName",9,8,2,false,"",9,9)
F397D=F(2,"RoleRecharge",".GPMsg.TMSG_MIC_INFORMATION.RoleRecharge",10,9,2,false,0,13,3)
M84G=D(1,"TMSG_MIC_INFORMATION",".GPMsg.TMSG_MIC_INFORMATION",false,{},{},nil,{})
F398D=F(2,"relativeID",".GPMsg.TMSG_RUNLUASCRIPT_CONTEXT.relativeID",1,0,1,false,"",9,9)
F399D=F(2,"ScriptName",".GPMsg.TMSG_RUNLUASCRIPT_CONTEXT.ScriptName",2,1,2,false,"",9,9)
F400D=F(2,"ActorID",".GPMsg.TMSG_RUNLUASCRIPT_CONTEXT.ActorID",3,2,2,false,0,13,3)
F401D=F(2,"Param",".GPMsg.TMSG_RUNLUASCRIPT_CONTEXT.Param",4,3,2,false,"",9,9)
M85G=D(1,"TMSG_RUNLUASCRIPT_CONTEXT",".GPMsg.TMSG_RUNLUASCRIPT_CONTEXT",false,{},{},nil,{})
F402D=F(2,"PDBID",".GPMsg.TGMOperPwd.PDBID",1,0,2,false,"",9,9)
M48G=D(1,"TGMOperPwd",".GPMsg.TGMOperPwd",false,{},{},nil,{})
F403D=F(2,"HuodongID",".GPMsg.TMSG_RENAME_BUDOTEAMNAME_INFO.HuodongID",1,0,2,false,0,13,3)
F404D=F(2,"LeaderID",".GPMsg.TMSG_RENAME_BUDOTEAMNAME_INFO.LeaderID",2,1,2,false,0,13,3)
F405D=F(2,"NewTeamName",".GPMsg.TMSG_RENAME_BUDOTEAMNAME_INFO.NewTeamName",3,2,2,false,"",9,9)
M49G=D(1,"TMSG_RENAME_BUDOTEAMNAME_INFO",".GPMsg.TMSG_RENAME_BUDOTEAMNAME_INFO",false,{},{},nil,{})
F406D=F(2,"AnchorID",".GPMsg.TMSG_ANCHOR_REQ.AnchorID",1,0,2,false,0,5,1)
F407D=F(2,"AnchorName",".GPMsg.TMSG_ANCHOR_REQ.AnchorName",2,1,2,false,"",9,9)
F408D=F(2,"UserID",".GPMsg.TMSG_ANCHOR_REQ.UserID",3,2,2,false,0,5,1)
F409D=F(2,"ActorID",".GPMsg.TMSG_ANCHOR_REQ.ActorID",4,3,2,false,0,5,1)
F410D=F(2,"ActorName",".GPMsg.TMSG_ANCHOR_REQ.ActorName",5,4,2,false,"",9,9)
F411D=F(2,"nOperateState",".GPMsg.TMSG_ANCHOR_REQ.nOperateState",6,5,2,false,0,5,1)
F412D=F(2,"LenTime",".GPMsg.TMSG_ANCHOR_REQ.LenTime",7,6,2,false,0,5,1)
M86G=D(1,"TMSG_ANCHOR_REQ",".GPMsg.TMSG_ANCHOR_REQ",false,{},{},nil,{})
F413D=F(2,"nWorldID",".GPMsg.TGMReportData.nWorldID",1,0,2,false,0,5,1)
M50G=D(1,"TGMReportData",".GPMsg.TGMReportData",false,{},{},nil,{})
F414D=F(2,"WorldID",".GPMsg.TMSG_COUNTRY_WORLDID.WorldID",1,0,2,false,0,13,3)
F415D=F(2,"COUNTRYID",".GPMsg.TMSG_COUNTRY_WORLDID.COUNTRYID",2,1,2,false,0,13,3)
M87G=D(1,"TMSG_COUNTRY_WORLDID",".GPMsg.TMSG_COUNTRY_WORLDID",false,{},{},nil,{})
F416D=F(2,"WorldID",".GPMsg.TMSG_COUNTRY_RANKINGS.WorldID",1,0,2,false,0,13,3)
F417D=F(2,"CountryID",".GPMsg.TMSG_COUNTRY_RANKINGS.CountryID",2,1,2,false,0,13,3)
F418D=F(2,"oldorder",".GPMsg.TMSG_COUNTRY_RANKINGS.oldorder",3,2,2,false,0,13,3)
F419D=F(2,"newOrder",".GPMsg.TMSG_COUNTRY_RANKINGS.newOrder",4,3,2,false,0,13,3)
F420D=F(2,"newRankings",".GPMsg.TMSG_COUNTRY_RANKINGS.newRankings",5,4,2,false,0,13,3)
M88G=D(1,"TMSG_COUNTRY_RANKINGS",".GPMsg.TMSG_COUNTRY_RANKINGS",false,{},{},nil,{})
F421D=F(2,"CountryData",".GPMsg.TMSG_CREATE_COUNTRY_UNION.CountryData",1,0,3,false,{},11,10)
F422D=F(2,"time",".GPMsg.TMSG_CREATE_COUNTRY_UNION.time",2,1,2,false,0,13,3)
M51G=D(1,"TMSG_CREATE_COUNTRY_UNION",".GPMsg.TMSG_CREATE_COUNTRY_UNION",false,{},{},nil,{})
F423D=F(2,"type",".GPMsg.TMSG_UPDATE_COUNTRY_UNION.type",1,0,2,false,0,13,3)
F424D=F(2,"nDBid",".GPMsg.TMSG_UPDATE_COUNTRY_UNION.nDBid",2,1,2,false,0,13,3)
F425D=F(2,"CountryData",".GPMsg.TMSG_UPDATE_COUNTRY_UNION.CountryData",3,2,3,false,{},11,10)
M52G=D(1,"TMSG_UPDATE_COUNTRY_UNION",".GPMsg.TMSG_UPDATE_COUNTRY_UNION",false,{},{},nil,{})
F426D=F(2,"CountryData",".GPMsg.TMSG_UPDATE_COUNTRY_RANKINGS.CountryData",1,0,3,false,{},11,10)
M53G=D(1,"TMSG_UPDATE_COUNTRY_RANKINGS",".GPMsg.TMSG_UPDATE_COUNTRY_RANKINGS",false,{},{},nil,{})
F427D=F(2,"szChatID",".GPMsg.TMSG_TChatRecall.szChatID",1,0,2,false,"",9,9)
F428D=F(2,"channel",".GPMsg.TMSG_TChatRecall.channel",2,1,2,false,0,5,1)
F429D=F(2,"leageID",".GPMsg.TMSG_TChatRecall.leageID",3,2,2,false,0,5,1)
F430D=F(2,"toRoleId",".GPMsg.TMSG_TChatRecall.toRoleId",4,3,1,false,0,5,1)
F431D=F(2,"toWorldid",".GPMsg.TMSG_TChatRecall.toWorldid",5,4,1,false,0,5,1)
F432D=F(2,"langtype",".GPMsg.TMSG_TChatRecall.langtype",6,5,1,false,0,5,1)
F433D=F(2,"channelStr",".GPMsg.TMSG_TChatRecall.channelStr",7,6,1,false,"",9,9)
F434D=F(2,"toRoleIdStr",".GPMsg.TMSG_TChatRecall.toRoleIdStr",8,7,1,false,"",9,9)
F435D=F(2,"senderId",".GPMsg.TMSG_TChatRecall.senderId",9,8,1,false,"",9,9)
M54G=D(1,"TMSG_TChatRecall",".GPMsg.TMSG_TChatRecall",false,{},{},nil,{})
F436D=F(2,"userId",".GPMsg.TMSG_UPDATE_ACTOR_NAME.userId",1,0,2,false,0,13,3)
F437D=F(2,"actorId",".GPMsg.TMSG_UPDATE_ACTOR_NAME.actorId",2,1,2,false,0,4,4)
F438D=F(2,"oldActorName",".GPMsg.TMSG_UPDATE_ACTOR_NAME.oldActorName",3,2,2,false,"",9,9)
F439D=F(2,"newActorName",".GPMsg.TMSG_UPDATE_ACTOR_NAME.newActorName",4,3,2,false,"",9,9)
M55G=D(1,"TMSG_UPDATE_ACTOR_NAME",".GPMsg.TMSG_UPDATE_ACTOR_NAME",false,{},{},nil,{})
F440D=F(2,"actorId",".GPMsg.TMSG_UPDATE_ACTOR_NAME_RSP.actorId",1,0,2,false,0,4,4)
F441D=F(2,"errorCode",".GPMsg.TMSG_UPDATE_ACTOR_NAME_RSP.errorCode",2,1,2,false,nil,14,8)
F442D=F(2,"desc",".GPMsg.TMSG_UPDATE_ACTOR_NAME_RSP.desc",3,2,2,false,"",9,9)
M89G=D(1,"TMSG_UPDATE_ACTOR_NAME_RSP",".GPMsg.TMSG_UPDATE_ACTOR_NAME_RSP",false,{},{},nil,{})
F443D=F(2,"worldId",".GPMsg.TMSG_GM_REPORT_LOG.worldId",1,0,2,false,0,4,4)
F444D=F(2,"actorId",".GPMsg.TMSG_GM_REPORT_LOG.actorId",2,1,2,false,0,4,4)
F445D=F(2,"logLevel",".GPMsg.TMSG_GM_REPORT_LOG.logLevel",3,2,2,false,0,5,1)
F446D=F(2,"logTime",".GPMsg.TMSG_GM_REPORT_LOG.logTime",4,3,2,false,.0,2,6)
F447D=F(2,"syslog",".GPMsg.TMSG_GM_REPORT_LOG.syslog",5,4,2,false,false,8,7)
M56G=D(1,"TMSG_GM_REPORT_LOG",".GPMsg.TMSG_GM_REPORT_LOG",false,{},{},nil,{})
F448D=F(2,"cpwindowsnum",".GPMsg.TMSG_GM_FRAMEPUSH.cpwindowsnum",1,0,2,false,0,5,1)
F449D=F(2,"actorId",".GPMsg.TMSG_GM_FRAMEPUSH.actorId",2,1,2,false,0,13,3)
F450D=F(2,"data",".GPMsg.TMSG_GM_FRAMEPUSH.data",3,2,3,false,{},11,10)
M57G=D(1,"TMSG_GM_FRAMEPUSH",".GPMsg.TMSG_GM_FRAMEPUSH",false,{},{},nil,{})
F451D=F(2,"type",".GPMsg.TMSG_CPWINDOWS_37SDKDATA.type",1,0,2,false,0,13,3)
F452D=F(2,"btnId",".GPMsg.TMSG_CPWINDOWS_37SDKDATA.btnId",2,1,2,false,"",9,9)
F453D=F(2,"endTime",".GPMsg.TMSG_CPWINDOWS_37SDKDATA.endTime",3,2,2,false,0,13,3)
F454D=F(2,"url",".GPMsg.TMSG_CPWINDOWS_37SDKDATA.url",4,3,2,false,"",9,9)
F455D=F(2,"red",".GPMsg.TMSG_CPWINDOWS_37SDKDATA.red",5,4,2,false,0,13,3)
M90G=D(1,"TMSG_CPWINDOWS_37SDKDATA",".GPMsg.TMSG_CPWINDOWS_37SDKDATA",false,{},{},nil,{})
F456D=F(2,"KinID",".GPMsg.TMSG_KIN_APPROVE.KinID",1,0,2,false,0,5,1)
F457D=F(2,"Sucess",".GPMsg.TMSG_KIN_APPROVE.Sucess",2,1,2,false,false,8,7)
F458D=F(2,"KinIDStr",".GPMsg.TMSG_KIN_APPROVE.KinIDStr",3,2,2,false,"",9,9)
M58G=D(1,"TMSG_KIN_APPROVE",".GPMsg.TMSG_KIN_APPROVE",false,{},{},nil,{})
F459D=F(2,"clanId",".GPMsg.TMSG_CLAN_NOTICESTATUS.clanId",1,0,2,false,0,5,1)
F460D=F(2,"status",".GPMsg.TMSG_CLAN_NOTICESTATUS.status",2,1,2,false,0,5,1)
M59G=D(1,"TMSG_CLAN_NOTICESTATUS",".GPMsg.TMSG_CLAN_NOTICESTATUS",false,{},{},nil,{})
F461D=F(2,"roleId",".GPMsg.TMSG_QUESTIONNAIRE.roleId",1,0,2,false,0,5,1)
F462D=F(2,"wjId",".GPMsg.TMSG_QUESTIONNAIRE.wjId",2,1,2,false,"",9,9)
M61G=D(1,"TMSG_QUESTIONNAIRE",".GPMsg.TMSG_QUESTIONNAIRE",false,{},{},nil,{})
F463D=F(2,"timestamp",".GPMsg.TMSG_QUESTIONNOTICE.timestamp",1,0,2,false,0,4,4)
M62G=D(1,"TMSG_QUESTIONNOTICE",".GPMsg.TMSG_QUESTIONNOTICE",false,{},{},nil,{})
F464D=F(2,"similarity",".GPMsg.tChatFilterConfigData.similarity",1,0,2,false,.0,2,6)
F465D=F(2,"maxCheckQueueSize",".GPMsg.tChatFilterConfigData.maxCheckQueueSize",2,1,2,false,0,5,1)
F466D=F(2,"minCheckQueueSize",".GPMsg.tChatFilterConfigData.minCheckQueueSize",3,2,2,false,0,5,1)
F467D=F(2,"checkContentLength",".GPMsg.tChatFilterConfigData.checkContentLength",4,3,2,false,0,5,1)
F468D=F(2,"channel",".GPMsg.tChatFilterConfigData.channel",5,4,2,false,nil,14,8)
M91G=D(1,"tChatFilterConfigData",".GPMsg.tChatFilterConfigData",false,{},{},nil,{})
F469D=F(2,"data",".GPMsg.TMSG_CHAT_FILTER_CONFIG_NTF.data",1,0,3,false,{},11,10)
M60G=D(1,"TMSG_CHAT_FILTER_CONFIG_NTF",".GPMsg.TMSG_CHAT_FILTER_CONFIG_NTF",false,{},{},nil,{})
F470D=F(2,"gameConfigId",".GPMsg.TMSG_PUSH_GAME_CONFIG_REQ.gameConfigId",1,0,2,false,0,4,4)
F471D=F(2,"addresseeType",".GPMsg.TMSG_PUSH_GAME_CONFIG_REQ.addresseeType",2,1,2,false,0,13,3)
F472D=F(2,"worldLimits",".GPMsg.TMSG_PUSH_GAME_CONFIG_REQ.worldLimits",3,2,3,false,{},11,10)
F473D=F(2,"channels",".GPMsg.TMSG_PUSH_GAME_CONFIG_REQ.channels",4,3,3,false,{},4,4)
F474D=F(2,"actorIds",".GPMsg.TMSG_PUSH_GAME_CONFIG_REQ.actorIds",5,4,3,false,{},4,4)
F475D=F(2,"goodsItems",".GPMsg.TMSG_PUSH_GAME_CONFIG_REQ.goodsItems",6,5,3,false,{},11,10)
F476D=F(2,"buyType",".GPMsg.TMSG_PUSH_GAME_CONFIG_REQ.buyType",7,6,2,false,0,13,3)
F477D=F(2,"displayPrice",".GPMsg.TMSG_PUSH_GAME_CONFIG_REQ.displayPrice",8,7,2,false,0,4,4)
F478D=F(2,"payIds",".GPMsg.TMSG_PUSH_GAME_CONFIG_REQ.payIds",9,8,2,false,"",9,9)
F479D=F(2,"payPrice",".GPMsg.TMSG_PUSH_GAME_CONFIG_REQ.payPrice",10,9,2,false,0,4,4)
F480D=F(2,"discount",".GPMsg.TMSG_PUSH_GAME_CONFIG_REQ.discount",11,10,2,false,.0,1,5)
F481D=F(2,"limitBuyCount",".GPMsg.TMSG_PUSH_GAME_CONFIG_REQ.limitBuyCount",12,11,2,false,0,13,3)
F482D=F(2,"duration",".GPMsg.TMSG_PUSH_GAME_CONFIG_REQ.duration",13,12,2,false,0,13,3)
F483D=F(2,"actionType",".GPMsg.TMSG_PUSH_GAME_CONFIG_REQ.actionType",14,13,2,false,0,13,3)
F484D=F(2,"startPushTime",".GPMsg.TMSG_PUSH_GAME_CONFIG_REQ.startPushTime",15,14,2,false,0,4,4)
F485D=F(2,"expireTime",".GPMsg.TMSG_PUSH_GAME_CONFIG_REQ.expireTime",16,15,2,false,0,4,4)
M93G=D(1,"TMSG_PUSH_GAME_CONFIG_REQ",".GPMsg.TMSG_PUSH_GAME_CONFIG_REQ",false,{},{},nil,{})
F486D=F(2,"limitType",".GPMsg.TMSG_PUSH_GAME_CONFIG_REQ_WORLDLIMIT.limitType",1,0,1,false,0,13,3)
F487D=F(2,"minValue",".GPMsg.TMSG_PUSH_GAME_CONFIG_REQ_WORLDLIMIT.minValue",2,1,1,false,0,4,4)
F488D=F(2,"maxValue",".GPMsg.TMSG_PUSH_GAME_CONFIG_REQ_WORLDLIMIT.maxValue",3,2,1,false,0,4,4)
M94G=D(1,"TMSG_PUSH_GAME_CONFIG_REQ_WORLDLIMIT",".GPMsg.TMSG_PUSH_GAME_CONFIG_REQ_WORLDLIMIT",false,{},{},nil,{})
F489D=F(2,"goodsId",".GPMsg.TMSG_PUSH_GAME_CONFIG_REQ_GOODS.goodsId",1,0,2,false,0,4,4)
F490D=F(2,"num",".GPMsg.TMSG_PUSH_GAME_CONFIG_REQ_GOODS.num",2,1,2,false,0,4,4)
M95G=D(1,"TMSG_PUSH_GAME_CONFIG_REQ_GOODS",".GPMsg.TMSG_PUSH_GAME_CONFIG_REQ_GOODS",false,{},{},nil,{})
F491D=F(2,"gameConfigId",".GPMsg.TMSG_PUSH_GAME_CONFIG_RSP.gameConfigId",1,0,2,false,0,4,4)
F492D=F(2,"status",".GPMsg.TMSG_PUSH_GAME_CONFIG_RSP.status",2,1,2,false,0,13,3)
F493D=F(2,"roleIDs",".GPMsg.TMSG_PUSH_GAME_CONFIG_RSP.roleIDs",3,2,3,false,{},11,10)
M96G=D(1,"TMSG_PUSH_GAME_CONFIG_RSP",".GPMsg.TMSG_PUSH_GAME_CONFIG_RSP",false,{},{},nil,{})
F494D=F(2,"actorId",".GPMsg.TMSG_PUSH_GAME_CONFIG_RSP_ACTOR.actorId",1,0,2,false,0,4,4)
F495D=F(2,"extType",".GPMsg.TMSG_PUSH_GAME_CONFIG_RSP_ACTOR.extType",2,1,1,false,0,13,3)
F496D=F(2,"extValue",".GPMsg.TMSG_PUSH_GAME_CONFIG_RSP_ACTOR.extValue",3,2,1,false,"",9,9)
M97G=D(1,"TMSG_PUSH_GAME_CONFIG_RSP_ACTOR",".GPMsg.TMSG_PUSH_GAME_CONFIG_RSP_ACTOR",false,{},{},nil,{})
F497D=F(2,"commandType",".GPMsg.TMSG_PUSH_GAME_DATA_REQ.commandType",1,0,2,false,nil,14,8)
F498D=F(2,"Data",".GPMsg.TMSG_PUSH_GAME_DATA_REQ.Data",2,1,2,false,"",9,9)
M98G=D(1,"TMSG_PUSH_GAME_DATA_REQ",".GPMsg.TMSG_PUSH_GAME_DATA_REQ",false,{},{},nil,{})
F499D=F(2,"cardSid",".GPMsg.TGMAlterCardData.cardSid",1,0,1,false,0,5,1)
F500D=F(2,"cardId",".GPMsg.TGMAlterCardData.cardId",2,1,2,false,0,5,1)
F501D=F(2,"cardLv",".GPMsg.TGMAlterCardData.cardLv",3,2,2,false,0,5,1)
F502D=F(2,"starLv",".GPMsg.TGMAlterCardData.starLv",5,3,2,false,0,5,1)
F503D=F(2,"stage",".GPMsg.TGMAlterCardData.stage",6,4,2,false,0,5,1)
M100G=D(1,"TGMAlterCardData",".GPMsg.TGMAlterCardData",false,{},{},nil,{})
F504D=F(2,"roleId",".GPMsg.TGMAlterCard.roleId",1,0,2,false,0,5,1)
F505D=F(2,"data",".GPMsg.TGMAlterCard.data",2,1,3,false,{},11,10)
M63G=D(1,"TGMAlterCard",".GPMsg.TGMAlterCard",false,{},{},nil,{})
F506D=F(2,"actorId",".GPMsg.TMSG_OPREATION_TITLE_REQ.actorId",1,0,2,false,"",9,9)
F507D=F(2,"titleId",".GPMsg.TMSG_OPREATION_TITLE_REQ.titleId",2,1,2,false,0,13,3)
F508D=F(2,"titleName",".GPMsg.TMSG_OPREATION_TITLE_REQ.titleName",3,2,2,false,"",9,9)
F509D=F(2,"worldId",".GPMsg.TMSG_OPREATION_TITLE_REQ.worldId",4,3,2,false,0,13,3)
F510D=F(2,"type",".GPMsg.TMSG_OPREATION_TITLE_REQ.type",5,4,2,false,0,13,3)
M101G=D(1,"TMSG_OPREATION_TITLE_REQ",".GPMsg.TMSG_OPREATION_TITLE_REQ",false,{},{},nil,{})
F511D=F(2,"actorId",".GPMsg.TMSG_OPREATION_TITLE_RSP.actorId",1,0,2,false,"",9,9)
F512D=F(2,"titleId",".GPMsg.TMSG_OPREATION_TITLE_RSP.titleId",2,1,2,false,0,13,3)
F513D=F(2,"status",".GPMsg.TMSG_OPREATION_TITLE_RSP.status",3,2,2,false,0,13,3)
F514D=F(2,"type",".GPMsg.TMSG_OPREATION_TITLE_RSP.type",4,3,2,false,0,13,3)
M102G=D(1,"TMSG_OPREATION_TITLE_RSP",".GPMsg.TMSG_OPREATION_TITLE_RSP",false,{},{},nil,{})
F515D=F(2,"srcRoleId",".GPMsg.TRoleTransfer.srcRoleId",1,0,2,false,0,13,3)
F516D=F(2,"destRoleId",".GPMsg.TRoleTransfer.destRoleId",2,1,2,false,0,13,3)
F517D=F(2,"destUserID",".GPMsg.TRoleTransfer.destUserID",3,2,2,false,0,13,3)
M65G=D(1,"TRoleTransfer",".GPMsg.TRoleTransfer",false,{},{},nil,{})
F518D=F(2,"actorId",".GPMsg.TUserMailReq.actorId",1,0,2,false,0,4,4)
F519D=F(2,"tabType",".GPMsg.TUserMailReq.tabType",2,1,2,false,0,5,1)
F520D=F(2,"mailIds",".GPMsg.TUserMailReq.mailIds",3,2,3,false,{},4,4)
M103G=D(1,"TUserMailReq",".GPMsg.TUserMailReq",false,{},{},nil,{})
F521D=F(2,"userReqs",".GPMsg.TMSG_DELETE_USER_MAILS_REQ.userReqs",1,0,3,false,{},11,10)
M104G=D(1,"TMSG_DELETE_USER_MAILS_REQ",".GPMsg.TMSG_DELETE_USER_MAILS_REQ",false,{},{},nil,{})
F522D=F(2,"status",".GPMsg.TMSG_DELETE_USER_MAILS_RSP.status",1,0,2,false,0,13,3)
F523D=F(2,"userReqs",".GPMsg.TMSG_DELETE_USER_MAILS_RSP.userReqs",2,1,3,false,{},11,10)
M105G=D(1,"TMSG_DELETE_USER_MAILS_RSP",".GPMsg.TMSG_DELETE_USER_MAILS_RSP",false,{},{},nil,{})
F524D=F(2,"goodsID",".GPMsg.TGMGoodsInfo.goodsID",1,0,2,false,0,5,1)
F525D=F(2,"num",".GPMsg.TGMGoodsInfo.num",2,1,2,false,0,5,1)
M106G=D(1,"TGMGoodsInfo",".GPMsg.TGMGoodsInfo",false,{},{},nil,{})
F526D=F(2,"vipLv",".GPMsg.TGMVIPInfo.vipLv",2,0,2,false,0,5,1)
M107G=D(1,"TGMVIPInfo",".GPMsg.TGMVIPInfo",false,{},{},nil,{})
F527D=F(2,"goodsList",".GPMsg.TGMOpUserGoods.goodsList",1,0,3,false,{},11,10)
M108G=D(1,"TGMOpUserGoods",".GPMsg.TGMOpUserGoods",false,{},{},nil,{})
F528D=F(2,"relativeID",".GPMsg.TMSG_OPUSERGOODS_REQ.relativeID",1,0,2,false,0,5,1)
F529D=F(2,"opType",".GPMsg.TMSG_OPUSERGOODS_REQ.opType",2,1,2,false,0,5,1)
F530D=F(2,"roleId",".GPMsg.TMSG_OPUSERGOODS_REQ.roleId",3,2,2,false,0,13,3)
F531D=F(2,"data",".GPMsg.TMSG_OPUSERGOODS_REQ.data",4,3,1,false,nil,11,10)
F532D=F(2,"vipLv",".GPMsg.TMSG_OPUSERGOODS_REQ.vipLv",5,4,1,false,0,5,1)
M109G=D(1,"TMSG_OPUSERGOODS_REQ",".GPMsg.TMSG_OPUSERGOODS_REQ",false,{},{},nil,{})
F533D=F(2,"errorCode",".GPMsg.TMSG_OPUSERGOODS_RSP.errorCode",1,0,2,false,0,5,1)
F534D=F(2,"relativeID",".GPMsg.TMSG_OPUSERGOODS_RSP.relativeID",2,1,2,false,0,5,1)
F535D=F(2,"opType",".GPMsg.TMSG_OPUSERGOODS_RSP.opType",3,2,2,false,0,5,1)
F536D=F(2,"roleId",".GPMsg.TMSG_OPUSERGOODS_RSP.roleId",4,3,2,false,0,13,3)
F537D=F(2,"data",".GPMsg.TMSG_OPUSERGOODS_RSP.data",5,4,1,false,nil,11,10)
F538D=F(2,"vipLv",".GPMsg.TMSG_OPUSERGOODS_RSP.vipLv",6,5,1,false,0,5,1)
M110G=D(1,"TMSG_OPUSERGOODS_RSP",".GPMsg.TMSG_OPUSERGOODS_RSP",false,{},{},nil,{})
F539D=F(2,"mailID",".GPMsg.TMSG_MailRecall.mailID",1,0,2,false,"",9,9)
M64G=D(1,"TMSG_MailRecall",".GPMsg.TMSG_MailRecall",false,{},{},nil,{})
F540D=F(2,"roleIds",".GPMsg.TMSG_REPORTRESULT.roleIds",1,0,2,false,"",9,9)
F541D=F(2,"resultFlag",".GPMsg.TMSG_REPORTRESULT.resultFlag",2,1,2,false,0,5,1)
F542D=F(2,"remark",".GPMsg.TMSG_REPORTRESULT.remark",3,2,2,false,"",9,9)
F543D=F(2,"imageId",".GPMsg.TMSG_REPORTRESULT.imageId",4,3,2,false,"",9,9)
F544D=F(2,"bereportedId",".GPMsg.TMSG_REPORTRESULT.bereportedId",5,4,2,false,"",9,9)
F545D=F(2,"reportedType",".GPMsg.TMSG_REPORTRESULT.reportedType",6,5,2,false,0,5,1)
M66G=D(1,"TMSG_REPORTRESULT",".GPMsg.TMSG_REPORTRESULT",false,{},{},nil,{})
F546D=F(2,"roleID",".GPMsg.TMSG_IMAGESHARE.roleID",1,0,2,false,0,3,2)
F547D=F(2,"imageUrl",".GPMsg.TMSG_IMAGESHARE.imageUrl",2,1,2,false,"",9,9)
M67G=D(1,"TMSG_IMAGESHARE",".GPMsg.TMSG_IMAGESHARE",false,{},{},nil,{})

E1M.values = {V1M,V2M,V3M,V4M,V5M,V6M,V7M,V8M,V9M,V10M,V11M,V12M,V13M,V14M,V15M,V16M,V17M,V18M,V19M,V20M,V21M,V22M,V23M,V24M,V25M,V26M}
E2M.values = {V27M,V28M,V29M,V30M,V31M,V32M,V33M,V34M,V35M,V36M,V37M,V38M,V39M}
E3M.values = {V40M,V41M,V42M,V43M,V44M,V45M,V46M,V47M,V48M,V49M,V50M}
E4M.values = {V51M,V52M,V53M,V54M,V55M,V56M,V57M,V58M,V59M,V60M,V61M}
E5M.values = {V62M,V63M,V64M,V65M}
E6M.values = {V66M,V67M,V68M,V69M}
E7M.values = {V70M,V71M,V72M}
E8M.values = {V73M,V74M,V75M,V76M,V77M,V78M,V79M,V80M,V81M,V82M,V83M,V84M,V85M,V86M,V87M,V88M,V89M,V90M,V91M,V92M,V93M,V94M,V95M,V96M,V97M,V98M,V99M,V100M,V101M,V102M,V103M,V104M,V105M,V106M,V107M,V108M}
E9M.values = {V109M,V110M,V111M,V112M,V113M,V114M,V115M,V116M,V117M,V118M,V119M,V120M,V121M}
E10M.values = {V122M,V123M}
E11M.values = {V124M,V125M,V126M,V127M,V128M,V129M,V130M}
E12M.values = {V131M,V132M,V133M}
E13M.values = {V134M,V135M}
F3D.enum_type=M2G
M1G.fields={F1D, F2D, F3D, F4D}
M3G.fields={F5D, F6D}
M4G.fields={F7D, F8D}
M5G.fields={F9D}
M6G.fields={F10D, F11D, F12D, F13D, F14D, F15D, F16D, F17D, F18D, F19D, F20D, F21D, F22D, F23D, F24D, F25D, F26D, F27D, F28D, F29D, F30D, F31D, F32D, F33D, F34D, F35D, F36D, F37D, F38D, F39D, F40D, F41D}
F52D.enum_type=M8G
M7G.fields={F42D, F43D, F44D, F45D, F46D, F47D, F48D, F49D, F50D, F51D, F52D, F53D, F54D, F55D, F56D, F57D, F58D, F59D, F60D, F61D, F62D, F63D, F64D, F65D, F66D, F67D, F68D, F69D, F70D, F71D, F72D}
M9G.fields={F73D, F74D, F75D, F76D, F77D, F78D, F79D, F80D, F81D, F82D, F83D, F84D, F85D, F86D, F87D}
M10G.fields={F88D, F89D, F90D, F91D, F92D, F93D, F94D, F95D, F96D, F97D, F98D, F99D, F100D}
M11G.fields={F101D, F102D, F103D, F104D, F105D, F106D, F107D, F108D, F109D, F110D, F111D}
M12G.fields={F112D, F113D, F114D, F115D, F116D, F117D}
M13G.fields={F118D, F119D, F120D, F121D, F122D, F123D, F124D, F125D, F126D}
M14G.fields={F127D, F128D, F129D, F130D, F131D, F132D}
M15G.fields={F133D, F134D}
M16G.fields={F135D, F136D, F137D, F138D, F139D, F140D, F141D, F142D}
M17G.fields={F143D, F144D, F145D, F146D}
M18G.fields={F147D, F148D, F149D, F150D, F151D}
M19G.fields={F152D, F153D, F154D, F155D}
F159D.enum_type=M21G
F160D.message_type=M17G
F161D.message_type=M18G
F163D.enum_type=M22G
F164D.message_type=M19G
M20G.fields={F156D, F157D, F158D, F159D, F160D, F161D, F162D, F163D, F164D}
F165D.message_type=M20G
M23G.fields={F165D, F166D}
F167D.enum_type=M25G
M24G.fields={F167D, F168D, F169D}
M26G.fields={F170D, F171D, F172D}
F173D.enum_type=M25G
M27G.fields={F173D, F174D, F175D}
M28G.fields={F176D, F177D, F178D, F179D}
F180D.message_type=M20G
M29G.fields={F180D, F181D}
F182D.message_type=M20G
M30G.fields={F182D, F183D}
F184D.enum_type=M25G
M31G.fields={F184D, F185D, F186D}
F189D.message_type=M19G
M32G.fields={F187D, F188D, F189D}
F190D.enum_type=M25G
M33G.fields={F190D, F191D, F192D}
M34G.fields={F193D, F194D, F195D, F196D, F197D, F198D, F199D}
M35G.fields={F200D, F201D, F202D, F203D, F204D, F205D, F206D}
F212D.message_type=M35G
M36G.fields={F207D, F208D, F209D, F210D, F211D, F212D}
M37G.fields={F213D, F214D, F215D, F216D, F217D, F218D}
M38G.fields={F219D, F220D, F221D}
M39G.fields={F222D, F223D, F224D, F225D, F226D, F227D, F228D, F229D, F230D, F231D, F232D}
M40G.fields={F233D}
M41G.fields={F234D, F235D, F236D, F237D, F238D}
M42G.fields={F239D, F240D, F241D, F242D, F243D}
M43G.fields={F244D, F245D, F246D, F247D}
M44G.fields={F248D, F249D}
M45G.fields={F250D, F251D}
F252D.enum_type=M47G
F254D.message_type=M36G
F255D.message_type=M39G
F256D.message_type=M40G
F257D.message_type=M34G
F258D.message_type=M41G
F259D.message_type=M42G
F260D.message_type=M38G
F261D.message_type=M37G
F262D.message_type=M48G
F263D.message_type=M49G
F264D.message_type=M50G
F265D.message_type=M51G
F266D.message_type=M52G
F267D.message_type=M53G
F268D.message_type=M54G
F269D.message_type=M55G
F270D.message_type=M56G
F271D.message_type=M57G
F272D.message_type=M58G
F273D.message_type=M59G
F274D.message_type=M60G
F275D.message_type=M61G
F276D.message_type=M62G
F277D.message_type=M35G
F278D.message_type=M43G
F279D.message_type=M63G
F280D.message_type=M44G
F281D.message_type=M45G
F282D.message_type=M64G
F283D.message_type=M65G
F284D.message_type=M66G
F285D.message_type=M67G
M46G.fields={F252D, F253D, F254D, F255D, F256D, F257D, F258D, F259D, F260D, F261D, F262D, F263D, F264D, F265D, F266D, F267D, F268D, F269D, F270D, F271D, F272D, F273D, F274D, F275D, F276D, F277D, F278D, F279D, F280D, F281D, F282D, F283D, F284D, F285D}
F286D.enum_type=M47G
F287D.enum_type=M69G
M68G.fields={F286D, F287D, F288D, F289D}
F291D.enum_type=M71G
M70G.fields={F290D, F291D, F292D, F293D, F294D}
F296D.enum_type=M71G
M72G.fields={F295D, F296D, F297D, F298D}
F305D.enum_type=common_new_pb.E11M
M73G.fields={F299D, F300D, F301D, F302D, F303D, F304D, F305D, F306D, F307D, F308D, F309D, F310D, F311D, F312D, F313D, F314D, F315D, F316D, F317D, F318D, F319D, F320D}
M75G.fields={F321D, F322D, F323D, F324D, F325D, F326D, F327D, F328D, F329D, F330D, F331D, F332D, F333D, F334D, F335D, F336D, F337D, F338D, F339D, F340D, F341D, F342D, F343D, F344D, F345D, F346D, F347D, F348D, F349D, F350D}
M76G.fields={F351D, F352D, F353D, F354D}
F358D.message_type=M78G
M77G.fields={F355D, F356D, F357D, F358D}
M78G.fields={F359D, F360D}
M79G.fields={F361D, F362D}
F369D.message_type=M79G
M80G.fields={F363D, F364D, F365D, F366D, F367D, F368D, F369D, F370D, F371D, F372D, F373D, F374D, F375D, F376D}
M81G.fields={F377D, F378D, F379D, F380D, F381D}
F384D.message_type=M83G
M82G.fields={F382D, F383D, F384D}
M83G.fields={F385D, F386D, F387D}
M84G.fields={F388D, F389D, F390D, F391D, F392D, F393D, F394D, F395D, F396D, F397D}
M85G.fields={F398D, F399D, F400D, F401D}
M48G.fields={F402D}
M49G.fields={F403D, F404D, F405D}
M86G.fields={F406D, F407D, F408D, F409D, F410D, F411D, F412D}
M50G.fields={F413D}
M87G.fields={F414D, F415D}
M88G.fields={F416D, F417D, F418D, F419D, F420D}
F421D.message_type=M87G
M51G.fields={F421D, F422D}
F425D.message_type=M87G
M52G.fields={F423D, F424D, F425D}
F426D.message_type=M88G
M53G.fields={F426D}
M54G.fields={F427D, F428D, F429D, F430D, F431D, F432D, F433D, F434D, F435D}
M55G.fields={F436D, F437D, F438D, F439D}
F441D.enum_type=M69G
M89G.fields={F440D, F441D, F442D}
M56G.fields={F443D, F444D, F445D, F446D, F447D}
F450D.message_type=M90G
M57G.fields={F448D, F449D, F450D}
M90G.fields={F451D, F452D, F453D, F454D, F455D}
M58G.fields={F456D, F457D, F458D}
M59G.fields={F459D, F460D}
M61G.fields={F461D, F462D}
M62G.fields={F463D}
F468D.enum_type=M92G
M91G.fields={F464D, F465D, F466D, F467D, F468D}
F469D.message_type=M91G
M60G.fields={F469D}
F472D.message_type=M94G
F475D.message_type=M95G
M93G.fields={F470D, F471D, F472D, F473D, F474D, F475D, F476D, F477D, F478D, F479D, F480D, F481D, F482D, F483D, F484D, F485D}
M94G.fields={F486D, F487D, F488D}
M95G.fields={F489D, F490D}
F493D.message_type=M97G
M96G.fields={F491D, F492D, F493D}
M97G.fields={F494D, F495D, F496D}
F497D.enum_type=M99G
M98G.fields={F497D, F498D}
M100G.fields={F499D, F500D, F501D, F502D, F503D}
F505D.message_type=M100G
M63G.fields={F504D, F505D}
M101G.fields={F506D, F507D, F508D, F509D, F510D}
M102G.fields={F511D, F512D, F513D, F514D}
M65G.fields={F515D, F516D, F517D}
M103G.fields={F518D, F519D, F520D}
F521D.message_type=M103G
M104G.fields={F521D}
F523D.message_type=M103G
M105G.fields={F522D, F523D}
M106G.fields={F524D, F525D}
M107G.fields={F526D}
F527D.message_type=M106G
M108G.fields={F527D}
F531D.message_type=M108G
M109G.fields={F528D, F529D, F530D, F531D, F532D}
F537D.message_type=M108G
M110G.fields={F533D, F534D, F535D, F536D, F537D, F538D}
M64G.fields={F539D}
M66G.fields={F540D, F541D, F542D, F543D, F544D, F545D}
M67G.fields={F546D, F547D}

Abuse = 600
Ad = 200
ChatFilterChannel_All = 6
ChatFilterChannel_Guild = 3
ChatFilterChannel_Langue = 1
ChatFilterChannel_Private = 5
ChatFilterChannel_Recruit = 4
ChatFilterChannel_Unknow = 0
ChatFilterChannel_World = 2
CommandType_BroadcastMsg = 11
CommandType_DBHttpSubAction_Notity_AlbumVerify = 17
CommandType_DeleteUserEmail = 23
CommandType_GMCommand = 7
CommandType_GMCommandResponse = 8
CommandType_HandShake = 1
CommandType_HandShakeResponse = 2
CommandType_HuoDongManager = 9
CommandType_Keepalive = 3
CommandType_KeepaliveResponse = 4
CommandType_OpUserGoods = 25
CommandType_OpUserGoodsResponse = 26
CommandType_OperationEmail = 12
CommandType_OperationEmailResponse = 13
CommandType_OperationEmailResponseMultiple = 16
CommandType_OperationTitle = 21
CommandType_OperationTitleResponse = 22
CommandType_PortraitVerifyResult = 10
CommandType_PushGameConfig = 18
CommandType_PushGameConfigRequest = 20
CommandType_PushGameConfigResponse = 19
CommandType_ReportWorld = 6
CommandType_RunLuaScript = 14
CommandType_SendData = 5
CommandType_UdpateActorNameResponse = 15
CommandType_Unknow = 0
Contraband = 400
Customized = 1000
DataType_AnchorData = 7
DataType_Chat = 1
DataType_ClanData = 5
DataType_ClanShow = 3
DataType_CountryRankings = 9
DataType_CountryUnion = 8
DataType_GMChatData = 10
DataType_GMMuteData = 11
DataType_GameData = 12
DataType_LoginXLogout = 2
DataType_ReportMsg = 4
DataType_Unknow = 0
DataType_VoiceRoomData = 6
Flood = 700
GMOPUSERTYPE_DELGOODS = 0
GMOPUSERTYPE_REDUCEVIPLV = 1
Meaningless = 900
Normal = 0
Politics = 500
Porn = 100
Spam = 300
TChatData_REQ =M(M7G)
TGMAlterCard =M(M63G)
TGMAlterCardData =M(M100G)
TGMCSNotify =M(M45G)
TGMChatData_REQ =M(M10G)
TGMClanName =M(M43G)
TGMClanNotice =M(M41G)
TGMClanShaikh =M(M42G)
TGMClanWX =M(M44G)
TGMFreeze =M(M34G)
TGMFreezeNew =M(M35G)
TGMGoodsInfo =M(M106G)
TGMKick =M(M36G)
TGMMute =M(M39G)
TGMOpUserGoods =M(M108G)
TGMOperMail =M(M40G)
TGMOperPwd =M(M48G)
TGMRemoveClan =M(M38G)
TGMReportData =M(M50G)
TGMVIPInfo =M(M107G)
TGMVoiceKick =M(M37G)
THuodongManagerCloseBrieflyDate =M(M19G)
THuodongManagerRelativeDate =M(M18G)
THuodongManagerSpecificDate =M(M17G)
TMSG_ANCHOR_REQ =M(M86G)
TMSG_BROADCAST_INFORMATION =M(M76G)
TMSG_CHATDATA_REQ =M(M9G)
TMSG_CHAT_FILTER_CONFIG_NTF =M(M60G)
TMSG_CHAT_REPORT_MSG_REQ =M(M73G)
TMSG_CHAT_REQ =M(M6G)
TMSG_CLANDATA_REQ =M(M75G)
TMSG_CLANSHOW_REQ =M(M12G)
TMSG_CLAN_NOTICESTATUS =M(M59G)
TMSG_COUNTRYRANKINGS_REQ =M(M14G)
TMSG_COUNTRYUNION_REQ =M(M13G)
TMSG_COUNTRY_RANKINGS =M(M88G)
TMSG_COUNTRY_WORLDID =M(M87G)
TMSG_CPWINDOWS_37SDKDATA =M(M90G)
TMSG_CREATE_COUNTRY_UNION =M(M51G)
TMSG_DELETE_USER_MAILS_REQ =M(M104G)
TMSG_DELETE_USER_MAILS_RSP =M(M105G)
TMSG_FACE_VERIFY_RESULT =M(M70G)
TMSG_FACE_VERIFY_RESULT_RSP =M(M72G)
TMSG_GM_COMMAND_NTF =M(M46G)
TMSG_GM_COMMAND_RSP =M(M68G)
TMSG_GM_FRAMEPUSH =M(M57G)
TMSG_GM_REPORT_LOG =M(M56G)
TMSG_HANDSHAKE_REQ =M(M3G)
TMSG_HANDSHAKE_RSP =M(M4G)
TMSG_HEADER =M(M1G)
TMSG_HUODONGMANAGER_CLOSEBRIEFLY_REQ =M(M32G)
TMSG_HUODONGMANAGER_CLOSEBRIEFLY_RSP =M(M33G)
TMSG_HUODONGMANAGER_CREATE_REQ =M(M23G)
TMSG_HUODONGMANAGER_CREATE_RSP =M(M24G)
TMSG_HUODONGMANAGER_DELETE_REQ =M(M26G)
TMSG_HUODONGMANAGER_DELETE_RSP =M(M27G)
TMSG_HUODONGMANAGER_PACKAGE =M(M20G)
TMSG_HUODONGMANAGER_QUERY_REQ =M(M28G)
TMSG_HUODONGMANAGER_QUERY_RSP =M(M29G)
TMSG_HUODONGMANAGER_UPDATE_REQ =M(M30G)
TMSG_HUODONGMANAGER_UPDATE_RSP =M(M31G)
TMSG_IMAGESHARE =M(M67G)
TMSG_IMAGE_INFORMATION =M(M78G)
TMSG_KEEPALIVE_RSP =M(M5G)
TMSG_KIN_APPROVE =M(M58G)
TMSG_LOGINXLOGOUT_REQ =M(M11G)
TMSG_MIC_INFORMATION =M(M84G)
TMSG_MailRecall =M(M64G)
TMSG_NOTITY_ALBUMVERIFY_INFORMATION =M(M77G)
TMSG_OPERATION_EMAIL_ATTACHMENT_PACKAGE =M(M79G)
TMSG_OPERATION_EMAIL_REQ =M(M80G)
TMSG_OPERATION_EMAIL_RSP =M(M81G)
TMSG_OPERATION_EMAIL_RSP_ACTOR =M(M83G)
TMSG_OPERATION_EMAIL_RSP_MULTI =M(M82G)
TMSG_OPREATION_TITLE_REQ =M(M101G)
TMSG_OPREATION_TITLE_RSP =M(M102G)
TMSG_OPUSERGOODS_REQ =M(M109G)
TMSG_OPUSERGOODS_RSP =M(M110G)
TMSG_PUSH_GAME_CONFIG_REQ =M(M93G)
TMSG_PUSH_GAME_CONFIG_REQ_GOODS =M(M95G)
TMSG_PUSH_GAME_CONFIG_REQ_WORLDLIMIT =M(M94G)
TMSG_PUSH_GAME_CONFIG_RSP =M(M96G)
TMSG_PUSH_GAME_CONFIG_RSP_ACTOR =M(M97G)
TMSG_PUSH_GAME_DATA_REQ =M(M98G)
TMSG_QUESTIONNAIRE =M(M61G)
TMSG_QUESTIONNOTICE =M(M62G)
TMSG_RENAME_BUDOTEAMNAME_INFO =M(M49G)
TMSG_REPORTRESULT =M(M66G)
TMSG_REPORTWORLD_REQ =M(M15G)
TMSG_ROLEMUTEDDATA_REQ =M(M16G)
TMSG_RUNLUASCRIPT_CONTEXT =M(M85G)
TMSG_TChatRecall =M(M54G)
TMSG_UPDATE_ACTOR_NAME =M(M55G)
TMSG_UPDATE_ACTOR_NAME_RSP =M(M89G)
TMSG_UPDATE_COUNTRY_RANKINGS =M(M53G)
TMSG_UPDATE_COUNTRY_UNION =M(M52G)
TRoleTransfer =M(M65G)
TUserMailReq =M(M103G)
Terrorism = 800
eGMCommandErrCode_ActorHeroBack = 12
eGMCommandErrCode_ChannelError = 7
eGMCommandErrCode_ClanNameDuplicate = 11
eGMCommandErrCode_ClanNoFind = 8
eGMCommandErrCode_DataLen = 3
eGMCommandErrCode_Execute = 5
eGMCommandErrCode_NoError = 0
eGMCommandErrCode_NoFindID = 2
eGMCommandErrCode_PublicWorld = 1
eGMCommandErrCode_RecallFailed = 9
eGMCommandErrCode_TimeError = 6
eGMCommandErrCode_UpdateActorNameFailed = 10
eGMCommandErrCode_ValueNum = 4
eGMCommandType_ActorHeroBack = 29
eGMCommandType_AddUnionData = 16
eGMCommandType_CSNotify = 31
eGMCommandType_CancleOperMail = 5
eGMCommandType_ChatConfig = 24
eGMCommandType_ChatRecall = 17
eGMCommandType_ClanName = 28
eGMCommandType_ClanNotice = 6
eGMCommandType_ClanNoticeStatus = 23
eGMCommandType_ClanShaikh = 7
eGMCommandType_ClanWX = 30
eGMCommandType_Freeze = 0
eGMCommandType_GMFramePush = 21
eGMCommandType_GMReportLog = 20
eGMCommandType_GMServiceNotice = 10
eGMCommandType_GMUpdPwd = 11
eGMCommandType_GMVoiceKick = 9
eGMCommandType_ImageShare = 35
eGMCommandType_Kick = 2
eGMCommandType_KinApprove = 22
eGMCommandType_MailRecall = 32
eGMCommandType_ModifyCountryRankings = 15
eGMCommandType_ModifyTeamName = 12
eGMCommandType_ModifyUnionData = 14
eGMCommandType_Mute = 1
eGMCommandType_NFreeze = 26
eGMCommandType_QuestionNotice = 27
eGMCommandType_Questionnaire = 25
eGMCommandType_RemoveClan = 8
eGMCommandType_RemoveFreeze = 3
eGMCommandType_RemoveMute = 4
eGMCommandType_ReportCountryRankings = 18
eGMCommandType_ReportResult = 34
eGMCommandType_ReportUnionData = 13
eGMCommandType_RoleTransfer = 33
eGMCommandType_UpdateActorName = 19
eGameDataCommandType_ChatConfigReq = 1
eGameDataCommandType_ClanLog = 3
eGameDataCommandType_GameLoginWorld = 0
eHuoDongManagerCommandType_CloseBriefly = 9
eHuoDongManagerCommandType_CloseBrieflyResponse = 10
eHuoDongManagerCommandType_Create = 1
eHuoDongManagerCommandType_CreateResponse = 4
eHuoDongManagerCommandType_Delete = 2
eHuoDongManagerCommandType_DeleteResponse = 5
eHuoDongManagerCommandType_Init = 0
eHuoDongManagerCommandType_Query = 3
eHuoDongManagerCommandType_QueryResponse = 6
eHuoDongManagerCommandType_Update = 7
eHuoDongManagerCommandType_UpdateResponse = 8
eHuoDongManagerTimeType_Date = 1
eHuoDongManagerTimeType_DateAndDay = 3
eHuoDongManagerTimeType_Day = 2
eHuoDongManagerTimeType_Init = 0
eHuodongManagerErrCode_DataLen = 1
eHuodongManagerErrCode_Execute = 2
eHuodongManagerErrCode_NoError = 0
eHuodongManagerStatus_Closed = 3
eHuodongManagerStatus_Created = 1
eHuodongManagerStatus_Init = 0
eHuodongManagerStatus_Runing = 2
ePortraitVerifyResultCode_Fail = 1
ePortraitVerifyResultCode_OK = 0
tChatFilterConfigData =M(M91G)

