local print = print
local require = require
local pairs = pairs
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local time_util = require "time_util"
local player_mgr = require "player_mgr"
local red_const = require "red_const"
local red_system = require "red_system"
local flow_text = require "flow_text"
local event = require "event"
local event_activity_define = require "event_activity_define"
local activity_pb = require "activity_pb"
local net_activity_module = require "net_activity_module"
local hero_first_charge_mgr = require "hero_first_charge_mgr"
local gw_independent_game_mgr = require "gw_independent_game_mgr"
local game_scheme = require "game_scheme"
local gw_independent_game_const = require "gw_independent_game_const"
local log = require "log"
local hero_first_charge_data = require "hero_first_charge_data"
local hero_first_charge_define = require "hero_first_charge_define"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local force_guide_system = require "force_guide_system"
local force_guide_event = require "force_guide_event"
local lang = require "lang"

--region Controller Life
module("ui_hero_first_charge_main_controller")
local controller = nil
local UIController = newClass("ui_hero_first_charge_main_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)
    self.testCreatRole = false
    self.isShowBubbleTip = false
    self.viewWnd = self.viewWnd or ui_window_mgr:GetWindowObj(self.view_name)
    --无尽模式是否解锁
    self.isLockEndless = hero_first_charge_data.GetEndlessIsLock()
    self:TriggerUIEvent("SetLockEndless", self.isLockEndless)
    
    if hero_first_charge_data.IsJumpEndlessPanel() then
        self.modelPanel = hero_first_charge_define.HeroFirstChargeLevelType.Endless
    else
        self.modelPanel = hero_first_charge_define.HeroFirstChargeLevelType.Normal
    end
    
    self:TriggerUIEvent("SwitchModelBtnState", self.modelPanel == hero_first_charge_define.HeroFirstChargeLevelType.Normal)
    self:GetActivityEndTimer()
    self:GetLevelData()

    net_activity_module.MSG_COMM_ACTIVITY_RANK_REQ(activity_pb.ACTTYPE_ENDLESS_MINILEVEL,hero_first_charge_data.GetRankActivityID())
end

function UIController:OnShow()
    self.__base.OnShow(self)
    self.isLockEndless = hero_first_charge_data.GetEndlessIsLock()
    if self.isLockEndless then
        local util = require "util"
        force_guide_system.TriEnterEvent(force_guide_event.tEventClickBtnEndless)
        util.SetEnterTime("ui_hero_first_charge_main_Endless")
    end
end

function UIController:Close(data)
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    ui_window_mgr:UnloadModule("ui_hero_first_charge_cultivate")
    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
    force_guide_system.TriComEvent(force_guide_event.cEventClickBtnEndless)
    force_guide_system.TriComEvent(force_guide_event.cEventClickBtnChange)
end

function UIController:AutoSubscribeEvents()
    self.rankInfoRsp = function(_,msg)
        if not msg.actRankType then
            return
        end
        local length = msg.rankInfo and #msg.rankInfo or 0
        if msg and length > 0 then
            if msg.actRankType == activity_pb.ACTTYPE_ENDLESS_MINILEVEL then
                local topRankData = {}
                for i, v in ipairs(msg.rankInfo) do
                    if i <= 3 then
                        table.insert(topRankData,v)
                    else
                        break
                    end
                end
                self:TriggerUIEvent("RefreshTopRankView",topRankData)
            end
        end
    end
    self:RegisterEvent(event_activity_define.TMSG_COMM_ACTIVITY_RANK_RSP,self.rankInfoRsp)

    self.refreshLevelData = function(_,msg)
        self:GetLevelData()
        --无尽模式是否解锁
        self.isLockEndless = hero_first_charge_data.GetEndlessIsLock()
        self:TriggerUIEvent("SetLockEndless", self.isLockEndless)
    end
    self:RegisterEvent(event.NEW_XYX_PASS_LEVEL,self.refreshLevelData)

    self.showGuid = function ()
        if ui_window_mgr:IsModuleShown("ui_hero_first_charge_main") then
            self.isLockEndless = hero_first_charge_data.GetEndlessIsLock()
            if self.isLockEndless then
                local util = require "util"
                force_guide_system.TriEnterEvent(force_guide_event.tEventClickBtnEndless)
                util.SetEnterTime("ui_hero_first_charge_main_Endless")
            end
        end

    end
    self:RegisterEvent(event.BATTLE_RESULT_CLOSE,self.showGuid)
end

function UIController:AutoUnsubscribeEvents()
end
--endregion

--region Controller Logic

--region 界面按钮绑定方法
---@public function 切换挑战分页
function UIController:OnTogChallengeValueChange(state)
    if state then
        self:TriggerUIEvent("SwitchPanel", true)
    end
end

---@public function 切换培养分页
function UIController:OnTogCultivateValueChange(state)
    if state then
        self:TriggerUIEvent("SwitchPanel", false)
        local viewData = {
            uiParent = self.viewWnd.rtf_pageParent
        }
        ui_window_mgr:ShowModule("ui_hero_first_charge_cultivate", nil, nil, viewData)
    else
        ui_window_mgr:UnloadModule("ui_hero_first_charge_cultivate")
    end
end

---@public function 点击养成英雄按钮
function UIController:OnBtnCultivateClickedProxy()
    --self:TriggerUIEvent("SwitchPanel", false)
    --local viewData = {
    --    uiParent = self.viewWnd.rtf_pageParent
    --}
    ui_window_mgr:ShowModule("ui_hero_first_charge_cultivate")
end

---@public function 点击普通模式挑战按钮
function UIController:OnBtnNormalChangeClickedProxy()
    if self.currentNormalData and self.currentNormalData.levelID then
        hero_first_charge_mgr.RecordChallengeNormalLevelID(self.currentNormalData.levelID)
        red_system.TriggerRed(red_const.Enum.HeroFirstNormalStartBtn)
        hero_first_charge_mgr.OpenActivityMiniGame(true,self.currentNormalData.levelNum)
    end
end

---@public function 点击无尽模式挑战按钮
function UIController:OnBtnEndlessChangeClickedProxy()
    if self.endlessData then
        force_guide_system.TriComEvent(force_guide_event.cEventClickBtnChange)
        hero_first_charge_mgr.OpenActivityMiniGame(false,self.endlessData.levelNum)
    end
end

---@public function 切换无尽模式
function UIController:OnBtnEndlessClickedProxy()
    if self.modelPanel ~= hero_first_charge_define.HeroFirstChargeLevelType.Endless then
        if self.isLockEndless then
            self.modelPanel = hero_first_charge_define.HeroFirstChargeLevelType.Endless
            self:TriggerUIEvent("SwitchEndlessMode")
            hero_first_charge_mgr.RecordClickEndlessTime() --记录集结气泡时间
            red_system.TriggerRed(red_const.Enum.HeroFirstEndlessStartBtn)
        else
            local _,preLevelNum = hero_first_charge_data.GetEndlessPreLevelID()
            flow_text.Add(string.format2(lang.Get(1008260),preLevelNum))
        end
    end
    if self.isLockEndless then
        force_guide_system.TriComEvent(force_guide_event.cEventClickBtnEndless)
        force_guide_system.TriEnterEvent(force_guide_event.tEventClickBtnChange)
    end
end
---@public function 切换普通模式
function UIController:OnBtnNormalClickedProxy()
    if self.modelPanel ~= hero_first_charge_define.HeroFirstChargeLevelType.Normal then
        self.modelPanel = hero_first_charge_define.HeroFirstChargeLevelType.Normal
        self:TriggerUIEvent("SwitchNormalMode")

    end
end

---@public function 帮助按钮
function UIController:OnBtnHelpClickedProxy()
    local ui_help = require "ui_help"
    ui_help.ShowWithDate(10087)
end
---@public function 奖励预览按钮
function UIController:OnBtnRewardClickedProxy()
    if self.modelPanel == hero_first_charge_define.HeroFirstChargeLevelType.Endless then
        ui_window_mgr:ShowModule("ui_hero_first_charge_reward",nil,nil,{ isEndlessModel = true})
    else
        ui_window_mgr:ShowModule("ui_hero_first_charge_reward")
    end
end
---@public function 排行榜按钮
function UIController:OnBtnRankClickedProxy()
    ui_window_mgr:ShowModule("ui_hero_first_charge_rank")
end
---@public function 礼包按钮
function UIController:OnBtnGiftClickedProxy()
    hero_first_charge_mgr.JumpHeroFirstChargeGift()
end

function UIController:OnBtnBubbletipsClickedProxy() 
    self.isShowBubbleTip = not self.isShowBubbleTip
    self:TriggerUIEvent("RefreshBubbleInfo", self.isShowBubbleTip)
end
--endregion

---@public function 获取活动结束时间
function UIController:GetActivityEndTimer()
    local activityData = hero_first_charge_data.GetActivityData()
    if activityData then
        local endTime = activityData.endTimeStamp
        self:TriggerUIEvent("SetActivityTimer", endTime)
    end
end

function UIController:GetLevelData()
    local normalIDArr, endlessIDArr = hero_first_charge_data.GetLevelCfg()
    self:HandelNormalModelData(normalIDArr)    
    self:HandelEndlessModelData(endlessIDArr)
    self:GetNextEndlessReward(endlessIDArr)
end

---@public function 处理普通模式数据
function UIController:HandelNormalModelData(normalIDArr)
    if normalIDArr then --普通模式的关卡数据处理
        local normalViewData = {}
        local currentLevelID = 9999
        self.currentNormalData = self.currentNormalData or {}
        
        local nextUnLockLevelID = 0
        local nextUnLockNormalData = nil
        
        for i, v in ipairs(normalIDArr) do
            local tempData = {}
            tempData.levelNum = i
            tempData.levelID = v
            local isChallenge = gw_independent_game_mgr.GetLevelCanChallenge(v) --是否解锁
            if isChallenge then --是否可以挑战
                currentLevelID = i      --当前关
                tempData.levelState = gw_independent_game_const.LevelType.Unlock --可以过关
                self.currentNormalData = tempData
            else
                local isFinish = gw_independent_game_mgr.GetIsFinishByLevelId(v)
                if isFinish then
                    tempData.levelState = gw_independent_game_const.LevelType.Finish --已完成
                   
                else
                    if currentLevelID == 9999 and nextUnLockLevelID == 0 then
                        nextUnLockLevelID = i
                        nextUnLockNormalData = tempData
                    end
                    tempData.levelState = gw_independent_game_const.LevelType.Lock --上锁
                end
            end
            table.insert(normalViewData, tempData)
        end

        self:TriggerUIEvent("RefreshLevelList", normalViewData)

        local lastLevel = normalIDArr[#normalIDArr]
        self.isNormalCleared = gw_independent_game_mgr.GetIsFinishByLevelId(lastLevel) 
        if self.isNormalCleared then --是否通关
            self:TriggerUIEvent("SetNormalCleared")
        else
            if currentLevelID ~= 9999 then
                self:TriggerUIEvent("JumpToLevel", currentLevelID)
            else
                if not self.testCreatRole then
                    local miniLevelCfg = game_scheme:MiniGameLevelControl_0(nextUnLockNormalData.levelID, gw_independent_game_const.UnlockType)
                    if miniLevelCfg.CreatRole and miniLevelCfg.CreatRole ~= 0 then
                        local dayTime = (miniLevelCfg.CreatRole - player_mgr.GetRoleCreateDaysFromZeroTime()) * 86400
                        if dayTime > 0 then
                            local targetTime = time_util.GetServerTime0() + dayTime
                            self:TriggerUIEvent("SetUnLockLevelTime", targetTime)
                        end
                    end
                end
            end
        end
    end
end

---@public function 处理无尽模式数据
function UIController:HandelEndlessModelData(endlessIDArr)
    if endlessIDArr then --无尽模式的关卡数据处理
        local curLevelData = nil
        local allLevelData = {} --全部的关卡数据

        self.endlessData = self.endlessData or {}
        self.endlessData.levelIDArr = {}
        for i, v in ipairs(endlessIDArr) do
            local tempData = {}
            tempData.levelNum = i
            tempData.levelID = v
            local isChallenge = gw_independent_game_mgr.GetLevelCanChallenge(v) --是否可以挑战
            if isChallenge then --是否可以挑战
                curLevelData = tempData
                self.endlessData.levelNum = i
                self.curEndlessLevelID = v --当前无尽关卡
            end
            if self.endlessData.levelNum and i >= self.endlessData.levelNum then
                table.insert(self.endlessData.levelIDArr, v)
            end
            table.insert(allLevelData, tempData)
        end

        if curLevelData then
            local endlessViewData = {}

            for i = 1, 3 do --前3个插入空位
                local tempData = { isEmpty = true }
                table.insert(endlessViewData, tempData)
            end
            for i, v in ipairs(allLevelData) do
                local levelData = v
                local tempData = {}
                if levelData then
                    tempData.levelNum = levelData.levelNum
                    tempData.levelID = levelData.levelID
                    if i == curLevelData.levelNum then
                        tempData.state = 2 --当前关
                    elseif i < curLevelData.levelNum then
                        tempData.state = 1 --已过关
                    elseif i  > curLevelData.levelNum then
                        tempData.state = 0 --未过关
                    end
                    local miniGameControlCfg = game_scheme:MiniGameLevelControl_0(levelData.levelID,0)
                    if miniGameControlCfg then
                        if miniGameControlCfg.isRewardPreview == 1 then --有奖励
                            tempData.isReward = true
                        end

                        local rewardArr = {}
                        for index = 0, miniGameControlCfg.MonsterRewardId.count - 1 do
                            table.insert(rewardArr, miniGameControlCfg.MonsterRewardId.data[index])
                        end
                        for index = 0, miniGameControlCfg.BossRewardId.count - 1 do
                            table.insert(rewardArr, miniGameControlCfg.BossRewardId.data[index])
                        end
                        tempData.rewardArr = rewardArr
                    end
                end
                table.insert(endlessViewData, tempData)
            end
            for i = 1, 3 do --后3个插入空位
                local tempData = { isEmpty = true }
                table.insert(endlessViewData, tempData)
            end
            self:TriggerUIEvent("RefreshEndlessShow",endlessViewData,curLevelData.levelNum)

            self:TriggerUIEvent("JumpToEndlessLevel", curLevelData.levelNum)
        else
            if self.isLockEndless then
                --已通关
                self:TriggerUIEvent("SetEndlessCleared", true)
            end
        end
    end
end

---@public function 获取下一关无尽奖励
function UIController:GetNextEndlessReward(endlessIDArr)
    if not endlessIDArr then
        return
    end
    local curLevelID = self.curEndlessLevelID
    if curLevelID then
        local nextRewardData = nil
        local curLevelNum =self.endlessData and self.endlessData.levelNum or 0
        for i, v in ipairs(endlessIDArr) do
            if i >= curLevelNum then
                local miniGameControlCfg = game_scheme:MiniGameLevelControl_0(v,0)
                if miniGameControlCfg then
                    if miniGameControlCfg.isRewardPreview == 1 then --有奖励
                        nextRewardData = {}
                        nextRewardData.levelNum = i - curLevelNum
                        nextRewardData.RewardID = miniGameControlCfg.BossRewardId.data[0]
                        break
                    end
                end
            end
        end
        self:TriggerUIEvent("RefreshNextRewardArea", nextRewardData)
    end
end



--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
