---
--- Created by ha<PERSON><PERSON><PERSON>.
--- DateTime: 2024/8/15 15:17
--- Desc: 家园气泡基类
local require = require
local newclass = newclass
local UIUtil = CS.Common_Util.UIUtil
local typeof = typeof
local Vector3 = CS.UnityEngine.Vector3
local Space = CS.UnityEngine.Space
local CanvasGroup = CS.UnityEngine.CanvasGroup
local gw_const = require "gw_const"
local main_slg_data = require "main_slg_data"
local music_contorller = require "music_contorller"
local easing = require "easing"
local os = require "os"
local util = require "util"
local GWConst = require "gw_const"
local gw_home_card_sprite_asset_mgr = require "gw_home_card_sprite_asset_mgr"
local unit_base_object = require "unit_base_object"
local game_scheme = require "game_scheme"
local GWG = GWG
local cc = cc
local RectTransformUtility = CS.UnityEngine.RectTransformUtility
local val = require("val")
local isPerf = val.IsTrue("sw_home_bubble_new", 0)
local countdownPerf = val.IsTrue("sw_bubble_count_down", 0)
local playShowAniTime = 0.2
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
if countdownPerf then
    local InitSpriteCountDown = UIUtil.InitSpriteCountDown
    if not InitSpriteCountDown then
        countdownPerf = false
    end
end

---@class GWBubbleObject : unit_base 城建基础对象
---@field __base GWBubbleObject
module("gw_home_comp_bubble_base")
local GWBubbleObject = newclass("gw_home_comp_bubble_base", unit_base_object)
--- 构造器
function GWBubbleObject:ctor()
    self:ClearData()
end
--- @public 设置数据 （现必须基成设置数据）
---@see override
function GWBubbleObject:InitData(buildData, bindParent, bubbleType)
    self.bindParent = bindParent    --绑定的父节点
    self.parentTrans = bindParent.transform
    self.bubbleType = bubbleType    --气泡类型
    self.bubbleState = GWG.GWConst.BubbleStateEnum.Normal --气泡状态
    self.bubbleCfg = game_scheme:BubbleManager_0(self.bubbleType)
    cc(self):addComponent("BaseSubset"):exportMethods()
    self:_InitSubSet()
end

--- @public 基类方法实例化模型
---@see override
function GWBubbleObject:InstantiateModelAsync(path, parent)
    unit_base_object.InstantiateModelAsync(self, path, parent)
end

--- 实例化成功 （现必须基成设置名字和组件Id）
---@see override
function GWBubbleObject:InstantiateSuccess(_obj)
    self.size = 1
    self.bubbleRotationActionOpen = false

    if self.bubbleCfg and self.bubbleCfg.size then
        self.size = self.size * self.bubbleCfg.size
        self.bubbleRotationActionOpen = self.bubbleCfg.animationOpen == 1
    end
    --初始化组件
    self.rectTransform = UIUtil.GetComponent(_obj.transform, "RectTransform")
    self.button = UIUtil.GetComponent(_obj.transform, "Button")
    if countdownPerf then
        self.spriteRenderer = UIUtil.GetComponent(_obj.transform, "SpriteRenderer")
        if util.IsObjNull(self.spriteRenderer) then
            self.spriteRenderer = UIUtil.GetComponent(_obj.transform, "Image")
        end
    else
        self.Image = UIUtil.GetComponent(_obj.transform, "Image")
    end
    --ButtonScale组件优化后导致所有Button都会走button_scale方法，此处移除气泡的scale动画
    local button_scale = require "button_scale"
    button_scale.AddFilter(_obj.transform)
--[[    if SupportInputHandler and typeof(SupportInputHandler) then
        UIUtil.GetOrAddComponent(_obj.transform, typeof(SupportInputHandler))
    end]]
    unit_base_object.InstantiateSuccess(self, _obj)
    if self.bubbleType and self.bubbleType == GWConst.EHomeBubbleEntityType.Upgrade then
        self.needAni = true
        self:StopAni()
    end    
    self:SetScale(1)
    --设置位置 因为旋转需要设置根据y轴底部旋转 所以设置-35值
    UIUtil.SetPivot(self.rectTransform, 0.5, 0)
    self.offset_y = self.offset_y - 35

    if isPerf then
        self.rotation_x = 40
        self.rotation_y = -45
    else
        self.rotation_x = 0
        self.rotation_y = 0
    end

    --设置展示
    if self.size then
        if main_slg_data.GetCurSceneType() ~= gw_const.ESceneType.Home then
            return
        end
        if GWG.GWMgr.comp then
            local cameraDxf = GWG.GWMgr.comp:GetCurrentCameraDxf()
            if isPerf then
                --估算初始状态，气泡大小需要缩放ScreenToWorldScale
                self.scale =  (self.scale and self.scale * GWConst.ScreenToWorldScale or GWConst.ScreenToWorldScale) * self.size
                self:ResetBubbleOffsetSize(self.offsetSize)
            else
                self:SetBubbleScale(GWG.GWConst.HomeCameraZoom / cameraDxf)
            end
        end
    end

    self:OpenScaleAnimation()
    self:AddBtnOnClick(self.button, function()
        self:OnClick()
    end)
    self:CheckFuncState()

    if isPerf then
        self:ResetBubblePosition()
    else
        self:MoveTransform()
    end
end

---@public 气泡缩放动画
function GWBubbleObject:OpenScaleAnimation()
    if isPerf then 
        self:StopScaleAnimation()
        self.scaleAnimationMark = true
        local startTime = os.clock()  -- 记录开始时间
        local endScale = self.scale or GWConst.ScreenToWorldScale  -- 自定义目标缩放
        local initialScale = endScale / 3.33       -- 初始大小
        local duration = 0.3
        -- 定义更新缩放的函数
        local function UpdateScale()
            if not util.IsObjNull(self.rectTransform) then
                UIUtil.SetRotation(self.rectTransform, self.rotation_x, self.rotation_y, 0)
            end
            local elapsedTime = os.clock() - startTime
            if elapsedTime <= duration then
                local newScale = easing.outBack(elapsedTime, initialScale, endScale - initialScale, duration, 1.7)
                self:SetScale(newScale,true)
            else
                -- 动画完成，设置最终的目标缩放
                self:SetScale(endScale,true)
                self:StopScaleAnimation()
            end
        end
        -- 启动一个定时器或循环，每帧调用更新函数
        self.scaleAnimationTimer = self:CreateTimer(0, UpdateScale)
    else
        self:StopScaleAnimation()
        self.scaleAnimationMark = true
        local startTime = os.clock()  -- 记录开始时间
        local initialScale = 0.3       -- 初始大小
        local endScale = self.scale or 1  -- 自定义目标缩放
        local duration = 0.3
        -- 定义更新缩放的函数
        local function UpdateScale()
            local elapsedTime = os.clock() - startTime
            if elapsedTime <= duration then
                local newScale = easing.outBack(elapsedTime, initialScale, endScale - initialScale, duration, 1.7)
                self:SetScale(newScale)
            else
                -- 动画完成，设置最终的目标缩放
                self:SetScale(endScale)
                if not util.IsObjNull(self.rectTransform) then
                    UIUtil.SetRotation(self.rectTransform, 0, 0, 0)
                end
                self:StopScaleAnimation()
            end
        end
        -- 启动一个定时器或循环，每帧调用更新函数
        self.scaleAnimationTimer = self:CreateTimer(0, UpdateScale)
    end
end

function GWBubbleObject:StopScaleAnimation()
    if self.scaleAnimationTimer then
        self:RemoveTimer(self.scaleAnimationTimer)
        self.scaleAnimationTimer = nil
    end
end

--- 更新
---@see override
function GWBubbleObject:OnUpdate(timer)
    if self.bubbleState == GWG.GWConst.BubbleStateEnum.WaitingDispose then
        return true
    end
    return false
end

function GWBubbleObject:OnLateUpdate()
    self:MoveTransform()
end

function GWBubbleObject:SetBubbleRotation(rotation_x, rotation_y)
    self.rotation_x = rotation_x
    self.rotation_y = rotation_y
    UIUtil.SetRotation(self.rectTransform, self.rotation_x, self.rotation_y, 0)
end

function GWBubbleObject:SetUpdateRotation(zRotation, first)
    if not self.bubbleRotationActionOpen then
        return
    end
    if not self.isLoaded then
        return
    end

    if isPerf then 
        if self.scaleAnimationMark and not first then
            -- 气泡缩放动画 执行过程中
            UIUtil.SetRotation(self.rectTransform, self.rotation_x, self.rotation_y, 0)
            self.scaleAnimationMark = self.scaleAnimationTimer ~= nil
            return
        end
        if not self.bubbleState or self.bubbleState ~= GWConst.BubbleStateEnum.Normal then
            return
        end
        if util.IsObjNull(self.rectTransform) then
            return
        end
        UIUtil.SetRotation(self.rectTransform, self.rotation_x, self.rotation_y, zRotation)
    else
        if self.scaleAnimationMark and not first then
            -- 气泡缩放动画 执行过程中
            UIUtil.SetRotation(self.rectTransform, 0, 0, 0)
            self.scaleAnimationMark = self.scaleAnimationTimer ~= nil
            return
        end
        if not self.bubbleState or self.bubbleState ~= GWConst.BubbleStateEnum.Normal then
            return
        end
        if util.IsObjNull(self.rectTransform) then
            return
        end
        UIUtil.SetRotation(self.rectTransform, 0, 0, zRotation)
    end  
end

--- 移动
---@see override
function GWBubbleObject:MoveTransform()
    if isPerf then return end
    if not self.bubbleFollow then
        return
    end
    if not self.isLoaded then
        return
    end
    if util.IsObjNull(self.bindParent)then
        return
    end
    if self.bubbleState == GWG.GWConst.BubbleStateEnum.Hide then
        return
    end
    if not GWG.GWMgr.comp then
        return
    end
    if not self.gwCameraComp then
        self.gwCameraComp = GWG.GWMgr.comp:GetCameraComponent()
    end
    if util.IsObjNull(self.gwCameraComp) then
        return
    end
    local screenPos = RectTransformUtility.WorldToScreenPoint(self.gwCameraComp, self.parentTrans.position)
    if not util.IsObjNull(self.rectTransform) then
        if not self.bubbleCanvas or not self.hudCamera then
            self.bubbleCanvas = GWG.GWHomeMgr.bubbleMgr.GetCanvas()
            self.hudCamera = GWG.GWHomeMgr.bubbleMgr.GetHudCamera()
        end
        UIUtil.SetAncoPosition(self.rectTransform, self.bubbleCanvas, self.hudCamera, screenPos.x, screenPos.y, 0, self.offset_x * self.scale, self.offset_y * self.scale)
    else
        UIUtil.SetUIScreenPos(self.transform, screenPos.x + self.offset_x, screenPos.y + self.offset_y)
    end
end

function GWBubbleObject:ResetBubblePosition()
    --先设置到需要跟随的父物体的位置，然后旋转到与屏幕平行，最后根据offset在Self空间平移，z轴朝向屏幕移动3避免气泡插入到模型中
    if util.IsObjNull(self.rectTransform) then
        return
    end
    if util.IsObjNull(self.parentTrans) then
        return
    end
    self.rectTransform.position = self.parentTrans.position
    UIUtil.SetRotation(self.rectTransform, self.rotation_x, self.rotation_y, 0)
    self.rectTransform:Translate(Vector3(self.offset_x*self.scale, self.offset_y*self.scale, -3), Space.Self)
end

--- @public基类方法设置缩放
---@see override
function GWBubbleObject:SetBubbleScale(scale)
    if isPerf then return end
    self:AddLoadEvent(function()
        self:StopScaleAnimation()
        if self.offsetSize == nil then
            self.offsetSize = 1
        end
        self.scale = scale * self.size * self.offsetSize
        self:SetScale(self.scale)
        self:MoveTransform()
    end)
end

function GWBubbleObject:ResetBubbleOffsetSize(offsetSize)
    self:StopScaleAnimation()
    self.offsetSize = offsetSize
    if self.offsetSize == nil then
        self.offsetSize = 1
    end
    self.scale = self.scale * self.offsetSize
    self:SetScale(self.scale,true)
end

---@public 设置点击回调
---@param func function 回调方法
---@see override
function GWBubbleObject:SetClickFunc(func)
    self.clickFunc = func        --点击回调
end

---@public 设置检测回调
---@param func function 检测回调方法
---@see override
function GWBubbleObject:SetCheckFunc(func)
    self.checkFunc = func
end

---@public 检测气泡
---@see override
function GWBubbleObject:CheckFuncState()
    if self.checkFunc then
        self.state = self.checkFunc()
        self:RefreshCheckState()
    end
end

function GWBubbleObject:SetCheckState(bool_s)
    self.state = bool_s
    self:RefreshCheckState()
end

function GWBubbleObject:SetStatePathType(type)
    self.statePathType = type
end

function GWBubbleObject:RefreshCheckState()
    if not self.baseBubbleSpriteAsset then
        self.baseBubbleSpriteAsset = gw_home_card_sprite_asset_mgr.GetOrCreateCardSpriteAsset("gwbubble")
    end
    local statePath = nil
    if self.state then
        statePath = GWConst.BubbleStatePath.Finished
    else
        if self.statePathType then
            statePath = self.statePathType
        else
            statePath = GWConst.BubbleStatePath.Normal
        end
    end
    --不需要刷新
    if self.lastStatePath == statePath then
        return
    end
    self.lastStatePath = statePath
    
    self.baseBubbleSpriteAsset:GetSprite(statePath, function(sprite)
        if countdownPerf then
            if util.IsObjNull(self.spriteRenderer) or util.IsObjNull(sprite) then
                return
            end
            self.spriteRenderer.sprite = sprite
            UIUtil.SetAlpha(self.spriteRenderer,self.isHideBg and 0 or 1)
        else
            if util.IsObjNull(self.Image) or util.IsObjNull(sprite) then
                return
            end
            self.Image.sprite = sprite
            self.Image.enabled = not self.isHideBg
        end
    end)
end

---@public 点击事件
---@see override
function GWBubbleObject:OnClick()
    --镜头正在移动的时候 不可点击
    if not GWG.GWAdmin.HomeSceneUtil.CanCameraIsClick() then
        return
    end
    if self.clickFunc then
        self.clickFunc()
    end
    -- 执行音效回调
end
---@public 隐藏
---@see override
function GWBubbleObject:OnHide()
    self.bubbleState = GWG.GWConst.BubbleStateEnum.Hide
    self:SetActive(false)
end
---@public 显示
---@see override
function GWBubbleObject:OnShow(size)
    self:SetActive(true)
    if self.bubbleState ~= GWG.GWConst.BubbleStateEnum.Normal then
        if size ~= nil then
            self.offsetSize = size
            if GWG.GWMgr.comp then
                local cameraDxf = GWG.GWMgr.comp:GetCurrentCameraDxf()
                if not isPerf then
                    self:SetBubbleScale(GWG.GWConst.HomeCameraZoom / cameraDxf)
                else
                    self:ResetBubbleOffsetSize(self.offsetSize)
                    self:ResetBubblePosition() 
                end        
            end
        else
            self:OpenScaleAnimation()
        end
        self.bubbleState = GWG.GWConst.BubbleStateEnum.Normal
    end
end

function GWBubbleObject:OnEnterEdit()
    if self.bubbleState and self.bubbleState ~= GWG.GWConst.BubbleStateEnum.Editing then
        self.lastBubbleState = self.bubbleState
        self.bubbleState = GWG.GWConst.BubbleStateEnum.Editing    
    end
    self:SetActive(false)
end

function GWBubbleObject:OnExitEdit()
    if self.lastBubbleState then
        self.bubbleState = (self.lastBubbleState == 4) and 0 or self.lastBubbleState
        self:SetActive(self.bubbleState == GWG.GWConst.BubbleStateEnum.Normal)
    end
end

---@public 更新数据
---@param data table
---@see override
function GWBubbleObject:OnUpdateData(data)

end

---@public 设置移除状态
---@see override
function GWBubbleObject:Remove()
    self.bubbleState = GWG.GWConst.BubbleStateEnum.WaitingDispose
end

---清楚基类数据
---@see override
function GWBubbleObject:ClearData()
    self.bindParent = nil       --绑定的父节点
    self.clickFunc = nil        --点击回调方法
    self.checkFunc = nil        --检测回调方法
    self.rectTransform = nil    --当前rectTransform
    self.offset_x = 0           --偏移x
    self.offset_y = 0           --偏移y
    self.size = 1               --气泡大小
    self.bgImage = nil          --气泡背景
    self.state = false          --气泡完成状态
    self.bubbleType = nil       --气泡类型
    self.scale = 1              --气泡缩放
    self.baseBubbleSpriteAsset = nil --气泡图
    self.bubbleFollow = true    --气泡跟随
    self.offsetSize = nil       --气泡偏移大小
    self.statePathType = nil    --气泡状态路径
    self.lastBubbleState = nil
    self.rotation_x = 0
    self.rotation_y = 0

    --UI跟随使用的临时数据
    self.parentTrans = nil
    self.bubbleCanvas = nil
    self.hudCamera = nil
    self.gwCameraComp = nil
    self.lastStatePath = nil
    self:StopScaleAnimation()
    if self.scaleHideAnimationTimer then
        self:RemoveTimer(self.scaleHideAnimationTimer)
        self.scaleHideAnimationTimer = nil
    end
    self.closeCallback = nil
end
function GWBubbleObject:SetScale(scale, zIsScale)
    unit_base_object.SetScale(self, scale, zIsScale)
end

function GWBubbleObject:PlayUIAudio(soundId)
    music_contorller.PlayFxAudio(soundId)    
end

function GWBubbleObject:OnPlayHideAni()
    if not self.rectTransform or util.IsObjNull(self.rectTransform) then
        return
    end
    local canvasGroupObj = self.rectTransform:GetComponent(typeof(CanvasGroup))
    if util.IsObjNull(canvasGroupObj) then
        if self:IsValid() then
            UIUtil.SetActive(self.rectTransform, false)
        end
        return
    end
    local duration = playShowAniTime
    local startTime = os.clock()  -- 记录开始时间
    if self.scaleHideAnimationTimer then
        self:RemoveTimer(self.scaleHideAnimationTimer)
        self.scaleHideAnimationTimer = nil
    end
    -- 定义更新函数
    local function Update()
        local elapsedTime = os.clock() - startTime
        if elapsedTime < duration then
            -- 计算新的透明度值
            local alpha = 1 - (elapsedTime / duration)  -- 从1渐变到0
            canvasGroupObj.alpha = alpha
        else
            canvasGroupObj.alpha = 0
            self:RemoveTimer(self.scaleHideAnimationTimer)
            self.scaleHideAnimationTimer = nil
        end
    end
    -- 启动更新循环，替换为你的引擎机制，比如使用协程
    self.scaleHideAnimationTimer = self:CreateTimer(0, Update)  -- 假设有一个创建定时器的函数
    util.DelayCallOnce(playShowAniTime, function()
        if util.IsObjNull(self.rectTransform) then
            return
        end
        if self:IsValid() then
            UIUtil.SetActive(self.rectTransform, false)
        end
    end)
end


function GWBubbleObject:StopAni(isShow)
    if self.ticker then
        util.RemoveDelayCall(self.ticker)
        self.ticker = nil
    end
    if self.needAni == true and self.rectTransform and not util.IsObjNull(self.rectTransform) then
        local canvasGroupObj = self.rectTransform:GetComponent(typeof(CanvasGroup))
        if canvasGroupObj and not canvasGroupObj:IsNull() then
            canvasGroupObj.alpha = 1
        end
    end
    if self.closeCallback then
        self.closeCallback()
        self.closeCallback = nil
    end
end


function GWBubbleObject:AniClose(callback)
    self:StopAni(false)
    self.closeCallback = callback
    if self:IsValid() and self.needAni == true then
        self:OnPlayHideAni()
        self.ticker = util.DelayCall(playShowAniTime, function()
            if self.closeCallback then
                self.closeCallback()
                self.closeCallback = nil
            end
            if self.ticker then
                util.RemoveDelayCall(self.ticker)
                self.ticker = nil
            end
        end)
    else
        if self.closeCallback then
            self.closeCallback()
            self.closeCallback = nil
        end
    end
end

--- 弃置
---@see override
function GWBubbleObject:Dispose()
    self.bubbleState = GWG.GWConst.BubbleStateEnum.Dispose
    self:DisposeAllSubset()
    unit_base_object.Dispose(self)
    self:ClearData()
end
    
--- 重置为了循环利用
---@see override
function GWBubbleObject:Recycle()
    self.bubbleState = GWG.GWConst.BubbleStateEnum.Dispose
    unit_base_object.Recycle(self)
    self:ClearData()
end
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

return GWBubbleObject
