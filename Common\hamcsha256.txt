local string    = string
local tostring  = tostring

local HMACSHA256 = CS.System.Security.Cryptography.HMACSHA256
local LogDebug = CS.War.Base.LogDebug

module("hamcsha256")

function StringToHex(str)
    local Strlen = string.len(str)
    local hex = ""
    for i = 1, Strlen do
        temp = string.byte(str,i,i)
        value = tostring(string.format("%x",temp))
        --这里 00 和 0a 这种会自动变成单个字符
        if string.len(value) == 1 then
            value = "0" .. value
        end
        hex = hex .. tostring(value)
    end
    return hex
end

function HAMCSHA256Encrypt(secret, signKey)
    local mac = HMACSHA256(LogDebug.GetBytes(secret))
    mac:Initialize()
    local hash = mac:ComputeHash(LogDebug.GetBytes(signKey));

    return StringToHex(hash)
end