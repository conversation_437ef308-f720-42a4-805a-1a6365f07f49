local require = require
local print = print
local pairs = pairs
local ipairs = ipairs
local typeof = typeof
local table = table
local string = string
local tonumber = tonumber
local math = math
local GWG = GWG
local event = require "event"
local class = require "class"
local ui_base = require "ui_base"
local com_class = require "com_class"
local windowMgr = require "ui_window_mgr"
local game_scheme = require "game_scheme"
local lang = require "lang"
local player_mgr = require "player_mgr"
local goods_assets = require "card_sprite_asset"
local iui_item_detail = require "iui_item_detail"
local item_data = require "item_data"
local goods_item = require "goods_item_new"
local common_new_pb = require "common_new_pb"
local util = require "util"
local force_guide_system = require "force_guide_system"
local force_guide_event = require "force_guide_event"
local message_box = require "message_box"
local lang_res_key = require "lang_res_key"
local ui_setting_util = require "ui_setting_util"
local sort_order = require "sort_order"
local CModelViewer = require "modelviewer"
local show_animation = require "show_animation"
local hero_item = require "hero_item_new"
local reward_mgr = require "reward_mgr"
-- local maze_mgr = require "maze_mgr"
local battle_data = require "battle_data"
local UIUtil        = CS.Common_Util.UIUtil
local GameObject = CS.UnityEngine.GameObject
local Button = CS.UnityEngine.UI.Button
local Image = CS.UnityEngine.UI.Image
local Text = CS.UnityEngine.UI.Text
local RectTransform = CS.UnityEngine.RectTransform
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local HorizontalLayoutGroup = CS.UnityEngine.UI.HorizontalLayoutGroup
local SortingGroup = CS.UnityEngine.Rendering.SortingGroup
local Canvas = CS.UnityEngine.Canvas
local Toggle = CS.UnityEngine.UI.Toggle
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local GridLayoutGroup = CS.UnityEngine.UI.GridLayoutGroup
local ContentSizeFitter = CS.UnityEngine.UI.ContentSizeFitter
local ScrollRect = CS.UnityEngine.UI.ScrollRect
local Outline = CS.UnityEngine.UI.Outline
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local  minigame_mgr = require "minigame_mgr"
local table=table
module("ui_mini_game_battle_victory")
local window = nil
local BattleVictory = {}
local BattleData = {}
local HeroData = {}

local arenaRsp = nil
local battleRsp = nil
local rivalArray = {}
local isVictory = true
local showType = nil
local isSwitchMode = nil
local scaleVector = { x = 0.95, y = 1.15 }
local isReplay = false
local isClickReplayBtn = false
--///战斗录像分享
local battleID = nil
local opponentName = nil
local countTimer = nil
local flyEffectList = {}
local parentTrans = windowMgr.canvasMeshT
-- local orderStart, orderEnd = sort_order.ApplyIndexs(parentTrans, 2)	--重新获得层级
local starCount = nil  -- 结算评价星星数量
local maxWidth = 0   -- 评价最长文字宽度

local closeCallback = nil   -- 关闭回调
local nBattleType = nil--战斗类型
local MultiHeroData = {}
local teamIndex = 1
local battleResult = {}
local isNewLevel = nil -- 好评弹窗用

--详情类型
local TogType = {
    ENUM_REWARD = 1,        --奖励
    ENUM_DAMAGE = 2,        --输出
    ENUM_HURT = 3,        --承伤

}
ENUM_ATTACK = 1
EUNM_DEFENT = 2
local curType = TogType.ENUM_REWARD
local curTeamIndex = 2
local selColor = { r = 190 / 255, g = 104 / 255, b = 27 / 255, a = 1 }
local unSelColor = { r = 28 / 255, g = 90 / 255, b = 130 / 255, a = 1 }

BattleVictory.widget_table = {
    okBtn = { path = "okBtn", type = "Button", backEvent = true },
    listSkin = { path = "ItemList", type = RectTransform },
    gameGoallistSkin = { path = "GameGoalItemList", type = RectTransform },
    listScrollRect = { path = "ItemList", type = ScrollRect },
    listContent = { path = "ItemList/Viewport/Content", type = RectTransform },
    listContentLayoutGroup = { path = "ItemList/Viewport/Content", type = GridLayoutGroup },
    listContentFitter = { path = "ItemList/Viewport/Content", type = ContentSizeFitter },
    listFitter = { path = "ItemList", type = ContentSizeFitter },
    gameGoallistContent = { path = "GameGoalItemList/Viewport/Content", type = RectTransform },
    listScrollTable = { path = "ItemList/Viewport/Content", type = ScrollRectTable },
    gameGoallistScrollTable = { path = "GameGoalItemList/Viewport/Content", type = ScrollRectTable },
    --contentLayout = {path = "ItemList/Viewport/Content", type = HorizontalLayoutGroup},
    viewportRect = { path = "ItemList/Viewport", type = RectTransform },
    damage = { path = "damageBG/damage", type = "Text" },
    damageBG = { path = "damageBG", type = RectTransform },

    titleImg = { path = "Auto_Win/shengli", type = SpriteSwitcher },
    effectTrans = { path = "newshenglidi", type = RectTransform },
    statisticsBtn = { path = "statisticsBtn", type = "Button" },
    replayBtn = { path = "Auto_Replay", type = "Button" },
    shareBtn = { path = "Auto_Share", type = Button },

    passList = { path = "passList", type = RectTransform },
    xingxingTrans = { path = "xingxingPic", type = RectTransform },
    shengliPic = { path = "xingxingPic/shengliPic", type = SpriteSwitcher },
    showDetailBtn = { path = "xingxingPic/showDetailBtn", type = "Button" },
    star1 = { path = "xingxingPic/stars/star1/selected", type = RectTransform },
    star2 = { path = "xingxingPic/stars/star2/selected", type = RectTransform },
    star3 = { path = "xingxingPic/stars/star3/selected", type = RectTransform },
    notstar1 = { path = "xingxingPic/stars/star1/di", type = RectTransform },
    notstar2 = { path = "xingxingPic/stars/star2/di", type = RectTransform },
    notstar3 = { path = "xingxingPic/stars/star3/di", type = RectTransform },
    detailList = { path = "xingxingPic/detailList", type = RectTransform },
    detailCanvas = { path = "xingxingPic/detailList", type = Canvas },
    closeDetailBtn = { path = "closeDetail", type = "Button" },

    attackTog = { path = "TogList/TogAttack", type = Toggle },
    defendTog = { path = "TogList/TogDefend", type = Toggle },
    attackSelTex = { path = "TogList/TogAttack/Selected", type = "Text" },
    attackTxt = { path = "TogList/TogAttack/UnSelected", type = "Text" },
    defendSelTex = { path = "TogList/TogDefend/Selected", type = "Text" },
    defendTxt = { path = "TogList/TogDefend/UnSelected", type = "Text" },
    HeroRect = { path = "HeroList/Viewport/Content", type = RectTransform },
    HeroItem = { path = "HeroList/Viewport/Content/HeroItem", type = RectTransform },

    rewardText = { path = "JiangLi", type = "Text" },
    rewardText1 = { path = "JiangLi1", type = "Text" },

    chapterRewardTrans = { path = "chapterReward", type = RectTransform },
    chapterNameText = { path = "chapterReward/chapterText", type = "Text" },
    chapterprogressValue = { path = "chapterReward/progress/progressValue", type = "Image" },
    chapterprogressValueText = { path = "chapterReward/progress/progressValueText", type = "Text" },
    chapterRewardList = { path = "chapterReward/rewardList/Viewport/Content", type = ScrollRectTable },

    MultiToggle = { path = "MultiToggle", type = RectTransform },
    OneBtn = { path = "MultiToggle/OneBtn", type = Toggle },
    TwoBtn = { path = "MultiToggle/TwoBtn", type = Toggle },
    ThreeBtn = { path = "MultiToggle/ThreeBtn", type = Toggle },
    FourBtn = { path = "MultiToggle/FourBtn", type = Toggle },

    winTxt1 = { path = "MultiToggle/OneBtn/win", type = SpriteSwitcher },
    winTxt1En = { path = "MultiToggle/OneBtn/winEn", type = SpriteSwitcher },
    txtOut1 = { path = "MultiToggle/OneBtn/Text", type = Outline },

    winTxt2 = { path = "MultiToggle/TwoBtn/win", type = SpriteSwitcher },
    winTxt2En = { path = "MultiToggle/TwoBtn/winEn", type = SpriteSwitcher },
    txtOut2 = { path = "MultiToggle/TwoBtn/Text", type = Outline },

    winTxt3 = { path = "MultiToggle/ThreeBtn/win", type = SpriteSwitcher },
    winTxt3En = { path = "MultiToggle/ThreeBtn/winEn", type = SpriteSwitcher },
    txtOut3 = { path = "MultiToggle/ThreeBtn/Text", type = Outline },

    winTxt4 = { path = "MultiToggle/FourBtn/win", type = SpriteSwitcher },
    winTxt4En = { path = "MultiToggle/FourBtn/winEn", type = SpriteSwitcher },
    txtOut4 = { path = "MultiToggle/FourBtn/Text", type = Outline },

    mazeMercenary = { path = "MazeMercenary", type = RectTransform },

    tipsText = { path = "tipTxt", type = "RectTransform" },

    switchRoot = { path = "SwitchRoot", type = "RectTransform" },
    confirmBtn = { path = "SwitchRoot/confirmBtn", type = "Button" },
    backBtn = { path = "SwitchRoot/backBtn", type = "Button" },
    countDown = { path = "SwitchRoot/countDown", type = "Text" },

    secretRoot = { path = "SecretPlaceRoot", type = "RectTransform" },
    canGetRoot = { path = "SecretPlaceRoot/canGetRoot", type = "RectTransform" },
    nextRewardRoot = { path = "SecretPlaceRoot/nextRewardRoot", type = "RectTransform" },
    canGetTxt = { path = "SecretPlaceRoot/canGetRoot/Text", type = "Text" },
    nextRewardTxt = { path = "SecretPlaceRoot/nextRewardRoot/Text", type = "Text" },
    canGetItem = { path = "SecretPlaceRoot/canGetRoot/Item", type = "RectTransform" },
    nextRewardItem = { path = "SecretPlaceRoot/nextRewardRoot/Item", type = "RectTransform" },

    --舰队远征星星条件
    starConditionRoot = { path = "starCondition", type = RectTransform, event_name = "" }, --星星条件节点
    starConditionItem = { path = "starCondition/ListItem", type = RectTransform, event_name = "" }, --星星条件Item
    contentSize = { path = "starCondition", type = ContentSizeFitter, event_name = "" }, --星星条件节点
    victoryEffect = { path = "Auto_Win/victoryEffect", type = RectTransform },
    victoryEffectEn = { path = "Auto_Win/victoryEffectEn", type = RectTransform },
    victoryEffect1 = { path = "Auto_Win/victoryEffect1", type = RectTransform },
    LeftBG = { path = "Auto_Win/LeftBG", type = RectTransform },
    RightBG = { path = "Auto_Win/RightBG", type = RectTransform },
    Image = { path = "Auto_Win/Image", type = RectTransform },
    BG = { path = "Auto_Win/BG", type = RectTransform },
    jiesuanzhipian = { path = "Auto_Win/jiesuanzhipian", type = RectTransform },
    shengli = { path = "Auto_Win/shengli", type = SpriteSwitcher },
    rankAddTip = { path = "RankTip", type = "RectTransform" },--星河神殿排行榜挑战提示文本


    --位面2新胜利界面
    LevelText = {path = "Main/LevelText", type = "Text" },
    RewardTog = {path = "Main/ToggleList/RewardTog", type = Toggle},
    DamageTog = {path = "Main/ToggleList/DamageTog", type = Toggle},
    DamageTogUnselect = {path = "Main/ToggleList/DamageTog/UnSelectShow", type = SpriteSwitcher},
    DamageTogSelect = {path = "Main/ToggleList/DamageTog/SelectShow", type = SpriteSwitcher},
    HurtTog = {path = "Main/ToggleList/HurtTog", type = Toggle},
    RewardMain = {path = "Main/RewardMain", type = RectTransform },
    RewardList = {path = "Main/RewardMain/RewardList", type = RectTransform },
    RewardContent = {path = "Main/RewardMain/RewardList/Viewport/Content", type = RectTransform },
    BtnList = {path = "Main/BtnList", type = RectTransform },
    btnShare =  {path = "Main/BtnList/btnShare", type = "Button" },
    btnClose =  {path = "Main/BtnList/btnClose", type = "Button" , backEvent = true },
    HeroMain = {path = "Main/HeroMain", type = RectTransform },
    NewHeroItem = {path = "Main/HeroMain/HeroList/Viewport/Content/HeroItem", type = RectTransform },

    nextBtn = {path = "nextBtn",type = Button}
}

function BattleVictory:SetUIActive()
    local isZH = lang.USE_LANG == lang.ZH

    self.LeftBG.gameObject:SetActive(not isZH)
    self.RightBG.gameObject:SetActive(not isZH)
    --self.Image.gameObject:SetActive(not isZH)
    self.BG.gameObject:SetActive(not isZH)
--[[    self.jiesuanzhipian.gameObject:SetActive(not isZH)
    self.shengli.gameObject:SetActive(not isZH)]]

    self.victoryEffect.gameObject:SetActive(isZH)
    self.victoryEffectEn.gameObject:SetActive(not isZH)
    self.victoryEffect1.gameObject:SetActive(true)
end


--（新）设置单个英雄方法
function BattleVictory:SetHeroNew(index,data,maxHeroNum)
    local item = self.heroObjList[index] or GameObject.Instantiate(self.NewHeroItem.gameObject, self.NewHeroItem.transform.parent):GetComponent(typeof(ScrollRectItem))
    if data ~= nil then
        local isHero=data.isHero
        local heroID = data.heroID or 0
        if item then
            UIUtil.SetActive(item.gameObject, true)
            local HeroTra = item:Get("HeroTra")
            local NameText = item:Get("NameText")
            local NumText = item:Get("NumText")
            local NumSliderFill = item:Get("NumSliderFill")
            local mvp = item:Get("mvp")

            local heroFaceItem = self.heroFaceList[index]
            local scale = isHero and 0.7 or 0.75
            local isGray = false
            if isHero then
                --isGray = data.statistics.isDead or (not data.statistics.hp) or (data.statistics.hp and data.statistics.hp <= 0)
                --临时处理，默认全部存活
                isGray = false
            else
                isGray = false
            end
            if not heroFaceItem then
                if isHero then
                    heroFaceItem = hero_item.CHeroItem()
                    local cfg_hero = game_scheme:Hero_0(heroID)
                    if cfg_hero then
                        NameText.text = lang.Get(cfg_hero.HeroNameID)
                    end
                else
                    local pet_item=require "pet_item"
                    heroFaceItem = pet_item.CPetItem()
                    local helper_personalInfo = require "helper_personalInfo"
                    local weaponCfg = helper_personalInfo.GetAnimalItemConfig(data.id)
                    if weaponCfg then
                        NameText.text = lang.Get(weaponCfg.nameId)
                    end
                end
            end

            heroFaceItem:Init(HeroTra.transform, nil, scale, nil, function(obj)
                obj:SetHero(data.heroData or data)
                obj:GrayHeroIcon(isGray)
                obj:DisplayInfo()
            end)

            self.heroFaceList[index] = heroFaceItem
            local heroNum=data.hurt
            NumText.text = util.PriceConvert(heroNum)
            local numX = heroNum / math.max(maxHeroNum, 0.0001) < 1 and heroNum / math.max(maxHeroNum, 0.0001) or 1
            local width = numX * NumSliderFill.transform.sizeDelta.x
            NumSliderFill.transform.anchoredPosition = {x = width, y = 0, z = 0}
            if isHero then
                local cfg_hero = game_scheme:Hero_0(heroID)
                if cfg_hero then
                    NameText.text = lang.Get(cfg_hero.HeroNameID)
                end
            end

            --local mvpGrade = game_scheme:InitBattleProp_0(854).szParam.data
            --local grade = mvpGrade[0] * data.statistics.damage + mvpGrade[1] * data.statistics.hurt + mvpGrade[2] * data.statistics.heal
            --self.heroGrade[index] = grade
            --util.DelayCallOnce(0, function()
            --	if not window or not window:IsValid() then
            --		return
            --	end
            --	local maxGrade = 0
            --	local maxIndex = 0
            --	if self.heroGrade then
            --		for i, v in pairs(self.heroGrade) do
            --			if v >= maxGrade then
            --				maxGrade = v
            --				maxIndex = i
            --			end
            --		end
            --	end
            --	--  --print("pos:",pos,"grade:",grade,"maxGrade:",maxGrade,"maxPos:",maxPos)
            --	if maxGrade > 0 and not util.IsObjNull(mvp) then
            --		UIUtil.SetActive(mvp, grade >= maxGrade and maxIndex == index)
            --	end
            --end)
        end
    else
        if item then
            UIUtil.SetActive(item.gameObject, false)
        end
    end
    self.heroObjList[index] = item
end

local mudule_scroll_list = require "scroll_list"
local CScrollList = mudule_scroll_list.CScrollList
local CScrollListItemBase = mudule_scroll_list.CScrollListItemBase

local CBattleVictoryScollListItem = com_class.CreateClass(CScrollListItemBase)

function CBattleVictoryScollListItem:Create(goSkin)
    CScrollListItemBase.Create(self, goSkin)
    self.ui = goSkin
end

function CBattleVictoryScollListItem:Draw(data, i)
    if data.id then
        --物品
        self.iconUI = self.iconUI or goods_item.CGoodsItem()
        self.iconUI:Init(self.ui.transform, nil, 0.7)
        local itemID = data.id
        local itemNum = data.num
        if data.id == 0 and data.rewardID ~= 0 then
            local rewardCfg = game_scheme:Reward_0(data.rewardID)
            local reward_data = reward_mgr.GetRewardGoods(data.rewardID)
            itemID = reward_data.id
            itemNum = reward_data.num
        end
        self.iconUI:SetGoods(nil, itemID, itemNum, function()
            iui_item_detail.Show(itemID, data.sid, item_data.Item_Show_Type_Enum.Reward_Interface, nil, nil, nil, data.rewardID)
        end)
        self.iconUI:SetFrameBg(3)
    elseif data.heroid then
        --英雄
        local rewardCfg = game_scheme:Reward_0(data.rewardID)
        if not rewardCfg then
            return
        end
        self.heroUI = self.heroUI or hero_item.CHeroItem()
        self.heroUI:Init(self.ui.transform, function()
            self.heroUI:DisplayInfo()
        end, 0.8)
        self.heroUI:SetHero({ heroID = rewardCfg.arrParam[0] }, function()
            local ui_archive_detail = require "ui_archive_detail"
            local windowMgr = require "ui_window_mgr"
            ui_archive_detail.SetData({ { heroID = rewardCfg.arrParam[0], starLv = rewardCfg.arrParam[1] } }, 1)
            windowMgr:ShowModule("ui_archive_detail", nil, function()
                window:UpdataHeroData()
            end)
        end)
    end
end

function CBattleVictoryScollListItem:Destroy(reself)
    if self.iconUI then
        self.iconUI:Dispose()
        self.iconUI = nil
    end

    if self.heroUI then
        self.heroUI:Dispose()
        self.heroUI = nil
    end
end

-- 显示成就类型的数据
local CPassTxtListItem = com_class.CreateClass(CScrollListItemBase)

function CPassTxtListItem:Create(goSkin)
    CScrollListItemBase.Create(self, goSkin)
    self.goImg = self:FindChild("gouxuanBIG"):GetComponent(typeof(Image))
    self.txt = self:FindChild("text"):GetComponent(typeof(Text))
end

function CPassTxtListItem:Draw(data, i)
    self.goImg.gameObject:SetActive(data.isFinish == 1)
    self.txt.text = data.txt
    self.txt.color = (data.isFinish == 1) and { r = 155 / 255, g = 1, b = 142 / 255, a = 1 } or { r = 1, g = 1, b = 1, a = 1 }
end

function CPassTxtListItem:Destroy(reself)
    CPassTxtListItem.__super.Destroy(self)
end

-- 显示评价详情的数据
local CStarInfoListItem = com_class.CreateClass(CScrollListItemBase)

function CStarInfoListItem:Create(goSkin)
    CScrollListItemBase.Create(self, goSkin)
    self.goImg = self:FindChild("gou"):GetComponent(typeof(Image))
    self.chaImg = self:FindChild("cha"):GetComponent(typeof(Image))
    self.txt = self:FindChild("text"):GetComponent(typeof(Text))
end

function CStarInfoListItem:Draw(data, i)
    self.goImg.gameObject:SetActive(data.isFinish)
    self.chaImg.gameObject:SetActive(not data.isFinish)
    self.txt.text = data.text
    self.txt.color = data.isFinish and { r = 1, g = 236 / 255, b = 131 / 255, a = 1 } or { r = 1, g = 1, b = 1, a = 1 }

    -- 列表宽度文字自适应
    if maxWidth < self.txt.preferredWidth then
        maxWidth = self.txt.preferredWidth
    end
end

function CStarInfoListItem:Destroy(reself)
    CStarInfoListItem.__super.Destroy(self)
end

function BattleVictory:ctor(selfType)
    self.goodAsset = goods_assets.CreateSpriteAsset()
end

--[[（服务端说麻烦）临时处理，活动掉落插入]]
function InsertActivityReward()
    ------  --print("临时处理，活动掉落插入")
    local net_festival_activity_module = require "net_festival_activity_module"
    local bonus = net_festival_activity_module.GetBattleVictoryReward()
    if bonus then
        for key, rewardID in pairs(bonus.reward) do
            local rewardCfg = game_scheme:Reward_0(rewardID)
            local data = reward_mgr.GetRewardGoods(rewardID)
            AddReward(data.id, data.num, rewardID)
        end
        net_festival_activity_module.ClearBatlleVictoryReward()
    end
end

--切换显示
function BattleVictory:UpdateBattleDetails(iType)
    curType = iType
    if not self.heroDataList then
        return
    end

    local dataList={}
    if iType ~= TogType.ENUM_REWARD then
        if iType == TogType.ENUM_DAMAGE then
            dataList=self.heroDataList
        elseif iType == TogType.ENUM_HURT then
            dataList=self.heroHitDataList
        end

        local maxNum=0
        for i, v in ipairs(dataList) do
            if v.hurt>maxNum then
                maxNum=v.hurt
            end
        end
        for i = 1, 6 do
            local itemData=dataList[i]
            self:SetHeroNew(i,itemData,maxNum)
        end

    end
end

function BattleVictory:OnShow()
    self.__base:OnShow()
    self.DamageTogUnselect:Switch(#BattleData > 0 and 0 or 1)
    self.DamageTogSelect:Switch(#BattleData > 0 and 0 or 1)
    UIUtil.SetActive(self.RewardTog, #BattleData > 0)
    if #BattleData > 0 then
        self:InitRewardData()
    else
        curType = TogType.ENUM_DAMAGE
        self:UpdateBattleDetails(curType)
    end
    local miniHookLevel = minigame_mgr:GetHookLevelCfg()
    -- 非主线关卡不显示下一关按钮
    if not miniHookLevel then
        UIUtil.SetActive(self.nextBtn, false)
    else
        local laymain_data = require "laymain_data"
        local curlevel = laymain_data.GetPassLevel()
        local gw_zombie_treasure_data = require "gw_zombie_treasure_data"
        if curlevel > gw_zombie_treasure_data.GetShowMainLineEntrance() then
            UIUtil.SetActive(self.nextBtn, true)
        else
            UIUtil.SetActive(self.nextBtn, false)
        end
    end
end

--初始化奖励列表
function BattleVictory:InitRewardData()
    if self.ListInstance == nil then
        self.ListInstance = CScrollList:CreateInstance({})
        self.ListInstance:Create(self.RewardList, CBattleVictoryScollListItem)
    end
    self.ListInstance:SetListData(BattleData)
    local modelPath = "art/effects/prefabs/ui/effect_ui_huodedaoju_02.prefab"
    show_animation.PlayAni("battle_victory_ani", self.RewardContent, modelPath, self.curOrder, #BattleData)
end

function onItemRenderBottom(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or { index, dataItem }
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    local rewardData = reward_mgr.GetRewardGoods(dataItem)

    local itemUI = scroll_rect_item.data["itemUI"] or goods_item.CGoodsItem():Init(scroll_rect_item.gameObject.transform, nil, 0.5)
    itemUI:SetGoods(nil, rewardData.id, rewardData.num, function()
        iui_item_detail.Show(rewardData.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil, nil, nil, rewardData.rewardID)
    end)
    scroll_rect_item.data["itemUI"] = itemUI
end

function onListItemRenderBottom(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or { index, dataItem }
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    local rewardData = dataItem
    if rewardData.type == item_data.Item_Type_Enum.Hero then
        local rewardCfg = game_scheme:Reward_0(rewardData.rewardID)
        if not rewardCfg then
            return
        end
        if scroll_rect_item.data["itemUI"] then
            scroll_rect_item.data["itemUI"]:Dispose()
            scroll_rect_item.data["itemUI"] = nil
        end
        scroll_rect_item.data["itemUI"] = hero_item.CHeroItem()
        scroll_rect_item.data["itemUI"]:Init(scroll_rect_item.gameObject.transform, function()
            scroll_rect_item.data["itemUI"]:DisplayInfo()
        end, 0.8)
        scroll_rect_item.data["itemUI"]:SetHero({ heroID = rewardCfg.arrParam[0] }, function()
            local ui_archive_detail = require "ui_archive_detail"
            local windowMgr = require "ui_window_mgr"
            ui_archive_detail.SetData({ { heroID = rewardCfg.arrParam[0], starLv = rewardCfg.arrParam[1] } }, 1)
            windowMgr:ShowModule("ui_archive_detail", nil, function()
                window:UpdataHeroData()
            end)
        end)
    else
        if scroll_rect_item.data["itemUI"] then
            scroll_rect_item.data["itemUI"]:Dispose()
            scroll_rect_item.data["itemUI"] = nil
        end
        scroll_rect_item.data["itemUI"] = goods_item.CGoodsItem()
        scroll_rect_item.data["itemUI"]:Init(scroll_rect_item.gameObject.transform, nil, 0.57)
        local itemID = rewardData.id
        local itemNum = rewardData.num
        if rewardData.id == 0 and rewardData.rewardID ~= 0 then
            local rewardCfg = game_scheme:Reward_0(rewardData.rewardID)
            local reward_data = reward_mgr.GetRewardGoods(rewardData.rewardID)
            itemID = reward_data.id
            itemNum = reward_data.num
        end
        scroll_rect_item.data["itemUI"]:SetGoods(nil, itemID, itemNum, function()
            iui_item_detail.Show(itemID, rewardData.sid, item_data.Item_Show_Type_Enum.Reward_Interface, nil, nil, nil, rewardData.rewardID)
        end)
        scroll_rect_item.data["itemUI"]:SetFrameBg(3)
    end
    -- CreateEffect(scroll_rect_item.gameObject.transform,index)
end

local CModelViewer = require "modelviewer"
function CreateEffect(parentTrans, pos)
    if not window then
        return
    end
    local modelPath = "art/effects/prefabs/ui/effect_ui_huodedaoju_02.prefab"
    if flyEffectList[pos] or not modelPath then
        return
    end
    if not flyEffectList[pos] then
        flyEffectList[pos] = CModelViewer()
        flyEffectList[pos]:Init(parentTrans, function()
            if pos and flyEffectList[pos] and modelPath then
                flyEffectList[pos]:ShowGameObject(modelPath)
            end
        end)
        --设置光效的渲染顺序
        if window then
            flyEffectList[pos]:SetRenderOrder(window.curOrder + 1)
        end
    end
end


function BattleVictory:SetInputParam(_HeroHurt,_HeroHitHurt,isShowStart)
    self.heroDataList = _HeroHurt
    self.heroHitDataList = _HeroHitHurt
    if isShowStart then 
        self.ShowStart = false
    else
        self.ShowStart = true
    end
    if self.heroDataList and #self.heroDataList>1 then
        table.sort( self.heroDataList,function(a,b) return a.index<b.index end)
    end
    if self.heroHitDataList and #self.heroHitDataList>1 then
        table.sort( self.heroHitDataList,function(a,b) return a.index<b.index end)
    end
    --如果正在显示，则更新一次窗口
    if self.UIRoot and self.UIRoot.activeSelf == true then
        self:SwitchPage()
    end
    --@region User
    --@endregion
end --///<<< function


function BattleVictory:Init()
    self.heroObjList = {}
    self.heroFaceList = {}
    self:SetUIActive()
    self.shengli:Switch(lang.USE_LANG == lang.ZH and 0 or 1)
    local music_contorller = require "music_contorller"
    music_contorller.PlayFxAudio(300025)
    --恢复背景音乐
    music_contorller.ResumeMusic()

    self.chapterRewardList.onItemRender = onItemRenderBottom
    self.chapterRewardList.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data and scroll_rect_item.data["itemUI"] then
            scroll_rect_item.data["itemUI"]:Dispose()
            scroll_rect_item.data["itemUI"] = nil
        end
    end

    self.gameGoallistScrollTable.onItemRender = onListItemRenderBottom
    self.gameGoallistScrollTable.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data and scroll_rect_item.data["itemUI"] then
            scroll_rect_item.data["itemUI"]:Dispose()
            scroll_rect_item.data["itemUI"] = nil
        end
    end

    InsertActivityReward()

    self.heroes = {}       -- 存放所有的英雄图标
    self.heroGrade = {}        -- 存放所以的英雄成绩，用于计算MVP
    --self:UpdateBattleDetails(ENUM_ATTACK)
    self.ThreeBtn.gameObject:SetActive(#MultiHeroData >= 3)
    self.FourBtn.gameObject:SetActive(#MultiHeroData >= 4)

    self:SwitchPage()
    self.curOrder, self.orderEnd = sort_order.ApplyBaseIndexs(self, parentTrans, 2)
    event.Trigger(event.ASH_HIDE_EFFECT, true)
    if lang.USE_LANG == lang.ZH then
        self.titleImg:Switch(0)
        self.shengliPic:Switch(0)
    else
        self.titleImg:Switch(1)
        self.shengliPic:Switch(1)
    end
    self.titleImg:GetComponent(typeof(Image)):SetNativeSize()


    -- show_animation.PlayAni("battle_victory_ani",self.listContent,modelPath,self.UIRoot.transform,#BattleData)

    if arenaRsp ~= nil then
        self.listSkin.gameObject:SetActive(false and (battleRsp == nil or #BattleData > 0))
    else
        self.listSkin.gameObject:SetActive(true and (battleRsp == nil or #BattleData > 0))
    end
    self:SubscribeEvents()

    --通关说明
    self.PassTxtListInstance = CScrollList:CreateInstance({})
    self.PassTxtListInstance:Create(self.passList, CPassTxtListItem)
    self.PassTxtListInstance:SetListData(self:BuildPassTxtData())

    -- 星星评价显示
    self.StarDetailListInstance = CScrollList:CreateInstance({})
    self.StarDetailListInstance:Create(self.detailList, CStarInfoListItem)
    --检测一下是否是主线
    local isGameGoal = false
    local level =  minigame_mgr.GetHookLevelId() or -1
    local miniGameLevel = game_scheme:MiniLevel_0(level)
    if miniGameLevel and miniGameLevel.Type and  miniGameLevel.Type == 0 then
        isGameGoal = true
    end
    if showType == common_new_pb.GameGoal  or  isGameGoal then
        --or showType == common_new_pb.TheTowerOfIllusion
        -- self.titleImg.gameObject:SetActive(false)
        --self.xingxingTrans.gameObject:SetActive(false)--暂时屏蔽
        --加载特效并播放
        --目前默认播放三星动画 
        self:SetActive(self.victoryEffect,false)
        --self:SetActive(self.victoryEffect1,false)
        self:SetActive(self.xingxingTrans,false)
        starCount = 3
        local modelPath = "art/effects/prefabs/ui/ui_jiesuanxingxing_xin.prefab"
        if lang.USE_LANG ~= lang.ZH then
            --modelPath = "art/effects/prefabs/ui/ui_jiesuanxingxing_xinen.prefab"
            modelPath = nil
        end
        if(modelPath)then
            self:LoadResultEffect(modelPath)
        end
        if starCount > 0 and self.ShowStart then
            self:ShowStarEffect(starCount)
        end
        self:UpdateStarInfo()
        --新手强制引导事件
        local battle_manager = require "battle_manager"
        if not battle_manager.GetIsReplay() then
            force_guide_system.TriEnterEvent(force_guide_event.tEventEnterSuccess)
        end
    elseif showType == common_new_pb.LeagueBoss then
        -- self.titleImg.gameObject:SetActive(true)
        self.xingxingTrans.gameObject:SetActive(false)
        --加载特效并播放
        -- local modelPath = "art/effects/prefabs/ui/ui_jiesuanxingxing_519.prefab"
        -- if lang.USE_LANG ~= lang.ZH then
        --     modelPath = "art/effects/prefabs/ui/ui_jiesuanxingxing_519en.prefab"
        -- end
        -- self:LoadResultEffect(modelPath)
    elseif nBattleType == common_new_pb.ChinaRed then
        -- self.titleImg.gameObject:SetActive(false)
        self.xingxingTrans.gameObject:SetActive(false)--暂时屏蔽
        self:UpdateStarInfo()
    else
        -- self.titleImg.gameObject:SetActive(true)
        --self.xingxingTrans.gameObject:SetActive(false)
        --加载特效并播放
        --local modelPath = "art/effects/prefabs/ui/ui_zhandoushenglixin.prefab"
        --if lang.USE_LANG ~= lang.ZH then
        --    modelPath = "art/effects/prefabs/ui/ui_zhandoushenglixin_en.prefab"
        --end
        -- self:LoadResultEffect(modelPath) --位面的特效屏蔽掉
    end
    self:SetTipsUI(showType)

    -- 回放按钮和分享按钮有等级开放限制
    local ui_pop_mgr = require "ui_pop_mgr"
    local isOpen = ui_pop_mgr.CheckIsOpen(283, false)
    --self.replayBtn.gameObject:SetActive(isOpen)
    --self.shareBtn.gameObject:SetActive(isOpen)
    local results = nil

    if nBattleType == common_new_pb.Maze or nBattleType == common_new_pb.GameGoal or nBattleType == common_new_pb.EquipEctypeZhuanShu then
        --TODO:何时显示多队
        local ui_maze_multi_battle_result = require "ui_maze_multi_battle_result"
        results = ui_maze_multi_battle_result.GetMultiBattleResult()
        local count = results and #results or 0
        self.MultiToggle.gameObject:SetActive(count>1)
        self.OneBtn.gameObject:SetActive(count>0)
        self.TwoBtn.gameObject:SetActive(count>1)
        self.ThreeBtn.gameObject:SetActive(count > 2)
        self.FourBtn.gameObject:SetActive(count > 3)
    end


    self.switchRoot:SetActive(isSwitchMode == true)
    self.tipsText:SetActive(isSwitchMode ~= true)

    if isSwitchMode then
        self:ShowCountDown()
    end


    self.isInit = true;
    local oversea_res = require "oversea_res"
    if (oversea_res.ISOverSeaLang()) then
        self:ChangeLangRes(results)
    else
        if (results) then
            for i, v in ipairs(results) do
                if self["winTxt" .. i] then
                    self["winTxt" .. i].gameObject:SetActive(lang.USE_LANG == lang.ZH)
                end
                if self["winTxt" .. i .. "En"] then
                    self["winTxt" .. i .. "En"].gameObject:SetActive(lang.USE_LANG ~= lang.ZH)
                end
                if self["winTxt" .. i] and lang.USE_LANG == lang.ZH then
                    self["winTxt" .. i]:Switch((v and v.isVictory) and 0 or 1)
                end
                if self["winTxt" .. i .. "En"] and lang.USE_LANG ~= lang.ZH then
                    self["winTxt" .. i .. "En"]:Switch((v and v.isVictory) and 0 or 1)
                end
            end
        end
    end

    --设置关卡
    local level = battle_data.GetStageLevel()
    if level ~= 0 then
        self.LevelText.text = string.format("%s %d", lang.Get(602039), level)
    end
end

function BattleVictory:ChangeLangRes(results)
    local oversea_res = require "oversea_res"
    local res = "victory_shengli"
    local callBack = function(asset)
        if self:IsValid() then
            if asset then
                self.shengli.transform:GetComponent("Image").sprite = asset
            end
        end
    end
    if oversea_res.LoadSprite(res, "ui_mini_game_battle_victory", callBack) then
        self.shengli:Switch(lang.USE_LANG == lang.ZH and 0 or 1)
    end
    if results then
        local res1 = "jiesuan_sheng_pic"
        local res2 = "jiesuan_fu_pic"
        local tab1 = {}
        local tab2 = {}
        for i, v in ipairs(results) do
            if self["winTxt" .. i .. "En"] then
                if v and v.isVictory then
                    table.insert(tab1, self["winTxt" .. i .. "En"])
                else
                    table.insert(tab2, self["winTxt" .. i .. "En"])

                end
            end
        end
        local callBack1 = function(asset)
            if self:IsValid() then
                if asset then
                    for i, v in ipairs(tab1) do
                        if v and not util.IsObjNull(v) then
                            v.transform:GetComponent("Image").sprite = asset
                        end
                    end

                end
            end
        end
        local callBack2 = function(asset)
            if self:IsValid() then
                if asset then
                    for i, v in ipairs(tab2) do
                        if v and not util.IsObjNull(v) then
                            v.transform:GetComponent("Image").sprite = asset
                        end
                    end

                end
            end
        end
        oversea_res.LoadSprite(res1, "ui_mini_game_battle_victory", callBack1)
        oversea_res.LoadSprite(res2, "ui_mini_game_battle_victory", callBack2)
    end
end

function BattleVictory:InitBattleData()
    if showType ~= common_new_pb.GameGoal and nBattleType ~= common_new_pb.ChinaRed then
        --self.listSkin.anchoredPosition = {x=0,y=-273}
        self.listContentLayoutGroup.padding.bottom = 25
        self.listContentLayoutGroup.enabled = true
        self.listContentFitter.enabled = true
        self.listScrollTable.enabled = false
        if self.ListInstance == nil then
            self.ListInstance = CScrollList:CreateInstance({})
            self.ListInstance:Create(self.listSkin, CBattleVictoryScollListItem)
        end
        self.ListInstance:SetListData(BattleData)
        self.listSkin.gameObject:SetActive(battleRsp == nil or #BattleData > 0)
        -- self.contentLayout.childAlignment = 3
        -- self.listContent.pivot = {x=0,y=0.5}
        -- self.contentLayout.padding.left = 4
        if #BattleData > 0 and #BattleData <= 5 then
            local width = (#BattleData * 105) + (#BattleData - 1) * 20
            self.viewportRect.sizeDelta = { x = width, y = self.viewportRect.sizeDelta.y } --调整宽高
        end
        for i = 0, self.listContent.childCount - 1 do
            self.listContent:GetChild(i).gameObject:SetActive(false)
            ui_setting_util.SetTransparency(self.listContent, 0)
            ------ --print("Pivot",self.listContent.pivot,self.contentLayout.padding.left,self.viewportRect.sizeDelta,"childAlignment",self.contentLayout.childAlignment)
        end
        local modelPath = "art/effects/prefabs/ui/effect_ui_huodedaoju_02.prefab"
        show_animation.PlayAni("battle_victory_ani", self.listContent, modelPath, self.curOrder, #BattleData)
    else
        self:UpdateStarInfo()
        if #BattleData > 0 and #BattleData <= 6 then
            --self.listSkin.anchoredPosition = {x=0,y=-273}
            self.listContentLayoutGroup.padding.bottom = 25
            self.listContentLayoutGroup.enabled = true
            self.listContentFitter.enabled = true
            self.listScrollTable.enabled = false
            if self.ListInstance == nil then
                self.ListInstance = CScrollList:CreateInstance({})
                self.ListInstance:Create(self.listSkin, CBattleVictoryScollListItem)
            end
            self.ListInstance:SetListData(BattleData)
            self.listSkin.gameObject:SetActive(battleRsp == nil or #BattleData > 0)
            -- self.contentLayout.childAlignment = 3
            -- self.listContent.pivot = {x=0,y=0.5}
            -- self.contentLayout.padding.left = 4
            if #BattleData <= 5 then
                local width = (#BattleData * 105) + (#BattleData - 1) * 20
                self.viewportRect.sizeDelta = { x = width, y = self.viewportRect.sizeDelta.y } --调整宽高
            end
            for i = 0, self.listContent.childCount - 1 do
                self.listContent:GetChild(i).gameObject:SetActive(false)
                ui_setting_util.SetTransparency(self.listContent, 0)
                ------ --print("Pivot",self.listContent.pivot,self.contentLayout.padding.left,self.viewportRect.sizeDelta,"childAlignment",self.contentLayout.childAlignment)
            end
            local modelPath = "art/effects/prefabs/ui/effect_ui_huodedaoju_02.prefab"
            show_animation.PlayAni("battle_victory_ani", self.listContent, modelPath, self.curOrder, #BattleData)
        elseif #BattleData > 6 then
            self.gameGoallistSkin.gameObject:SetActive(true)
            self.listSkin.gameObject:SetActive(false)
            local scrollTableData = {}
            for k, v in pairs(BattleData) do
                table.insert(scrollTableData, v)
            end
            -- self.listScrollTable.columns = #scrollTableData
            self.gameGoallistScrollTable.pageSize = #scrollTableData + 1
            self.gameGoallistScrollTable.data = scrollTableData

            self.gameGoallistScrollTable:Refresh(-1, -1)
            local modelPath = "art/effects/prefabs/ui/effect_ui_huodedaoju_02.prefab"
            show_animation.PlayAni("battle_victory_ani", self.gameGoallistContent, modelPath, self.curOrder, #BattleData)
        end
    end
end

function BattleVictory:ShowCountDown()
    local target = util.GetServerTime() + game_scheme:InitBattleProp_0(853).szParam.data[0]
    self.ticker = util.IntervalCall(1, function()
        if self:IsValid() then
            local time = target - util.GetServerTime()
            if time > 0 then
                self.countDown.text = string.format(lang.Get(2156), math.floor(time))
            else
                self.confirmBtnEvent()
                return true
            end
        end
    end)
end

function BattleVictory:SwitchPage()
    -- --print("SwitchPage curTeamIndex:", curTeamIndex, "nBattleType:", nBattleType)
   -- if nBattleType == common_new_pb.Maze or nBattleType == common_new_pb.GameGoal or nBattleType == common_new_pb.EquipEctypeZhuanShu then
        self:UpdateBattleDetails(curType)
        --self.OneBtn.isOn = curTeamIndex == 1 and #MultiHeroData >= 2
        --self.TwoBtn.isOn = curTeamIndex == 2 and #MultiHeroData >= 2
        --self.ThreeBtn.isOn = curTeamIndex == 3 and #MultiHeroData >= 3
        --self.FourBtn.isOn = curTeamIndex == 4 and #MultiHeroData >= 4
        --
        --self.txtOut1.effectColor = self.OneBtn.isOn and selColor or unSelColor
        --self.txtOut2.effectColor = self.TwoBtn.isOn and selColor or unSelColor
        --self.txtOut3.effectColor = self.ThreeBtn.isOn and selColor or unSelColor
        --self.txtOut4.effectColor = self.FourBtn.isOn and selColor or unSelColor

    --    self:UpdataHeroData()
    --else
    --    self:UpdateBattleDetails(TogType.ENUM_REWARD)
    --end
end

function BattleVictory:SubscribeEvents()
    self.onOKClick = function()
        windowMgr:UnloadModule("ui_mini_game_battle_victory")
        minigame_mgr.MiniGameClose()
        --GWG.GWMgr.ShowCurScene()

        local gw_zombie_treasure_data = require "gw_zombie_treasure_data"
        local laymain_data = require "laymain_data"
        local isShow = gw_zombie_treasure_data.GetMiniGamePassLevel() ~=  laymain_data.GetPassLevel()
        event.Trigger(event.BATTLEFINAL,common_new_pb.GameGoal,isShow)
        event.Trigger(event.BATTLE_RESULT_CLOSE)
        local result = minigame_mgr.GetGameResult()
        event.Trigger(event.MINIGAME_CLOSE,result)
        

        --关闭战报事件
       -- event.Trigger(event.RESULT_WINDOW_CLOSE, true)

        --新手强制引导事件
      --  force_guide_system.TriComEvent(force_guide_event.cEventClickOk)

    end
    self.okBtn.onClick:AddListener(self.onOKClick)
    self.btnClose.onClick:AddListener(self.onOKClick)

    self.confirmBtnEvent = function()

        windowMgr:UnloadModule("ui_mini_game_battle_victory")
        minigame_mgr.MiniGameClose()
        --GWG.GWMgr.ShowCurScene()
        event.Trigger(event.BATTLE_RESULT_CLOSE)
        local result = minigame_mgr.GetGameResult()
        event.Trigger(event.MINIGAME_CLOSE,result)
    end
    self.widget_table["confirmBtn"].event_name = "confirmBtnEvent"

    self.backBtnEvent = function()
        CheckIsClearSeriesData()
        local battle_switch_manager = require "battle_switch_manager"
        battle_switch_manager.Jump(nBattleType)
        event.Trigger(event.BATTLE_RESULT_CLOSE)
        local result = minigame_mgr.GetGameResult()
        event.Trigger(event.MINIGAME_CLOSE,result)
    end
    self.widget_table["backBtn"].event_name = "backBtnEvent"

    self.showStatisticsClick = function()
        windowMgr:ShowModule("ui_battle_result")
        local ui_battle_result = require "ui_battle_result"
        if #MultiHeroData > 1 then
            ui_battle_result.SetMultiData(MultiHeroData, curTeamIndex)
        else
            ui_battle_result.SetData(HeroData)
        end
    end
    self.statisticsBtn.onClick:AddListener(self.showStatisticsClick)

    self.replayBtnClick = function()
        isClickReplayBtn = true
        message_box.Open(lang.Get(18520), message_box.STYLE_YESNO, function(callbackData, nRet)
            if message_box.RESULT_YES == nRet then
                isReplay = true
                local battle_series_manager = require "battle_series_manager"
                local isSeriesBattle = battle_series_manager.IsSeriesBattle(nBattleType)
                windowMgr:UnloadModule("ui_mini_game_battle_victory")
                local battle_manager = require "battle_manager"
                local ui_maze_multi_battle_result = require "ui_maze_multi_battle_result"
                local results = ui_maze_multi_battle_result.GetMultiBattleResult()
                local count = results and #results or 0
                if count > 1 and not isSeriesBattle then
                    battle_manager.StartMultiBattleReplay(curTeamIndex)
                else
                    battle_manager.StartBattleReplay()
                    --battle_manager.StartAllWaveBattleReplay()
                end
            end
        end, 0, lang_res_key.KEY_OK, lang_res_key.KEY_CANCEL)
    end
    self.replayBtn.onClick:AddListener(self.replayBtnClick)

    self.shareBtnClick = function()
        -- local ui_share = require "ui_share"
        -- local battleStarVal = nil
        -- if nBattleType == common_new_pb.ChinaRed then
        --     local fleet_expedition_data = require "fleet_expedition_data"
        --     _, _, battleStarVal = fleet_expedition_data.GetBattleStar()
        -- end
        -- ui_share.ShareBattle(battleID, opponentName, "1", nBattleType, nil, battleStarVal)
    end
    self.shareBtn.onClick:AddListener(self.shareBtnClick)

    self.OnAttackToggleChange = function()
        if self.attackTog.isOn then
            self:UpdateBattleDetails(ENUM_ATTACK)
        end
    end
    self.attackTog.onValueChanged:AddListener(self.OnAttackToggleChange)

    self.OnDefendToggleChange = function()
        if self.defendTog.isOn then
            self:UpdateBattleDetails(EUNM_DEFENT)
        end
    end
    self.defendTog.onValueChanged:AddListener(self.OnDefendToggleChange)

    self.showDetailBtnClick = function()
        self.detailList.gameObject:SetActive(true)
        self.closeDetailBtn.gameObject:SetActive(true)
    end
    self.showDetailBtn.onClick:AddListener(self.showDetailBtnClick)

    self.closeDetailBtnClick = function()
        self.closeDetailBtn.gameObject:SetActive(false)
        if self.detailList.gameObject.activeSelf == true then
            self.detailList.gameObject:SetActive(false)
            return
        end
    end
    self.closeDetailBtn.onClick:AddListener(self.closeDetailBtnClick)

    self.EnemyViewOneStateEvent = function(isOn)
        if not isOn then
            return
        end
        --切换切页
        curTeamIndex = 1
        local ui_maze_multi_battle_result = require "ui_maze_multi_battle_result"
        ui_maze_multi_battle_result.switchPageOpt = true
        ui_maze_multi_battle_result.SwitchPage(curTeamIndex)
        ui_maze_multi_battle_result.switchPageOpt = false
        self:SwitchPage()
    end
    self.OneBtn.onValueChanged:AddListener(self.EnemyViewOneStateEvent)
    self.EnemyViewTwoStateEvent = function(isOn)
        if not isOn then
            return
        end
        --切换切页
        curTeamIndex = 2
        local ui_maze_multi_battle_result = require "ui_maze_multi_battle_result"
        ui_maze_multi_battle_result.switchPageOpt = true
        ui_maze_multi_battle_result.SwitchPage(curTeamIndex)
        self:SwitchPage()
        ui_maze_multi_battle_result.switchPageOpt = false
    end
    self.TwoBtn.onValueChanged:AddListener(self.EnemyViewTwoStateEvent)
    self.EnemyViewThreeStateEvent = function(isOn)
        if not isOn then
            return
        end
        --切换切页
        curTeamIndex = 3
        local ui_maze_multi_battle_result = require "ui_maze_multi_battle_result"
        ui_maze_multi_battle_result.switchPageOpt = true
        ui_maze_multi_battle_result.SwitchPage(curTeamIndex)
        self:SwitchPage()
        ui_maze_multi_battle_result.switchPageOpt = false
    end
    self.ThreeBtn.onValueChanged:AddListener(self.EnemyViewThreeStateEvent)

    self.EnemyViewFourStateEvent = function(isOn)
        if not isOn then
            return
        end
        --切换切页
        curTeamIndex = 4
        local ui_maze_multi_battle_result = require "ui_maze_multi_battle_result"
        ui_maze_multi_battle_result.switchPageOpt = true
        ui_maze_multi_battle_result.SwitchPage(curTeamIndex)
        self:SwitchPage()
        ui_maze_multi_battle_result.switchPageOpt = false
    end
    self.FourBtn.onValueChanged:AddListener(self.EnemyViewFourStateEvent)


    --位面2新增
    self.OnRewardToggleChange = function()
        if self.RewardTog.isOn then
            self:UpdateBattleDetails(TogType.ENUM_REWARD)
        end
    end
    self.OnDamageToggleChange = function()
        if self.DamageTog.isOn then
            self:UpdateBattleDetails(TogType.ENUM_DAMAGE)
        end
    end
    self.OnHurtToggleChange = function()
        if self.HurtTog.isOn then
            self:UpdateBattleDetails(TogType.ENUM_HURT)
        end
    end
    self.RewardTog.onValueChanged:AddListener(self.OnRewardToggleChange)
    self.DamageTog.onValueChanged:AddListener(self.OnDamageToggleChange)
    self.HurtTog.onValueChanged:AddListener(self.OnHurtToggleChange)

    self.OnNextlevel = function()
        local equipment_mgr = require "equipment_mgr"
        if equipment_mgr.CheckEquipIsFull() == true then
            equipment_mgr.ShowEquipFullTip()
            return
        end
        local const = require "const"
        local laymain_data = require "laymain_data"
        local hookGrade = laymain_data.GetPassLevel() + 1
        if const.OPEN_NEW_HOOK_SCENE then
            local new_hook_scene = require("new_hook_scene")
            new_hook_scene.OnFightClicked(hookGrade,true)
        else
            local laymain_top_scene = require("laymain_top_scene")
            laymain_top_scene.OnFightClicked(hookGrade)
        end 
        local gw_zombie_treasure_data = require "gw_zombie_treasure_data"
        local laymain_data = require "laymain_data"
        local isShow = gw_zombie_treasure_data.GetMiniGamePassLevel() ~=  laymain_data.GetPassLevel()
        gw_zombie_treasure_data.SetStarsShow(isShow)
        windowMgr:UnloadModule("ui_mini_game_battle_victory")
        minigame_mgr.MiniGameClose()
        --GWG.GWMgr.ShowCurScene()
        event.Trigger(event.BATTLE_RESULT_CLOSE)
        local result = minigame_mgr.GetGameResult()
        event.Trigger(event.MINIGAME_CLOSE,result)
    end
    self.nextBtn.onClick:AddListener(self.OnNextlevel)
end

function BattleVictory:UnsubscribeEvents()
    self.okBtn.onClick:RemoveListener(self.onOKClick)
    self.statisticsBtn.onClick:RemoveListener(self.showStatisticsClick)
    self.replayBtn.onClick:RemoveListener(self.replayBtnClick)
    self.attackTog.onValueChanged:RemoveListener(self.OnAttackToggleChange)
    self.defendTog.onValueChanged:RemoveListener(self.OnDefendToggleChange)
    self.shareBtn.onClick:RemoveListener(self.shareBtnClick)
    self.showDetailBtn.onClick:RemoveListener(self.showDetailBtnClick)
    self.closeDetailBtn.onClick:RemoveListener(self.closeDetailBtnClick)
    self.OneBtn.onValueChanged:RemoveListener(self.EnemyViewOneStateEvent)
    self.TwoBtn.onValueChanged:RemoveListener(self.EnemyViewTwoStateEvent)
    self.ThreeBtn.onValueChanged:RemoveListener(self.EnemyViewThreeStateEvent)
    self.FourBtn.onValueChanged:RemoveListener(self.EnemyViewFourStateEvent)

    --位面2新增
    self.RewardTog.onValueChanged:RemoveListener(self.OnRewardToggleChange)
    self.DamageTog.onValueChanged:RemoveListener(self.OnDamageToggleChange)
    self.HurtTog.onValueChanged:RemoveListener(self.OnHurtToggleChange)
    self.nextBtn.onClick:RemoveListener(self.OnNextlevel)

    if self.heroFaceList then
        for k, v in pairs(self.heroFaceList) do
            if v then
                v:Dispose()
                v = nil
            end
        end
    end
    self.heroFaceList = nil
end

function OnPassLevelEvent()
    isNewLevel = true
    if window and window:IsValid() then
        window:SetTipsUI(showType)
    end
end
event.Register(event.CHANGE_PASS_LEVEL, OnPassLevelEvent)

function BattleVictory:SetTipsUI(battleType)
    self.chapterRewardTrans.gameObject:SetActive(false)
    return
    --[[local common_pb = require "common_new_pb"
    self.chapterRewardTrans.gameObject:SetActive(battleType == common_pb.GameGoal)
    if battleType == common_pb.SpaceGap then
        -- self.spaceGapTip.gameObject:SetActive(true)
    elseif battleType == common_pb.GameGoal then
        -- self.spaceGapTip.gameObject:SetActive(false)
        local laymain_data = require "laymain_data"
        local passLv = laymain_data.GetPassLevel()
        local lvCfg = game_scheme:HookLevel_0(passLv)
        if lvCfg then
            local chestID = laymain_data.GetChapterShowChestID(lvCfg.mapID)
            local chestCfg = game_scheme:HookLevelChest_0(chestID)
            local reward_list = {}
            if chestCfg then
                for i = 0, #chestCfg.RewardID.data do
                    local rewardID = chestCfg.RewardID.data[i]
                    table.insert(reward_list, rewardID)
                end
            end
            self.chapterRewardList.data = reward_list
            self.chapterRewardList:Refresh(-1, -1)

            self.chapterNameText.text = string.format(lang.Get(7093), lvCfg.mapID)
            local targetLvCfg = chestCfg and game_scheme:HookLevel_0(chestCfg.ConditionValue)
            if targetLvCfg then
                self.chapterprogressValueText.text = string.format("%s/%s", lvCfg.checkpoint, targetLvCfg.checkpoint)
                self.chapterprogressValue.transform.localScale = { x = lvCfg.checkpoint / targetLvCfg.checkpoint, y = 1, z = 1 }
            end
        end
    end]]
end

function BattleVictory:ShowHeroCard(closeFun)
    local arrReward = {}
    for k, v in ipairs(BattleData) do
        local rewardCfg = game_scheme:Reward_0(v.rewardID)
        if rewardCfg and rewardCfg.iRewardType == 2 then
            table.insert(arrReward, rewardCfg)

            --飘图标
            --[[                local rewardData={}
                    rewardData.sid=nil
                    rewardData.id=rewardCfg.arrParam[0]
                    rewardData.num=1
                    rewardData.nType=item_data.Reward_Type_Enum.Hero
                    rewardData.starLevel=rewardCfg.arrParam[1]
                    local flow_good = require "flow_good"
                    flow_good.Add({rewardData})
                ]]
        end
    end
    --容错处理(第二次点击直接关闭)
    self.ClickCloseTimes = self.ClickCloseTimes or 0

    if #arrReward > 0 and self.ClickCloseTimes < 1 then
        self.ClickCloseTimes = self.ClickCloseTimes + 1

        local flow_text = require "flow_text"
        flow_text.Add(lang.Get(9363))
        self.effectTrans:SetActive(false)
        local ui_show_hero_card = require "ui_show_hero_card"
        ui_show_hero_card.ShowWithData(ui_show_hero_card.ParseArrRewardID(arrReward), function()
            closeFun()
        end, nil, true)
    else
        closeFun()
    end
end

function BattleVictory:BuildPassTxtData()
    local task_data = require "task_data"
    local data = task_data.GetAchiList()
    local canShowData = {}
    local table1 = {}
    local table2 = {}
    local listData = {}
    for i, v in pairs(data) do
        ------print(#data,showType)
        local cfg = game_scheme:Achievement_0(v.achieveId)
        if cfg and cfg.nIsShow ~= 0 and cfg.needShowType ~= 0 and showType == cfg.needShowType and v.achieveState ~= 1 then
            ------ --print("成就数据",i,v.achieveId,v.currentValue,v.achieveState,cfg.nIsShow,cfg.needShowType)
            table.insert(canShowData, v)
        end
    end
    table.sort(canShowData, function(t1, t2)
        return t1.achieveId < t2.achieveId
    end)
    for i, v in pairs(canShowData) do
        ------print(#data,showType)
        local cfg = game_scheme:Achievement_0(v.achieveId)
        local conditionValue = cfg.nConditionValue
        local preTaskConditionValue = cfg.nPreTaskID ~= -1 and task_data.GetAchievementConditionValue(cfg.nPreTaskID) or nil
        if cfg.nConditionType == 31 then
            --狩猎数据单独处理
            local cfg_hook_cur = game_scheme:HookLevel_0(v.currentValue)
            if cfg_hook_cur then
                v.currentValue = cfg_hook_cur.checkNumber
            end
            local cfg_hook_last = game_scheme:HookLevel_0(cfg.nConditionValue)
            if cfg_hook_last then
                conditionValue = cfg_hook_last.checkNumber
            end
            if preTaskConditionValue then
                local cfg_hook_before = game_scheme:HookLevel_0(preTaskConditionValue)
                if cfg_hook_before then
                    preTaskConditionValue = cfg_hook_before.checkNumber
                end
            end
        end
        local tempTable = { txt = nil, isFinish = 0, achieveId = v.achieveId }
        if v.currentValue <= conditionValue and cfg.nPreTaskID == -1 or (cfg.nPreTaskID ~= -1 and v.currentValue > preTaskConditionValue) then
            if cfg.nIsShow == 1 then
                tempTable.txt = "[" .. v.currentValue .. "/" .. conditionValue .. "] " .. lang.Get(cfg.nDesc)
                if v.currentValue >= conditionValue then
                    tempTable.isFinish = 1
                end
                --table1 = tempTable
                -- 修改 类型1的 需要区分条件类型，每种条件类型只显示id最大的一个
                for i, data in ipairs(table1) do
                    local cfg_ach = game_scheme:Achievement_0(data.achieveId)
                    if cfg_ach.nConditionType == cfg.nConditionType then
                        table.remove(table1, i)
                        break
                    end
                end

                table.insert(table1, tempTable)
            end
            if cfg.nIsShow == 2 then
                if v.currentValue >= conditionValue then
                    tempTable.txt = "[" .. v.currentValue .. "/" .. conditionValue .. "] " .. lang.Get(cfg.nDesc)
                    tempTable.isFinish = 1
                    table2 = { tempTable }
                end
            end
        end
    end

    -- 规则 显示类型为2的显示id最大的一条成就，显示类型为1的显示不同条件类型的成就数据
    if #table2 > 0 then
        for i, data in ipairs(table1) do
            local cfg_ach = game_scheme:Achievement_0(data.achieveId)
            local cfg_finish = game_scheme:Achievement_0(table2[1].achieveId)
            if cfg_ach.nConditionType == cfg_finish.nConditionType then
                if data.achieveId > table2[1].achieveId then
                    table2 = {}
                else
                    table.remove(table1, i)
                end
                break
            end
        end
    end

    for i, data in ipairs(table1) do
        if data.txt then
            table.insert(listData, data)
        end
    end
    for i, data in ipairs(table2) do
        if data.txt then
            table.insert(listData, data)
        end
    end

    table.sort(listData, function(t1, t2)
        return t1.isFinish > t2.isFinish
    end)

    return listData
end


--[[加载结算特效]]
function BattleVictory:LoadResultEffect(resPath)
    if self.effectModelViewer then
        self.effectModelViewer:Dispose()
        self.effectModelViewer = nil
    end

    if self.effectModelViewer == nil then
        self.effectModelViewer = CModelViewer()
        self.effectModelViewer:Init(self.effectTrans.transform, function()
            self.effectModelViewer:ShowGameObject(resPath, function(goEffect)
                if not self.ShowStart then 
                    self.effectTrans:SetActive(true)
                else
                    self.effectTrans:SetActive(false)
                end
                --self.effectTrans:SetActive(true)
                local sortingGroup = goEffect:GetComponent(typeof(SortingGroup))
                if not sortingGroup or util.IsObjNull(sortingGroup) then
                    sortingGroup = goEffect:AddComponent(typeof(SortingGroup))
                end
                sortingGroup.sortingOrder = self.orderEnd
                self.detailCanvas.sortingOrder = self.orderEnd + 1
            end)
        end)
    end
end

--[[加载星星特效]]
function BattleVictory:ShowStarEffect(starCount)
    if self.starEffect then
        self.starEffect:Dispose()
        self.starEffect = nil
    end
    --加载特效并播放
    local modelPaths = {
        "art/effects/prefabs/ui/ui_jiesuanxingxing_yixing.prefab",
        "art/effects/prefabs/ui/ui_jiesuanxingxing_erxing.prefab",
        "art/effects/prefabs/ui/ui_jiesuanxingxing_sanxing.prefab",
    }
    local modelPos = {
        { x = 3.58, y = 12.7, z = 0 },
        { x = 0, y = 0, z = 0 },
        { x = 0, y = 0, z = 0 },
    }
    if self.starEffect == nil then
        self.starEffect = CModelViewer()
        self.starEffect:Init(self.effectTrans.transform, function()
            self.starEffect:ShowGameObject(modelPaths[starCount], function(goEffect)
                --self.effectTrans:SetActive(false)
                self.effectTrans:SetActive(true)
                local sortingGroup = goEffect:GetComponent(typeof(SortingGroup))
                if not sortingGroup or util.IsObjNull(sortingGroup) then
                    sortingGroup = goEffect:AddComponent(typeof(SortingGroup))
                end
                goEffect.transform.localPosition = modelPos[starCount]
                sortingGroup.sortingOrder = self.orderEnd + 1
                -- 设置详情canvas
                self.detailCanvas.sortingOrder = self.orderEnd + 2
            end)
        end)
    end
end

function BattleVictory:UpdataHeroData()
    local AttackerID = battle_data.attacker
    local DefenderID = battle_data.defender
    local roleID = player_mgr.GetPlayerRoleID()
    local lowerLimit = 0
    local upperLimit = 5
    if roleID == DefenderID then
        lowerLimit = 6
        upperLimit = 11
    end
    local damage = 0
    for i = lowerLimit, upperLimit do
        local data = HeroData[i]
        -- i<6:攻击方；i>=6:防守方
        if data ~= nil then
            damage = damage + data.statistics.damage
        end
    end
    local zhanjianPower = nil
    if HeroData[13] then
        zhanjianPower = HeroData[13].statistics.damage
        damage = zhanjianPower + damage
    end
    -- self.damage.text = lang.Get(2104) .. damage
    self.damageBG:SetActive(true)
    self.damage.text = string.format(lang.Get(204006), util.PriceConvert(damage))
end

-- 显示星星评价  在位面战纪和星际通缉结算使用(屏蔽星级通缉星星显示)
function BattleVictory:UpdateStarInfo()
    local teamid = 0
    local battle_data = require "battle_data"
    if showType == common_new_pb.GameGoal then
        local cfg = game_scheme:HookLevel_0(battle_data.stageLv)
        teamid = cfg and cfg.ranksID and cfg.ranksID.data[0] or 0
    end
    if showType == common_new_pb.TheTowerOfIllusion then
        local cfg = game_scheme:IllusionTower_0(battle_data.stageLv)
        teamid = cfg and cfg.Teampid or 0
    end
    if nBattleType == common_new_pb.ChinaRed then
        local cfg = game_scheme:Odyssey_0(battle_data.stageLv)
        teamid = cfg and cfg.monsterID or 0
    end
    local monsterTeamCfg = game_scheme:monsterTeam_0(teamid)
    -- if nBattleType == common_new_pb.ChinaRed then
    --     if not starCount or starCount == 0 then
    --         local fleet_expedition_data = require "fleet_expedition_data"
    --         starCount = fleet_expedition_data.GetBattleStar()
    --         -- --print("battle_data.stageLv",battle_data.stageLv,"starCount",starCount)
    --         if not starCount or starCount == 0 then
    --             local hookStarDatas = fleet_expedition_data.GetIdleStar()
    --             local data = hookStarDatas and battle_data.stageLv and hookStarDatas[battle_data.stageLv]
    --             if data then
    --                 starCount = data.count
    --             end
    --         end
    --     else
    --         -- --print("battle_data.stageLv",battle_data.stageLv,"starCount",starCount)
    --     end
    -- end
    if not monsterTeamCfg or monsterTeamCfg.gradeEv == "" then
        -- self.titleImg.gameObject:SetActive(true)
        self.xingxingTrans.gameObject:SetActive(false)
        --加载特效并播放
        local modelPath = "art/effects/prefabs/ui/ui_zhandoushenglixin.prefab"
        if lang.USE_LANG ~= lang.ZH then
            modelPath = nil --"art/effects/prefabs/ui/ui_zhandoushenglixin_en.prefab"
        end
        if(modelPath)then
            self:LoadResultEffect(modelPath)
        end
        if starCount and starCount > 0 then
            --加载特效并播放
            local modelPath = "art/effects/prefabs/ui/ui_jiesuanxingxing_xin.prefab"
            if lang.USE_LANG ~= lang.ZH then
                modelPath = nil --"art/effects/prefabs/ui/ui_jiesuanxingxing_xinen.prefab"
            end
            if(modelPath)then
                if not self.ShowStart then 
                    self.effectTrans:SetActive(true)
                end
                self:LoadResultEffect(modelPath)
            end
            if self.ShowStart then 
                self:ShowStarEffect(starCount)
            end
        end
        return
    end
    --加载特效并播放
    -- local modelPath = "art/effects/prefabs/ui/ui_jiesuanxingxing_xin.prefab"
    -- if lang.USE_LANG ~= lang.ZH then
    --     modelPath = "art/effects/prefabs/ui/ui_jiesuanxingxing_xinen.prefab"
    -- end
    -- self:LoadResultEffect(modelPath)
    if nBattleType == common_new_pb.ChinaRed then
        if starCount and starCount > 0 then
            self:ShowStarEffect(starCount)
        end
        return
    end
    local deadCount = 0
    local allHeroCount = 0
    for i = 0, 5 do
        local heroData = battle_data.GetHeroByPos(i)
        if heroData then
            allHeroCount = allHeroCount + 1--上阵英雄数量
        end
        if heroData and heroData.statistics.isDead == true then
            deadCount = deadCount + 1--获取死亡英雄数量
        end
    end
    local roundTimes = battle_data.GetBattleRound()--获取战斗回合数
    local strKeys = util.SplitString(monsterTeamCfg.gradeEv, ";")
    -- 数据设置
    local gradeData = {}
    -- 战斗评价(1 战斗胜利  2 回合数少于等于 3上阵英雄数量少于等于 4 阵亡英雄数少于等于  5 存活英雄大于等于)
    local tempStarCount = 0
    if strKeys ~= nil then
        for i = 1, #strKeys do
            local tempData = { nType = 0, text = "", isFinish = false }
            local arr = util.SplitString(strKeys[i], "#")
            tempData.nType = arr[1]
            if arr[1] == "1" then
                tempStarCount = tempStarCount + 1
                tempData.text = lang.Get(7081)
                tempData.isFinish = true
            end
            if arr[1] == "2" then
                tempData.text = string.format(lang.Get(7082), arr[2])
                if roundTimes <= tonumber(arr[2]) then
                    tempStarCount = tempStarCount + 1
                    tempData.isFinish = true
                end
            end
            if arr[1] == "3" then
                tempData.text = string.format(lang.Get(7085), arr[2])
                if allHeroCount <= tonumber(arr[2]) then
                    tempStarCount = tempStarCount + 1
                    tempData.isFinish = true
                end
            end
            if arr[1] == "4" then
                tempData.text = deadCount > 0 and string.format(lang.Get(7083), arr[2]) or string.format(lang.Get(7084), arr[2])
                if deadCount <= tonumber(arr[2]) then
                    tempStarCount = tempStarCount + 1
                    tempData.isFinish = true
                end
            end
            if arr[1] == "5" then
                local aliveCount = allHeroCount - deadCount
                tempData.text = string.format(lang.Get(7086), arr[2])
                if aliveCount >= tonumber(arr[2]) then
                    tempStarCount = tempStarCount + 1
                    tempData.isFinish = true
                end
            end
            table.insert(gradeData, tempData)
        end
    end

    -- 星星显示设置
    --[[    self.stars = {self.star1, self.star2, self.star3}
    for i = 1, 3 do
        self.stars[i].gameObject:SetActive(i <= starCount)
    end
    --]]
    if not starCount or starCount == 0 then
        starCount = tempStarCount
    end
    local isShowStar
    for i = 1, 3, 1 do
        isShowStar = starCount >= i
        self["star" .. i]:SetActive(isShowStar)
        self["notstar" .. i]:SetActive(not isShowStar)
    end
    if starCount and starCount > 0 then
        self:ShowStarEffect(starCount)
    end
    -- 详情显示设置
    maxWidth = 0
    self.StarDetailListInstance:SetListData(gradeData)
    if maxWidth <= 0 then
        maxWidth = 230
    end
    self.detailList.sizeDelta = { x = maxWidth + 100, y = self.detailList.sizeDelta.y }
end

function SetCloseCallback(callback)
    closeCallback = callback
end

function BattleVictory:Close()
    if not isClickReplayBtn then
        --界面关闭的时候，清除多队战斗信息，保证观看录像的时候正常显示录像信息
        local ui_maze_multi_battle_result = require "ui_maze_multi_battle_result"
        ui_maze_multi_battle_result.ResetMultiBattle()
    end
    isClickReplayBtn = false
    local battle_message = require "battle_message"
    local battle_data = require "battle_data"
    if showType == common_new_pb.GameGoal and not isReplay then
        battle_data.Clear()
        --battle_message.SetBattleResult(nil)
        isReplay = false
    elseif showType ~= common_new_pb.GameGoal then
        battle_data.Clear()
    end
    show_animation.ClearData()
    event.Trigger(event.ASH_HIDE_EFFECT, false)
    if self.UIRoot then
        self:UnsubscribeEvents()

        curType = TogType.ENUM_REWARD
        if self.chapterRewardList then
            self.chapterRewardList:ItemsDispose()
        end
        if self.gameGoallistScrollTable then
            self.gameGoallistScrollTable:ItemsDispose()
        end

        if countTimer then
            countTimer:Dispose()
            countTimer = nil
        end
        for i, v in ipairs(flyEffectList) do
            if v then
                v:Dispose()
                v = nil
            end
        end
        flyEffectList = {}
        if self.heroes then
            for k, v in pairs(self.heroes) do
                if v then
                    v:Dispose()
                    v = nil
                end
            end
            self.heroes = nil
        end
        if self.heroGrade then
            self.heroGrade = nil
        end
        if self.canGetItemUI then
            for i, v in pairs(self.canGetItemUI) do
                if v then
                    v:Dispose()
                    v = nil
                end
            end
        end
    end
    if self.ListInstance then
        self.ListInstance:ReleaseInstance()
        self.ListInstance = nil
    end

    if self.PassTxtListInstance then
        self.PassTxtListInstance:ReleaseInstance()
        self.PassTxtListInstance = nil
    end

    if self.StarDetailListInstance then
        self.StarDetailListInstance:ReleaseInstance()
        self.StarDetailListInstance = nil
    end

    if self.goodAsset then
        self.goodAsset:Dispose()
        self.goodAsset = nil
    end

    if self.ticker then
        util.RemoveDelayCall(self.ticker)
        self.ticker = nil
    end

    if closeCallback then
        closeCallback()
        closeCallback = nil
    end
    starCount = nil
    arenaRsp = nil
    BattleData = {}
    HeroData = {}
    battleID = nil
    opponentName = nil
    nBattleType = nil
    MultiHeroData = {}
    isSwitchMode = nil

    if self.effectModelViewer then
        self.effectModelViewer:Dispose()
        self.effectModelViewer = nil
    end

    if self.starEffect then
        self.starEffect:Dispose()
        self.starEffect = nil
    end
    if self.mazeIconUI then
        self.mazeIconUI:Dispose()
        self.mazeIconUI = nil
    end

    local story_mgr = require "story_mgr"
    local common_pb = require "common_new_pb"
    local lv = story_mgr.GetCacheLevel()
    local battleType = story_mgr.GetBattleType()
    if battleType == common_pb.GameGoal then
        -- 主綫
        story_mgr.PlayStory(1, lv)
    elseif battleType == common_new_pb.TheTowerOfIllusion then
        -- 通緝
        story_mgr.PlayStory(6, lv)
    end

    local new_hook_scene = require "new_hook_scene"
    local laymain_data = require "laymain_data"
    local curChapterID = new_hook_scene.GetCurChapterID()
    --local curChestID = laymain_data.GetChapterShowChestID(curChapterID)
    --local idleReward = laymain_data.GetIdleReward()

    local nextChapterID = laymain_data.GetNextLevelCfg().ChapterID
    if nextChapterID and curChapterID and nextChapterID > curChapterID  then
        local world_map_data = require "world_map_data"
        world_map_data.Go2NextChapter(nextChapterID)
    end
    
    --if nextChapterID and curChapterID and nextChapterID > curChapterID and curChestID and idleReward and curChestID > idleReward then
    --    local data = laymain_data.GetChapterChestList(curChapterID)
    --    local ui_pass_chapter_reward = require "ui_pass_chapter_reward"
    --    ui_pass_chapter_reward.SetChapter(curChapterID)
    --    windowMgr:ShowModule("ui_pass_chapter_reward")
    --end

    showType = nil

    -- if isNewLevel == true then
    --     -- 首次通关，检查好评弹窗
    --     -- 不放在hookscene 是因为狩猎数据会初始化，没办法判断首次通关
    --     local ui_grade_tip = require "ui_grade_tip"
    --     ui_grade_tip.CheckHookShow()
    -- end
    isNewLevel = false
    if self.UIRoot and (not util.IsNullOrEmpty(self.UIRoot)) then
        self.UIRoot:SetActive(false)
    end
    self.__base:Close()
    window = nil
    --判断引导
    local gw_home_novice_chapter_data = require "gw_home_novice_chapter_data"
    if gw_home_novice_chapter_data:CheckPassLevel() then
        local force_guide_system=require"force_guide_system"
        local force_guide_event=require"force_guide_event"
        force_guide_system.TriEnterEvent(force_guide_event.tEventBomberBegin)
    end
    local result = minigame_mgr.GetGameResult()
    event.Trigger(event.GW_LEVEL_PASS_EVENT,result,0)
end

local CBattleVictory = class(ui_base, nil, BattleVictory)

function Show()
    if window == nil then
        window = CBattleVictory()
        window._NAME = _NAME;
        window.delayOpenMain = 0
        window.delayCloseMain = 0
        window:LoadUIResource("ui/prefabs/minigame/uiminigamebattlevictory.prefab", nil, nil, nil)
    end
    window:Show()
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        window:Close()
        window = nil
    end
end
function GetWin()
    return window
end

function CheckIsClearSeriesData()
    --如果是连续战斗则清空连续战斗信息
    local battle_series_manager = require "battle_series_manager"
    if battle_series_manager.IsSeriesBattle(nBattleType) then
        --  --print("清理连续战斗信息：", stageType)
        battle_series_manager.Clear()
    end

end

function ResetReward()
    BattleData = {}
end

function AddReward(id, num, rewardID, sid)
    local count = #BattleData
    BattleData[count + 1] = {}
    BattleData[count + 1].id = id
    BattleData[count + 1].num = num
    BattleData[count + 1].rewardID = rewardID
    BattleData[count + 1].sid = sid
end

function SetScore(score)
    starCount = score
end

function SetArenaRsp(rsp)
    arenaRsp = rsp
end
function SetBattleRsp(rsp)
    battleRsp = rsp
end

-- repeated PlayerBasicInfo
function SetRivalInfo(rivals)
    rivalArray = rivals
end

function ResetHeroData()
    HeroData = {}
    MultiHeroData = {}
end

function SetHeroData(pos, hero)
    -- print("qsy_yxsy:[ui_battle_victory]SetHeroData>>>>>",pos, hero)
    HeroData[pos] = hero
end

function SetMultiHeroData(data)
    MultiHeroData = data
end

--[[需要显示成就的结算面板类型]]
function SetShowType(showtype)
    nBattleType = showtype
    if showtype == common_new_pb.GameGoal or showtype == common_new_pb.TheTowerOfIllusion or showtype == common_new_pb.LeagueBoss or showtype == common_new_pb.ChinaRed then
        --主线 1,幻境之塔 2
        showType = showtype
    else
        showType = nil
    end
end

function SetTeamIndex(index)
    curTeamIndex = index
end

function SetBattleRecordData(battleid, opponentname)
    battleID = battleid
    opponentName = opponentname or "1"
end

function SetSwitchMode(switchMode)
    isSwitchMode = switchMode
end

function UpdateAchiTxt(t, showtype)
    if window then
        showType = showtype or showType
        if showtype then
            SetShowType(showType)
        end
        if window.PassTxtListInstance then
            window.PassTxtListInstance:SetListData(window:BuildPassTxtData())
        end
    end
end
event.Register(event.UPDATE_STAGE_TYPE, UpdateAchiTxt)
event.Register(event.UPDATE_ACHI_DATA, UpdateAchiTxt)

--用于新手引导时主动调用关闭
function CloseForGuide()
    if window then
        window.onOKClick()
    end
end