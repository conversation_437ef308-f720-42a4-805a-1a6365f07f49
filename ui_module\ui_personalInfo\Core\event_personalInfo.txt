---
--- Created by: yuan<PERSON>.
--- DateTime: 2024/9/4.
--- Desc: 事件Key模块
---

---@module event_personalInfo
module("event_personalInfo")

Init_PERSONAL_INFO = "Init_PERSONAL_INFO"   -- 初始化个人信息

UPDATE_PERSONAL_INFO = "UPDATE_PERSONAL_INFO"   -- 更新个人信息

UPDATE_BASE_INFO = "UPDATE_BASE_INFO"   -- 更新个人基本信息
UPDATE_SOCIALIZE_INFO = "UPDATE_SOCIALIZE_INFO"   -- 更新个人社交信息
UPDATE_PERSONALISED_INFO = "UPDATE_PERSONALISED_INFO"   -- 更新个性化装扮信息
UPDATE_CACHE_GOODS = "UPDATE_CACHE_GOODS"   -- 更新缓存的GOODS信息

ROLE_PRAISE_UPDATE_MSG = "ROLE_PRAISE_UPDATE_MSG"   -- 点赞回调
PLAYER_INFO_SUCCESS = "PLAYER_INFO_SUCCESS"   -- 查看他人信息成功
PLAYER_INFO_FAIL = "PLAYER_DETAILS_FAIL"   -- 查看他人信息失败


UPDATE_CUSTOM_AVATAR_STATUS = "UPDATE_CUSTOM_AVATAR_STATUS"   -- 更新上传按钮状态

UPLOAD_CUSTOM_AVATAR_REQUEST = "UPLOAD_CUSTOM_AVATAR_REQUEST"   -- 上传头像请求
UPLOAD_CUSTOM_AVATAR_RESULT_REPORT = "UPLOAD_CUSTOM_AVATAR_REPORT"   -- 上传头像审核结果

CUSTOM_AVATAR_VERIFY = "CUSTOM_AVATAR_VERIFY"   -- 自定义头像审核结果通知
CUSTOM_AVATAR_ACTIVE = "CUSTOM_AVATAR_ACTIVE"   -- 自定义头像是否激活
CUSTOM_AVATAR_REMOVE = "CUSTOM_AVATAR_REMOVE"   -- 自定义头像删除
CUSTOM_AVATAR_USE = "CUSTOM_AVATAR_USE"   -- 自定义头像使用

REFRESH_LIKE_DATA = "REFRESH_LIKE_DATA" --点赞页面显示
OPEN_HISTORY_PANEL = "OPEN_HISTORY_PANEL" --打开点赞历史记录页面
REFRESH_MAIL_DETAIL = "REFRESH_MAIL_DETAIL" --邮件点赞数据响应时 打开邮件详情页面
REFRESH_ARENA_LIKE = "REFRESH_ARENA_LIKE" --刷新竞技场的点赞数据
REFRESH_ARENA_TOPIC_LIKENUM = "REFRESH_ARENA_TOPIC_LIKENUM"--刷新竞技场的可点赞次数

OPEN_CHAT_BLOCK_LIST = "CHAT_BLOCK_LIST" --聊天屏蔽列表