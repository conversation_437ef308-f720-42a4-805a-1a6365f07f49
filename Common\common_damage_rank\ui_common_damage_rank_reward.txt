local require = require
local pairs = pairs     
local table = table
local type = type

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local class = require "class"
local binding = require "ui_common_damage_rank_reward_binding"
local item_rank_reward_template1 = require "item_rank_reward_template1"
--region View Life
module("ui_common_damage_rank_reward")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)
    self:InitScrollRectTable()
    self.VData = {}
end


function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base:OnHide()
end

function UIView:Close()   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    --滑动列表的ItemsDispose
    if  self.srt_content then
        self.srt_content:ItemsDispose()
    end
    --滑动列表的ItemsDispose
    if  self.srt_content_self then
        self.srt_content_self:ItemsDispose()
    end
    self.VData = nil

    self.__base.Close(self)
end
--endregion

--region View Logic
--region 处理SrollRectTable
function UIView:InitScrollRectTable()
    --当前换页的时候采用直接换了一个滑动组件  --测试对比每次重新生成
    self.srt_content.onItemRender = OnItemRender   
    self.srt_content.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data  then
            if scroll_rect_item.data["templateItem"] then
                scroll_rect_item.data["templateItem"]:Dispose()
            end           
        end
    end
end

function OnItemRender(scroll_rect_item,index,dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem  
    --绑定上点击等事件，主要是为了抛给controller层进行分离   
    scroll_rect_item.InvokeFunc =  function(funcname,obj)
        --注意  这里的事件是存在多种的clickItemEvent 
        if dataItem[funcname] then
            dataItem[funcname](index,dataItem)
        end
    end    
    if scroll_rect_item.data["templateItem"] then
        scroll_rect_item.data["templateItem"]:Dispose()
    end
    local title
    if dataItem.rankRange2 == -1 then
        title = lang.GetFormat(dataItem.titleLangId,dataItem.rankRange1)
    else
        title = lang.GetFormat(dataItem.titleLangId,dataItem.rankRange1,dataItem.rankRange2)        
    end
    local isSelf = false
    if window.VData.selfRank then
        isSelf = window.VData.selfRank  >= dataItem.rankRange1 and window.VData.selfRank  <= dataItem.rankRange2
    end
     scroll_rect_item.data["templateItem"] = item_rank_reward_template1.NewItem(scroll_rect_item.transform,index,dataItem.rewardId,title,isSelf)
end

function UIView:UpdateScrollList(data,len)
    if  not data then
        return
    end
    self.srt_content:SetData(data,len or #data)
    self.srt_content:Refresh(0, -1)
    --这个renderPerFrames 可以传过来，也可以直接v层自己设置
    self.srt_content.renderPerFrames = 10
end

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
		window.isBlurBg = true
		window:LoadUIResource(ui_path, nil, nil, nil)
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
