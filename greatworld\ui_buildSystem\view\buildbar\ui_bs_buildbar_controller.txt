﻿--- ui_bs_buildbar_controller.txt
--- ui_bs_buildbar_controller.txt
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by .
--- DateTime: 
--- desc:    
---
local require = require
local pairs = pairs
local ipairs = ipairs
local string = string
local table = table
local newclass = newclass
local tonumber = tonumber

local sand_ui_event_define = require "sand_ui_event_define"
local event = require "event"
local gw_home_cfg_util = require "gw_home_cfg_util"
local GWConst = require "gw_const"
local gw_home_red_mgr = require "gw_home_red_mgr"
local lang = require "lang"
local util = require "util"
local module_scroll_list = require "scroll_list"
local controller_base = require "controller_base"
local e_handler_mgr = require "e_handler_mgr"
local player_mgr = require "player_mgr"
local flow_text = require "flow_text"
local util = require "util"
local game_scheme = require "game_scheme"
local windowMgr = require "ui_window_mgr"
local gw_home_building_data = require "gw_home_building_data"
local GWHomeMgr = GWHomeMgr
local GWAdmin = GWAdmin
module("ui_bs_buildbar_controller")
--TODO  类的实现方式后续需要优化，或者不用基类 现在的class的继承很耗时，
local controller = nil
local UIController = newclass("ui_bs_buildbar_controller", controller_base)

local buildTypeData = nil
local buildTypeIndex = {
    [1] = GWConst.BuildType.Economy,
    [2] = GWConst.BuildType.Military,
    [3] = GWConst.BuildType.Decoration
}

--[[窗口初始化]]
function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    self:SubscribeEvents()
    if data then
        util.DelayCallOnce(0, function()
            JumpToBuildTypeItem(data.TypeID)
        end)
    else
        --初始化
        local refreshType = GWConst.BuildType.Economy
        for i, type in pairs(buildTypeIndex) do
            local t = gw_home_red_mgr.GetTypeAllIndex(type)
            if t > 0 then
                refreshType = type
                break
            end
        end

        local force_guide_system = require "force_guide_system"
        local guideId = force_guide_system.GetCurStep()
        if guideId and (guideId == 886) then
            --新手引导885步骤 强制设置军事
            refreshType = GWConst.BuildType.Military
        end
        self:RefreshTypeView(refreshType, true)
        e_handler_mgr.TriggerHandler(self.view_name, "InitView", refreshType)
    end
end

--[[界面被显示的时候调用]]
function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close()
    buildTypeData = {}
    buildTypeData = nil
    self.__base.Close(self)
    controller = nil
end

function UIController:SubscribeEvents()
    -- 注意 事件建议使用self:RegisterEvent(event_name,func)来注册，不需要手动维护注销了        
end
---********************功能函数区**********---
function UIController:OnTogEcoValueChanged(value)
    self:RefreshTypeView(GWConst.BuildType.Economy, value)
end

function UIController:OnTogAffairValueChanged(value)
    self:RefreshTypeView(GWConst.BuildType.Military, value)
end

function UIController:OnTogDecorValueChanged(value)
    self:RefreshTypeView(GWConst.BuildType.Decoration, value)
end

function UIController:RefreshTypeView(type, value)
    local data = self:GetBuildTypeData(type)
    e_handler_mgr.TriggerHandler(self.view_name, "UpdateToggleList", data, value, type)
end

function UIController:OnCloseBg()
    windowMgr:UnloadModule(self.view_name)
end
---获取建筑数据
---@param type number 建筑类型
---@return table 当前类型的所有数据
function UIController:GetBuildTypeData(type)
    if not buildTypeData then
        self:InitBuildTypeData()
    end
    if buildTypeData[type] then
        return buildTypeData[type]
    end
    return nil
end
-- 自定义比较函数，用于根据 isBuildable 字段排序
local function compare(a, b)
    if a.isBuildable ~= b.isBuildable then
        -- 按照 isBuildable 字段降序排序：true 在前，false 在后
        return (a.isBuildable and 1 or 0) > (b.isBuildable and 1 or 0)
    else
        -- 如果 isBuildable 字段相同，则按照 type 字段升序排序
        --new排在最前
        if a.isNew ~= b.isNew then
            return (a.isNew and 1 or 0) > (b.isNew and 1 or 0)
        end
        return a.type < b.type
    end
end
--初始化数据
function UIController:InitBuildTypeData()
    buildTypeData = {}
    local buildTypeLen = game_scheme:BuildingType_nums()
    for i = 0, buildTypeLen - 1 do
        local cfg = game_scheme:BuildingType(i)
        if cfg and cfg.BuildingSubtype and cfg.BuildingSubtype > 0 then
            if not buildTypeData[cfg.BuildingSubtype] then
                buildTypeData[cfg.BuildingSubtype] = {}
            end

            local data = {}
            data.type = cfg.TypeID
            data.iron = player_mgr.GetPlayerIron()
            data.food = player_mgr.GetPlayerFood()
            data.buildId = GWHomeMgr.cfg.GetBuildId(cfg.TypeID)
            data.buildLevel = GWHomeMgr.buildingData.GetBuildingDataMaxLevel(data.buildId)
            data.curBuild = self:GetCurUserBuildNum(data.buildId)
            data.needBuildId = GWHomeMgr.cfg.GetBuildId(cfg.Preconditions)
            data.level = GWHomeMgr.buildingData.GetBuildingDataMaxLevel(data.needBuildId)
            data.mapId = GWHomeMgr.cfg.GetRepairData(data.buildId)
            data.module = BuildTypeItemClick
            data.ironHaveNum = player_mgr.GetPlayerIron()
            data.foodHaveNum = player_mgr.GetPlayerFood()
            data.isNew = false
            local cfg_buidType = game_scheme:BuildingType_0(data.type)
            local Arr = string.split(cfg_buidType.buildable, ";")
            data.maxBuild = 0
            for _, v in pairs(Arr) do
                local arr = string.split(v, "#")
                local needLevel = tonumber(arr[1])
                if needLevel > data.level then
                    break
                end
                local buildNum = tonumber(arr[2])
                if needLevel == data.level then
                    data.isNew = true
                end
                data.maxBuild = buildNum
            end
            --建筑建造
            if data.curBuild == 0 and data.maxBuild > 0 then
                local isUpgrade = gw_home_cfg_util.IsBuildUpgradeByPreconditions(data.buildId,1)
                if not isUpgrade then
                    data.isNew = false
                    data.maxBuild = 0
                end
            end
            --添加额外建造
            local extraBuildNum = gw_home_cfg_util.GetBuildExtraBuildNum(data.type)
            data.maxBuild = data.maxBuild + extraBuildNum
            --判断是否可以建造
            data.isBuildable = data.maxBuild > data.curBuild
            if data.isNew then
                data.isNew = data.isBuildable
            end


            --增加建造队列是否显示处理
            local canShow = true
            if gw_home_building_data.ShowConditionFunc[data.type] then
                if not gw_home_building_data.ShowConditionFunc[data.type]() then
                    canShow = false
                end
            end
            if canShow then
                table.insert(buildTypeData[cfg.BuildingSubtype], data)
            end

        end
    end
    for k, v in pairs(buildTypeData) do
        table.sort(v, compare)
    end
    local force_guide_system = require "force_guide_system"
    local guideId = force_guide_system.GetCurStep()
    if guideId and (guideId == 886) then
        local guideTable = buildTypeData[GWConst.BuildType.Military]
        local index = 0 -- 替换为你需要的元素索引
        for i, buildData in ipairs(guideTable) do
            if buildData.type == GWConst.enBuildingType.enBuildingType_Alliance then
                index = i
            end
        end
        local element = table.remove(guideTable, index) -- 从原始位置删除该元素
        table.insert(guideTable, 1, element) -- 将该元素插入到第一位
    end
end
---@see 建筑点事件
function BuildTypeItemClick(index, dataItem)
    local Cfg_Build_Type = game_scheme:BuildingType_0(dataItem.type)
    local result = true
    if dataItem.isBuildable then
        --1 跳转生成位置 
        result = JumpToBuild(dataItem)
    elseif (dataItem.mapId) then
        --2 跳转到修复位置
        JumpToRepair(dataItem)
    elseif (Cfg_Build_Type.BuildingSubtype == 3) then
        --装饰建筑
        if dataItem.curBuild > 0 then
            GWAdmin.HomeCameraUtil.DoCameraToBuildMove(dataItem.buildId,nil,GWConst.CameraMoveTime,true)
        end
    else
        --3 跳转到大本营
        JumpToMain(dataItem)
    end
    
    local main_slg_mgr = require "main_slg_mgr"
    if result then
        main_slg_mgr.SetDelayMainTime(controller.view_name,true,0.2)        
    end
    windowMgr:UnloadModule(controller.view_name)
    if result then
        main_slg_mgr.DelaySlgMainUI(false,0)    
    end
    local force_guide_system = require "force_guide_system"
    local force_guide_event = require "force_guide_event"
    force_guide_system.TriComEvent(force_guide_event.cEventBuildPlaneClick)
end

---跳转到建造栏的建筑
---@param TypeID number 建造类型Id
function JumpToBuildTypeItem(TypeID)
    if not controller then
        return
    end
    local togType = game_scheme:BuildingType_0(TypeID).BuildingSubtype
    if togType == GWConst.BuildType.Economy then
        controller:OnTogEcoValueChanged(true)
    elseif togType == GWConst.BuildType.Military then
        controller:OnTogAffairValueChanged(true)
    else
        controller:OnTogDecorValueChanged(true)
    end

    local data = controller:GetBuildTypeData(togType)
    local index = 0
    for k, v in pairs(data) do
        index = v.type == TypeID and k or index
    end
    e_handler_mgr.TriggerHandler(controller.view_name, "InitView", togType)
    e_handler_mgr.TriggerHandler(controller.view_name, "ScrollToPosition", index)
end

---跳转到建筑
---@param buildTypeId number 建筑类型id
---@param buildId number 建筑id
function JumpToBuild(dataItem)
    --建造不判断队列
    --if not GWHomeMgr.buildingQueueData.HasFreeQueue() then
    --    --没有空闲队列 
    --    windowMgr:ShowModule("ui_bs_new_que")
    --    return
    --end
    local buildTypeId = dataItem.type
    local buildId = dataItem.buildId
    local Cfg_Build = game_scheme:Building_0(buildId, 1)
    if player_mgr.GetPlayerIron() < Cfg_Build.iron then
        local  skep_mgr = require "skep_mgr"
        local itemCfg = game_scheme:Item_0(skep_mgr.const_id.iron)
        if itemCfg then
            flow_text.Add(string.format2(lang.Get(601336), lang.Get(itemCfg.nameKey)))
            
        end
        return false
    end
    if player_mgr.GetPlayerFood() < Cfg_Build.food then
        local  skep_mgr = require "skep_mgr"
        local itemCfg = game_scheme:Item_0(skep_mgr.const_id.food)
        if itemCfg then
            flow_text.Add(string.format2(lang.Get(601336), lang.Get(itemCfg.nameKey)))

        end
        return false
    end
    local cfg = game_scheme:BuildingType_0(buildTypeId)
    if cfg and cfg.commendLocal and cfg.commendBuild then
        local pos
        if util.get_len(cfg.commendLocal.data) >= 2 then
            pos = { x = cfg.commendLocal.data[0], y = cfg.commendLocal.data[1] }
        end
        GWAdmin.HomeBuildingEditUtil.CreatTempComp(buildTypeId, pos, cfg.commendBuild)
    end
end
---跳转到修复位置
---@param repairMapId number 修复地图id
---@param buildId number 建筑id
function JumpToRepair(dataItem)
    local repairMapId = dataItem.mapId
    local buildId = dataItem.buildId
    local curBuild = dataItem.curBuild
    local cfg = game_scheme:BuildMaincityMap_0(repairMapId)
    if not cfg then
        GWAdmin.SwitchUtility.Error("JumpToRepair cfg = nil")
        return
    end
    local isRepair = GWAdmin.GWHomeCfgUtil.IsCanRepair(cfg.MapID)
    if (isRepair and dataItem.level <= 0) or curBuild <= 0 then
        --当前建 造的数量>1
        if cfg.x and cfg.y then
            --跳转镜头到修复位置
            GWAdmin.HomeCameraUtil.GWCameraDoMoveToGridPos(cfg.x, cfg.y, GWConst.CameraMoveTime, false, function()
                event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
            end)
        end
    else
        --跳转到大本营
        JumpToMain(dataItem)
    end
end
---跳转到大本营
function JumpToMain(dataItem)
    local cfg_buidType = game_scheme:BuildingType_0(dataItem.type)
    local Arr = string.split(cfg_buidType.buildable, ";")
    local maxBuild = 0
    for _, v in pairs(Arr) do
        local arr = string.split(v, "#")
        local extraBuildNum = gw_home_cfg_util.GetBuildExtraBuildNum(dataItem.type)
        maxBuild = tonumber(arr[2]) + extraBuildNum
    end
    --跳转到当前建筑位置
    if dataItem.curBuild >= maxBuild then
        local buildData = GWHomeMgr.buildingData.GetMinLevelBuildingDataByBuildingID(dataItem.buildId)
        local x = buildData.x
        local y = buildData.y
        GWAdmin.HomeCameraUtil.DoCameraToBuildMove(dataItem.buildId,nil,GWConst.CameraMoveTime,true)
        return
    end
    GWAdmin.HomeCameraUtil.DoCameraToBuildMove(dataItem.needBuildId,nil,GWConst.CameraMoveTime,true,false,nil,2)
end
---获取玩家建筑数量
---@param id number 建筑id
---@return number num 建筑数量
function UIController:GetCurUserBuildNum(id)
    local buildList = GWHomeMgr.buildingData.GetBuildingDataListByBuildingID(id)
    local index = util.get_len(buildList)
    if index == 0 then

    end
    return index
end
--获取玩家基地等级
---@return number level 基地等级
function UIController:GetCurUserBaseCampLevel()
    local buildList = GWHomeMgr.buildingData.GetBuildingDataMaxLevel(1000)
    for i, v in pairs(buildList) do
        return v.nLevel
    end
    return 0
end

---********************end功能函数区**********---
--region ModuleFunction 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end

--endregion
