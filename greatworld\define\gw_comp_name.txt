--- Created by fgy.
--- DateTime: 2024/6/11 16:10
--- Des:组件名称

---@class GWCompName
local GWCompName = {
    -- - - - - - - - - - - - - - - - - - - - - 沙盘 START - - - - - - - - - - - - - - - - - - - -
    gw_comp_sand_scene = "gw_comp_sand_scene",

    comp_map = "gw_comp_map",
    comp_topMap = "gw_comp_topMap",

    --玩家实体
    comp_basic = "gw_comp_basic",
    --移动组件
    comp_movement = "gw_comp_movement",
    --特效组件
    comp_vfx = "gw_comp_vfx",
    --玩家基地
    comp_unit_base = "gw_comp_unit_base",
    --中立城池基地
    comp_city = "gw_comp_city",
    --游荡怪行军
    comp_wanderer = "gw_comp_wanderer",
    --玩家行军
    comp_march_item = "gw_comp_march_item",
    --行军线
    comp_march_line = "gw_comp_march_line",
    --城际货车
    comp_carriage_item = "gw_comp_carriage_item",
    --联盟火车
    comp_alliancetrain_item = "gw_comp_alliancetrain_item",
    --巨炮
    comp_bigGun = "gw_comp_bigGun",
    --营寨
    comp_camp = "gw_comp_camp",
    
    ----------------------------------------沙盘 HUD START------------------------------------------
    --个人标记
    comp_personal_mark = "gw_comp_personal_mark",
    --联盟标记
    comp_union_mark = "gw_comp_union_mark",
    --等级hud
    comp_level_hud = "gw_comp_level_hud",
    --图标hud
    comp_icon_hud = "gw_comp_icon_hud",
    --名字hud
    comp_name_hud = "gw_comp_name_hud",
    -- 血条
    comp_hp_hud = "gw_comp_hp_hud",
    --采集提示
    comp_res_tip_hud = "gw_comp_res_tip_hud",
    --城市hud
    comp_city_hud = "gw_comp_city_hud",
    --迁城提示hud
    comp_move_base_hud = "gw_comp_move_base_hud",
    --迁城移动hud
    comp_move_chose_hud = "gw_comp_move_chose_hud",
    --行军操作hud
    comp_march_operate_hud = "gw_comp_march_operate_hud",
    --行军玩家信息hud
    comp_player_info_hud = "gw_comp_player_info_hud",
    --行军英雄头像
    comp_hero_tip_hud = "gw_comp_hero_tip_hud",
    --基地玩家头像
    comp_face_tip_hud = "gw_comp_face_tip_hud",
    --中立城池奖励hud
    comp_reward_hud = "gw_comp_reward_hud",
    --中立城池倒计时hud
    comp_protectCD_hud = "gw_comp_protectCD_hud",
    --国会占领进度hud
    comp_progress_hud = "gw_comp_progress_hud",
    --全军出击hud
    comp_offline_attack_hud = "gw_comp_offline_attack_hud",
    --同盟boss hud 
    comp_alliance_boss_hud = "gw_comp_alliance_boss_hud",
    --总统图标icon
    comp_president_tip_hud = "gw_comp_president_tip_hud",
    --酒馆任务
    comp_tavern_tip_hud = "gw_tavern_task_hud",
    --城市背景hud
    comp_city_bg_hud = "gw_comp_city_bg_hud",

    --沙漠风暴
    gw_comp_storm_scene = "gw_comp_storm_scene",
    comp_storm_score_hud = "comp_storm_score_hud",
    comp_storm_name_hud = "comp_storm_name_hud",
    comp_storm_box_name_hud = "comp_storm_box_name_hud",
    comp_storm_box_score_hud = "comp_storm_box_score_hud",

    comp_zombie_storm_monster = "gw_comp_march_zombie_storm_monster",
    gw_comp_zombie_storm_hud = "gw_comp_zombie_storm_hud",
    
    gw_comp_buff_hud = "gw_comp_buff_hud",
    
    --战区对决
    comp_congress_face_tip_hud = "gw_comp_congress_face_tip_hud",
    comp_tmpOccupyInfo_hud= "gw_comp_tmpOccupyInfo_hud",--临时占领信息
    
    ----------------------------------------沙盘 HUD END------------------------------------------

    -- - - - - - - - - - - - - - - - - - - - - 家园主城 START - - - - - - - - - - - - - - - - - - - -
    gw_home_comp_scene = "gw_home_comp_scene", --场景组件和沙盘的区分了
    gw_home_comp_map = "gw_home_comp_map",
    gw_home_comp_map_edit_grid = "gw_home_comp_map_edit_grid",
    gw_home_comp_hero = "gw_home_comp_hero", --主城地图上用到的Hero 实体
    gw_home_building_edit = "gw_home_comp_building_edit", --地图建筑编辑实体，在游戏中是一个单例
    gw_home_comp_building_default = "gw_home_comp_building_default", --默认示例建筑实体
    gw_home_comp_building_normal = "gw_home_comp_building_normal", --通用普通建筑
    gw_home_comp_building_main = "gw_home_comp_building_main", --大本营
    gw_home_comp_building_squad = "gw_home_comp_building_squad", --编队
    gw_home_comp_building_resource = "gw_home_comp_building_resource", --资源田
    gw_home_comp_cut = "gw_home_comp_cut", --剪彩   
    gw_home_comp_building_barracks = "gw_home_comp_building_barracks", --兵营
    gw_home_comp_building_hospital = "gw_home_comp_building_hospital", --医院
    gw_home_comp_building_storage = "gw_home_comp_building_storage", --仓库
    gw_home_comp_building_equip = "gw_home_comp_building_equip", --装备工坊
    gw_home_comp_building_reconnaissance = "gw_home_comp_building_reconnaissance", --侦察机
    gw_home_comp_building_drone_factory = "gw_home_comp_building_drone_factory", --无人机工厂
    gw_home_comp_building_drone_center = "gw_home_comp_building_drone_center", --无人机中心
    gw_home_comp_building_honor_wall = "gw_home_comp_building_honor_wall", --荣誉墙
    gw_home_comp_building_radar = "gw_home_comp_building_radar", --雷达车
    gw_home_comp_building_worker_center = "gw_home_comp_building_worker_center", --人才大厅

    gw_home_comp_building_garden = "gw_home_comp_building_garden", --花园
    gw_home_comp_building_giant_market = "gw_home_comp_building_giant_market", --巨人游商
    gw_home_comp_building_scientific = "gw_home_comp_building_scientific", --科研中心
    gw_home_comp_building_summon = "gw_home_comp_building_summon", --召唤圣殿
    gw_home_comp_building_wall = "gw_home_comp_building_wall", --城墙
    gw_home_comp_building_alliance = "gw_home_comp_building_alliance", --城墙
    gw_home_comp_building_train_center = "gw_home_comp_building_train_center", --校场
    gw_home_comp_building_road = "gw_home_comp_building_road", --道路
    gw_home_comp_building_wall_rail = "gw_home_comp_building_wall_rail", --城墙围墙
    gw_home_comp_building_carriage_center = "gw_home_comp_building_carriage_center", --城建货车
    gw_home_comp_building_alliancetrain_center = "gw_home_comp_building_alliancetrain_center", --城际联盟火车中心

    gw_home_comp_building_hangup = "gw_home_comp_building_hangup", --挂机建筑
    gw_home_comp_building_collect_hall = "gw_home_comp_building_collect_hall", --建筑物收藏馆
    gw_home_comp_building_tavern = "gw_home_comp_building_tavern", --橡果酒馆
    gw_home_comp_building_first_recharge = "gw_home_comp_building_first_recharge", --橡果酒馆
    gw_home_comp_building_arena = "gw_home_comp_building_arena", --竞技场
    gw_home_comp_building_drone_star = "gw_home_comp_building_drone_star",--结晶研究所
    gw_home_comp_building_dog_house = "gw_home_comp_building_dog_house",--狗屋

    gw_home_comp_lord = "gw_home_comp_lord",--领主实体
    gw_home_comp_stray_dog = "gw_home_comp_stray_dog", --流浪狗

    -- - - - - - - - - - - - - - - - - - - - - 家园主城气泡 START - - - - - - - - - - - - - - - - - - - -
    gw_home_comp_bubble_resource = "gw_home_comp_bubble_resource", --资源气泡
    gw_home_comp_bubble_function = "gw_home_comp_bubble_function", --功能气泡
    gw_home_comp_bubble_Event_function = "gw_home_comp_bubble_Event_function", --事件功能气泡
    gw_home_comp_bubble_upgrade = "gw_home_comp_bubble_upgrade", --升级倒计时类型气泡
    gw_home_comp_bubble_repair = "gw_home_comp_bubble_repair", --修复气泡
    gw_home_comp_bubble_worker = "gw_home_comp_bubble_worker", --工人气泡
    gw_home_comp_bubble_help = "gw_home_comp_bubble_help", --联盟帮助气泡
    gw_home_comp_bubble_soldiers = "gw_home_comp_bubble_soldiers", --带有士兵头像的气泡
    gw_home_comp_bubble_clock_down = "gw_home_comp_bubble_clock_down", --非升级的倒计时类型气泡
    gw_home_comp_bubble_arena = "gw_home_comp_bubble_arena", --竞技场
    gw_home_comp_bubble_scientific_clock_down = "gw_home_comp_bubble_scientific_clock_down", --科研倒计时气泡
    gw_home_comp_bubble_scientific_finish = "gw_home_comp_bubble_scientific_finish", --科研结束领取气泡
    gw_home_comp_bubble_emoji = "gw_home_comp_bubble_emoji", --3d模型走线用的emoji表情气泡
    gw_home_comp_bubble_text = "gw_home_comp_bubble_text", --3d模型走线用的对话表情气泡
    gw_home_comp_bubble_carriage_depart = "gw_home_comp_bubble_carriage_depart", --货车发车气泡
    gw_home_comp_bubble_carriage_reward = "gw_home_comp_bubble_carriage_reward", --货车领奖气泡
    gw_home_comp_bubble_hangup = "gw_home_comp_bubble_hangup", --挂机货车领奖气泡
    gw_home_comp_bubble_truck_timer = "gw_home_comp_bubble_truck_timer",--货车开始倒计时气泡
    gw_home_comp_bubble_Event_hand = "gw_home_comp_bubble_Event_hand", --事件手势气泡

    gw_home_comp_bubble_alliance_train_lock = "gw_home_comp_bubble_alliance_train_lock", --火车未解锁气泡
    gw_home_comp_bubble_alliance_train_gift = "gw_home_comp_bubble_alliance_train_gift", --火车礼包气泡
    gw_home_comp_bubble_alliance_train_add = "gw_home_comp_bubble_alliance_train_add", --火车添加位置气泡

    gw_home_comp_bubble_visitor = "gw_home_comp_bubble_visitor", --访客气泡

    gw_home_comp_bubble_main_line = "gw_home_comp_bubble_main_line", --主线奖励气泡
    gw_home_comp_bubble_xyx = "gw_home_comp_bubble_xyx", --小游戏气泡

    gw_home_comp_bubble_doghouse = "gw_home_comp_bubble_doghouse", --狗屋
    
    -- - - - - - - - - - - - - - - - - - - - - 家园主城气泡 END - - - - - - - - - - - - - - - - - - - -

    -- - - - - - - - - - - - - - - - - - - - - 家园主城事件 START - - - - - - - - - - - - - - - - - - - -
    gw_home_comp_event_model = "gw_home_comp_event_model", --事件模型
    gw_home_comp_event_base = "gw_home_comp_event_base", --事件基类
    gw_home_comp_event_player = "gw_home_comp_event_player", --事件玩家 
    gw_home_comp_event_complete = "gw_home_comp_event_complete", --事件区域完成

    gw_home_novice_comp_event_base = "gw_home_novice_comp_event_base", --新手事件基类 
    gw_home_novice_comp_event_model = "gw_home_novice_comp_event_model", --新手事件模型
    gw_home_novice_comp_event_player = "gw_home_novice_comp_event_player", --新手事件玩家 
    -- - - - - - - - - - - - - - - - - - - - - 家园主城事件 END - - - - - - - - - - - - - - - - - - - -

    gw_home_comp_model_worker = "gw_home_comp_model_worker", --走线模型基础（工人）
    gw_home_comp_model_base = "gw_home_comp_model_base", --模型基类

    -- - - - - - - - - - - - - - - - - - - - - 家园主城 HUD START - - - - - - - - - - - - - - - - - - - -
    gw_home_comp_hud_fight = "gw_home_comp_hud_fight", --战斗HUD
    gw_home_comp_hud_event_lock = "gw_home_comp_hud_event_lock", --事件锁
    gw_home_comp_hud_upgrade = "gw_home_comp_hud_upgrade", --升级HUD
    gw_home_comp_hud_get_resource = "gw_home_comp_hud_get_resource", --获得资源HUD
    gw_home_comp_hud_head_bubble = "gw_home_comp_hud_head_bubble",
    gw_home_comp_hud_lord_bubble = "gw_home_comp_hud_lord_bubble", --头像聊天气泡HUD
    gw_home_comp_hud_stray_dog_bubble = "gw_home_comp_hud_stray_dog_bubble", --狗狗聊天气泡HUD
    gw_home_comp_hud_arrow = "gw_home_comp_hud_arrow", --箭头HUD
    gw_home_comp_hud_xyx = "gw_home_comp_hud_xyx", --小游戏HUD
    
    -- - - - - - - - - - - - - - - - - - - - - 家园主城 HUD END - - - - - - - - - - - - - - - - - - - -
    gw_home_comp_visitor_base = "gw_home_comp_visitor_base",
    gw_home_comp_visitor = "gw_home_comp_visitor", --访客
    -- - - - - - - - - - - - - - - - - - - - - 家园主城 END - - - - - - - - - - - - - - - - - - - -

    -- - - - - - - - - - - - - - - - - - - - - 雷达 HUD START - - - - - - - - - - - - - - - - - - - -
    comp_radar_demon_castle_hud = "gw_radar_demon_castle_hud",
    comp_gw_dialog_hud = "gw_dialog_hud",
    comp_sand_treasure_hud = "gw_sand_treasure_hud",
    comp_sand_vanish_time_hud = "gw_entity_vanish_time_hud",
    -- - - - - - - - - - - - - - - - - - - - - 雷达 HUD END - - - - - - - - - - - - - - - - - - - -

    -- - - - - - - - - - - - - - - - - - - - - 家园特效 START - - - - - - - - - - - - - - - - - - - -
    gw_home_comp_effect_base = "gw_home_comp_effect_base",
    -- - - - - - - - - - - - - - - - - - - - - 家园特效 START - - - - - - - - - - - - - - - - - - - -
    -- - - - - - - - - - - - - - - - - - - - - 家园装饰 START - - - - - - - - - - - - - - - - - - - -
    gw_home_comp_decorate_base = "gw_home_comp_decorate_base",
    -- - - - - - - - - - - - - - - - - - - - - 家园装饰 START - - - - - - - - - - - - - - - - - - - 

    -- - - - - - - - - - - - - - - - - - - - - 通用组件 START - - - - - - - - - - - - - - - - - - - 
    gw_comp_cfg_model = "gw_comp_cfg_model",
    gw_novice_comp_director = "gw_novice_comp_director",
    -- - - - - - - - - - - - - - - - - - - - - 通用组件 END - - - - - - - - - - - - - - - - - - -
}

return GWCompName

