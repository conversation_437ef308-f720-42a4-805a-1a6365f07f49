---
--- Created by: ljj
--- DateTime: 2025/7/18.
--- Desc: 点赞通信模块
---
local require = require

local error_code_pb = require "error_code_pb"
local role_pb = require "role_pb"

local net = require "net"
local net_route = require "net_route"
local xManMsg_pb = require "xManMsg_pb"

local flow_text = require "flow_text"
local util = require "util"
local lang = require "lang"
local msg_pb = require "msg_pb"
local data_personalInfo = require "data_personalInfo"
local tonumber = tonumber 
local game_scheme = require "game_scheme"
local const_personalInfo = require "const_personalInfo"
local event = require "event"
local event_personalInfo = require "event_personalInfo"
local chat_pb = require "chat_pb"
local click_liking_define = require "click_liking_define"
local log = require "log"
local ipairs = ipairs 
local table = table

module("net_click_liking")

local function OnErrorCode(enErr)
    flow_text.Clear()
    flow_text.Add(util.GetErrorLangText(enErr))
end

--请求邮件点赞数据
function MSG_GET_MAIL_LIKE_REQ(mailID,exMailId)
    local msg = role_pb.TMSG_GET_MAIL_LIKE_REQ()
    msg.mailId = mailID
    msg.exMailId = exMailId
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_GET_MAIL_LIKE_REQ, msg)
end

--回复邮件点赞数据
function MSG_GET_MAIL_LIKE_RSP(msg)
    if msg and msg.errorcode == error_code_pb.enErr_NoError then 
        event.Trigger(event_personalInfo.REFRESH_MAIL_DETAIL,msg)
    else
        OnErrorCode(msg.errorcode)
    end
end

--请求特殊点赞数据
function MSG_GET_SP_LIKENUM_REQ(Liketype,group)
    if not Liketype or not group then 
        return
    end 
    if #group == 0 then 
        return
    end
    local msg = role_pb.TMSG_GET_SP_LIKENUM_REQ()
    msg.nLikeSysType = Liketype
    for i,v in ipairs(group) do 
        table.insert(msg.dbids,v)
    end
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_GET_SP_LIKENUM_REQ, msg)
end

--回复特殊点赞数据
function MSG_GET_SP_LIKENUM_RSP(msg)
    if msg and msg.errorcode == error_code_pb.enErr_NoError then 
        event.Trigger(event_personalInfo.REFRESH_ARENA_LIKE,msg)
    else
        OnErrorCode(msg.errorcode)
    end
end

--打开主页时，点赞记录请求
function MSG_LIKE_GET_ROLE_RECORD_REQ()
    local msg = role_pb.TMSG_LIKE_GET_ROLE_RECORD_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_LIKE_GET_ROLE_RECORD_REQ, msg)
end

--打开主页时，点赞记录响应
function MSG_LIKE_GET_ROLE_RECORD_RSP(msg)
    if msg and msg.errorcode == error_code_pb.enErr_NoError then 
        local click_liking_data = require "click_liking_data"
        click_liking_data.InitDataFromServer(msg)
        if msg.roleLikes then 
            event.Trigger(event_personalInfo.REFRESH_LIKE_DATA,msg.roleLikes)
        end
    else
        OnErrorCode(msg.errorcode)
    end
end

--点赞记录请求
function MSG_LIKE_GET_RECORD_REQ()
    local msg = role_pb.TMSG_LIKE_GET_RECORD_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_LIKE_GET_RECORD_REQ, msg)
end

--点赞记录回复
function MSG_LIKE_GET_RECORD_RSP(msg)
    if msg and msg.errorcode == error_code_pb.enErr_NoError then 
        local click_liking_data = require "click_liking_data"
        click_liking_data.InitDataFromServer(msg)
        event.Trigger(event_personalInfo.OPEN_HISTORY_PANEL)
    else
        OnErrorCode(msg.errorcode)
    end
end

--点赞请求
function MSG_ZONE_ROLE_PRAISE_UPDATE_REQ(playId,likeSysType,mailID)
    if not playId then
        return
    end

    local msg = role_pb.TMSG_ZONE_ROLE_PRAISE_UPDATE_REQ()
    msg.playId = playId
    msg.nNums = 1
    if likeSysType then
        msg.nLikeSysType = likeSysType
    end
    if mailID then 
        msg.exMailId = mailID
    end
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_ZONE_ROLE_PRAISE_UPDATE_REQ, msg)
end

--点赞响应
function MSG_ZONE_ROLE_PRAISE_UPDATE_RSP(msg)
    if msg and msg.errorcode == error_code_pb.enErr_NoError then
        local rolePraiseCount = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RolePraiseCount)
        data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RolePraiseCount, rolePraiseCount + msg.nNums)
        -- 点赞成功消息
        event.Trigger(event_personalInfo.ROLE_PRAISE_UPDATE_MSG, msg)
    else
        OnErrorCode(msg.errorcode)
    end
end

--登录下发已经被点赞的私聊信息
function MSG_PRIVATE_CHAT_LIKE_ALL_NTF(msg) 
    -- log.Warning("MSG_PRIVATE_CHAT_LIKE_ALL_NTF", msg.msg)
    if msg and msg.msg then 
        local click_liking_data = require "click_liking_data"
        click_liking_data.SetPrivateChatLikeData( msg.msg,click_liking_define.Enum_Private_Chat_UpdateType.AllInfo)
    end
end

--点赞成功后下发某条私聊信息
function MSG_PRIVATE_CHAT_LIKE_NTF(msg)
    if msg and msg.msg then 
        local click_liking_data = require "click_liking_data"
        click_liking_data.SetPrivateChatLikeData( msg.msg,click_liking_define.Enum_Private_Chat_UpdateType.OneInfo)
        event.Trigger(click_liking_define.REFRESH_PRIVATE_CHAT,msg.msg.szChatID)
    end
end

--私聊点赞请求
function MSG_PRIVATE_CHAT_LIKE_REQ(playerId,chatId) 
    if not playerId or not chatId then 
        return
    end
    local msg = role_pb.TMSG_PRIVATE_CHAT_LIKE_REQ()
    msg.playId = playerId
    msg.szChatID = chatId
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_PRIVATE_CHAT_LIKE_REQ, msg)
end

--私聊点赞回复
function MSG_PRIVATE_CHAT_LIKE_RSP(msg) 
    if msg and msg.errorcode == error_code_pb.enErr_NoError then 
        local click_liking_data = require "click_liking_data"
        click_liking_data.InitDataFromServer(msg)
    else 
        OnErrorCode(msg.errorcode)
    end
end

local MessageTable ={
    --邮件点赞数据
    { msg_pb.MSG_GET_MAIL_LIKE_RSP, MSG_GET_MAIL_LIKE_RSP, role_pb.TMSG_GET_MAIL_LIKE_RSP },
    --特殊点赞
    { msg_pb.MSG_GET_SP_LIKENUM_RSP, MSG_GET_SP_LIKENUM_RSP, role_pb.TMSG_GET_SP_LIKENUM_RSP },
    -- 主页打开时，响应点赞记录
    { xManMsg_pb.MSG_LIKE_GET_ROLE_RECORD_RSP, MSG_LIKE_GET_ROLE_RECORD_RSP, role_pb.TMSG_LIKE_GET_ROLE_RECORD_RSP },
    --点赞记录 酒馆协助记录
    { xManMsg_pb.MSG_LIKE_GET_RECORD_RSP, MSG_LIKE_GET_RECORD_RSP, role_pb.TMSG_LIKE_GET_RECORD_RSP },
    ---点赞请求 
    { msg_pb.MSG_ZONE_ROLE_PRAISE_UPDATE_RSP, MSG_ZONE_ROLE_PRAISE_UPDATE_RSP, role_pb.TMSG_ZONE_ROLE_PRAISE_UPDATE_RSP},
    --私聊点赞登录推送
    { msg_pb.MSG_PRIVATE_CHAT_LIKE_ALL_NTF,MSG_PRIVATE_CHAT_LIKE_ALL_NTF,chat_pb.TMSG_PRIVATE_CHAT_LIKE_ALL_NTF},
    --私聊点赞 响应点赞时推送
    { msg_pb.MSG_PRIVATE_CHAT_LIKE_NTF,MSG_PRIVATE_CHAT_LIKE_NTF,chat_pb.TMSG_PRIVATE_CHAT_LIKE_NTF},
    --私聊点赞 响应
    {msg_pb.MSG_PRIVATE_CHAT_LIKE_RSP,MSG_PRIVATE_CHAT_LIKE_RSP,role_pb.TMSG_PRIVATE_CHAT_LIKE_RSP}
}
net_route.RegisterMsgHandlers(MessageTable)

