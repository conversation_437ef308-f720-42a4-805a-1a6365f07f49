﻿--region FileHead
--- ui_new_magic_weapon_equip_upgrade_tips.txt
-- author:  nyz
-- ver:     1.0
-- desc:    
-------------------------------------------------
--endregion 

--region Require
local require = require
local type = type
local table = table

local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local log = require "log"
local Image = CS.UnityEngine.UI.Image
local enum_define = require "enum_define"
local class = require "class"
local lang = require "lang"
local ui_base = require "ui_base"
local module_scroll_list = require "scroll_list"
local Common_Util = CS.Common_Util.UIUtil
local game_scheme = require "game_scheme"
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local GWG = GWG
local cfg_util = require "cfg_util"
local card_sprite_asset = require "card_sprite_asset"
local Gradient = CS.War.UI.Gradient
local tonumber = tonumber
local player_mgr = require "player_mgr"
local ImageGray = CS.War.UI.ImageGray

local RectTransform = CS.UnityEngine.RectTransform
--endregion 

--region ModuleDeclare
module("ui_new_magic_weapon_equip_upgrade_tips")
local ui_path = "ui/prefabs/gw/buildsystem/uinewmagicweaponequipupgradetips.prefab"
local window = nil
local UIView = {}
local QualityTextColor =
{
    [1] =
    {
        baseColor = {r=(tonumber("4d",16))/255,g=(tonumber("66",16))/255,b=(tonumber("76",16))/255,a=1},
        topColor = {r=1,g=1,b=1,a=1},
        bottomColor = {r=1,g=1,b=1,a=1},
    },
    [2] =
    {
        baseColor = {r=(tonumber("31",16))/255,g=(tonumber("9f",16))/255,b=(tonumber("38",16))/255,a=1},
        topColor = {r=1,g=1,b=1,a=1},
        bottomColor = {r=1,g=1,b=1,a=1},
    },
    [3] =
    {
        baseColor = {r=(tonumber("31",16))/255,g=(tonumber("7b",16))/255,b=(tonumber("c7",16))/255,a=1},
        topColor = {r=1,g=1,b=1,a=1},
        bottomColor = {r=1,g=1,b=1,a=1},
    },
    [4] =
    {
        baseColor = {r=(tonumber("a0",16))/255,g=(tonumber("36",16))/255,b=(tonumber("d5",16))/255,a=1},
        topColor = {r=1,g=1,b=1,a=1},
        bottomColor = {r=1,g=1,b=1,a=1},
    },
    [5] =
    {
        baseColor = {r=(tonumber("ed",16))/255,g=(tonumber("81",16))/255,b=(tonumber("0f",16))/255,a=1},
        topColor = {r=1,g=1,b=1,a=1},
        bottomColor = {r=1,g=1,b=1,a=1},
    },
    [6] =
    {
        baseColor = {r=(tonumber("e6",16))/255,g=(tonumber("38",16))/255,b=(tonumber("38",16))/255,a=1},
        topColor = {r=1,g=1,b=1,a=1},
        bottomColor = {r=1,g=1,b=1,a=1},
    },
    [7] =
    {
        baseColor = {r=1,g=1,b=1,a=1},
        topColor = {r=(tonumber("39",16))/255,g=(tonumber("6e",16))/255,b=(tonumber("db",16))/255,a=1},
        bottomColor = {r=(tonumber("b1",16))/255,g=(tonumber("55",16))/255,b=(tonumber("ff",16))/255,a=1},
    },
    [8] =
    {
        baseColor = {r=1,g=1,b=1,a=1},
        bottomColor = {r=(tonumber("ff",16))/255,g=(tonumber("ad",16))/255,b=(tonumber("2d",16))/255,a=1},
        topColor = {r=(tonumber("ff",16))/255,g=(tonumber("4b",16))/255,b=(tonumber("b9",16))/255,a=1},
    }
}
--endregion 

--region WidgetTable
UIView.widget_table = {
    closeBtn = {path = "closeBtn", type = "Button",event_name = "OnBtn_closeBtnClickedProxy"},
    equipName = {path = "UpgradeTips/EquipName/value",type = "Text"},
    equipColor = {path = "UpgradeTips/EquipName/value",type = Gradient},
    
    oldEquipItemQuality = {path = "UpgradeTips/Icon/OldEquipQuality",type = SpriteSwitcher},
    oldEquipItemIcon = {path = "UpgradeTips/Icon/OldEquipQuality/Icon",type = "Image"},

    newEquipItemQuality = {path = "UpgradeTips/Icon/NewEquipQuality",type = SpriteSwitcher},
    newEquipItemIcon = {path = "UpgradeTips/Icon/NewEquipQuality/Icon",type = "Image"},
    
    upgradeObj_Lv = {path = "UpgradeTips/AttributeObj_Lv/UpgradeObj",type = "RectTransform"},
    oldLevel = {path = "UpgradeTips/AttributeObj_Lv/UpgradeObj/OldValue",type = "Text"},
    newLevel = {path = "UpgradeTips/AttributeObj_Lv/UpgradeObj/NewValue",type = "Text"},
    maxLevel = {path = "UpgradeTips/AttributeObj_Lv/MaxValue",type = "Text"},

    upgradeObj_Value1 = {path = "UpgradeTips/AttributeObj_Attribute_1/UpgradeObj",type = "RectTransform"},
    oldValue1 = {path = "UpgradeTips/AttributeObj_Attribute_1/UpgradeObj/OldValue",type = "Text"},
    newValue1 = {path = "UpgradeTips/AttributeObj_Attribute_1/UpgradeObj/NewValue",type = "Text"},
    value1Title = {path = "UpgradeTips/AttributeObj_Attribute_1/Title",type = "Text"},
    maxValue1 = {path = "UpgradeTips/AttributeObj_Attribute_1/MaxValue",type = "Text"},

    upgradeObj_Value2 = {path = "UpgradeTips/AttributeObj_Attribute_2/UpgradeObj",type = "RectTransform"},
    value2Obj = {path = "UpgradeTips/AttributeObj_Attribute_2",type = "RectTransform"},
    oldValue2 = {path = "UpgradeTips/AttributeObj_Attribute_2/UpgradeObj/OldValue",type = "Text"},
    newValue2 = {path = "UpgradeTips/AttributeObj_Attribute_2/UpgradeObj/NewValue",type = "Text"},
    value2Title = {path = "UpgradeTips/AttributeObj_Attribute_2/Title",type = "Text"},
    maxValue2 = {path = "UpgradeTips/AttributeObj_Attribute_2/MaxValue",type = "Text"},

    upgradeObj_Value3 = {path = "UpgradeTips/AttributeObj_Attribute_3/UpgradeObj",type = "RectTransform"},
    value3Obj = {path = "UpgradeTips/AttributeObj_Attribute_3",type = "RectTransform"},
    oldValue3 = {path = "UpgradeTips/AttributeObj_Attribute_3/UpgradeObj/OldValue",type = "Text"},
    newValue3 = {path = "UpgradeTips/AttributeObj_Attribute_3/UpgradeObj/NewValue",type = "Text"},
    value3Title = {path = "UpgradeTips/AttributeObj_Attribute_3/Title",type = "Text"},
    maxValue3 = {path = "UpgradeTips/AttributeObj_Attribute_3/MaxValue",type = "Text"},

    costItemQuality = {path = "UpgradeTips/UpdateObj/CostItem",type = SpriteSwitcher},
    costItemIcon = {path = "UpgradeTips/UpdateObj/CostItem/Icon",type = "Image"},
    costCount = {path = "UpgradeTips/UpdateObj/CostItem/Count",type = "Text"},
    costItemLv = {path = "UpgradeTips/UpdateObj/CostItem/Lv",type = "Text"},
    upgradeBtn = {path = "UpgradeTips/UpdateObj/Upgrade",type = "Button",event_name = "OnUpgradeBtnClick"},
    synthesis = {path = "UpgradeTips/UpdateObj/Synthesis",type = "Button",event_name = "OnSynthesisBtnClick"},
    
    arrowObj = {path = "UpgradeTips/Icon/Arrow",type = "RectTransform"},
    upgradeGray = {path = "UpgradeTips/UpdateObj/Upgrade",type = ImageGray},
    synthesisGray = {path = "UpgradeTips/UpdateObj/Synthesis",type = ImageGray},
    --upgradeTextGray = {path = "UpgradeTips/UpdateObj/Upgrade/Text_1",type = "RectTransform"},
    --synthesisTextGray = {path = "UpgradeTips/UpdateObj/Synthesis/Text_1",type = "RectTransform"},
    upgradeFalseBtn = {path = "UpgradeTips/UpdateObj/UpgradeFalse",type = "Button",event_name = "OnGrayBtnClick"},
    synthesisFalseBtn = {path = "UpgradeTips/UpdateObj/SynthesisFalse",type = "Button",event_name = "OnSynthesisBtnClick"},
    
    upgradeObj = {path = "UpgradeTips/UpdateObj",type = "RectTransform"},
    maxLevelTips = {path = "UpgradeTips/MaxLevelTips",type = "RectTransform"},
    
    synthesisRedDot = {path = "UpgradeTips/UpdateObj/Synthesis/RedDot",type = "RectTransform"},
    upgradeRedDot = {path = "UpgradeTips/UpdateObj/Upgrade/RedDot",type = "RectTransform"},
    
    oldEquipIconBtn = {path = "UpgradeTips/Icon/OldEquipQuality/Icon",type = "Button",event_name = "OnShowOldEquipInfo"},
    newEquipIconBtn = {path = "UpgradeTips/Icon/NewEquipQuality/Icon",type = "Button",event_name = "OnShowNewEquipInfo"},
    costItemBtn = {path = "UpgradeTips/UpdateObj/CostItem",type = "Button",event_name = "OnShowCostEquipInfo"},
}
--endregion 

--region function 设置View-Controller模式的UI
-- return type  ---- 未定义/VC/纯V   
-- 注意，View-Controller模式的ui必须要重写这个接口
function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end
--endregion 

--region WindowInit
--[[窗口初始化]]
function UIView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)
    self:SubscribeEvents()
    self.cardSpriteAsset = card_sprite_asset.CreateSpriteAsset()
    --self.equipmentSpriteAsset = card_sprite_asset.CreateDroneEquipmentAsset()
    --region User
    --endregion 
end --///<<< function

--endregion 


--region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIView:OnShow()
    self.__base:OnShow()
end --///<<< function

--endregion 

--region WindowOnHide
--[[界面隐藏时调用]]
function UIView:OnHide()
    self.__base:OnHide()    
end --///<<< function

--endregion 


--region WindowClose
function UIView:Close()
    self.__base:Close()
    self:UnsubscribeEvents() 
    window = nil
    if self.cardSpriteAsset then
        self.cardSpriteAsset:Dispose()
        self.cardSpriteAsset = nil
    end
    --if self.equipmentSpriteAsset then
    --    self.equipmentSpriteAsset:Dispose()
    --    self.equipmentSpriteAsset = nil
    --end
    --region User
    --endregion 
end --///<<< function
--endregion 
--region 事件注册
function UIView:SubscribeEvents()    
    -- 注意 事件建议使用self:RegisterEvent(event_name,func)来注册，不需要手动维护注销了    
end
function UIView:UnsubscribeEvents()    
    
end

--endregion

--region 功能函数区
---********************功能函数区**********---
function UIView:InitPanelData(data)
    self.slotIndex = data.slotIndex
    self.slotLv = GWG.GWHomeMgr.droneData.OnGetEquipLevelList()[self.slotIndex]
    local slotCfg = GWG.GWHomeMgr.droneData.GetEquipCfgData(self.slotIndex)
    local nextSlotCfg = game_scheme:MagicWeapon_0(GWG.GWHomeMgr.droneData.GetDroneId(),self.slotIndex + 1,self.slotLv.lv + 1,0)
    local maxSlotCfg = GWG.GWHomeMgr.droneData.GetMaxLvEquipCfg(self.slotIndex)
    --TODO lang缺失

    local quality = self.slotLv.lv 
    local nextQuality = self.slotLv.lv + 1
    if quality >= 7 then
        if quality < 9 then
            quality = 7
        else
            quality = 8
        end
    end
    if nextQuality >= 7 then
        if nextQuality < 9 then
            nextQuality = 7
        else
            nextQuality = 8
        end
    end
    
    self.equipName.color = QualityTextColor[nextQuality].baseColor
    if nextQuality >= 7 then
        self.equipColor.TopColor = QualityTextColor[nextQuality].topColor
        self.equipColor.BottomColor = QualityTextColor[nextQuality].bottomColor
    end
    self.equipName.text = lang.Get(slotCfg.weaponNameID)
    self.oldEquipItemQuality:Switch(quality)
    self.newEquipItemQuality:Switch(nextQuality)
    self.oldLevel.text = self.slotLv.lv;
    self.maxLevel.text = self.oldLevel.text;
    local oldValue = cfg_util.StringToArray(slotCfg.strParam1,";","#")
    local maxValue = cfg_util.StringToArray(maxSlotCfg.strParam1,";","#")
    self.oldValue1.text = tonumber(oldValue[1][2])
    local value1Cfg = game_scheme:ProToLang_0(tonumber(oldValue[1][1]))
    self.value1Title.text = lang.Get(value1Cfg.iLangId)
    if value1Cfg.ProType == 2 then
        self.oldValue1.text = (tonumber(oldValue[1][2]/100)).."%"
    elseif value1Cfg.ProType == 3 then
        self.oldValue1.text = (tonumber(oldValue[1][2]/10000))
    end
    self.maxValue1.text = self.oldValue1.text
    local value2Cfg = {}
    if oldValue[2] or maxValue[2] then
        local tempName = oldValue[2] and oldValue[2][1] or maxValue[2][1]
        local tempValue = oldValue[2] and tonumber(oldValue[2][2]) or 0
        value2Cfg = game_scheme:ProToLang_0(tonumber(tempName))
        self.value2Title.text = lang.Get(value2Cfg.iLangId)
        Common_Util.SetActive(self.value2Obj,true)
        self.oldValue2.text = tempValue
        if value2Cfg.ProType == 2 then
            self.oldValue2.text = (tempValue/100).."%"
        elseif value2Cfg.ProType == 3 then
            self.oldValue2.text = (tempValue/10000)
        end
        self.maxValue2.text = self.oldValue2.text
    else
        Common_Util.SetActive(self.value2Obj,false)
    end

    local value3Cfg = {}
    if oldValue[3] or maxValue[3] then
        local tempName = oldValue[3] and oldValue[3][1] or maxValue[3][1]
        local tempValue = oldValue[3] and tonumber(oldValue[3][2]) or 0
        value3Cfg = game_scheme:ProToLang_0(tonumber(tempName))
        self.value3Title.text = lang.Get(value3Cfg.iLangId)
        Common_Util.SetActive(self.value3Obj,true)
        self.oldValue3.text = tempValue
        if value3Cfg.ProType == 2 then
            self.oldValue3.text = (tempValue/100).."%"
        elseif value3Cfg.ProType == 3 then
            self.oldValue3.text = (tempValue/10000)
        end
        self.maxValue3.text = self.oldValue3.text
    else
        Common_Util.SetActive(self.value3Obj,false)
    end
    
    local oldIconName = self.slotIndex.."_"..quality
    self.cardSpriteAsset:GetSprite(slotCfg.icon, function(sprite)
        self.oldEquipItemIcon.sprite = sprite
    end)
    if nextSlotCfg then
        Common_Util.SetActive(self.arrowObj,true)
        Common_Util.SetActive(self.newEquipItemQuality,true)
        Common_Util.SetActive(self.upgradeObj,true)
        Common_Util.SetActive(self.maxLevelTips,false)
        
        Common_Util.SetActive(self.upgradeObj_Lv,true)
        Common_Util.SetActive(self.upgradeObj_Value1,true)
        Common_Util.SetActive(self.upgradeObj_Value2,true)
        Common_Util.SetActive(self.upgradeObj_Value3,true)
        
        Common_Util.SetActive(self.maxLevel,false)
        Common_Util.SetActive(self.maxValue1,false)
        Common_Util.SetActive(self.maxValue2,false)
        Common_Util.SetActive(self.maxValue3,false)
        local newValue = cfg_util.StringToArray(nextSlotCfg.strParam1,";","#")
        self.newLevel.text = self.slotLv.lv + 1
        self.costItemLv.text = "Lv."..self.slotLv.lv --todo 暂时取当前等级，后续读取值与策划确认
        self.newValue1.text = tonumber(newValue[1][2])
        if value1Cfg.ProType == 2 then
            self.newValue1.text = (tonumber(newValue[1][2]/100)).."%"
        elseif value1Cfg.ProType == 3 then
            self.newValue1.text = (tonumber(newValue[1][2]/10000))
        end
        if newValue[2] then
            Common_Util.SetActive(self.newValue2,true)
            local temp = newValue[2] and tonumber(newValue[2][2]) or 0
            self.newValue2.text = temp
            if value2Cfg.ProType == 2 then
                self.newValue2.text = (temp/100).."%"
            elseif value2Cfg.ProType == 3 then
                self.newValue2.text = (temp/10000)
            end
        else
            Common_Util.SetActive(self.newValue2,false)
            Common_Util.SetActive(self.value2Obj,false)
        end
        
        if newValue[3] then
            Common_Util.SetActive(self.newValue3,true)
            local temp = newValue[3] and tonumber(newValue[3][2]) or 0
            self.newValue3.text = temp
            if value3Cfg.ProType == 2 then
                self.newValue3.text = (temp/100).."%"
            elseif value3Cfg.ProType == 3 then
                self.newValue3.text = (temp/10000)
            end
        else
            Common_Util.SetActive(self.newValue3,false)
            Common_Util.SetActive(self.value3Obj,false)
        end
        
        local newIconName = self.slotIndex.."_"..nextQuality
        self.cardSpriteAsset:GetSprite(nextSlotCfg.icon, function(sprite)
            self.newEquipItemIcon.sprite = sprite
        end)
        local costItemCfg = game_scheme:Item_0(slotCfg.costGoods.data[0])
        self.cardSpriteAsset:GetSprite(costItemCfg.icon,function(sprite)
            self.costItemIcon.sprite = sprite
        end)
        self.costItemQuality:Switch(costItemCfg.quality - 1)
        local costItemCount = player_mgr.GetPlayerOwnNum(slotCfg.costGoods.data[0])
        local textColor = costItemCount < slotCfg.costGoodsNum.data[0] and "<color=#FF4D2A>" or "<color=#319F38>"
        self.costCount.text = textColor..costItemCount.."</color>/"..slotCfg.costGoodsNum.data[0]
        --self.upgradeGray:SetEnable(costItemCount < slotCfg.costGoodsNum.data[0])
        Common_Util.SetActive(self.upgradeFalseBtn,costItemCount < slotCfg.costGoodsNum.data[0])
        
        log.Warning("测试用："..costItemCount..":"..slotCfg.costGoodsNum.data[0])
        
        Common_Util.SetActive(self.upgradeBtn,costItemCount >= slotCfg.costGoodsNum.data[0])
        Common_Util.SetActive(self.upgradeRedDot,costItemCount >= slotCfg.costGoodsNum.data[0])
        --self.upgradeBtn.interactable = costItemCount >= slotCfg.costGoodsNum.data[0]
        --self.synthesisGray:SetEnable(costItemCount >= slotCfg.costGoodsNum.data[0] or not self.slotLv.result.isEnough)--not self.slotLv.result.canSynthesis)
        Common_Util.SetActive(self.synthesisFalseBtn,costItemCount >= slotCfg.costGoodsNum.data[0] or not self.slotLv.result.canSynthesis)--not self.slotLv.result.isEnough)--
        Common_Util.SetActive(self.synthesis,costItemCount < slotCfg.costGoodsNum.data[0])
        --self.synthesis.interactable = costItemCount < slotCfg.costGoodsNum.data[0]
        Common_Util.SetActive(self.synthesisRedDot,costItemCount < slotCfg.costGoodsNum.data[0] and self.slotLv.redDot)
    else
        Common_Util.SetActive(self.arrowObj,false)
        Common_Util.SetActive(self.newEquipItemQuality,false)
        Common_Util.SetActive(self.upgradeObj,false)
        Common_Util.SetActive(self.maxLevelTips,true)
        Common_Util.SetActive(self.upgradeObj_Lv,false)
        Common_Util.SetActive(self.upgradeObj_Value1,false)
        Common_Util.SetActive(self.upgradeObj_Value2,false)
        Common_Util.SetActive(self.upgradeObj_Value3,false)
        Common_Util.SetActive(self.maxLevel,true)
        Common_Util.SetActive(self.maxValue1,true)
        if oldValue[2] then
            Common_Util.SetActive(self.maxValue2,true)
        end
        if oldValue[3] then
            Common_Util.SetActive(self.maxValue3,true)
        end
        --TODO 满级相关……
    end
end

---********************end功能函数区**********---
--endregion
--region WindowInherited
local CUIView = class(ui_base, nil, UIView)
--endregion 

--region static ModuleFunction 
-- 特别注意，当前并不是由controller层来驱动ui的生命流程的 
-- 当前因为需要view层 也就是ui_base来驱动ui的init  加载完成，show等流程，所以流程仍然保留，而controller层的流程逻辑受view流程影响，
-- view对应的Init/Show 加载完后，当前会通过事件同步调用controller层的Init/Show流程，controller层的流程逻辑才会执行。
--当前仍然保留了静态的Show，Close接口流程
function Show(data)
     if  data  and type(data) == "table" and data["uipath"] then
        ui_path = data["uipath"];   
    end  
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
        window:LoadUIResource(ui_path, nil, nil, nil)
    end
    --这里调用window:Show()  会造成多次调用 但hide后却又需要 uiwindowmgr有问题 TODO
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end
--endregion
