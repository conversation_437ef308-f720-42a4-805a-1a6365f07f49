-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
local prop_pb=require("prop_pb")
local error_code_pb=require("error_code_pb")
local common_new_pb=require("common_new_pb")
module('challenge_pb')


V1M=V(4,"ResourceReward",0,1)
V2M=V(4,"GoodsReward",1,2)
E1M=E(3,"IdleRewardType",".CSMsg.IdleRewardType")
V3M=V(4,"OnlyResource",0,1)
V4M=V(4,"OnlyGoods",1,2)
E2M=E(3,"IdleRewardNTFType",".CSMsg.IdleRewardNTFType")
V5M=V(4,"IDLE_REWARD_Init",0,1)
V6M=V(4,"IDLE_REWARD_Add",1,2)
E3M=E(3,"IdleRewardIDNTFType",".CSMsg.IdleRewardIDNTFType")
V7M=V(4,"IDLE_PART_IDLE_REWARD_STAGE",0,0)
V8M=V(4,"IDLE_PART_STAR_REWARD_STAGE",1,1)
V9M=V(4,"IDLE_PART_STAR_NUMBER",2,2)
V10M=V(4,"IDLE_PART_IDLE_GIFT_REWARD_STAGE",3,3)
V11M=V(4,"IDLE_PART_STAR_GIFT_REWARD_STAGE",4,4)
E4M=E(3,"IDLEStarEnum",".CSMsg.IDLEStarEnum")
V12M=V(4,"Oper_Catch",0,1)
V13M=V(4,"Oper_FightAgainst",1,2)
V14M=V(4,"Oper_Conquer",2,3)
E5M=E(3,"EnSlaveOperType",".CSMsg.EnSlaveOperType")
V15M=V(4,"enSdReward_Rank",0,1)
V16M=V(4,"enSdReward_Box",1,2)
V17M=V(4,"enSdReward_Both",2,3)
E6M=E(3,"enSdSettleReward",".CSMsg.enSdSettleReward")
V18M=V(4,"HookLevelChallengeType_MiniLevel",0,0)
V19M=V(4,"HookLevelChallengeType_MonsterComing",1,1)
E7M=E(3,"HookLevelChallengeType",".CSMsg.HookLevelChallengeType")
V20M=V(4,"MailBattleResultType_Min",0,0)
V21M=V(4,"MailBattleResultType_Arena",1,1)
E8M=E(3,"MailBattleResultType",".CSMsg.MailBattleResultType")
F1D=F(2,"nRewardID",".CSMsg.RewardDesc.nRewardID",1,0,2,false,0,13,3)
F2D=F(2,"nRewardNum",".CSMsg.RewardDesc.nRewardNum",2,1,2,false,0,13,3)
M1G=D(1,"RewardDesc",".CSMsg.RewardDesc",false,{},{},nil,{})
F3D=F(2,"stage",".CSMsg.IdleStageStar.stage",1,0,2,false,0,13,3)
F4D=F(2,"star",".CSMsg.IdleStageStar.star",2,1,2,false,0,13,3)
M2G=D(1,"IdleStageStar",".CSMsg.IdleStageStar",false,{},{},nil,{})
F5D=F(2,"starProp",".CSMsg.TMSG_IDLE_BATTLE_RATING_PROP_NTF.starProp",1,0,3,false,{},11,10)
F6D=F(2,"stageStarUpdate",".CSMsg.TMSG_IDLE_BATTLE_RATING_PROP_NTF.stageStarUpdate",2,1,3,false,{},11,10)
M3G=D(1,"TMSG_IDLE_BATTLE_RATING_PROP_NTF",".CSMsg.TMSG_IDLE_BATTLE_RATING_PROP_NTF",false,{},{},nil,{})
F7D=F(2,"coins",".CSMsg.HUNYINGINFO.coins",1,0,2,false,0,13,3)
F8D=F(2,"souls",".CSMsg.HUNYINGINFO.souls",2,1,2,false,0,13,3)
F9D=F(2,"exp",".CSMsg.HUNYINGINFO.exp",3,2,2,false,0,13,3)
F10D=F(2,"idleStage",".CSMsg.HUNYINGINFO.idleStage",4,3,2,false,0,13,3)
F11D=F(2,"passStage",".CSMsg.HUNYINGINFO.passStage",5,4,2,false,0,13,3)
F12D=F(2,"pals",".CSMsg.HUNYINGINFO.pals",6,5,3,false,{},11,10)
F13D=F(2,"idleTime",".CSMsg.HUNYINGINFO.idleTime",7,6,2,false,0,13,3)
M5G=D(1,"HUNYINGINFO",".CSMsg.HUNYINGINFO",false,{},{},nil,{})
F14D=F(2,"nType",".CSMsg.HUNTINGRESINFO.nType",1,0,2,false,nil,14,8)
F15D=F(2,"nRewards",".CSMsg.HUNTINGRESINFO.nRewards",2,1,3,false,{},11,10)
M7G=D(1,"HUNTINGRESINFO",".CSMsg.HUNTINGRESINFO",false,{},{},nil,{})
F16D=F(2,"magicWater",".CSMsg.ILLUSIONTOWERINFO.magicWater",1,0,2,false,0,13,3)
F17D=F(2,"towerOfIllusionStage",".CSMsg.ILLUSIONTOWERINFO.towerOfIllusionStage",2,1,2,false,0,13,3)
F18D=F(2,"recoverWaterTimeRemain",".CSMsg.ILLUSIONTOWERINFO.recoverWaterTimeRemain",3,2,2,false,0,13,3)
F19D=F(2,"remainSweepTimes",".CSMsg.ILLUSIONTOWERINFO.remainSweepTimes",4,3,2,false,0,13,3)
F20D=F(2,"buySweepTimes",".CSMsg.ILLUSIONTOWERINFO.buySweepTimes",5,4,2,false,0,13,3)
F21D=F(2,"baseSweepTimes",".CSMsg.ILLUSIONTOWERINFO.baseSweepTimes",6,5,1,false,0,13,3)
M9G=D(1,"ILLUSIONTOWERINFO",".CSMsg.ILLUSIONTOWERINFO",false,{},{},nil,{})
F22D=F(2,"stage",".CSMsg.FactionInfo.stage",1,0,2,false,0,5,1)
F23D=F(2,"level",".CSMsg.FactionInfo.level",2,1,2,false,0,5,1)
F24D=F(2,"count",".CSMsg.FactionInfo.count",3,2,2,false,0,5,1)
M10G=D(1,"FactionInfo",".CSMsg.FactionInfo",false,{},{},nil,{})
F25D=F(2,"arenaType",".CSMsg.ArenaEnterBattle.arenaType",1,0,2,false,nil,14,8)
F26D=F(2,"rivalID",".CSMsg.ArenaEnterBattle.rivalID",2,1,2,false,0,13,3)
F27D=F(2,"lineupList",".CSMsg.ArenaEnterBattle.lineupList",3,2,3,false,{},11,10)
M11G=D(1,"ArenaEnterBattle",".CSMsg.ArenaEnterBattle",false,{},{},nil,{})
F28D=F(2,"arenaType",".CSMsg.ArenaEnterBattleRsp.arenaType",1,0,2,false,nil,14,8)
F29D=F(2,"winnerScoreChange",".CSMsg.ArenaEnterBattleRsp.winnerScoreChange",2,1,1,false,0,17,1)
F30D=F(2,"loserScoreChange",".CSMsg.ArenaEnterBattleRsp.loserScoreChange",3,2,1,false,0,17,1)
F31D=F(2,"winnerCurScores",".CSMsg.ArenaEnterBattleRsp.winnerCurScores",4,3,1,false,0,13,3)
F32D=F(2,"loserCurScores",".CSMsg.ArenaEnterBattleRsp.loserCurScores",5,4,1,false,0,13,3)
F33D=F(2,"remainFreeTimes",".CSMsg.ArenaEnterBattleRsp.remainFreeTimes",6,5,1,false,0,13,3)
F34D=F(2,"winnerWinFlags",".CSMsg.ArenaEnterBattleRsp.winnerWinFlags",7,6,1,false,0,13,3)
F35D=F(2,"loserWinFlags",".CSMsg.ArenaEnterBattleRsp.loserWinFlags",8,7,1,false,0,13,3)
F36D=F(2,"remainVitalityPoint",".CSMsg.ArenaEnterBattleRsp.remainVitalityPoint",9,8,1,false,0,13,3)
F37D=F(2,"rewardID",".CSMsg.ArenaEnterBattleRsp.rewardID",10,9,3,false,{},13,3)
F38D=F(2,"winnerOldGradesID",".CSMsg.ArenaEnterBattleRsp.winnerOldGradesID",11,10,1,false,0,13,3)
F39D=F(2,"loserOldGradesID",".CSMsg.ArenaEnterBattleRsp.loserOldGradesID",12,11,1,false,0,13,3)
F40D=F(2,"winnerOldRanking",".CSMsg.ArenaEnterBattleRsp.winnerOldRanking",13,12,1,false,0,13,3)
F41D=F(2,"loserOldRanking",".CSMsg.ArenaEnterBattleRsp.loserOldRanking",14,13,1,false,0,13,3)
M14G=D(1,"ArenaEnterBattleRsp",".CSMsg.ArenaEnterBattleRsp",false,{},{},nil,{})
F42D=F(2,"iTeamId",".CSMsg.TrialEnterBattle.iTeamId",1,0,2,false,0,5,1)
F43D=F(2,"iEventSid",".CSMsg.TrialEnterBattle.iEventSid",2,1,2,false,0,5,1)
M15G=D(1,"TrialEnterBattle",".CSMsg.TrialEnterBattle",false,{},{},nil,{})
F44D=F(2,"errorcode",".CSMsg.TrialEnterBattleRsp.errorcode",1,0,2,false,nil,14,8)
F45D=F(2,"iTeamId",".CSMsg.TrialEnterBattleRsp.iTeamId",2,1,2,false,0,5,1)
F46D=F(2,"iEventSid",".CSMsg.TrialEnterBattleRsp.iEventSid",3,2,2,false,0,5,1)
M16G=D(1,"TrialEnterBattleRsp",".CSMsg.TrialEnterBattleRsp",false,{},{},nil,{})
F47D=F(2,"sid",".CSMsg.LeagueBossBattle.sid",1,0,2,false,0,5,1)
M18G=D(1,"LeagueBossBattle",".CSMsg.LeagueBossBattle",false,{},{},nil,{})
F48D=F(2,"errorcode",".CSMsg.LeagueBossBattleRsp.errorcode",1,0,2,false,0,5,1)
M19G=D(1,"LeagueBossBattleRsp",".CSMsg.LeagueBossBattleRsp",false,{},{},nil,{})
F49D=F(2,"roleID",".CSMsg.MateMonsterBattle.roleID",1,0,2,false,0,5,1)
M20G=D(1,"MateMonsterBattle",".CSMsg.MateMonsterBattle",false,{},{},nil,{})
F50D=F(2,"errorcode",".CSMsg.MateMonsterBattleRsp.errorcode",1,0,2,false,0,5,1)
F51D=F(2,"roleID",".CSMsg.MateMonsterBattleRsp.roleID",2,1,2,false,0,5,1)
M21G=D(1,"MateMonsterBattleRsp",".CSMsg.MateMonsterBattleRsp",false,{},{},nil,{})
F52D=F(2,"dwRivalRoleId",".CSMsg.CompeteEnterBattle.dwRivalRoleId",1,0,2,false,0,5,1)
M22G=D(1,"CompeteEnterBattle",".CSMsg.CompeteEnterBattle",false,{},{},nil,{})
F53D=F(2,"errorcode",".CSMsg.CompeteEnterBattleRsp.errorcode",1,0,2,false,0,5,1)
F54D=F(2,"dwRivalRoleId",".CSMsg.CompeteEnterBattleRsp.dwRivalRoleId",2,1,2,false,0,5,1)
M23G=D(1,"CompeteEnterBattleRsp",".CSMsg.CompeteEnterBattleRsp",false,{},{},nil,{})
F55D=F(2,"bossid",".CSMsg.SlaveBattle.bossid",1,0,2,false,0,5,1)
F56D=F(2,"slaveid",".CSMsg.SlaveBattle.slaveid",2,1,2,false,0,5,1)
F57D=F(2,"type",".CSMsg.SlaveBattle.type",3,2,2,false,nil,14,8)
F58D=F(2,"conquerid",".CSMsg.SlaveBattle.conquerid",4,3,1,false,0,5,1)
M24G=D(1,"SlaveBattle",".CSMsg.SlaveBattle",false,{},{},nil,{})
F59D=F(2,"errorcode",".CSMsg.SlaveBattleRsp.errorcode",1,0,2,false,0,5,1)
F60D=F(2,"bossid",".CSMsg.SlaveBattleRsp.bossid",2,1,1,false,0,5,1)
F61D=F(2,"slaveid",".CSMsg.SlaveBattleRsp.slaveid",3,2,1,false,0,5,1)
F62D=F(2,"type",".CSMsg.SlaveBattleRsp.type",4,3,1,false,nil,14,8)
F63D=F(2,"conquerid",".CSMsg.SlaveBattleRsp.conquerid",5,4,1,false,0,5,1)
M26G=D(1,"SlaveBattleRsp",".CSMsg.SlaveBattleRsp",false,{},{},nil,{})
F64D=F(2,"worldid",".CSMsg.FriendBattle.worldid",1,0,2,false,0,5,1)
F65D=F(2,"dbid",".CSMsg.FriendBattle.dbid",2,1,2,false,0,5,1)
M27G=D(1,"FriendBattle",".CSMsg.FriendBattle",false,{},{},nil,{})
F66D=F(2,"errorcode",".CSMsg.FriendBattleRsp.errorcode",1,0,2,false,0,5,1)
F67D=F(2,"worldid",".CSMsg.FriendBattleRsp.worldid",2,1,2,false,0,5,1)
F68D=F(2,"dbid",".CSMsg.FriendBattleRsp.dbid",3,2,2,false,0,5,1)
M28G=D(1,"FriendBattleRsp",".CSMsg.FriendBattleRsp",false,{},{},nil,{})
F69D=F(2,"pos",".CSMsg.MazeBattle.pos",1,0,2,false,0,5,1)
M29G=D(1,"MazeBattle",".CSMsg.MazeBattle",false,{},{},nil,{})
F70D=F(2,"paramName",".CSMsg.ExtraErrData.paramName",1,0,2,false,"",9,9)
F71D=F(2,"paramValue",".CSMsg.ExtraErrData.paramValue",2,1,2,false,"",9,9)
M30G=D(1,"ExtraErrData",".CSMsg.ExtraErrData",false,{},{},nil,{})
F72D=F(2,"errorcode",".CSMsg.MazeBattleRsp.errorcode",1,0,2,false,0,5,1)
F73D=F(2,"pos",".CSMsg.MazeBattleRsp.pos",2,1,2,false,0,5,1)
F74D=F(2,"errdata",".CSMsg.MazeBattleRsp.errdata",3,2,3,false,{},11,10)
M31G=D(1,"MazeBattleRsp",".CSMsg.MazeBattleRsp",false,{},{},nil,{})
F75D=F(2,"sid",".CSMsg.PeakBattle.sid",1,0,2,false,0,5,1)
M32G=D(1,"PeakBattle",".CSMsg.PeakBattle",false,{},{},nil,{})
F76D=F(2,"errorcode",".CSMsg.PeakBattleRsp.errorcode",1,0,2,false,0,5,1)
F77D=F(2,"sid",".CSMsg.PeakBattleRsp.sid",2,1,2,false,0,5,1)
F78D=F(2,"id",".CSMsg.PeakBattleRsp.id",3,2,1,false,0,5,1)
F79D=F(2,"index",".CSMsg.PeakBattleRsp.index",4,3,1,false,0,5,1)
M33G=D(1,"PeakBattleRsp",".CSMsg.PeakBattleRsp",false,{},{},nil,{})
F80D=F(2,"errorcode",".CSMsg.FactionBattleRsp.errorcode",1,0,2,false,0,5,1)
M34G=D(1,"FactionBattleRsp",".CSMsg.FactionBattleRsp",false,{},{},nil,{})
F81D=F(2,"mapType",".CSMsg.AshdungeonBattle.mapType",1,0,2,false,0,5,1)
F82D=F(2,"stageLevel",".CSMsg.AshdungeonBattle.stageLevel",2,1,2,false,0,5,1)
F83D=F(2,"skipBattle",".CSMsg.AshdungeonBattle.skipBattle",3,2,2,false,false,8,7)
M35G=D(1,"AshdungeonBattle",".CSMsg.AshdungeonBattle",false,{},{},nil,{})
F84D=F(2,"type",".CSMsg.AshShopReward.type",1,0,2,false,0,5,1)
F85D=F(2,"id",".CSMsg.AshShopReward.id",2,1,2,false,0,5,1)
M36G=D(1,"AshShopReward",".CSMsg.AshShopReward",false,{},{},nil,{})
F86D=F(2,"errorcode",".CSMsg.AshdungeonBattleRsp.errorcode",1,0,2,false,nil,14,8)
F87D=F(2,"map",".CSMsg.AshdungeonBattleRsp.map",2,1,2,false,0,5,1)
F88D=F(2,"level",".CSMsg.AshdungeonBattleRsp.level",3,2,2,false,0,5,1)
F89D=F(2,"arrLevelMedicine",".CSMsg.AshdungeonBattleRsp.arrLevelMedicine",4,3,3,false,{},5,1)
F90D=F(2,"arrLevelShop",".CSMsg.AshdungeonBattleRsp.arrLevelShop",5,4,3,false,{},11,10)
F91D=F(2,"arrMapReward",".CSMsg.AshdungeonBattleRsp.arrMapReward",6,5,3,false,{},5,1)
M37G=D(1,"AshdungeonBattleRsp",".CSMsg.AshdungeonBattleRsp",false,{},{},nil,{})
F92D=F(2,"enType",".CSMsg.ActivityBrokenSpaceTime.enType",1,0,2,false,nil,14,8)
F93D=F(2,"enBattleType",".CSMsg.ActivityBrokenSpaceTime.enBattleType",2,1,2,false,nil,14,8)
F94D=F(2,"mopUpTimes",".CSMsg.ActivityBrokenSpaceTime.mopUpTimes",3,2,1,false,0,5,1)
M38G=D(1,"ActivityBrokenSpaceTime",".CSMsg.ActivityBrokenSpaceTime",false,{},{},nil,{})
F95D=F(2,"ectypeID",".CSMsg.EquipEctypeInfo.ectypeID",1,0,2,false,0,5,1)
F96D=F(2,"bBigPrize",".CSMsg.EquipEctypeInfo.bBigPrize",2,1,2,false,false,8,7)
M41G=D(1,"EquipEctypeInfo",".CSMsg.EquipEctypeInfo",false,{},{},nil,{})
F97D=F(2,"stageType",".CSMsg.TMSG_PLAYER_INPUT_CHALLENGE_REQ.stageType",1,0,2,false,nil,14,8)
M42G=D(1,"TMSG_PLAYER_INPUT_CHALLENGE_REQ",".CSMsg.TMSG_PLAYER_INPUT_CHALLENGE_REQ",false,{},{},nil,{})
F98D=F(2,"stageType",".CSMsg.TMSG_PLAYER_INPUT_CHALLENGE_RSP.stageType",1,0,2,false,nil,14,8)
F99D=F(2,"huntingInfo",".CSMsg.TMSG_PLAYER_INPUT_CHALLENGE_RSP.huntingInfo",2,1,1,false,nil,11,10)
F100D=F(2,"illusionTowerInfo",".CSMsg.TMSG_PLAYER_INPUT_CHALLENGE_RSP.illusionTowerInfo",3,2,1,false,nil,11,10)
F101D=F(2,"faction",".CSMsg.TMSG_PLAYER_INPUT_CHALLENGE_RSP.faction",4,3,1,false,nil,11,10)
M44G=D(1,"TMSG_PLAYER_INPUT_CHALLENGE_RSP",".CSMsg.TMSG_PLAYER_INPUT_CHALLENGE_RSP",false,{},{},nil,{})
F102D=F(2,"ntfType",".CSMsg.TMSG_PLAYER_IDLE_REWARD_NTF.ntfType",1,0,2,false,nil,14,8)
F103D=F(2,"huntingResInfo",".CSMsg.TMSG_PLAYER_IDLE_REWARD_NTF.huntingResInfo",2,1,1,false,nil,11,10)
F104D=F(2,"illusionTowerInfo",".CSMsg.TMSG_PLAYER_IDLE_REWARD_NTF.illusionTowerInfo",3,2,1,false,nil,11,10)
M45G=D(1,"TMSG_PLAYER_IDLE_REWARD_NTF",".CSMsg.TMSG_PLAYER_IDLE_REWARD_NTF",false,{},{},nil,{})
F105D=F(2,"stageType",".CSMsg.TMSG_PLAYER_GET_IDLE_REWARD_REQ.stageType",1,0,2,false,nil,14,8)
F106D=F(2,"rewardType",".CSMsg.TMSG_PLAYER_GET_IDLE_REWARD_REQ.rewardType",2,1,2,false,nil,14,8)
M46G=D(1,"TMSG_PLAYER_GET_IDLE_REWARD_REQ",".CSMsg.TMSG_PLAYER_GET_IDLE_REWARD_REQ",false,{},{},nil,{})
F107D=F(2,"itemID",".CSMsg.IdleGoodsRsp.itemID",1,0,2,false,0,13,3)
F108D=F(2,"itemNum",".CSMsg.IdleGoodsRsp.itemNum",2,1,2,false,0,13,3)
F109D=F(2,"sid",".CSMsg.IdleGoodsRsp.sid",3,2,3,false,{},13,3)
M48G=D(1,"IdleGoodsRsp",".CSMsg.IdleGoodsRsp",false,{},{},nil,{})
F110D=F(2,"rewardArr",".CSMsg.TCrushBlowRsp.rewardArr",1,0,1,false,nil,11,10)
F111D=F(2,"checkpointScores",".CSMsg.TCrushBlowRsp.checkpointScores",2,1,2,false,0,5,1)
F112D=F(2,"pals",".CSMsg.TCrushBlowRsp.pals",3,2,3,false,{},11,10)
F113D=F(2,"monsters",".CSMsg.TCrushBlowRsp.monsters",4,3,3,false,{},5,1)
M49G=D(1,"TCrushBlowRsp",".CSMsg.TCrushBlowRsp",false,{},{},nil,{})
F114D=F(2,"stageType",".CSMsg.TMSG_PLAYER_GET_IDLE_REWARD_RSP.stageType",1,0,2,false,nil,14,8)
F115D=F(2,"rewardType",".CSMsg.TMSG_PLAYER_GET_IDLE_REWARD_RSP.rewardType",2,1,2,false,nil,14,8)
F116D=F(2,"code",".CSMsg.TMSG_PLAYER_GET_IDLE_REWARD_RSP.code",3,2,2,false,nil,14,8)
F117D=F(2,"idleTime",".CSMsg.TMSG_PLAYER_GET_IDLE_REWARD_RSP.idleTime",4,3,2,false,0,13,3)
F118D=F(2,"rewardArr",".CSMsg.TMSG_PLAYER_GET_IDLE_REWARD_RSP.rewardArr",5,4,3,false,{},11,10)
F119D=F(2,"decrArr",".CSMsg.TMSG_PLAYER_GET_IDLE_REWARD_RSP.decrArr",6,5,1,false,nil,11,10)
M51G=D(1,"TMSG_PLAYER_GET_IDLE_REWARD_RSP",".CSMsg.TMSG_PLAYER_GET_IDLE_REWARD_RSP",false,{},{},nil,{})
F120D=F(2,"stageType",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_REQ.stageType",1,0,2,false,nil,14,8)
F121D=F(2,"stageLv",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_REQ.stageLv",2,1,1,false,0,13,3)
F122D=F(2,"isFriendHelp",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_REQ.isFriendHelp",3,2,1,false,false,8,7)
F123D=F(2,"friendId",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_REQ.friendId",4,3,1,false,0,13,3)
F124D=F(2,"pals",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_REQ.pals",5,4,3,false,{},11,10)
F125D=F(2,"arenaEnterBattle",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_REQ.arenaEnterBattle",6,5,1,false,nil,11,10)
F126D=F(2,"isSkipBattle",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_REQ.isSkipBattle",7,6,1,false,false,8,7)
F127D=F(2,"trialEnterBattle",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_REQ.trialEnterBattle",8,7,1,false,nil,11,10)
F128D=F(2,"ashdungeonBattle",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_REQ.ashdungeonBattle",9,8,1,false,nil,11,10)
F129D=F(2,"brokenSpaceTimeBattle",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_REQ.brokenSpaceTimeBattle",10,9,1,false,nil,11,10)
F130D=F(2,"leagueBossBattle",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_REQ.leagueBossBattle",11,10,1,false,nil,11,10)
F131D=F(2,"competeBattle",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_REQ.competeBattle",12,11,1,false,nil,11,10)
F132D=F(2,"mateBattle",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_REQ.mateBattle",13,12,1,false,nil,11,10)
F133D=F(2,"mazeBattle",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_REQ.mazeBattle",14,13,1,false,nil,11,10)
F134D=F(2,"friendBattle",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_REQ.friendBattle",15,14,1,false,nil,11,10)
F135D=F(2,"weaponId",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_REQ.weaponId",16,15,1,false,0,5,1)
F136D=F(2,"peakBattle",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_REQ.peakBattle",17,16,1,false,nil,11,10)
F137D=F(2,"equipEctype",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_REQ.equipEctype",18,17,1,false,nil,11,10)
F138D=F(2,"bCrushBlow",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_REQ.bCrushBlow",19,18,1,false,false,8,7)
F139D=F(2,"VoidArenaAddLv",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_REQ.VoidArenaAddLv",20,19,1,false,0,5,1)
F140D=F(2,"VoidArenaID",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_REQ.VoidArenaID",21,20,1,false,0,5,1)
F141D=F(2,"palsLineUp",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_REQ.palsLineUp",22,21,3,false,{},11,10)
F142D=F(2,"SpaceExplorePosition",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_REQ.SpaceExplorePosition",23,22,1,false,0,5,1)
F143D=F(2,"ActivityID",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_REQ.ActivityID",24,23,1,false,0,5,1)
F144D=F(2,"slaveBattle",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_REQ.slaveBattle",25,24,1,false,nil,11,10)
M52G=D(1,"TMSG_PLAYER_ENTER_BATTLE_REQ",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_REQ",false,{},{},nil,{})
F145D=F(2,"nStarVal",".CSMsg.ChinaRedBattleRsp.nStarVal",1,0,2,false,0,13,3)
M53G=D(1,"ChinaRedBattleRsp",".CSMsg.ChinaRedBattleRsp",false,{},{},nil,{})
F146D=F(2,"level",".CSMsg.MultiKillingTowerRsp.level",1,0,2,false,0,5,1)
F147D=F(2,"wave",".CSMsg.MultiKillingTowerRsp.wave",2,1,2,false,0,5,1)
M54G=D(1,"MultiKillingTowerRsp",".CSMsg.MultiKillingTowerRsp",false,{},{},nil,{})
F148D=F(2,"stageType",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_RSP.stageType",1,0,2,false,nil,14,8)
F149D=F(2,"battleResult",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_RSP.battleResult",2,1,1,false,false,8,7)
F150D=F(2,"idleStage",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_RSP.idleStage",3,2,1,false,0,13,3)
F151D=F(2,"passStage",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_RSP.passStage",4,3,1,false,0,13,3)
F152D=F(2,"arenaRsp",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_RSP.arenaRsp",5,4,1,false,nil,11,10)
F153D=F(2,"trialRsp",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_RSP.trialRsp",6,5,1,false,nil,11,10)
F154D=F(2,"ashdungeonRsp",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_RSP.ashdungeonRsp",7,6,1,false,nil,11,10)
F155D=F(2,"leagueBossRsp",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_RSP.leagueBossRsp",8,7,1,false,nil,11,10)
F156D=F(2,"competeBattleRsp",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_RSP.competeBattleRsp",9,8,1,false,nil,11,10)
F157D=F(2,"mateBattleRsp",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_RSP.mateBattleRsp",10,9,1,false,nil,11,10)
F158D=F(2,"mazeBattleRsp",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_RSP.mazeBattleRsp",11,10,1,false,nil,11,10)
F159D=F(2,"friendBattleRsp",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_RSP.friendBattleRsp",12,11,1,false,nil,11,10)
F160D=F(2,"factionBattleRsp",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_RSP.factionBattleRsp",13,12,1,false,nil,11,10)
F161D=F(2,"err",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_RSP.err",14,13,1,false,nil,14,8)
F162D=F(2,"peakBattleRsp",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_RSP.peakBattleRsp",15,14,1,false,nil,11,10)
F163D=F(2,"bCrushBlow",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_RSP.bCrushBlow",16,15,1,false,false,8,7)
F164D=F(2,"crushBlowInfo",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_RSP.crushBlowInfo",17,16,1,false,nil,11,10)
F165D=F(2,"ActivityID",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_RSP.ActivityID",18,17,1,false,0,5,1)
F166D=F(2,"chinaRedBattleRsp",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_RSP.chinaRedBattleRsp",19,18,1,false,nil,11,10)
F167D=F(2,"multiKillingTowerRsp",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_RSP.multiKillingTowerRsp",20,19,1,false,nil,11,10)
F168D=F(2,"slaveBattleRsp",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_RSP.slaveBattleRsp",21,20,1,false,nil,11,10)
M55G=D(1,"TMSG_PLAYER_ENTER_BATTLE_RSP",".CSMsg.TMSG_PLAYER_ENTER_BATTLE_RSP",false,{},{},nil,{})
F169D=F(2,"stageType",".CSMsg.TMSG_PLAYER_MODIFY_IDLE_STAGE_REQ.stageType",1,0,2,false,nil,14,8)
F170D=F(2,"modifyHurdle",".CSMsg.TMSG_PLAYER_MODIFY_IDLE_STAGE_REQ.modifyHurdle",2,1,2,false,0,13,3)
M56G=D(1,"TMSG_PLAYER_MODIFY_IDLE_STAGE_REQ",".CSMsg.TMSG_PLAYER_MODIFY_IDLE_STAGE_REQ",false,{},{},nil,{})
F171D=F(2,"successFlag",".CSMsg.TMSG_PLAYER_MODIFY_IDLE_STAGE_RSP.successFlag",1,0,2,false,false,8,7)
F172D=F(2,"curHurdle",".CSMsg.TMSG_PLAYER_MODIFY_IDLE_STAGE_RSP.curHurdle",2,1,2,false,0,13,3)
M57G=D(1,"TMSG_PLAYER_MODIFY_IDLE_STAGE_RSP",".CSMsg.TMSG_PLAYER_MODIFY_IDLE_STAGE_RSP",false,{},{},nil,{})
F173D=F(2,"itemID",".CSMsg.TItemRewardAfterBattle.itemID",1,0,2,false,0,13,3)
F174D=F(2,"itemNum",".CSMsg.TItemRewardAfterBattle.itemNum",2,1,2,false,0,4,4)
F175D=F(2,"itemSid",".CSMsg.TItemRewardAfterBattle.itemSid",3,2,3,false,{},13,3)
F176D=F(2,"itemFlag",".CSMsg.TItemRewardAfterBattle.itemFlag",4,3,1,false,0,5,1)
M58G=D(1,"TItemRewardAfterBattle",".CSMsg.TItemRewardAfterBattle",false,{},{},nil,{})
F177D=F(2,"rewardID",".CSMsg.TRewardIDAfterBattle.rewardID",1,0,2,false,0,13,3)
F178D=F(2,"Num",".CSMsg.TRewardIDAfterBattle.Num",2,1,2,false,0,4,4)
F179D=F(2,"sid",".CSMsg.TRewardIDAfterBattle.sid",3,2,3,false,{},13,3)
M59G=D(1,"TRewardIDAfterBattle",".CSMsg.TRewardIDAfterBattle",false,{},{},nil,{})
F180D=F(2,"itemReward",".CSMsg.TRewardAfterBattle.itemReward",1,0,3,false,{},11,10)
F181D=F(2,"RewardID",".CSMsg.TRewardAfterBattle.RewardID",2,1,3,false,{},11,10)
M50G=D(1,"TRewardAfterBattle",".CSMsg.TRewardAfterBattle",false,{},{},nil,{})
F182D=F(2,"lefttroopce",".CSMsg.tBattleTroopCE.lefttroopce",1,0,2,false,0,5,1)
F183D=F(2,"righttroopce",".CSMsg.tBattleTroopCE.righttroopce",2,1,2,false,0,5,1)
M60G=D(1,"tBattleTroopCE",".CSMsg.tBattleTroopCE",false,{},{},nil,{})
F184D=F(2,"stageType",".CSMsg.TMSG_GAMEOVER_NTF.stageType",1,0,2,false,nil,14,8)
F185D=F(2,"battleReport",".CSMsg.TMSG_GAMEOVER_NTF.battleReport",2,1,3,false,{},12,9)
F186D=F(2,"battleID",".CSMsg.TMSG_GAMEOVER_NTF.battleID",3,2,3,false,{},9,9)
F187D=F(2,"battlePartyName",".CSMsg.TMSG_GAMEOVER_NTF.battlePartyName",4,3,1,false,"",9,9)
F188D=F(2,"battleTrName",".CSMsg.TMSG_GAMEOVER_NTF.battleTrName",5,4,1,false,"",9,9)
F189D=F(2,"battleTimeStr",".CSMsg.TMSG_GAMEOVER_NTF.battleTimeStr",6,5,1,false,"",9,9)
F190D=F(2,"battleTrName1",".CSMsg.TMSG_GAMEOVER_NTF.battleTrName1",7,6,1,false,"",9,9)
F191D=F(2,"battleTimeStr1",".CSMsg.TMSG_GAMEOVER_NTF.battleTimeStr1",8,7,1,false,"",9,9)
F192D=F(2,"damage",".CSMsg.TMSG_GAMEOVER_NTF.damage",9,8,1,false,0,3,2)
F193D=F(2,"rewardArr",".CSMsg.TMSG_GAMEOVER_NTF.rewardArr",10,9,1,false,nil,11,10)
F194D=F(2,"checkpointScores",".CSMsg.TMSG_GAMEOVER_NTF.checkpointScores",11,10,1,false,0,5,1)
F195D=F(2,"equipEctype",".CSMsg.TMSG_GAMEOVER_NTF.equipEctype",12,11,1,false,nil,11,10)
F196D=F(2,"ntfStep",".CSMsg.TMSG_GAMEOVER_NTF.ntfStep",13,12,1,false,0,5,1)
F197D=F(2,"WeekendArenaWinnerId",".CSMsg.TMSG_GAMEOVER_NTF.WeekendArenaWinnerId",14,13,1,false,0,5,1)
F198D=F(2,"arrce",".CSMsg.TMSG_GAMEOVER_NTF.arrce",15,14,3,false,{},11,10)
M61G=D(1,"TMSG_GAMEOVER_NTF",".CSMsg.TMSG_GAMEOVER_NTF",false,{},{},nil,{})
F199D=F(2,"stageType",".CSMsg.TMSG_SAVE_PLAYER_LINEUP_REQ.stageType",1,0,2,false,nil,14,8)
F200D=F(2,"pals",".CSMsg.TMSG_SAVE_PLAYER_LINEUP_REQ.pals",2,1,3,false,{},11,10)
F201D=F(2,"arenaType",".CSMsg.TMSG_SAVE_PLAYER_LINEUP_REQ.arenaType",3,2,1,false,nil,14,8)
M62G=D(1,"TMSG_SAVE_PLAYER_LINEUP_REQ",".CSMsg.TMSG_SAVE_PLAYER_LINEUP_REQ",false,{},{},nil,{})
F202D=F(2,"stageType",".CSMsg.TMSG_SAVE_PLAYER_LINEUP_RSP.stageType",1,0,2,false,nil,14,8)
F203D=F(2,"code",".CSMsg.TMSG_SAVE_PLAYER_LINEUP_RSP.code",2,1,2,false,nil,14,8)
F204D=F(2,"arenaType",".CSMsg.TMSG_SAVE_PLAYER_LINEUP_RSP.arenaType",3,2,1,false,nil,14,8)
M63G=D(1,"TMSG_SAVE_PLAYER_LINEUP_RSP",".CSMsg.TMSG_SAVE_PLAYER_LINEUP_RSP",false,{},{},nil,{})
F205D=F(2,"stageType",".CSMsg.TMSG_EXIT_MODULE_NTF.stageType",1,0,2,false,nil,14,8)
M64G=D(1,"TMSG_EXIT_MODULE_NTF",".CSMsg.TMSG_EXIT_MODULE_NTF",false,{},{},nil,{})
F206D=F(2,"roleID",".CSMsg.TRankListData.roleID",1,0,2,false,0,13,3)
F207D=F(2,"roleLv",".CSMsg.TRankListData.roleLv",2,1,2,false,0,13,3)
F208D=F(2,"faceID",".CSMsg.TRankListData.faceID",3,2,2,false,0,13,3)
F209D=F(2,"name",".CSMsg.TRankListData.name",4,3,2,false,"",9,9)
F210D=F(2,"mapType",".CSMsg.TRankListData.mapType",5,4,2,false,0,13,3)
F211D=F(2,"stageLv",".CSMsg.TRankListData.stageLv",6,5,2,false,0,13,3)
F212D=F(2,"lastChallengeTime",".CSMsg.TRankListData.lastChallengeTime",7,6,2,false,0,13,3)
F213D=F(2,"rank",".CSMsg.TRankListData.rank",8,7,2,false,0,13,3)
F214D=F(2,"isOwn",".CSMsg.TRankListData.isOwn",9,8,2,false,false,8,7)
F215D=F(2,"frameID",".CSMsg.TRankListData.frameID",10,9,1,false,0,13,3)
M65G=D(1,"TRankListData",".CSMsg.TRankListData",false,{},{},nil,{})
F216D=F(2,"partType",".CSMsg.TMSG_CLIENT_GET_RANK_LIST_REQ.partType",1,0,2,false,nil,14,8)
F217D=F(2,"campType",".CSMsg.TMSG_CLIENT_GET_RANK_LIST_REQ.campType",2,1,1,false,nil,14,8)
M66G=D(1,"TMSG_CLIENT_GET_RANK_LIST_REQ",".CSMsg.TMSG_CLIENT_GET_RANK_LIST_REQ",false,{},{},nil,{})
F218D=F(2,"partType",".CSMsg.TMSG_CLIENT_GET_RANK_LIST_RSP.partType",1,0,2,false,nil,14,8)
F219D=F(2,"enErrorCode",".CSMsg.TMSG_CLIENT_GET_RANK_LIST_RSP.enErrorCode",2,1,2,false,nil,14,8)
M69G=D(1,"TMSG_CLIENT_GET_RANK_LIST_RSP",".CSMsg.TMSG_CLIENT_GET_RANK_LIST_RSP",false,{},{},nil,{})
F220D=F(2,"partType",".CSMsg.TMSG_CLIENT_UPDATE_RANK_LIST_NTF.partType",1,0,2,false,nil,14,8)
F221D=F(2,"campType",".CSMsg.TMSG_CLIENT_UPDATE_RANK_LIST_NTF.campType",2,1,1,false,nil,14,8)
F222D=F(2,"rankData",".CSMsg.TMSG_CLIENT_UPDATE_RANK_LIST_NTF.rankData",3,2,3,false,{},11,10)
M70G=D(1,"TMSG_CLIENT_UPDATE_RANK_LIST_NTF",".CSMsg.TMSG_CLIENT_UPDATE_RANK_LIST_NTF",false,{},{},nil,{})
F223D=F(2,"sweepStage",".CSMsg.TMSG_ILLUSION_TOWER_SWEEP_REQ.sweepStage",1,0,2,false,0,13,3)
F224D=F(2,"sweepTimes",".CSMsg.TMSG_ILLUSION_TOWER_SWEEP_REQ.sweepTimes",2,1,2,false,0,13,3)
M71G=D(1,"TMSG_ILLUSION_TOWER_SWEEP_REQ",".CSMsg.TMSG_ILLUSION_TOWER_SWEEP_REQ",false,{},{},nil,{})
F225D=F(2,"itemID",".CSMsg.TowerSweepReward.itemID",1,0,2,false,0,13,3)
F226D=F(2,"itemNum",".CSMsg.TowerSweepReward.itemNum",2,1,2,false,0,4,4)
F227D=F(2,"sid",".CSMsg.TowerSweepReward.sid",3,2,3,false,{},13,3)
M72G=D(1,"TowerSweepReward",".CSMsg.TowerSweepReward",false,{},{},nil,{})
F228D=F(2,"enErrorCode",".CSMsg.TMSG_ILLUSION_TOWER_SWEEP_RSP.enErrorCode",1,0,2,false,nil,14,8)
F229D=F(2,"rewardArr",".CSMsg.TMSG_ILLUSION_TOWER_SWEEP_RSP.rewardArr",2,1,3,false,{},11,10)
F230D=F(2,"remainSweepTimes",".CSMsg.TMSG_ILLUSION_TOWER_SWEEP_RSP.remainSweepTimes",3,2,1,false,0,13,3)
F231D=F(2,"buySweepTimes",".CSMsg.TMSG_ILLUSION_TOWER_SWEEP_RSP.buySweepTimes",4,3,1,false,0,13,3)
F232D=F(2,"baseSweepTimes",".CSMsg.TMSG_ILLUSION_TOWER_SWEEP_RSP.baseSweepTimes",5,4,1,false,0,13,3)
M73G=D(1,"TMSG_ILLUSION_TOWER_SWEEP_RSP",".CSMsg.TMSG_ILLUSION_TOWER_SWEEP_RSP",false,{},{},nil,{})
F233D=F(2,"stageType",".CSMsg.TMSG_BATTLE_END_OF_THE_PLAY_NTF.stageType",1,0,2,false,nil,14,8)
M74G=D(1,"TMSG_BATTLE_END_OF_THE_PLAY_NTF",".CSMsg.TMSG_BATTLE_END_OF_THE_PLAY_NTF",false,{},{},nil,{})
F234D=F(2,"isTakeDiamond",".CSMsg.TMSG_QIDLE_SWEEP_TIMES_REQ.isTakeDiamond",1,0,2,false,false,8,7)
M75G=D(1,"TMSG_QIDLE_SWEEP_TIMES_REQ",".CSMsg.TMSG_QIDLE_SWEEP_TIMES_REQ",false,{},{},nil,{})
F235D=F(2,"errCode",".CSMsg.TMSG_QIDLE_SWEEP_TIMES_RSP.errCode",1,0,2,false,nil,14,8)
F236D=F(2,"rewardArr",".CSMsg.TMSG_QIDLE_SWEEP_TIMES_RSP.rewardArr",2,1,3,false,{},11,10)
M76G=D(1,"TMSG_QIDLE_SWEEP_TIMES_RSP",".CSMsg.TMSG_QIDLE_SWEEP_TIMES_RSP",false,{},{},nil,{})
F237D=F(2,"isOpenQIdle",".CSMsg.TMSG_QIDLE_DATA_INFORM_NTF.isOpenQIdle",1,0,2,false,false,8,7)
F238D=F(2,"freeTimes",".CSMsg.TMSG_QIDLE_DATA_INFORM_NTF.freeTimes",2,1,1,false,0,13,3)
F239D=F(2,"buyTimes",".CSMsg.TMSG_QIDLE_DATA_INFORM_NTF.buyTimes",3,2,1,false,0,13,3)
F240D=F(2,"remBuyTimes",".CSMsg.TMSG_QIDLE_DATA_INFORM_NTF.remBuyTimes",4,3,1,false,0,13,3)
F241D=F(2,"remainTime",".CSMsg.TMSG_QIDLE_DATA_INFORM_NTF.remainTime",5,4,1,false,0,13,3)
F242D=F(2,"privilegetime",".CSMsg.TMSG_QIDLE_DATA_INFORM_NTF.privilegetime",6,5,1,false,0,13,3)
F243D=F(2,"totalTimes",".CSMsg.TMSG_QIDLE_DATA_INFORM_NTF.totalTimes",7,6,1,false,0,13,3)
M77G=D(1,"TMSG_QIDLE_DATA_INFORM_NTF",".CSMsg.TMSG_QIDLE_DATA_INFORM_NTF",false,{},{},nil,{})
F244D=F(2,"stage",".CSMsg.TMSG_BATTLE_DYNAMIC_DIFFI_INFO_NTF.stage",1,0,2,false,0,5,1)
F245D=F(2,"stype",".CSMsg.TMSG_BATTLE_DYNAMIC_DIFFI_INFO_NTF.stype",2,1,2,false,nil,14,8)
F246D=F(2,"CERate",".CSMsg.TMSG_BATTLE_DYNAMIC_DIFFI_INFO_NTF.CERate",3,2,2,false,.0,2,6)
M78G=D(1,"TMSG_BATTLE_DYNAMIC_DIFFI_INFO_NTF",".CSMsg.TMSG_BATTLE_DYNAMIC_DIFFI_INFO_NTF",false,{},{},nil,{})
M79G=D(1,"TMSG_ENTER_MAIN_BATTLE_NTF",".CSMsg.TMSG_ENTER_MAIN_BATTLE_NTF",false,{},{},{},{})
F247D=F(2,"powerid",".CSMsg.tSpaceDominatorEndGroup.powerid",1,0,3,false,{},5,1)
M80G=D(1,"tSpaceDominatorEndGroup",".CSMsg.tSpaceDominatorEndGroup",false,{},{},nil,{})
F248D=F(2,"roleid",".CSMsg.tSpaceDominatorGroupUser.roleid",1,0,2,false,0,13,3)
F249D=F(2,"powerid",".CSMsg.tSpaceDominatorGroupUser.powerid",2,1,2,false,0,5,1)
F250D=F(2,"idx",".CSMsg.tSpaceDominatorGroupUser.idx",3,2,2,false,0,5,1)
M81G=D(1,"tSpaceDominatorGroupUser",".CSMsg.tSpaceDominatorGroupUser",false,{},{},nil,{})
F251D=F(2,"uinfo",".CSMsg.tSpaceDominatorGroupUserInfo.uinfo",1,0,3,false,{},11,10)
M82G=D(1,"tSpaceDominatorGroupUserInfo",".CSMsg.tSpaceDominatorGroupUserInfo",false,{},{},nil,{})
F252D=F(2,"robotid",".CSMsg.tSpaceDominatorGroupRobot.robotid",1,0,2,false,0,13,3)
F253D=F(2,"powerid",".CSMsg.tSpaceDominatorGroupRobot.powerid",2,1,2,false,0,5,1)
F254D=F(2,"idx",".CSMsg.tSpaceDominatorGroupRobot.idx",3,2,3,false,{},5,1)
M83G=D(1,"tSpaceDominatorGroupRobot",".CSMsg.tSpaceDominatorGroupRobot",false,{},{},nil,{})
F255D=F(2,"info",".CSMsg.tSpaceDominatorGroupRobotInfo.info",1,0,3,false,{},11,10)
M84G=D(1,"tSpaceDominatorGroupRobotInfo",".CSMsg.tSpaceDominatorGroupRobotInfo",false,{},{},nil,{})
F256D=F(2,"powerid",".CSMsg.tSpaceDominatorDurationTime.powerid",1,0,2,false,0,5,1)
F257D=F(2,"durationTime",".CSMsg.tSpaceDominatorDurationTime.durationTime",2,1,2,false,0,5,1)
M85G=D(1,"tSpaceDominatorDurationTime",".CSMsg.tSpaceDominatorDurationTime",false,{},{},nil,{})
F258D=F(2,"startwday",".CSMsg.tSpaceDominatorDuration.startwday",1,0,2,false,0,5,1)
F259D=F(2,"starttime",".CSMsg.tSpaceDominatorDuration.starttime",2,1,2,false,0,5,1)
F260D=F(2,"info",".CSMsg.tSpaceDominatorDuration.info",3,2,3,false,{},11,10)
M86G=D(1,"tSpaceDominatorDuration",".CSMsg.tSpaceDominatorDuration",false,{},{},nil,{})
F261D=F(2,"baseinfo",".CSMsg.tSpaceDominatorRank.baseinfo",1,0,2,false,nil,11,10)
F262D=F(2,"worldid",".CSMsg.tSpaceDominatorRank.worldid",2,1,2,false,0,5,1)
F263D=F(2,"ce",".CSMsg.tSpaceDominatorRank.ce",3,2,2,false,0,5,1)
F264D=F(2,"damage",".CSMsg.tSpaceDominatorRank.damage",4,3,2,false,0,3,2)
F265D=F(2,"rankfloat",".CSMsg.tSpaceDominatorRank.rankfloat",5,4,2,false,0,5,1)
F266D=F(2,"pals",".CSMsg.tSpaceDominatorRank.pals",6,5,3,false,{},11,10)
F267D=F(2,"battleid",".CSMsg.tSpaceDominatorRank.battleid",7,6,1,false,"",9,9)
F268D=F(2,"todaydmg",".CSMsg.tSpaceDominatorRank.todaydmg",8,7,3,false,{},3,2)
F269D=F(2,"dmgyday",".CSMsg.tSpaceDominatorRank.dmgyday",9,8,1,false,0,5,1)
M87G=D(1,"tSpaceDominatorRank",".CSMsg.tSpaceDominatorRank",false,{},{},nil,{})
F270D=F(2,"rinfo",".CSMsg.tSpaceDominatorRobotInfo.rinfo",1,0,3,false,{},11,10)
M90G=D(1,"tSpaceDominatorRobotInfo",".CSMsg.tSpaceDominatorRobotInfo",false,{},{},nil,{})
F271D=F(2,"rid",".CSMsg.tSDRobotUpdateInfo.rid",1,0,2,false,0,5,1)
F272D=F(2,"yday",".CSMsg.tSDRobotUpdateInfo.yday",2,1,2,false,0,5,1)
M91G=D(1,"tSDRobotUpdateInfo",".CSMsg.tSDRobotUpdateInfo",false,{},{},nil,{})
F273D=F(2,"info",".CSMsg.tSpaceDominatorRobotUpdateTime.info",1,0,3,false,{},11,10)
M92G=D(1,"tSpaceDominatorRobotUpdateTime",".CSMsg.tSpaceDominatorRobotUpdateTime",false,{},{},nil,{})
F274D=F(2,"powerid",".CSMsg.TSpaceDominatorSettleInfo.powerid",1,0,2,false,0,5,1)
F275D=F(2,"rank",".CSMsg.TSpaceDominatorSettleInfo.rank",2,1,2,false,0,5,1)
F276D=F(2,"damage",".CSMsg.TSpaceDominatorSettleInfo.damage",3,2,2,false,0,3,2)
M93G=D(1,"TSpaceDominatorSettleInfo",".CSMsg.TSpaceDominatorSettleInfo",false,{},{},nil,{})
F277D=F(2,"mosterTeamId",".CSMsg.TMSG_SPACE_DOMINATOR_INFO_NTF.mosterTeamId",1,0,2,false,0,5,1)
F278D=F(2,"entry",".CSMsg.TMSG_SPACE_DOMINATOR_INFO_NTF.entry",2,1,3,false,{},11,10)
F279D=F(2,"settle",".CSMsg.TMSG_SPACE_DOMINATOR_INFO_NTF.settle",3,2,1,false,nil,11,10)
F280D=F(2,"starttime",".CSMsg.TMSG_SPACE_DOMINATOR_INFO_NTF.starttime",4,3,1,false,0,13,3)
F281D=F(2,"duration",".CSMsg.TMSG_SPACE_DOMINATOR_INFO_NTF.duration",5,4,1,false,0,13,3)
F282D=F(2,"bBattleRsp",".CSMsg.TMSG_SPACE_DOMINATOR_INFO_NTF.bBattleRsp",6,5,1,false,false,8,7)
F283D=F(2,"endtime",".CSMsg.TMSG_SPACE_DOMINATOR_INFO_NTF.endtime",7,6,1,false,0,13,3)
F284D=F(2,"boxReward",".CSMsg.TMSG_SPACE_DOMINATOR_INFO_NTF.boxReward",8,7,1,false,nil,11,10)
M94G=D(1,"TMSG_SPACE_DOMINATOR_INFO_NTF",".CSMsg.TMSG_SPACE_DOMINATOR_INFO_NTF",false,{},{},nil,{})
F285D=F(2,"rtype",".CSMsg.TMSG_SPACE_DOMINATOR_GET_RANK_REWARD_REQ.rtype",1,0,1,false,nil,14,8)
M95G=D(1,"TMSG_SPACE_DOMINATOR_GET_RANK_REWARD_REQ",".CSMsg.TMSG_SPACE_DOMINATOR_GET_RANK_REWARD_REQ",false,{},{},nil,{})
F286D=F(2,"err",".CSMsg.TMSG_SPACE_DOMINATOR_GET_RANK_REWARD_RSP.err",1,0,2,false,nil,14,8)
F287D=F(2,"rtype",".CSMsg.TMSG_SPACE_DOMINATOR_GET_RANK_REWARD_RSP.rtype",2,1,1,false,nil,14,8)
M97G=D(1,"TMSG_SPACE_DOMINATOR_GET_RANK_REWARD_RSP",".CSMsg.TMSG_SPACE_DOMINATOR_GET_RANK_REWARD_RSP",false,{},{},nil,{})
M98G=D(1,"TMSG_SPACE_DOMINATOR_GET_RANK_INFO_REQ",".CSMsg.TMSG_SPACE_DOMINATOR_GET_RANK_INFO_REQ",false,{},{},{},{})
F288D=F(2,"err",".CSMsg.TMSG_SPACE_DOMINATOR_GET_RANK_INFO_RSP.err",1,0,2,false,nil,14,8)
M99G=D(1,"TMSG_SPACE_DOMINATOR_GET_RANK_INFO_RSP",".CSMsg.TMSG_SPACE_DOMINATOR_GET_RANK_INFO_RSP",false,{},{},nil,{})
F289D=F(2,"rank",".CSMsg.TMSG_SPACE_DOMINATOR_SELF_RANK_NTF.rank",1,0,2,false,0,5,1)
F290D=F(2,"powerid",".CSMsg.TMSG_SPACE_DOMINATOR_SELF_RANK_NTF.powerid",2,1,2,false,0,5,1)
F291D=F(2,"groupid",".CSMsg.TMSG_SPACE_DOMINATOR_SELF_RANK_NTF.groupid",3,2,2,false,0,5,1)
F292D=F(2,"endtime",".CSMsg.TMSG_SPACE_DOMINATOR_SELF_RANK_NTF.endtime",4,3,1,false,0,13,3)
F293D=F(2,"hasreward",".CSMsg.TMSG_SPACE_DOMINATOR_SELF_RANK_NTF.hasreward",5,4,1,false,false,8,7)
M100G=D(1,"TMSG_SPACE_DOMINATOR_SELF_RANK_NTF",".CSMsg.TMSG_SPACE_DOMINATOR_SELF_RANK_NTF",false,{},{},nil,{})
F294D=F(2,"sinfo",".CSMsg.TMSG_SPACE_DOMINATOR_USER_SETTLE_NTF.sinfo",1,0,2,false,nil,11,10)
M101G=D(1,"TMSG_SPACE_DOMINATOR_USER_SETTLE_NTF",".CSMsg.TMSG_SPACE_DOMINATOR_USER_SETTLE_NTF",false,{},{},nil,{})
F295D=F(2,"count",".CSMsg.TMSG_SPACE_DOMINATOR_CHALLENGE_COUNT_NTF.count",1,0,2,false,0,5,1)
M102G=D(1,"TMSG_SPACE_DOMINATOR_CHALLENGE_COUNT_NTF",".CSMsg.TMSG_SPACE_DOMINATOR_CHALLENGE_COUNT_NTF",false,{},{},nil,{})
F296D=F(2,"powerid",".CSMsg.TMSG_SPACE_DOMINATOR_END_GROUP_NTF.powerid",1,0,3,false,{},5,1)
M103G=D(1,"TMSG_SPACE_DOMINATOR_END_GROUP_NTF",".CSMsg.TMSG_SPACE_DOMINATOR_END_GROUP_NTF",false,{},{},nil,{})
F297D=F(2,"powerid",".CSMsg.TMSG_SPACE_DOMINATOR_GROUP_ID_NTF.powerid",1,0,2,false,0,5,1)
F298D=F(2,"groupid",".CSMsg.TMSG_SPACE_DOMINATOR_GROUP_ID_NTF.groupid",2,1,2,false,0,5,1)
M104G=D(1,"TMSG_SPACE_DOMINATOR_GROUP_ID_NTF",".CSMsg.TMSG_SPACE_DOMINATOR_GROUP_ID_NTF",false,{},{},nil,{})
F299D=F(2,"stageType",".CSMsg.TMSG_SPACE_DOMINATOR_BATTLE_VIDEO_REQ.stageType",1,0,2,false,nil,14,8)
M105G=D(1,"TMSG_SPACE_DOMINATOR_BATTLE_VIDEO_REQ",".CSMsg.TMSG_SPACE_DOMINATOR_BATTLE_VIDEO_REQ",false,{},{},nil,{})
F300D=F(2,"hookLevel",".CSMsg.TMSG_SET_HOOKLEVELCHALLENGE_RESULT_REQ.hookLevel",1,0,2,false,0,5,1)
F301D=F(2,"starNum",".CSMsg.TMSG_SET_HOOKLEVELCHALLENGE_RESULT_REQ.starNum",2,1,2,false,0,5,1)
F302D=F(2,"miniGameType",".CSMsg.TMSG_SET_HOOKLEVELCHALLENGE_RESULT_REQ.miniGameType",3,2,1,false,0,5,1)
F303D=F(2,"miniID",".CSMsg.TMSG_SET_HOOKLEVELCHALLENGE_RESULT_REQ.miniID",4,3,1,false,0,5,1)
F304D=F(2,"coinNum",".CSMsg.TMSG_SET_HOOKLEVELCHALLENGE_RESULT_REQ.coinNum",5,4,1,false,0,5,1)
F305D=F(2,"abTestId",".CSMsg.TMSG_SET_HOOKLEVELCHALLENGE_RESULT_REQ.abTestId",6,5,1,false,0,5,1)
F306D=F(2,"bUnusualCreate",".CSMsg.TMSG_SET_HOOKLEVELCHALLENGE_RESULT_REQ.bUnusualCreate",7,6,1,false,false,8,7)
M106G=D(1,"TMSG_SET_HOOKLEVELCHALLENGE_RESULT_REQ",".CSMsg.TMSG_SET_HOOKLEVELCHALLENGE_RESULT_REQ",false,{},{},nil,{})
F307D=F(2,"errCode",".CSMsg.TMSG_SET_HOOKLEVELCHALLENGE_RESULT_RSP.errCode",1,0,2,false,nil,14,8)
F308D=F(2,"rewardId",".CSMsg.TMSG_SET_HOOKLEVELCHALLENGE_RESULT_RSP.rewardId",2,1,1,false,0,5,1)
M107G=D(1,"TMSG_SET_HOOKLEVELCHALLENGE_RESULT_RSP",".CSMsg.TMSG_SET_HOOKLEVELCHALLENGE_RESULT_RSP",false,{},{},nil,{})
F309D=F(2,"battleid",".CSMsg.TSdBattleVideoContext.battleid",1,0,2,false,"",9,9)
F310D=F(2,"damage",".CSMsg.TSdBattleVideoContext.damage",2,1,2,false,0,3,2)
F311D=F(2,"tmstamp",".CSMsg.TSdBattleVideoContext.tmstamp",3,2,2,false,0,13,3)
F312D=F(2,"pals",".CSMsg.TSdBattleVideoContext.pals",4,3,3,false,{},11,10)
M108G=D(1,"TSdBattleVideoContext",".CSMsg.TSdBattleVideoContext",false,{},{},nil,{})
F313D=F(2,"errCode",".CSMsg.TMSG_SPACE_DOMINATOR_BATTLE_VIDEO_RSP.errCode",1,0,2,false,nil,14,8)
F314D=F(2,"stageType",".CSMsg.TMSG_SPACE_DOMINATOR_BATTLE_VIDEO_RSP.stageType",2,1,1,false,nil,14,8)
F315D=F(2,"context",".CSMsg.TMSG_SPACE_DOMINATOR_BATTLE_VIDEO_RSP.context",3,2,3,false,{},11,10)
M109G=D(1,"TMSG_SPACE_DOMINATOR_BATTLE_VIDEO_RSP",".CSMsg.TMSG_SPACE_DOMINATOR_BATTLE_VIDEO_RSP",false,{},{},nil,{})
F316D=F(2,"errCode",".CSMsg.TMSG_LIFETIME_CARD_REWARD_RSP.errCode",1,0,2,false,nil,14,8)
F317D=F(2,"rewardArr",".CSMsg.TMSG_LIFETIME_CARD_REWARD_RSP.rewardArr",2,1,3,false,{},11,10)
M110G=D(1,"TMSG_LIFETIME_CARD_REWARD_RSP",".CSMsg.TMSG_LIFETIME_CARD_REWARD_RSP",false,{},{},nil,{})
F318D=F(2,"battleID",".CSMsg.TMSG_PLAYER_BATTLE_RESULT_REQ.battleID",1,0,2,false,"",9,9)
M111G=D(1,"TMSG_PLAYER_BATTLE_RESULT_REQ",".CSMsg.TMSG_PLAYER_BATTLE_RESULT_REQ",false,{},{},nil,{})
F319D=F(2,"battleID",".CSMsg.TMSG_PLAYER_BATTLE_RESULT_RSP.battleID",1,0,2,false,"",9,9)
F320D=F(2,"errCode",".CSMsg.TMSG_PLAYER_BATTLE_RESULT_RSP.errCode",2,1,2,false,nil,14,8)
M112G=D(1,"TMSG_PLAYER_BATTLE_RESULT_RSP",".CSMsg.TMSG_PLAYER_BATTLE_RESULT_RSP",false,{},{},nil,{})
F321D=F(2,"storytype",".CSMsg.TStorySystemModuleData.storytype",1,0,2,false,0,5,1)
F322D=F(2,"storyval",".CSMsg.TStorySystemModuleData.storyval",2,1,3,false,{},13,3)
M113G=D(1,"TStorySystemModuleData",".CSMsg.TStorySystemModuleData",false,{},{},nil,{})
F323D=F(2,"data",".CSMsg.TStorySystemData.data",1,0,3,false,{},11,10)
M114G=D(1,"TStorySystemData",".CSMsg.TStorySystemData",false,{},{},nil,{})
F324D=F(2,"battleID",".CSMsg.TMSG_BATTLE_RESULT_REQ.battleID",1,0,1,false,"",9,9)
F325D=F(2,"resultType",".CSMsg.TMSG_BATTLE_RESULT_REQ.resultType",2,1,1,false,0,5,1)
M115G=D(1,"TMSG_BATTLE_RESULT_REQ",".CSMsg.TMSG_BATTLE_RESULT_REQ",false,{},{},nil,{})
F326D=F(2,"errorCode",".CSMsg.TMSG_BATTLE_RESULT_RSP.errorCode",1,0,1,false,0,5,1)
F327D=F(2,"battleID",".CSMsg.TMSG_BATTLE_RESULT_RSP.battleID",2,1,1,false,"",9,9)
F328D=F(2,"data",".CSMsg.TMSG_BATTLE_RESULT_RSP.data",3,2,1,false,"",12,9)
F329D=F(2,"resultType",".CSMsg.TMSG_BATTLE_RESULT_RSP.resultType",4,3,1,false,0,5,1)
M116G=D(1,"TMSG_BATTLE_RESULT_RSP",".CSMsg.TMSG_BATTLE_RESULT_RSP",false,{},{},nil,{})
M117G=D(1,"TMSG_IDLE_DATA_REQ",".CSMsg.TMSG_IDLE_DATA_REQ",false,{},{},{},{})
F330D=F(2,"errorCode",".CSMsg.TMSG_IDLE_DATA_RSP.errorCode",1,0,1,false,0,5,1)
F331D=F(2,"idlePart",".CSMsg.TMSG_IDLE_DATA_RSP.idlePart",2,1,1,false,nil,11,10)
M118G=D(1,"TMSG_IDLE_DATA_RSP",".CSMsg.TMSG_IDLE_DATA_RSP",false,{},{},nil,{})

E1M.values = {V1M,V2M}
E2M.values = {V3M,V4M}
E3M.values = {V5M,V6M}
E4M.values = {V7M,V8M,V9M,V10M,V11M}
E5M.values = {V12M,V13M,V14M}
E6M.values = {V15M,V16M,V17M}
E7M.values = {V18M,V19M}
E8M.values = {V20M,V21M}
M1G.fields={F1D, F2D}
M2G.fields={F3D, F4D}
F5D.message_type=prop_pb.M9G
F6D.message_type=M2G
M3G.fields={F5D, F6D}
F12D.message_type=common_new_pb.M2G
M5G.fields={F7D, F8D, F9D, F10D, F11D, F12D, F13D}
F14D.enum_type=M8G
F15D.message_type=M1G
M7G.fields={F14D, F15D}
M9G.fields={F16D, F17D, F18D, F19D, F20D, F21D}
M10G.fields={F22D, F23D, F24D}
F25D.enum_type=common_new_pb.E2M
F27D.message_type=common_new_pb.M3G
M11G.fields={F25D, F26D, F27D}
F28D.enum_type=common_new_pb.E2M
M14G.fields={F28D, F29D, F30D, F31D, F32D, F33D, F34D, F35D, F36D, F37D, F38D, F39D, F40D, F41D}
M15G.fields={F42D, F43D}
F44D.enum_type=error_code_pb.E1M
M16G.fields={F44D, F45D, F46D}
M18G.fields={F47D}
M19G.fields={F48D}
M20G.fields={F49D}
M21G.fields={F50D, F51D}
M22G.fields={F52D}
M23G.fields={F53D, F54D}
F57D.enum_type=M25G
M24G.fields={F55D, F56D, F57D, F58D}
F62D.enum_type=M25G
M26G.fields={F59D, F60D, F61D, F62D, F63D}
M27G.fields={F64D, F65D}
M28G.fields={F66D, F67D, F68D}
M29G.fields={F69D}
M30G.fields={F70D, F71D}
F74D.message_type=M30G
M31G.fields={F72D, F73D, F74D}
M32G.fields={F75D}
M33G.fields={F76D, F77D, F78D, F79D}
M34G.fields={F80D}
M35G.fields={F81D, F82D, F83D}
M36G.fields={F84D, F85D}
F86D.enum_type=error_code_pb.E1M
F90D.message_type=M36G
M37G.fields={F86D, F87D, F88D, F89D, F90D, F91D}
F92D.enum_type=common_new_pb.E4M
F93D.enum_type=common_new_pb.E5M
M38G.fields={F92D, F93D, F94D}
M41G.fields={F95D, F96D}
F97D.enum_type=common_new_pb.E1M
M42G.fields={F97D}
F98D.enum_type=common_new_pb.E1M
F99D.message_type=M5G
F100D.message_type=M9G
F101D.message_type=M10G
M44G.fields={F98D, F99D, F100D, F101D}
F102D.enum_type=common_new_pb.E1M
F103D.message_type=M7G
F104D.message_type=M9G
M45G.fields={F102D, F103D, F104D}
F105D.enum_type=common_new_pb.E1M
F106D.enum_type=M47G
M46G.fields={F105D, F106D}
M48G.fields={F107D, F108D, F109D}
F110D.message_type=M50G
F112D.message_type=common_new_pb.M2G
M49G.fields={F110D, F111D, F112D, F113D}
F114D.enum_type=common_new_pb.E1M
F115D.enum_type=M47G
F116D.enum_type=error_code_pb.E1M
F118D.message_type=M48G
F119D.message_type=M48G
M51G.fields={F114D, F115D, F116D, F117D, F118D, F119D}
F120D.enum_type=common_new_pb.E1M
F124D.message_type=common_new_pb.M2G
F125D.message_type=M11G
F127D.message_type=M15G
F128D.message_type=M35G
F129D.message_type=M38G
F130D.message_type=M18G
F131D.message_type=M22G
F132D.message_type=M20G
F133D.message_type=M29G
F134D.message_type=M27G
F136D.message_type=M32G
F137D.message_type=M41G
F141D.message_type=common_new_pb.M3G
F144D.message_type=M24G
M52G.fields={F120D, F121D, F122D, F123D, F124D, F125D, F126D, F127D, F128D, F129D, F130D, F131D, F132D, F133D, F134D, F135D, F136D, F137D, F138D, F139D, F140D, F141D, F142D, F143D, F144D}
M53G.fields={F145D}
M54G.fields={F146D, F147D}
F148D.enum_type=common_new_pb.E1M
F152D.message_type=M14G
F153D.message_type=M16G
F154D.message_type=M37G
F155D.message_type=M19G
F156D.message_type=M23G
F157D.message_type=M21G
F158D.message_type=M31G
F159D.message_type=M28G
F160D.message_type=M34G
F161D.enum_type=error_code_pb.E1M
F162D.message_type=M33G
F164D.message_type=M49G
F166D.message_type=M53G
F167D.message_type=M54G
F168D.message_type=M26G
M55G.fields={F148D, F149D, F150D, F151D, F152D, F153D, F154D, F155D, F156D, F157D, F158D, F159D, F160D, F161D, F162D, F163D, F164D, F165D, F166D, F167D, F168D}
F169D.enum_type=common_new_pb.E1M
M56G.fields={F169D, F170D}
M57G.fields={F171D, F172D}
M58G.fields={F173D, F174D, F175D, F176D}
M59G.fields={F177D, F178D, F179D}
F180D.message_type=M58G
F181D.message_type=M59G
M50G.fields={F180D, F181D}
M60G.fields={F182D, F183D}
F184D.enum_type=common_new_pb.E1M
F193D.message_type=M50G
F195D.message_type=M41G
F198D.message_type=M60G
M61G.fields={F184D, F185D, F186D, F187D, F188D, F189D, F190D, F191D, F192D, F193D, F194D, F195D, F196D, F197D, F198D}
F199D.enum_type=common_new_pb.E1M
F200D.message_type=common_new_pb.M2G
F201D.enum_type=common_new_pb.E2M
M62G.fields={F199D, F200D, F201D}
F202D.enum_type=common_new_pb.E1M
F203D.enum_type=error_code_pb.E1M
F204D.enum_type=common_new_pb.E2M
M63G.fields={F202D, F203D, F204D}
F205D.enum_type=common_new_pb.E1M
M64G.fields={F205D}
M65G.fields={F206D, F207D, F208D, F209D, F210D, F211D, F212D, F213D, F214D, F215D}
F216D.enum_type=prop_pb.E19M
F217D.enum_type=prop_pb.E20M
M66G.fields={F216D, F217D}
F218D.enum_type=prop_pb.E19M
F219D.enum_type=error_code_pb.E1M
M69G.fields={F218D, F219D}
F220D.enum_type=prop_pb.E19M
F221D.enum_type=prop_pb.E20M
F222D.message_type=M65G
M70G.fields={F220D, F221D, F222D}
M71G.fields={F223D, F224D}
M72G.fields={F225D, F226D, F227D}
F228D.enum_type=error_code_pb.E1M
F229D.message_type=M72G
M73G.fields={F228D, F229D, F230D, F231D, F232D}
F233D.enum_type=common_new_pb.E1M
M74G.fields={F233D}
M75G.fields={F234D}
F235D.enum_type=error_code_pb.E1M
F236D.message_type=M72G
M76G.fields={F235D, F236D}
M77G.fields={F237D, F238D, F239D, F240D, F241D, F242D, F243D}
F245D.enum_type=common_new_pb.E1M
M78G.fields={F244D, F245D, F246D}
M80G.fields={F247D}
M81G.fields={F248D, F249D, F250D}
F251D.message_type=M81G
M82G.fields={F251D}
M83G.fields={F252D, F253D, F254D}
F255D.message_type=M83G
M84G.fields={F255D}
M85G.fields={F256D, F257D}
F260D.message_type=M85G
M86G.fields={F258D, F259D, F260D}
F261D.message_type=common_new_pb.M1G
F266D.message_type=common_new_pb.M5G
M87G.fields={F261D, F262D, F263D, F264D, F265D, F266D, F267D, F268D, F269D}
F270D.message_type=M87G
M90G.fields={F270D}
M91G.fields={F271D, F272D}
F273D.message_type=M91G
M92G.fields={F273D}
M93G.fields={F274D, F275D, F276D}
F278D.message_type=M87G
F279D.message_type=M93G
F284D.message_type=M93G
M94G.fields={F277D, F278D, F279D, F280D, F281D, F282D, F283D, F284D}
F285D.enum_type=M96G
M95G.fields={F285D}
F286D.enum_type=error_code_pb.E1M
F287D.enum_type=M96G
M97G.fields={F286D, F287D}
F288D.enum_type=error_code_pb.E1M
M99G.fields={F288D}
M100G.fields={F289D, F290D, F291D, F292D, F293D}
F294D.message_type=M93G
M101G.fields={F294D}
M102G.fields={F295D}
M103G.fields={F296D}
M104G.fields={F297D, F298D}
F299D.enum_type=common_new_pb.E1M
M105G.fields={F299D}
M106G.fields={F300D, F301D, F302D, F303D, F304D, F305D, F306D}
F307D.enum_type=error_code_pb.E1M
M107G.fields={F307D, F308D}
F312D.message_type=common_new_pb.M5G
M108G.fields={F309D, F310D, F311D, F312D}
F313D.enum_type=error_code_pb.E1M
F314D.enum_type=common_new_pb.E1M
F315D.message_type=M108G
M109G.fields={F313D, F314D, F315D}
F316D.enum_type=error_code_pb.E1M
F317D.message_type=M72G
M110G.fields={F316D, F317D}
M111G.fields={F318D}
F320D.enum_type=error_code_pb.E1M
M112G.fields={F319D, F320D}
M113G.fields={F321D, F322D}
F323D.message_type=M113G
M114G.fields={F323D}
M115G.fields={F324D, F325D}
M116G.fields={F326D, F327D, F328D, F329D}
F331D.message_type=prop_pb.M27G
M118G.fields={F330D, F331D}

ActivityBrokenSpaceTime =M(M38G)
ArenaEnterBattle =M(M11G)
ArenaEnterBattleRsp =M(M14G)
AshShopReward =M(M36G)
AshdungeonBattle =M(M35G)
AshdungeonBattleRsp =M(M37G)
ChinaRedBattleRsp =M(M53G)
CompeteEnterBattle =M(M22G)
CompeteEnterBattleRsp =M(M23G)
EquipEctypeInfo =M(M41G)
ExtraErrData =M(M30G)
FactionBattleRsp =M(M34G)
FactionInfo =M(M10G)
FriendBattle =M(M27G)
FriendBattleRsp =M(M28G)
GoodsReward = 2
HUNTINGRESINFO =M(M7G)
HUNYINGINFO =M(M5G)
HookLevelChallengeType_MiniLevel = 0
HookLevelChallengeType_MonsterComing = 1
IDLE_PART_IDLE_GIFT_REWARD_STAGE = 3
IDLE_PART_IDLE_REWARD_STAGE = 0
IDLE_PART_STAR_GIFT_REWARD_STAGE = 4
IDLE_PART_STAR_NUMBER = 2
IDLE_PART_STAR_REWARD_STAGE = 1
IDLE_REWARD_Add = 2
IDLE_REWARD_Init = 1
ILLUSIONTOWERINFO =M(M9G)
IdleGoodsRsp =M(M48G)
IdleStageStar =M(M2G)
LeagueBossBattle =M(M18G)
LeagueBossBattleRsp =M(M19G)
MailBattleResultType_Arena = 1
MailBattleResultType_Min = 0
MateMonsterBattle =M(M20G)
MateMonsterBattleRsp =M(M21G)
MazeBattle =M(M29G)
MazeBattleRsp =M(M31G)
MultiKillingTowerRsp =M(M54G)
OnlyGoods = 2
OnlyResource = 1
Oper_Catch = 1
Oper_Conquer = 3
Oper_FightAgainst = 2
PeakBattle =M(M32G)
PeakBattleRsp =M(M33G)
ResourceReward = 1
RewardDesc =M(M1G)
SlaveBattle =M(M24G)
SlaveBattleRsp =M(M26G)
TCrushBlowRsp =M(M49G)
TItemRewardAfterBattle =M(M58G)
TMSG_BATTLE_DYNAMIC_DIFFI_INFO_NTF =M(M78G)
TMSG_BATTLE_END_OF_THE_PLAY_NTF =M(M74G)
TMSG_BATTLE_RESULT_REQ =M(M115G)
TMSG_BATTLE_RESULT_RSP =M(M116G)
TMSG_CLIENT_GET_RANK_LIST_REQ =M(M66G)
TMSG_CLIENT_GET_RANK_LIST_RSP =M(M69G)
TMSG_CLIENT_UPDATE_RANK_LIST_NTF =M(M70G)
TMSG_ENTER_MAIN_BATTLE_NTF =M(M79G)
TMSG_EXIT_MODULE_NTF =M(M64G)
TMSG_GAMEOVER_NTF =M(M61G)
TMSG_IDLE_BATTLE_RATING_PROP_NTF =M(M3G)
TMSG_IDLE_DATA_REQ =M(M117G)
TMSG_IDLE_DATA_RSP =M(M118G)
TMSG_ILLUSION_TOWER_SWEEP_REQ =M(M71G)
TMSG_ILLUSION_TOWER_SWEEP_RSP =M(M73G)
TMSG_LIFETIME_CARD_REWARD_RSP =M(M110G)
TMSG_PLAYER_BATTLE_RESULT_REQ =M(M111G)
TMSG_PLAYER_BATTLE_RESULT_RSP =M(M112G)
TMSG_PLAYER_ENTER_BATTLE_REQ =M(M52G)
TMSG_PLAYER_ENTER_BATTLE_RSP =M(M55G)
TMSG_PLAYER_GET_IDLE_REWARD_REQ =M(M46G)
TMSG_PLAYER_GET_IDLE_REWARD_RSP =M(M51G)
TMSG_PLAYER_IDLE_REWARD_NTF =M(M45G)
TMSG_PLAYER_INPUT_CHALLENGE_REQ =M(M42G)
TMSG_PLAYER_INPUT_CHALLENGE_RSP =M(M44G)
TMSG_PLAYER_MODIFY_IDLE_STAGE_REQ =M(M56G)
TMSG_PLAYER_MODIFY_IDLE_STAGE_RSP =M(M57G)
TMSG_QIDLE_DATA_INFORM_NTF =M(M77G)
TMSG_QIDLE_SWEEP_TIMES_REQ =M(M75G)
TMSG_QIDLE_SWEEP_TIMES_RSP =M(M76G)
TMSG_SAVE_PLAYER_LINEUP_REQ =M(M62G)
TMSG_SAVE_PLAYER_LINEUP_RSP =M(M63G)
TMSG_SET_HOOKLEVELCHALLENGE_RESULT_REQ =M(M106G)
TMSG_SET_HOOKLEVELCHALLENGE_RESULT_RSP =M(M107G)
TMSG_SPACE_DOMINATOR_BATTLE_VIDEO_REQ =M(M105G)
TMSG_SPACE_DOMINATOR_BATTLE_VIDEO_RSP =M(M109G)
TMSG_SPACE_DOMINATOR_CHALLENGE_COUNT_NTF =M(M102G)
TMSG_SPACE_DOMINATOR_END_GROUP_NTF =M(M103G)
TMSG_SPACE_DOMINATOR_GET_RANK_INFO_REQ =M(M98G)
TMSG_SPACE_DOMINATOR_GET_RANK_INFO_RSP =M(M99G)
TMSG_SPACE_DOMINATOR_GET_RANK_REWARD_REQ =M(M95G)
TMSG_SPACE_DOMINATOR_GET_RANK_REWARD_RSP =M(M97G)
TMSG_SPACE_DOMINATOR_GROUP_ID_NTF =M(M104G)
TMSG_SPACE_DOMINATOR_INFO_NTF =M(M94G)
TMSG_SPACE_DOMINATOR_SELF_RANK_NTF =M(M100G)
TMSG_SPACE_DOMINATOR_USER_SETTLE_NTF =M(M101G)
TRankListData =M(M65G)
TRewardAfterBattle =M(M50G)
TRewardIDAfterBattle =M(M59G)
TSdBattleVideoContext =M(M108G)
TSpaceDominatorSettleInfo =M(M93G)
TStorySystemData =M(M114G)
TStorySystemModuleData =M(M113G)
TowerSweepReward =M(M72G)
TrialEnterBattle =M(M15G)
TrialEnterBattleRsp =M(M16G)
enSdReward_Both = 3
enSdReward_Box = 2
enSdReward_Rank = 1
tBattleTroopCE =M(M60G)
tSDRobotUpdateInfo =M(M91G)
tSpaceDominatorDuration =M(M86G)
tSpaceDominatorDurationTime =M(M85G)
tSpaceDominatorEndGroup =M(M80G)
tSpaceDominatorGroupRobot =M(M83G)
tSpaceDominatorGroupRobotInfo =M(M84G)
tSpaceDominatorGroupUser =M(M81G)
tSpaceDominatorGroupUserInfo =M(M82G)
tSpaceDominatorRank =M(M87G)
tSpaceDominatorRobotInfo =M(M90G)
tSpaceDominatorRobotUpdateTime =M(M92G)

