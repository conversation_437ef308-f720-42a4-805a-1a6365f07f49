
local require = require
local print = print 

module("value_box")

local cache = {}
function inst(mark)
    local _inst = cache[mark]
    if not _inst then
        cache[mark] = {}
    end
    return cache[mark]
end 
function get(_inst,mark,key)
    _inst = _inst or inst(mark)
    return _inst[key]
end
function set(_inst,mark,key,value)
    _inst = _inst or inst(mark)
    if _inst[key] then
        local log = require "log"
        print(mark,"reset keyvalue",key,value)
    end
    _inst[key] = value
    return value
end