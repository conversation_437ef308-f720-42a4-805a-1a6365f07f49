---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON>ua)
--- Created by ha<PERSON><PERSON><PERSON>.
--- DateTime: 2024/8/16 16:53
--- Des: 功能类型气泡
local require = require
local newclass = newclass
local gw_home_comp_bubble_base = require "gw_home_comp_bubble_base"
local UIUtil = CS.Common_Util.UIUtil
local gw_home_card_sprite_asset_mgr = require "gw_home_card_sprite_asset_mgr"
module("gw_home_comp_bubble_emoji")
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
---@class GWHomeCompBubbleEmoji : GWDisposableObject
local GWHomeCompBubbleEmoji = newclass("gw_home_comp_bubble_emoji", gw_home_comp_bubble_base)

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

function GWHomeCompBubbleEmoji:InitData(buildData, bindParent, parent, bubbleType)
    gw_home_comp_bubble_base.InitData(self, buildData, bindParent, bubbleType)
    if not self.spriteAsset then
        self.spriteAsset = gw_home_card_sprite_asset_mgr.GetOrCreateCardSpriteAsset("gwbubble")
    end
    --读表 获取模型
    self:InstantiateModelAsync("ui/prefabs/gw/buildsystem/bubbleui/bubblefunctionui.prefab", parent);
end

function GWHomeCompBubbleEmoji:InstantiateSuccess(_obj)
    self.bgImage = UIUtil.GetComponent(_obj.transform, "Image", "Image")
    gw_home_comp_bubble_base.InstantiateSuccess(self, _obj)
end

function GWHomeCompBubbleEmoji:OnUpdateData(data)
    gw_home_comp_bubble_base.OnUpdateData(self, data)
    if not self.isLoaded then
        self:AddLoadEvent(function()
            self.spriteAsset:GetSprite(data.iconPath, function(sprite)
                self.bgImage.sprite = sprite
            end)
        end)
        return
    end
end

function GWHomeCompBubbleEmoji:ClearData()
    self.spriteAsset = nil
    gw_home_comp_bubble_base.ClearData(self)
end

return GWHomeCompBubbleFunction
