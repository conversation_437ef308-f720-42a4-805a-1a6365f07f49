---
--- Created by ha<PERSON><PERSON><PERSON>.
--- DateTime: 2024/8/15 15:17
--- Desc: 家园城建气泡基类
local require = require
local newclass = newclass
local gw_home_comp_bubble_base = require "gw_home_comp_bubble_base"
local game_scheme = require "game_scheme"
local GWG = GWG
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
---@class GWBuildBubbleObject : unit_base 城建基础对象
---@field __base GWBubbleObject
module("gw_home_comp_build_bubble_base")
local GWBubbleObject = newclass("gw_home_comp_build_bubble_base", gw_home_comp_bubble_base)
--- 构造器
function GWBubbleObject:ctor()
    self:ClearData()
end
--- @public 设置数据 （现必须基成设置数据）
---@see override
function GWBubbleObject:InitData(buildData, bindParent, bubbleType)
    gw_home_comp_bubble_base.InitData(self,buildData,  bindParent, bubbleType)
    self.buildData = buildData      --实体数据 建筑数据
    self:InitCfgData()    
end

--- @public 初始化配置数据
function GWBubbleObject:InitCfgData()
    self.buildCfg = game_scheme:Building_0(self.buildData.nBuildingID, self.buildData.nLevel)
    self.buildTypeCfg = game_scheme:BuildingType_0(self.buildCfg.TypeID)
    if self.buildTypeCfg and self.bubbleType ~= GWG.GWConst.EHomeBubbleEntityType.Upgrade then
        if self.buildTypeCfg.offsetx then
            self.offset_x = self.buildTypeCfg.offsetx       --偏移x
        end
        if self.buildTypeCfg.offsety then
            self.offset_y = self.buildTypeCfg.offsety       --偏移y
        end
        if self.buildTypeCfg.bubbleSize then
            self.size = self.size * self.buildTypeCfg.bubbleSize
        end
    end
end

--- @public 基类方法实例化模型
---@see override
function GWBubbleObject:InstantiateModelAsync(path, parent)
    gw_home_comp_bubble_base.InstantiateModelAsync(self, path, parent)
end

--- 实例化成功 （现必须基成设置名字和组件Id）
---@see override
function GWBubbleObject:InstantiateSuccess(_obj)
    gw_home_comp_bubble_base.InstantiateSuccess(self, _obj)
end

---清楚基类数据
---@see override
function GWBubbleObject:ClearData()
    self.buildData = nil        --实体数据
    self.buildCfg = nil         --建筑配置
    self.buildTypeCfg = nil     --建筑类型配置
    gw_home_comp_bubble_base.ClearData(self)
end


-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

return GWBubbleObject
