-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
local error_code_pb=require("error_code_pb")
module('nationalFlag_pb')


V1M=V(4,"EnNationalFlagType_Role",0,1)
V2M=V(4,"EnNationalFlagType_Alliance",1,2)
E1M=E(3,"EnNationalFlagType",".CSMsg.EnNationalFlagType")
F1D=F(2,"SKDChannelID",".CSMsg.TMSG_LOGIN_NATIONAL_FLAG_REQ.SKDChannelID",1,0,2,false,"",9,9)
F2D=F(2,"countryID",".CSMsg.TMSG_LOGIN_NATIONAL_FLAG_REQ.countryID",2,1,2,false,0,13,3)
M1G=D(1,"TMSG_LOGIN_NATIONAL_FLAG_REQ",".CSMsg.TMSG_LOGIN_NATIONAL_FLAG_REQ",false,{},{},nil,{})
F3D=F(2,"errCode",".CSMsg.TMSG_LOGIN_NATIONAL_FLAG_RSP.errCode",1,0,2,false,nil,14,8)
M2G=D(1,"TMSG_LOGIN_NATIONAL_FLAG_RSP",".CSMsg.TMSG_LOGIN_NATIONAL_FLAG_RSP",false,{},{},nil,{})
F4D=F(2,"flagType",".CSMsg.TMSG_NATIONAL_FLAG_CHANGE_REQ.flagType",1,0,2,false,nil,14,8)
F5D=F(2,"nationalFlagID",".CSMsg.TMSG_NATIONAL_FLAG_CHANGE_REQ.nationalFlagID",2,1,2,false,0,13,3)
M4G=D(1,"TMSG_NATIONAL_FLAG_CHANGE_REQ",".CSMsg.TMSG_NATIONAL_FLAG_CHANGE_REQ",false,{},{},nil,{})
F6D=F(2,"errCode",".CSMsg.TMSG_NATIONAL_FLAG_CHANGE_RSP.errCode",1,0,2,false,nil,14,8)
F7D=F(2,"flagType",".CSMsg.TMSG_NATIONAL_FLAG_CHANGE_RSP.flagType",2,1,2,false,nil,14,8)
F8D=F(2,"nationalFlagID",".CSMsg.TMSG_NATIONAL_FLAG_CHANGE_RSP.nationalFlagID",3,2,1,false,0,13,3)
M6G=D(1,"TMSG_NATIONAL_FLAG_CHANGE_RSP",".CSMsg.TMSG_NATIONAL_FLAG_CHANGE_RSP",false,{},{},nil,{})

E1M.values = {V1M,V2M}
M1G.fields={F1D, F2D}
F3D.enum_type=error_code_pb.E1M
M2G.fields={F3D}
F4D.enum_type=M5G
M4G.fields={F4D, F5D}
F6D.enum_type=error_code_pb.E1M
F7D.enum_type=M5G
M6G.fields={F6D, F7D, F8D}

EnNationalFlagType_Alliance = 2
EnNationalFlagType_Role = 1
TMSG_LOGIN_NATIONAL_FLAG_REQ =M(M1G)
TMSG_LOGIN_NATIONAL_FLAG_RSP =M(M2G)
TMSG_NATIONAL_FLAG_CHANGE_REQ =M(M4G)
TMSG_NATIONAL_FLAG_CHANGE_RSP =M(M6G)

