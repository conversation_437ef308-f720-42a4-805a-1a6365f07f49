--- Created by lzx.
--- DateTime: 2024/7/27 12:14
--- Des:家园数据管理 

local require = require
local tonumber = tonumber
local math = math
local log = log
local table = table
local ipairs = ipairs
local pairs = pairs
local GWConst = require "gw_const"
local net_city_module = require "net_city_module"
local data_mgr = require "data_mgr"
local gw_ed = require("gw_ed")
local game_scheme = require "game_scheme"
local table_pb_util = require "table_pb_util"
local table_util = require "table_util"
local prop = require "prop_new"
local require_optimize_mgr = require("require_optimize_mgr")
local tostring = tostring
local util = util
local GWG = GWG
local string = string
local os = os
local player_mgr = require "player_mgr"
local red_system = require "red_system"
local red_const = require "red_const"
local event         = require "event"
local frame_task_queue_optimize = require "frame_task_queue_optimize"
local net_module_open = require "net_module_open"
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

---@class GWHomeBuildingData
module("gw_home_building_data")
local M = {}
local self = M --简化写法，静态类中直接也用Self获取自身
---所有数据存储
local _d = data_mgr:CreateData("gw_home_building_data")
---非服务器数据存储
local mc = _d.mde.const
---服务器数据存储
local mn = _d.mde.net
---@field 服务器下发repeat数据匹配查找规则
---其实就是对应的PBmessage的匹配规则，eg.字段名对应的是message TCityBuilding{}
local msg_item_match_funs = {
    ["TCityBuilding"] = function(item1, item2)
        return item1.uSid == item2.uSid
    end
}


-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
---@see 初始化
function M.Init()
    mc.building = { }
    mc.buildingBuff = { } --建筑的所有加成数组。
    mc.creatTempBuilding = nil      -- 建筑建造时临时的数据(定义成不是数组)
    mc.cachedEffects = {} --缓存
    mc.tempHelpPlayerInfo={} --临时帮助玩家信息
    event.Register(event.UPDATE_MODULE_OPEN, self.CheckBuildingModuleOpen)
end

---@public
--- @param data table 建筑数据
--- @param isAll boolean 是否全量
--- @param notRefreshShow boolean 是否不刷新表现  --默认会刷新
function M.UpdateData(data, isAll,notRefreshShow)
    if not mc.building then
        mc.building = { }
    end  
 	--检测一下是否更新的数据中含有临时创建的
    self.CheckTempCreateBuilding(data.building) 
    table_pb_util.UpdateDataByProp(mc.building, data.building, msg_item_match_funs)
    self.ReMapDataWithSid()
    self.SetBuildingBuff()
    --只更新增量的的
    for i, v in ipairs(data.building) do
        local bData = self.GetBuildingDataBySid(v.uSid)
        local buildingID = bData.nBuildingID
        if buildingID and not isAll then
            self.UpdateDecorateDataForBuildingID(buildingID)
        end
    end   
    
    if isAll then
        local technology_data = require "technology_data"
        technology_data.SetScientificGroupData()
    end
    --刷新表现
    if not notRefreshShow then
        self.RefreshShowByData(data.building)
    end
end

---@public 利用已有数据重新刷新表现
function M.RefreshShowByData(refreshBuilding)    
    local refreshFunc = function()
        if not refreshBuilding then
            refreshBuilding = mc.building
        end
        --表现和逻辑分离；逻辑是一定要收到就执行，防止时序问题；但表现我们队列控制
        for i, v in ipairs(refreshBuilding) do
            local bData = self.GetBuildingDataBySid(v.uSid)
            if bData then
                gw_ed.mgr:Trigger(gw_ed.GW_HOME_INTERNAL_DATA_CHANGE, "BuildingNTF", bData)
            end
        end
    end
    refreshFunc()
    ----排序执行
    --require_optimize_mgr.AddTask(0,refreshFunc )
end

function M.CheckBuildingModuleOpen()
    local removeBuildingFlag = false
    for i = #mc.building, 1, -1 do
        local bData = self.GetBuildingDataBySid(mc.building[i].uSid)
        if bData then
            local buildingID = bData.nBuildingID
            local buildTypeConfig = game_scheme:BuildingType_0(self.GetBuildingTypeByBuildingId(buildingID))
            --配置了ModuleOpen并且开关关闭时，删除建筑
            if buildTypeConfig and buildTypeConfig.ModuleOpen and buildTypeConfig.ModuleOpen > 0 then
                if not net_module_open.CheckModuleOpen(buildTypeConfig.ModuleOpen) then
                    GWG.GWAdmin.HomeBuildingUtil.DisposeComp(bData.compId)
                    table.remove(mc.building, i)
                    removeBuildingFlag = true
                end
            end
        end
    end
    --有建筑被删除需要重新映射数据
    if removeBuildingFlag then
        self.ReMapDataWithSid()
    end
end

---设置建筑的buff加成
function M.SetBuildingBuff()
    mc.buildingBuff = { }
    for i,v in ipairs(GWG.GWConst.BuffBuildingType) do
        local buildingList = self.GetBuildingDataListByBuildingID(v * 1000)
        for j,k in ipairs(buildingList) do
            local cfgKeyStr = {k.nBuildingID,"_",k.nLevel}
            local cfgKey = table.concat(cfgKeyStr)
            if not mc.cachedEffects[cfgKey] then
                local buildingCfg = game_scheme:Building_0(k.nBuildingID,k.nLevel)
                mc.cachedEffects[cfgKey] =
                {
                    buildingCfg = buildingCfg,
                    skill1 = game_scheme:GWMapEffect_0(buildingCfg.BuildingPara1),
                    skill2 = game_scheme:GWMapEffect_0(buildingCfg.BuildingPara2),
                    skill3 = game_scheme:GWMapEffect_0(buildingCfg.BuildingPara3),
                    skill4 = game_scheme:GWMapEffect_0(buildingCfg.BuildingPara4),
                }
            end
            --local cfg = game_scheme:Building_0(k.nBuildingID,k.nLevel)
            local skill1 = mc.cachedEffects[cfgKey].skill1
            if skill1 then
                if not mc.buildingBuff[skill1.nGroupID] then
                    mc.buildingBuff[skill1.nGroupID] = 0
                end
                mc.buildingBuff[skill1.nGroupID] = mc.buildingBuff[skill1.nGroupID] + skill1.strParam[0];
            end
            local skill2 = mc.cachedEffects[cfgKey].skill2--game_scheme:GWMapEffect_0(cfg.BuildingPara2)
            if skill2 then
                if not mc.buildingBuff[skill2.nGroupID] then
                    mc.buildingBuff[skill2.nGroupID] = 0
                end
                mc.buildingBuff[skill2.nGroupID] = mc.buildingBuff[skill2.nGroupID] + skill2.strParam[0];
            end
            local skill3 = mc.cachedEffects[cfgKey].skill3--game_scheme:GWMapEffect_0(cfg.BuildingPara3)
            if skill3 then
                if not mc.buildingBuff[skill3.nGroupID] then
                    mc.buildingBuff[skill3.nGroupID] = 0
                end
                mc.buildingBuff[skill3.nGroupID] = mc.buildingBuff[skill3.nGroupID] + skill3.strParam[0];
            end
            local skill4 = mc.cachedEffects[cfgKey].skill4--game_scheme:GWMapEffect_0(cfg.BuildingPara4)
            if skill4 then
                if not mc.buildingBuff[skill4.nGroupID] then
                    mc.buildingBuff[skill4.nGroupID] = 0
                end
                mc.buildingBuff[skill4.nGroupID] = mc.buildingBuff[skill4.nGroupID] + skill4.strParam[0];
            end
        end
    end
end
--根据groupID获取加成。
function M.GetBuildingBuffByGroupID(nGroupID)
    return mc.buildingBuff[nGroupID] or 0
end

function M.FakeBuildingData(build)
    --假数据，为了数据安全注掉
    --[[
    if not build then
        build = {
            uSid = 10, --建筑唯一ID
            nBuildingID = 9000, --建筑ID
            nLevel = 3, --建筑等级
            x = 4, --地图坐标x
            y = 5, --地图坐标y
            --nState = 7;				--建筑状态 enBuildingState
            --uFlag = 8;				--建筑标志 enCityFlag
            --military = 9;	--兵营数据
            --exp = 10;		--训练基地数据
            --farm = 11;		--农田数据
            --gold = 12;		--金矿数据
            --iron = 13;		--铁矿数据
            --uMapUnitID = 14;		--地图物件ID（比如修复时使用）
        }
    end
    local pBuild = prop.CPropNew(build)
    local target
    if mc.building == nil then
        mc.building = {}
    end
    for i, v in pairs(mc.building) do
        if v.uSid == pBuild.uSid then
            target = v
            break
        end
    end
    if not target then
        table.insert(mc.building, pBuild)
        target = pBuild
    else
        for i, v in pairs(pBuild._propertyNames) do
            target[i] = v.value
        end
    end
    self.ReMapDataWithSid()
    gw_ed.mgr:Trigger(gw_ed.GW_HOME_INTERNAL_DATA_CHANGE, "BuildingNTF", target)
    ]]
end

---检测是否有临时创建的建筑
function M.CheckTempCreateBuilding(pbDatas)
    if mc.creatTempBuilding then
        local target = mc.creatTempBuilding
        for i, v in ipairs(pbDatas) do
            if v.nBuildingID == target.nBuildingID
                    and v.x == target.x
                    and v.y == target.y then
                local bData = self.GetBuildingDataBySid(target.uSid)
                if bData then
                    if mc.sidBuildingComponentId and mc.sidBuildingComponentId[target.uSid] then
                        mc.sidBuildingComponentId[target.uSid] = nil
                    end
                    bData.uSid = v.uSid                   
                    M.ReMapUSidRefComponentID(bData.uSid, bData.compId)
                    return true
                end
            end
        end
    end
end

---@public 构造临时的建造建筑  --建筑建造时客户端先创建的
function M.CreateTempBuilding(nBuildingID, x, y,uSid)
    if not uSid then
        uSid = -1
    end
    local build = {
        uSid = uSid, --建筑唯一ID
        nBuildingID = nBuildingID, --建筑ID   
        nLevel = 1, --建筑等级
        x = x, --地图坐标x
        y = y,
        uFlag = 0,
        nState = 1,
        uMapUnitID = 0,
    }
    local pBuild = prop.CPropNew(build)
    table.insert(mc.building, pBuild)
    self.ReMapDataWithSid()
    mc.creatTempBuilding = pBuild
    gw_ed.mgr:Trigger(gw_ed.GW_HOME_INTERNAL_DATA_CHANGE, "BuildingNTF", pBuild)
    return build.uSid
end

---@public 清空临时的建造建筑数据 --只有放弃或者失败时调用 
function M.ClearTempCreateBuilding(tempId)
   local compId =  self.ClearTempBuildingData(tempId)   
    if compId then
        GWG.GWAdmin.PushBSComponentByID(compId)
    end     
    mc.creatTempBuilding = nil
end

---@public 清空临时的建筑数据 
function M.ClearTempBuildingData(tempId)
    if not tempId then
        tempId = -1
    end
    --只找一个
    local curData = nil
    for i, v in ipairs(mc.building) do
        if v.uSid == tempId then
            curData = v
            table.remove(mc.building, i)
            break
        end
    end
    self.ReMapDataWithSid()   
    if curData then
        return curData.compId
    end
    return nil
end

function M.OnBuildABuildingRSP(msg)
    if msg.errCode == 0 then
        local comp = self.GetBuildingCompBySid(msg.uSid)
        if comp then
            comp:UpdatePosition(true)
        end
    else
        GWG.GWHomeMgr.buildingData.ClearTempCreateBuilding()
    end
    mc.creatTempBuilding = nil
end

function M.OnChangeBuildingLocationRSP(msg)
    local comp = self.GetBuildingCompBySid(msg.uSid)
    if not comp then
        GWG.GWAdmin.SwitchUtility.Error("OnChangeBuildingLocationRSP 获取comp失败  msg.uSid=", msg.uSid)
        return
    end
    if msg.errCode ~= 0 then
        --如果发生错误，则必须放置回原来位置  
        comp:UpdatePosition()
    else
        --位置变更打点
        local reportMsg =
        {
            BuildingID = comp.serData.nBuildingID, --建筑ID
            before = comp.serData.oldX.."#"..comp.serData.oldY, --变更前坐标
            after = comp.serData.x.."#"..comp.serData.y --变更后坐标
        }
        event.EventReport("location", reportMsg)
        comp:UpdatePosition(true)
    end
end

---@public 获取所有建筑里驻扎的幸存者的总战力
function M.GetAllBuildingSurvivorPower()
    local power = 0
    for i,v in pairs(mc.building) do
        power = power + GWG.GWHomeMgr.survivorData.GetSurvivorPowerByBuildingSid(v.uSid)
    end
    return power
end

---@public 获取城墙值
function M.GetWallHp(hp, endBurningTime, beginTime, burningSpeed, recoverSpeed)
    local wallHp = hp;
    local burningTime = 0;
    local startRecoverTime = 0;
    if endBurningTime > 0 then
        startRecoverTime = -1
        local totalBurningTime = endBurningTime - beginTime

        local isBurningTime = os.server_time() - beginTime; --计算已经烧了多久
        if isBurningTime > totalBurningTime then
            isBurningTime = totalBurningTime
        end
        wallHp = wallHp - isBurningTime * burningSpeed;
        burningTime = endBurningTime - os.server_time();
        if burningTime <= 0 then
            burningTime = 0;
            startRecoverTime = endBurningTime
            local totalRecoverTime = os.server_time() - startRecoverTime
            wallHp = wallHp + totalRecoverTime * recoverSpeed
        end
    else
        startRecoverTime = beginTime
        burningTime = 0
        local totalRecoverTime = os.server_time() - startRecoverTime
        wallHp = wallHp + totalRecoverTime * recoverSpeed
    end
    return wallHp, startRecoverTime, burningTime;
end

function M.GetWallHpByLevel(hp, endBurningTime, beginTime, wallLevel)
    local buildingCfg = game_scheme:Building_0(GWConst.enBuildingType.enBuildingType_Wall * 1000,wallLevel)
    if buildingCfg then
        local maxHp = buildingCfg.BuildingPara1
        local burningSpeed = (buildingCfg.BuildingPara2 / 10000) * (1 - buildingCfg.BuildingPara3 / 10000); -- 燃烧速度
        local recoverSpeed = (buildingCfg.BuildingPara4 / 10000) * (1 + buildingCfg.BuildingPara5 / 10000); -- 恢复速度
        
        local wallHp = self.GetWallHp(hp, endBurningTime, beginTime, burningSpeed, recoverSpeed)
        if wallHp > maxHp then
            wallHp = maxHp
        elseif wallHp <= 0 then
            wallHp = 1
        end
        return maxHp, wallHp--self.GetWallHp(hp, endBurningTime, beginTime, burningSpeed, recoverSpeed)
    end
end

---@public 重新用Sid 映射数据 主要是为了查找效率
function M.ReMapDataWithSid()   
    if mc.sidBuilding then
        table_util.ClearTable(mc.sidBuilding)
    else
        mc.sidBuilding = {}
    end
    for i, v in ipairs(mc.building) do
        mc.sidBuilding[v.uSid] = v
    end   
end
---@public 绑定建筑的uSid和componentID
function M.ReMapUSidRefComponentID(uSid, componentID)
    if not mc.sidBuildingComponentId then
        mc.sidBuildingComponentId = {}
    end
    mc.sidBuildingComponentId[uSid] = componentID
end
---@public 获取uSid对应的componentID
function M.GetComponentIDByUSid(uSid)
    if not mc.sidBuildingComponentId then
        return 0
    end
    return mc.sidBuildingComponentId[uSid] 
end

---@public 检查数据是否无效 主要为了数据未赋值，时序问题
function M.CheckDataInvalid()
    return mc.building == nil
end
function M.GetBuildingData()
    return mc.building
end

---@public 获取建筑数据通过sid
function M.GetBuildingDataBySid(sid)
    if mc.sidBuilding then
        return mc.sidBuilding[sid]
    end
    return nil
end

---@public 获取所有指定类型的建筑
function M.GetBuildingDataListByBuildingID(buildId)
    local buildingList = {}
    if self.CheckDataInvalid() then
        return buildingList
    end
    for i, v in pairs(mc.building) do
        if v.nBuildingID == buildId then
            table.insert(buildingList, v);
        end
        end
    return buildingList;
end

---@public 获取所有指定类型的建筑（但不包括0级建筑。0级建筑：即正在修复中，或者尚未修复的建筑）
function M.GetBuildingDataListByBuildingIdWithoutZeroLevel(buildId)
    local buildingList = {}
    if self.CheckDataInvalid() then
        return buildingList
    end
    for i, v in pairs(mc.building) do
        if v.nBuildingID == buildId and v.nLevel > 0 then
            table.insert(buildingList, v);
        end
    end
    return buildingList;
end

---@public 返回大本data等级  (0级就是没解锁)
---@return number 建筑等级+nBuildingId
function M.GetBuildingMainLevel()
    local buildingId = M.GetBuildingIdByBuildingType(GWConst.enBuildingType.enBuildingType_Main)
    local buildData = self.GetBuildingDataListByBuildingID(buildingId)
    if buildData and #buildData > 0 then
        for i, v in ipairs(buildData) do
            return v.nLevel,v.nBuildingID
        end
    end
    return 0,0
end

---@public 返回建筑的建筑状态 通过nBuildingId
---@return number   GWConst.enBuildingState.  返回nil时表示没有该建筑
function M.GetBuildingStateByBuildId(buildingId)
    if not buildingId then
        log.Error("GetBuildingStateByBuildId: buildingId is nil")
        return nil
    end
    local buildData = self.GetBuildingDataListByBuildingID(buildingId)
    if buildData and #buildData > 0 then
        for i, v in ipairs(buildData) do
            return v.nState
        end
    end
    return nil
end

---@public 返回当前科研建筑等级（返回第一科研中心与第二科研中心中较高级的那个）
function M.GetBuildingTechnologyLevel()
    local level = 0;
    for i = 20,21 do
        local buildingLevel = M.GetMaxLevelBuildingDataByBuildingID(i * 1000)
        if buildingLevel and buildingLevel.nLevel > 0 and buildingLevel.nLevel > level then
            level = buildingLevel.nLevel
        end
    end
    return level
end

function M.GetMaxLevelTechnologyBuilding()
    local buildingData = nil;
    local level = 0;
    for i = 20,21 do
        local buildingLevel = M.GetMaxLevelBuildingDataByBuildingID(i * 1000)
        if buildingLevel and buildingLevel.nLevel > 0 and buildingLevel.nLevel > level then
            level = buildingLevel.nLevel
            buildingData = buildingLevel
        end
    end
    return buildingData
end

---@public 返回指定建筑类型的最大等级的建筑数据
---@return  table build 
---message TCityBuilding
--{
--	required uint32 uSid = 1;				//建筑唯一ID
--	required int32 nBuildingID = 2;			//建筑ID
--	required int32 nLevel = 3;				//建筑等级
--	required int32 x = 4;					//地图坐标x
--	required int32 y = 5;					//地图坐标y
--	required int32 nState = 7;				//建筑状态 enBuildingState
--	required uint32 uFlag = 8;				//建筑标志 enCityFlag
--	...}
function M.GetMaxLevelBuildingDataByBuildingID(buildId)    
    local build
     if not self.CheckDataInvalid() then
        local n_buildId = tonumber(buildId)
        for i, v in pairs(mc.building) do
            if v.nBuildingID == n_buildId then
                if not build or build.nLevel < v.nLevel then
                    build = v
                end
            end
        end        
    end
    return build;
end
---@public 返回指定建筑类型的最低等级的建筑数据
function M.GetMinLevelBuildingDataByBuildingID(buildId)
    local build
    if not self.CheckDataInvalid() then
        local n_buildId = tonumber(buildId)
        for i, v in pairs(mc.building) do
            if v.nBuildingID == n_buildId then
                if not build or build.nLevel > v.nLevel then
                    build = v
                end
            end
        end
    end
    return build;
end

--获取当前可升级的最低级建筑。
function M.GetMinLevelBuildingData(isCanUpgrade)
    local build
    if not self.CheckDataInvalid() then
        for i, v in pairs(mc.building) do
            --先判断建筑是否不在迷雾中 是否完全解锁
            local enCityFlag = GWG.GWAdmin.HomeCommonUtil.GetCityFlagState(v.uFlag, GWConst.enCityFlag.enCityFlag_BuildingOpenGift)
            if not enCityFlag then
                if v.nState == GWG.GWConst.enBuildingState.enBuildingState_Normal then --只考虑当前状态为正常的建筑
                    local  posId = GWG.GWHomeMgr.gridData.GetGridPosIdByGridXY(v.x, v.y)
                    if GWG.GWHomeMgr.gridData.IsUnlockGrid(posId) then
                        local buildingData = game_scheme:Building_0(v.nBuildingID, v.nLevel + 1) --获取当前等级的下一级，如果获取不到说明到达最高级
                        if buildingData and (not build or build.nLevel > v.nLevel) then
                            if isCanUpgrade or self.BuildingCanUpgrade(buildingData) then
                                build = v
                            end
                        end
                    end
                end
            end

        end        
    end
    return build
end

--检测该建筑是否能升级
function M.BuildingCanUpgrade(buildCfg)
    if buildCfg.Preconditions or buildCfg.PreconditionsOr then
        local isUpgrade = GWG.GWAdmin.GWHomeCfgUtil.IsBuildUpgradeByPreconditions(buildCfg.BuildingID,buildCfg.level)
        if not isUpgrade then
            return false
        end
    end
    if buildCfg.food and buildCfg.food > 0 then
        needNumber = buildCfg.food
        number = player_mgr.GetPlayerFood()
        if needNumber > number then
            return false
        end
    end
    if buildCfg.iron and buildCfg.iron > 0 then
        needNumber = buildCfg.iron
        number = player_mgr.GetPlayerIron()
        if needNumber > number then
            return false
        end
    end
    if buildCfg.money and buildCfg.money > 0 then
        needNumber = buildCfg.money
        number = player_mgr.GetPlayerCoin()
        if needNumber > number then
            return false
        end
    end
    if buildCfg.cost then
        local Arr = string.split(buildCfg.cost, ";")
        for _, v in pairs(Arr) do
            local arr = string.split(v, "#")
            id = tonumber(arr[1])
            needNumber = tonumber(arr[2])
            number = player_mgr.GetPlayerOwnNum(id)
            if needNumber > number then
                return false
            end
        end
    end

    return true;
end

---@public 获取建筑组最大等级
function M.GetBuildingDataMaxLevel(buildId)
    local buildList = self.GetBuildingDataListByBuildingID(buildId)
    local level = 0
    if  buildList then
        for i, v in pairs(buildList) do
            if v.nLevel > level then
                level = v.nLevel
            end
        end
    end
    return level
end

function M.GetBuildingDataByBuildingType(buildType)
    if  self.CheckDataInvalid() then
        return
    end
    --注意，当前配置规则时配置表id = 1000*buildType
    local n_buildId = tonumber(buildType) * 1000
    for i, v in pairs(mc.building) do
        if v.nBuildingID == n_buildId then
            return v
        end
    end
end

function M.GetBuildingDataMaxLevelByType(buildType)
    local n_buildId = tonumber(buildType) * 1000
    return M.GetBuildingDataMaxLevel(n_buildId)
end
---@public 通过建筑id获取建筑类型
---@param buildId number
function M.GetBuildingTypeByBuildingId(buildId)
    return buildId /1000     
end
---@public 通过建筑Type获取建筑Id
---@param buildingType number
function M.GetBuildingIdByBuildingType(buildingType)
    return buildingType * 1000
end

---@public 获取建筑组件通过sid
function M.GetBuildingCompBySid(sid)
    if not sid or self.CheckDataInvalid() then
        return
    end
    local data = mc.sidBuilding[sid]
    if not data then
        --GWG.GWAdmin.SwitchUtility.Error("GetBuildingCompBySid失败 sid=", sid)
        return
    end
    local compId = data.compId
    return GWG.GWAdmin.GetBSComponentByID(compId)
end
---@public 获取建筑组件通过类型 --如果有多个，默认找的就是最大等级的
function M.GetBuildingCompByType(buildType)
    local targetBuildId = self.GetBuildingIdByBuildingType(buildType)
    local build
    if not self.CheckDataInvalid() then        
        for i, v in pairs(mc.building) do
            if v.nBuildingID == targetBuildId then
                if not build or build.nLevel < v.nLevel then
                    build = v
                end
            end
        end
    end
    if  build then
       return self.GetBuildingCompBySid(build.uSid)
    end
end
---@public 获取建筑数据通过compId
function M.GetBuildingDataByCompId(compId)
    if  self.CheckDataInvalid() then
        return
    end
    local comp = GWG.GWAdmin.GetBSComponentByID(compId)
    if not comp then
        GWG.GWAdmin.SwitchUtility.Error("GetBuildingDataByCompId 失败 compId=", compId)
        return
    end
    local sid = comp.serData and comp.serData.uSid or -100
    return mc.sidBuilding[sid]
end
---@public获取建筑对应的状态
function M.GetBuildingCityFlagByCompId(compId, enCityFlag)
    if  self.CheckDataInvalid() then
        return
    end
    local buildData = GWG.GWAdmin.GetBSComponentByID(compId)
    if not buildData then
        GWG.GWAdmin.SwitchUtility.Error("获取建筑失败 compId=", compId)
        return
    end
    return self.GetBuildingCityFlagBySid(buildData.serData.uFlag, enCityFlag)
end
---public 获取建筑数据通过uFlag
---@param enCityFlag table enCityFlag GWConst.CityFlag
---@param uFlag number uFlag 建筑的标志
function M.GetBuildingCityFlagBySid(sid, enCityFlag)
    if  self.CheckDataInvalid() then
        return
    end
    if not mc.sidBuilding[sid] then
        GWG.GWAdmin.SwitchUtility.Error("获取建筑数据失败 sid=", sid)
        return
    end
    local uFlag = mc.sidBuilding[sid].uFlag
    return self.GetBuildingCityFlag(uFlag, enCityFlag)
end

function M.GetBuildingCityFlag(uFlag, enCityFlag)
    return GWG.GWAdmin.HomeCommonUtil.GetCityFlagState(uFlag, enCityFlag)
end

---@public 获取当前建筑的建造队列时间戳
---@param sid number sid 建筑的sid
---@return number 队列时间戳 如果建筑不在建造中，则返回nil
function M.GetBuildQueueTimeStampBySid(sid)
    if  self.CheckDataInvalid() then
        return 0
    end
    local v = mc.sidBuilding[sid]
    if not v then
        return
    end
    --非升级，建造，修复中 直接不去计算队列
    if not (v.nState == GWG.GWConst.enBuildingState.enBuildingState_Repairing
            or v.nState == GWG.GWConst.enBuildingState.enBuildingState_Constructing
            or v.nState == GWG.GWConst.enBuildingState.enBuildingState_Upgrading) then
        return
    end
    local queues = GWG.GWHomeMgr.buildQueue.GetAllQueue()
    if queues then
        for i, v in ipairs(queues) do
            local work, uSid = GWG.GWHomeMgr.buildQueue.IsWorkingQueue(i)
            if work and uSid == sid then
                return v.uDoneTime
            end
        end
    end
end

---@public 检查建筑是否可以编辑
function M.IsBuildingCanLocationBySid(sid)
    if  self.CheckDataInvalid() then
        return false
    end
    local bData = self.GetBuildingDataBySid(sid)
    if not bData then
        return false
    end
    --如果破损，将不容许编辑位置
    if bData.nState == GWG.GWConst.enBuildingState.enBuildingState_Broken or bData.nState == GWG.GWConst.enBuildingState.enBuildingState_Repairing 
            or bData.nState == GWG.GWConst.enBuildingState.enBuildingState_Constructing then
        return false
    end
    local b = game_scheme:Building_0(bData.nBuildingID, 0)
    if b and b.TypeID then
        local buildingTypeCfg = game_scheme:BuildingType_0(b.TypeID)
        return buildingTypeCfg.location == 1
    end
    return false
end

---@public 检查建筑是否可以编辑
function M.IsBuildingCanLocationByCompId(compId)
    if  self.CheckDataInvalid() then
        return false
    end
    local curComp = GWG.GWAdmin.GetBSComponentByID(compId)
    if not curComp or not curComp.serData then
        return false
    end
    return self.IsBuildingCanLocationBySid(curComp.serData.uSid)
end

---@当前建筑是否正在建造、升级、修复
function M.IsBuildingUnderConstruction(sid)
    if  self.CheckDataInvalid() then
        return false
    end
    local v = mc.sidBuilding[sid]
    if not v then
        return false
    end
    if v.nState == GWG.GWConst.enBuildingState.enBuildingState_Repairing
            or v.nState == GWG.GWConst.enBuildingState.enBuildingState_Constructing
            or v.nState == GWG.GWConst.enBuildingState.enBuildingState_Upgrading then
        return true
    end
    return false
end
-- - - - - - - - - - - - - - - - - - - - - 资源 - - - - - - - - - - - - - - - - - - - -
--region 资源
---@public 获取当前类型资源所产生的数量
---@param buildType number buildType建筑类型
function M.GetTypeAllResourceNumber(buildType)
    if  self.CheckDataInvalid() then
        return 0
    end
    local buildId = GWG.GWHomeMgr.cfg.GetBuildId(buildType)
    local number = 0
    for i, buildData in pairs(mc.building) do
        if buildData.nBuildingID == buildId and buildData.nLevel > 0 then
            local buildCfg = game_scheme:Building_0(buildId, buildData.nLevel)
            if buildCfg and buildCfg.BuildingPara1 then
                number = number + GWG.GWAdmin.GWHomeCfgUtil.GetProduceNumber(buildData.uSid, buildData.resource, buildCfg.BuildingPara1)
            end
        end
    end
    number = math.floor(number)
    return number
end

---@public 获取item索引资源所产生的数量
---@param itemId number itemId 道具id
---@return number number 资源数量
function M.GetItemAllResourceNumber(itemId)
    if  self.CheckDataInvalid() then
        return 0
    end
    local typeId = GWG.GWHomeMgr.cfg.ItemGetBuildType(itemId)
    if typeId == 0 then
        --没有对应的建筑类型
        return 0
    end
    local number = self.GetTypeAllResourceNumber(typeId)
    return number
end
---@public 发送领取建筑资源请求
function M.SendCityGetBuildingResource(itemId)
    if  self.CheckDataInvalid() then
        return
    end
    local typeId = GWG.GWHomeMgr.cfg.ItemGetBuildType(itemId)
    if typeId == 0 then
        GWG.GWAdmin.SwitchUtility.Error("没有对应的建筑类型,不应该产生发送事件")
        return
    end
    local buildId = GWG.GWHomeMgr.cfg.GetBuildId(typeId)
    for i, buildData in pairs(mc.building) do
        if buildData.nBuildingID == buildId then
            net_city_module.MSG_CITY_GET_BUILDING_RESOURCE_REQ(buildData.uSid, true)
            break
        end
    end
end

---@public 领取建筑资源处理
function M.GetBuildingResourceRSP(msg)
    if  self.CheckDataInvalid() then
        return
    end
    if msg.errCode == 0 then      
        --注意 现在同一种类直接
        local gw_home_get_resource_mgr = require "gw_home_get_resource_mgr"
        gw_home_get_resource_mgr.ReceiveResourcesWithBizerCure(msg.uSid,msg.nCount,msg.nResourceID)           
        for i, sid in ipairs(msg.uSid) do
            local comp = self.GetBuildingCompBySid(sid)
            if comp then
                comp:GetBuildingResourceRSP()
            end
        end
        local gw_home_event_report_util = require "gw_home_event_report_util"
        gw_home_event_report_util.EventReportBuildingReceive(msg)
    end
end

--装饰建筑
function M.SetDecorateBuildingData(data)
    mc.decorateData = mc.decorateData or {}
    local buildingData =  data.data
    local operator = data.operator
    local buildingID = buildingData.nBuildingID or buildingData.buildingID
    local buildingLv = buildingData.nLevel or buildingData.buildingLv
    local building_cfg = game_scheme:Building_0(buildingID,buildingLv)

    local nextBuildingLv = buildingLv + 1
    if operator and operator > 1 then
        nextBuildingLv = buildingLv
    end
    local next_building_cfg = game_scheme:Building_0(buildingID,nextBuildingLv)

    local propData = {}
    for i=1,5 do
        local cur_prop_id = building_cfg["BuildingPara"..tostring(i)]
        local next_prop_id = nil
        if next_building_cfg then
            next_prop_id = next_building_cfg["BuildingPara"..tostring(i)]
        end
        local prop = {}
        if cur_prop_id then
            prop.curPropID = cur_prop_id
        end
        if next_prop_id then
            prop.nextPropID = next_prop_id
        end
        if  (not cur_prop_id or cur_prop_id <= 0) and (next_prop_id and next_prop_id > 0) then
            prop.curPropID =  prop.nextPropID
            cur_prop_id = prop.curPropID
            next_prop_id = nil
            prop.nextPropID = nil
        end
        if (buildingLv == 0 and next_prop_id and next_prop_id>0) or (cur_prop_id and cur_prop_id>0) then
            propData[#propData+1] = prop
        end
        --propData[#propData+1] = prop
    end
    mc.decorateData.buildingID = buildingID
    mc.decorateData.propDataTable = propData

    local buildingIcon = building_cfg.BuildIcon
    mc.decorateData.buildingIcon = buildingIcon

    local buildingType = game_scheme:BuildingType_0(building_cfg.TypeID)
    local nameID =  buildingType.NameID
    mc.decorateData.buildingNameID = nameID
    mc.decorateData.buildingLv = buildingLv

    local Arr = string.split(buildingType.buildable,";")
    local canBuild = -1
    for _,v in pairs(Arr) do
        local arr = string.split(v,"#")
        canBuild = tonumber(arr[2])
    end
    mc.decorateData.maxBuildNum = canBuild
    
    local buildList =  GWG.GWHomeMgr.buildingData.GetBuildingDataListByBuildingIdWithoutZeroLevel(buildingID)
    local curBuild = util.get_len(buildList)
    mc.decorateData.curBuildNum = curBuild
    mc.decorateData.buildingDesc = buildingType.BuildingDes
    mc.decorateData.rarity = buildingType.Rarity

    mc.decorateData.maxLevel = false
    if next_building_cfg then
        local costStr =  next_building_cfg.cost
        local arr =  string.split(costStr,"#")
        local needItemID = tonumber(arr[1])
        local ownItemNum = player_mgr.GetPlayerOwnNum(needItemID)
        local needItemNum = tonumber(arr[2])
        mc.decorateData.ownItemNum = ownItemNum
        mc.decorateData.needItemNum = needItemNum
        mc.decorateData.needItemID = needItemID
    else
        mc.decorateData.maxLevel = true
    end
end

function M.GetPropIdLstByBuildingId(buildingID)
    local lv = 1
    local building_table = {}
    while true do
        local buildingData = game_scheme:Building_0(buildingID,lv)
        if not buildingData then
            break
        end
        building_table[#building_table+1] = buildingData
        lv = lv +1
    end
    return building_table
end

function M.GetDecorateBuildingData()
    return mc.decorateData
end

--获取装饰物通用碎片数据
--返回id + nums
function M.GetUniversalExchangeItem(itemID)
    if not mc.decorateData.exchangeItems  then
        mc.decorateData.exchangeItems = {}
    end
    if not mc.decorateData.exchangeItems[itemID] then
        local nums = game_scheme:Prescription_nums()
        for i=0,nums-1 do
            local prescription = game_scheme:Prescription(i)
            if prescription.target == itemID then
                local costItemId = prescription.cost
                local costItemNum = prescription.costnum
                local prescriptionData = {}
                prescriptionData.costItemId = costItemId
                prescriptionData.costItemNum = costItemNum
                prescriptionData.rulerId = i+1
                mc.decorateData.exchangeItems[itemID] = prescriptionData
                break
            end
        end
    end
    
    return mc.decorateData.exchangeItems[itemID]
end


function M.UpdateDecorateDataForBuildingID(buildingID)
    if not mc.decorateData or not mc.decorateData.BaseData then
        return
    end

    local buildingCfg = game_scheme:Building_0(buildingID,1)
    if not buildingCfg then return end -- If building config doesn't exist, exit

    local buildingTypeData = game_scheme:BuildingType_0(buildingCfg.TypeID)
    if not buildingTypeData then return end

    local subType = buildingTypeData.BuildingSubtype
    if subType ~= 3 or buildingTypeData.Rarity <= 1 then return end

    -- Remove existing building data for this `buildingID` if it exists
    for i, data in ipairs(mc.decorateData.BaseData) do
        if data.buildingID == buildingID then
            table.remove(mc.decorateData.BaseData, i)
            if data.sumPower then
                mc.decorateData.SumPower = mc.decorateData.SumPower - data.sumPower or 0 
            end
            
            if data.curBuildNum > 0 then
                local cur_build_cfg = game_scheme:Building_0(buildingID, data.buildingLv)
                for j = 1, 5 do
                    local cur_prop_id = cur_build_cfg["BuildingPara" .. tostring(j)]
                    if cur_prop_id and cur_prop_id > 0 then
                        local map_effect = game_scheme:GWMapEffect_0(cur_prop_id)
                        local iProID =   map_effect.nGroupID
                        local proData =  self.GetDecorateProTable(iProID)
                        if  mc.decorateData.EffectGroupData then
                            for i=1,#mc.decorateData.EffectGroupData do
                                if mc.decorateData.EffectGroupData[i].groupID == proData.decorationLang then
                                    if mc.decorateData.EffectGroupData[i].props then
                                        for j=1,#mc.decorateData.EffectGroupData[i].props do
                                            if mc.decorateData.EffectGroupData[i].props[j].proName == proData.proLang then
                                                mc.decorateData.EffectGroupData[i].props[j].proValue = mc.decorateData.EffectGroupData[i].props[j].proValue - map_effect.strParam[0]
                                                break
                                            end
                                        end
                                    end
                                end
                            end
                        end
                    end
                end
            end

            if data.rarity == 4 then
                mc.decorateData.Progress.collected_s_plus = mc.decorateData.Progress.collected_s_plus - 1
                mc.decorateData.RedDotData.SPlusData[buildingID] = nil
            elseif data.rarity == 3 then
                mc.decorateData.Progress.collected_s = mc.decorateData.Progress.collected_s - 1
                mc.decorateData.RedDotData.SData[buildingID] = nil
            elseif data.rarity == 2 then
                mc.decorateData.Progress.collected_a = mc.decorateData.Progress.collected_a - 1
                mc.decorateData.RedDotData.AData[buildingID] = nil
            end
            break
        end
    end

    -- Initialize variables
    local baseBuildingData = {}
    baseBuildingData.buildingID = buildingID
    baseBuildingData.maxLevel = false

    -- Scene building data
    local buildList = GWG.GWHomeMgr.buildingData.GetBuildingDataListByBuildingIdWithoutZeroLevel(buildingID)
    local curBuildNum = util.get_len(buildList)
    baseBuildingData.curBuildNum = curBuildNum
    baseBuildingData.buildingLv = 0

    for _, v in ipairs(buildList) do
        baseBuildingData.buildingLv = v.nLevel
    end

    -- Backpack data
    local lv = baseBuildingData.buildingLv
    local cur_build_cfg = game_scheme:Building_0(buildingID, lv)

    if baseBuildingData.curBuildNum > 0 then
        local cur_power = 0
        for j = 1, 5 do
            local cur_prop_id = cur_build_cfg["BuildingPara" .. tostring(j)]
            if cur_prop_id and cur_prop_id > 0 then
                local map_effect = game_scheme:GWMapEffect_0(cur_prop_id)
                local iProID =   map_effect.nGroupID
                local proType =  game_scheme:ProToLang_0(iProID)
                local power_coe = proType.Power
                local building_prop_power = map_effect.strParam[0] * power_coe
                cur_power = cur_power + building_prop_power


                local proData =  self.GetDecorateProTable(iProID)
                
                local existProName = false
                local existGroup = false
                if  mc.decorateData.EffectGroupData then
                    for i=1,#mc.decorateData.EffectGroupData do
                        if mc.decorateData.EffectGroupData[i].groupID == proData.decorationLang then
                            existGroup = true
                            if mc.decorateData.EffectGroupData[i].props then
                                for j=1,#mc.decorateData.EffectGroupData[i].props do
                                    if mc.decorateData.EffectGroupData[i].props[j].proName == proData.proLang then
                                        mc.decorateData.EffectGroupData[i].props[j].proValue = mc.decorateData.EffectGroupData[i].props[j].proValue + map_effect.strParam[0]
                                        existProName = true
                                        break
                                    end
                                end
                            end
                        end
                    end
                end
                
                if not existProName then
                    mc.decorateData.EffectGroupData = mc.decorateData.EffectGroupData or {}
                    local proName = proData.proLang
                    local proValue = map_effect.strParam[0]
                    local groupID =  proData.decorationLang
                    local props = {}
                    props[#props+1] = {proName=proName,proValue = proValue,proType = proData.proType }
                    local data = {groupID = groupID,props =props}
                    if not existGroup then
                        mc.decorateData.EffectGroupData[#mc.decorateData.EffectGroupData+1] = data
                    else
                        for i=1,#mc.decorateData.EffectGroupData do
                            if mc.decorateData.EffectGroupData[i].groupID == proData.decorationLang then
                                props = mc.decorateData.EffectGroupData[i].props or {}
                                props[#props+1] = {proName=proName,proValue = proValue,proType = proData.proType}
                                mc.decorateData.EffectGroupData[i].props = props
                                break
                            end
                        end
                    end
                end
                
                --[[local group_effect =  mc.decorateData.EffectGroupData[proData.decorationLang] -- EffectGroupData[proData.decorationLang]
                if group_effect then
                    local effect_prop =  group_effect[proData.proLang]
                    if effect_prop then
                        group_effect[proData.proLang] = group_effect[proData.proLang] + map_effect.strParam[0]
                    else
                        group_effect[proData.proLang] = map_effect.strParam[0]
                    end
                else
                    mc.decorateData.EffectGroupData[proData.decorationLang] = {}
                    mc.decorateData.EffectGroupData[proData.decorationLang][proData.proLang] = map_effect.strParam[0]
                end]]--
            end
        end
        if mc.decorateData.EffectGroupData then
            table.sort( mc.decorateData.EffectGroupData,function(a,b)
                return a.groupID<=b.groupID
            end)
        end
        baseBuildingData.sumPower = cur_build_cfg.power + cur_power/100 * player_mgr.GetPalPartCount()
        mc.decorateData.SumPower = mc.decorateData.SumPower + baseBuildingData.sumPower
    end

    -- Check for next level building data
    local nextBuildingLv = lv + 1
    local next_building_cfg = game_scheme:Building_0(buildingID, nextBuildingLv)

    if next_building_cfg then
        local costStr = next_building_cfg.cost
        local arr = string.split(costStr, "#")
        local needItemID = tonumber(arr[1])
        baseBuildingData.needItemID = needItemID
        baseBuildingData.ownItemNum = player_mgr.GetPlayerOwnNum(needItemID)
        baseBuildingData.upgrade_cost_item_num = tonumber(arr[2])
    else
        local costStr = cur_build_cfg.cost
        local arr = string.split(costStr,"#")
        local needItemID = tonumber(arr[1])
        baseBuildingData.needItemID = needItemID
        baseBuildingData.maxLevel = true
    end

    baseBuildingData.buildIcon = buildingCfg.BuildIcon
    baseBuildingData.buildNameID = buildingTypeData.NameID
    baseBuildingData.rarity = buildingTypeData.Rarity == 0 and 1 or buildingTypeData.Rarity

    -- Update Progress and Red Dot Data
    if baseBuildingData.rarity == 4 then
        mc.decorateData.Progress.total_s_plus = mc.decorateData.Progress.total_s_plus + 1
        if curBuildNum > 0 then
            mc.decorateData.Progress.collected_s_plus = mc.decorateData.Progress.collected_s_plus + 1
        end
        if not baseBuildingData.maxLevel and
                ((curBuildNum <= 0 and baseBuildingData.ownItemNum > 0) or
                        (curBuildNum > 0 and baseBuildingData.ownItemNum >= baseBuildingData.upgrade_cost_item_num)) then
            mc.decorateData.RedDotData.SPlusData[buildingID] = baseBuildingData
        end
    elseif baseBuildingData.rarity == 3 then
        mc.decorateData.Progress.total_s = mc.decorateData.Progress.total_s + 1
        if curBuildNum > 0 then
            mc.decorateData.Progress.collected_s = mc.decorateData.Progress.collected_s + 1
        end
        if not baseBuildingData.maxLevel and
                ((curBuildNum <= 0 and baseBuildingData.ownItemNum > 0) or
                        (curBuildNum > 0 and baseBuildingData.ownItemNum >= baseBuildingData.upgrade_cost_item_num)) then
            mc.decorateData.RedDotData.SData[buildingID] = baseBuildingData
        end
    elseif baseBuildingData.rarity == 2 then
        mc.decorateData.Progress.total_a = mc.decorateData.Progress.total_a + 1
        if curBuildNum > 0 then
            mc.decorateData.Progress.collected_a = mc.decorateData.Progress.collected_a + 1
        end
        if not baseBuildingData.maxLevel and
                ((curBuildNum <= 0 and baseBuildingData.ownItemNum > 0) or
                        (curBuildNum > 0 and baseBuildingData.ownItemNum >= baseBuildingData.upgrade_cost_item_num)) then
            mc.decorateData.RedDotData.AData[buildingID] = baseBuildingData
        end
    end

    local Arr = string.split(buildingTypeData.buildable, ";")
    for _, v in ipairs(Arr) do
        local arr = string.split(v, "#")
        baseBuildingData.maxBuildNum = tonumber(arr[2])
    end

    -- Add the updated building data to BaseData
    mc.decorateData.BaseData[#mc.decorateData.BaseData + 1] = baseBuildingData
    
    -- Update Red Dot notifications
    red_system.TriggerRed(red_const.Enum.BuildListTogDecor)
    red_system.TriggerRed(red_const.Enum.SPlusBuilding)
    red_system.TriggerRed(red_const.Enum.SBuilding)
    red_system.TriggerRed(red_const.Enum.ABuilding)
end

--装饰属性受击
function M.DecoratePropCollect()
    mc.decorateData = mc.decorateData or {}
    if mc.decorateData.ProTable then
        return
    end
    mc.decorateData.ProTable = {}
    local nums =  game_scheme:HeroProList_nums()
    for i=0,nums -1 do
        local pro = game_scheme:HeroProList(i)
        --不等于装饰物直接return 
        if pro.panelID == 6 then
            local data = {}
            data.proType = pro.proType[1][0]
            data.proLang = pro.proLang
            data.decorationLang = pro.decorationLang
            data.proDisplay = pro.proDisplay
            mc.decorateData.ProTable[data.proType] = data
        end
    end
end

function M.GetDecorateProTable(proType)
    if not mc.decorateData.ProTable then
        return
    end
    if mc.decorateData.ProTable[proType] then
        return mc.decorateData.ProTable[proType]
    else
        log.Error("[Decorate Building] Error: proType = " .. proType.." dont find config in HeroProList")
        return {proType = 101,proLang = 650601,decorationLang = 601509, proDisplay = 2 }
    end
end

--搜集所有装饰物建筑数据
--便利building表，筛选出subtype = 3建筑，判断其当前场景是否有建造，判断当前背包是否有该建筑物品
function M.CreateAllDecorateData()
    mc.decorateData = mc.decorateData or {}
    mc.decorateData.BaseData = {}
    mc.decorateData.Progress = {}
    mc.decorateData.Progress.collected_s_plus = 0
    mc.decorateData.Progress.total_s_plus = 0
    mc.decorateData.Progress.collected_s = 0
    mc.decorateData.Progress.total_s = 0
    mc.decorateData.Progress.collected_a = 0
    mc.decorateData.Progress.total_a = 0
    
    mc.decorateData.RedDotData = {}
    mc.decorateData.RedDotData.SPlusData = {}
    mc.decorateData.RedDotData.SData = {}
    mc.decorateData.RedDotData.AData = {}
    local EffectGroupData = {}
    mc.decorateData.EffectGroupData ={}
    
    mc.decorateData.SumPower = 0
    
    self.DecoratePropCollect()
    
    local buildingLen = game_scheme:Building_nums()
    local memory_building_id_table = {}
    for i = 0, buildingLen - 1 do
        local cfg = game_scheme:Building(i)
        if cfg and cfg.TypeID and cfg.level >=0 and not memory_building_id_table[cfg.BuildingID] then
            local buildingTypeData = game_scheme:BuildingType_0(cfg.TypeID)
            local subType = buildingTypeData.BuildingSubtype
            if subType == 3 and  buildingTypeData.Rarity > 1 then
                local baseBuildingData = {}
                local buildingID = cfg.BuildingID
                memory_building_id_table[buildingID] = true
                
                baseBuildingData.buildingID = buildingID;
                baseBuildingData.maxLevel = false
                
                --场景建造数据
                local buildList =  GWG.GWHomeMgr.buildingData.GetBuildingDataListByBuildingIdWithoutZeroLevel(buildingID)
                local curBuildNum = util.get_len(buildList)
                baseBuildingData.curBuildNum = curBuildNum

                baseBuildingData.buildingLv = 0
                for k,v in ipairs(buildList) do
                    local buildingLevel = v.nLevel
                    baseBuildingData.buildingLv = buildingLevel
                end
                
                --背包数据
                local lv = baseBuildingData.buildingLv
                local cur_build_cfg =  game_scheme:Building_0(buildingID,lv)
                
                --属性分类,只有建造了才有属性加成
                if baseBuildingData.curBuildNum > 0 then
                    local cur_power = 0
                    for j=1,5 do
                        local cur_prop_id = cur_build_cfg["BuildingPara"..tostring(j)]
                        if cur_prop_id and cur_prop_id>0 then
                            local map_effect = game_scheme:GWMapEffect_0(cur_prop_id)
                            local iProID =   map_effect.nGroupID
                            local proType =  game_scheme:ProToLang_0(iProID)
                            local power_coe = proType.Power
                            local building_prop_power = map_effect.strParam[0] * power_coe
                            cur_power = cur_power + building_prop_power
                            
                            local proData =  self.GetDecorateProTable(iProID)
                            local group_effect = EffectGroupData[proData.decorationLang]
                            if group_effect then
                                local effect_prop =  group_effect[proData.proLang]
                                if effect_prop then
                                    group_effect[proData.proLang].value = group_effect[proData.proLang].value + map_effect.strParam[0]
                                else
                                    group_effect[proData.proLang] = {value = map_effect.strParam[0],proType = iProID}
                                end
                            else
                                EffectGroupData[proData.decorationLang] = {}
                                EffectGroupData[proData.decorationLang][proData.proLang] = {value =  map_effect.strParam[0],proType = iProID}
                            end
                        end
                    end
                    baseBuildingData.sumPower = cur_build_cfg.power+cur_power/100 * player_mgr.GetPalPartCount()
                    mc.decorateData.SumPower = mc.decorateData.SumPower + baseBuildingData.sumPower
                end

                local nextBuildingLv = lv + 1
                local next_building_cfg = game_scheme:Building_0(buildingID,nextBuildingLv)

                local ownItemNum = nil
                local upgrade_cost_item_num = nil
                if next_building_cfg then
                    local costStr =  next_building_cfg.cost
                    local arr =  string.split(costStr,"#")
                    
                    local needItemID = tonumber(arr[1])
                    ownItemNum = player_mgr.GetPlayerOwnNum(needItemID)
                    baseBuildingData.ownItemNum = ownItemNum
                    baseBuildingData.needItemID = needItemID
                    
                    upgrade_cost_item_num = tonumber(arr[2])
                    baseBuildingData.upgrade_cost_item_num = upgrade_cost_item_num
                else
                    local costStr = cur_build_cfg.cost
                    local arr =  string.split(costStr,"#")
                    local needItemID = tonumber(arr[1])
                    baseBuildingData.needItemID = needItemID
                    baseBuildingData.maxLevel = true
                end
                
                baseBuildingData.buildIcon = cfg.BuildIcon
                baseBuildingData.buildNameID = buildingTypeData.NameID
                
                --升级所需要的道具数量及已有的道具数量
                baseBuildingData.rarity = buildingTypeData.Rarity == 0 and 1 or buildingTypeData.Rarity

                if baseBuildingData.rarity ==4 then
                    if curBuildNum > 0 then
                        mc.decorateData.Progress.collected_s_plus = mc.decorateData.Progress.collected_s_plus+1
                    end
                    mc.decorateData.Progress.total_s_plus =  mc.decorateData.Progress.total_s_plus+1
                elseif baseBuildingData.rarity == 3 then
                    if curBuildNum > 0 then
                        mc.decorateData.Progress.collected_s = mc.decorateData.Progress.collected_s+1
                    end
                    mc.decorateData.Progress.total_s =  mc.decorateData.Progress.total_s+1
                elseif baseBuildingData.rarity ==2 then
                    if curBuildNum > 0 then
                        mc.decorateData.Progress.collected_a = mc.decorateData.Progress.collected_a+1
                    end
                    mc.decorateData.Progress.total_a =  mc.decorateData.Progress.total_a+1
                end
                
                local Arr = string.split(buildingTypeData.buildable,";")
                local canBuild = -1
                for _,v in pairs(Arr) do
                    local arr = string.split(v,"#")
                    canBuild = tonumber(arr[2])
                end

                if  baseBuildingData.maxLevel ==nil or baseBuildingData.maxLevel == false then
                    if  (curBuildNum<= 0 and ownItemNum >0) or (curBuildNum>0 and ownItemNum>=baseBuildingData.upgrade_cost_item_num) then
                        if baseBuildingData.rarity == 2 then
                            mc.decorateData.RedDotData.AData[buildingID] = baseBuildingData
                        elseif baseBuildingData.rarity==3 then
                            mc.decorateData.RedDotData.SData[buildingID] = baseBuildingData
                        elseif baseBuildingData.rarity == 4 then
                            mc.decorateData.RedDotData.SPlusData[buildingID] = baseBuildingData
                        end
                    end
                end
     
                baseBuildingData.maxBuildNum = tonumber(canBuild)
                mc.decorateData.BaseData[#mc.decorateData.BaseData+1] = baseBuildingData
            end
        end
    end
    
    for k,v in pairs(EffectGroupData) do
        local group_effect_data = {}
        local groupID =  k
        group_effect_data.groupID = groupID
        for k1,v1 in pairs(v) do
            local propData = {}
            local proName = k1
            local proValue = v1.value
            propData.proName = proName
            propData.proValue = proValue
            propData.proType = v1.proType
            group_effect_data.props = group_effect_data.props or {}
            group_effect_data.props[#group_effect_data.props+1] =  propData
        end
        mc.decorateData.EffectGroupData[#mc.decorateData.EffectGroupData+1] = group_effect_data
    end
    table.sort( mc.decorateData.EffectGroupData,function(a,b)
        return a.groupID<=b.groupID
    end)

    red_system.TriggerRed(red_const.Enum.BuildListTogDecor)
    red_system.TriggerRed(red_const.Enum.SPlusBuilding)
    red_system.TriggerRed(red_const.Enum.SBuilding)
    red_system.TriggerRed(red_const.Enum.ABuilding)
end

function M.DecorateSort(data)
    if  not data then
        return 
    end
    
    table.sort(data, function(data1, data2)
        data1.curBuildNum = data1.curBuildNum or 0
        data2.curBuildNum = data2.curBuildNum or 0
        local active1 = data1.curBuildNum > 0 and 1 or 0
        local active2 = data2.curBuildNum > 0 and 1 or 0
        
        --已建造优先级最高
        if active1 ~= active2 then
            return active1 > active2
        end
        
        --开始比是否激活
        if active1 == 0 then
            data1.ownItemNum = data1.ownItemNum or 0
            data2.ownItemNum = data2.ownItemNum or 0
            local own1 = data1.ownItemNum > 0 and 1 or 0
            local own2 = data2.ownItemNum > 0 and 1 or 0
            if own1~=own2 then
                return own1>own2
            end
        end

        --比属性
        if data1.propValue and data2.propValue then
            return data1.propValue > data2.propValue
        end
        
        --比等级
        if data1.buildingLv ~= data2.buildingLv then
            return data1.buildingLv > data2.buildingLv
        end
        
        --最后比id
        return data1.buildingID > data2.buildingID
    end)
end

function M.GetDecorateDataByProType(_proType)
    if not mc.decorateData.BaseData then
        return
    end
    
    --[[local result_data = 
    {
        [1] = 
        {
            baseData = baseData,
            value = value 
        }
    }]]--
    local result_data = {}
    for i=1,#mc.decorateData.BaseData do
        local baseData = mc.decorateData.BaseData[i]
        if baseData then
            local lv = baseData.buildingLv>0 and baseData.buildingLv or 1
            local buildingID = baseData.buildingID
            local cur_build_cfg =  game_scheme:Building_0(buildingID,lv)
            for j=1,5 do
                local cur_prop_id = cur_build_cfg["BuildingPara"..tostring(j)]
                if cur_prop_id and cur_prop_id>0 then
                    local map_effect = game_scheme:GWMapEffect_0(cur_prop_id)
                    local iProID =   map_effect.nGroupID
                    if iProID == _proType then
                        baseData.propValue = map_effect.strParam[0]
                        result_data[#result_data+1] = baseData
                        break
                    end
                end
            end
        end
    end
    
    return result_data
end

function M.GetDecorateDataByItemID(itemID)
    if not mc.decorateData.BaseData then
        return
    end
    
    for i=1,#mc.decorateData.BaseData do
        local baseData = mc.decorateData.BaseData[i]
        if baseData then
            if baseData.needItemID == itemID then
                return baseData
            end
        end
    end
    
    return nil
end

--得到装饰属性全部加成
function M.GetDecorateEffectGroupData()
    return mc.decorateData.EffectGroupData
end

--获取现拥有所有装饰建筑的基础信息
function M.GetAllDecorateBuildBaseData()
    return  mc.decorateData.BaseData
end

function M.GetDecorateCollectProgressData()
    return mc.decorateData.Progress
end

--检查buildId 的装饰建筑解锁了没
function M.IsDecorateBuildUnlock(buildId,typeId)
    if not mc.decorateData then
        return
    end
    local baseData = mc.decorateData.BaseData
    local typeData =  game_scheme:BuildingType_0(typeId)
    --稀有度绿色品质以上才要判断
    if typeData and typeData.Rarity<2 then
        return true
    end
    for i=1,#baseData do
        local  buildData =  baseData[i]
        if buildData.buildingID == buildId then
            --背包里有物品则表示解锁
            if buildData.ownItemNum and buildData.ownItemNum > 0 then
                return true
            end
            --说明场景有建筑
            if buildData.curBuildNum and buildData.curBuildNum>0 then
                return true
            end
        end
    end
    return false
end

--当前buildId装饰物是否是新获得的
function M.IsDecorateNewGet(buildId)
    local baseData = mc.decorateData.BaseData
    for i=1,#baseData do
        local  buildData =  baseData[i]
        if buildData.buildingID == buildId then
            --背包里有物品则表示解锁
            if  buildData and buildData.ownItemNum and buildData.ownItemNum > 0 and  buildData.curBuildNum<=0 then
                return true
            end
        end
    end
    return false
end

function M.GetRedDataByIDAndRarity(rarity,buildID)
    if rarity == 2 then
        return mc.decorateData.RedDotData.AData[buildID]
    elseif rarity == 3 then
        return mc.decorateData.RedDotData.SData[buildID]
    elseif rarity == 4 then
        return mc.decorateData.RedDotData.SPlusData[buildID]
    end
    return nil
end

--得到指定品阶装饰物红点激活数据
function M.GetRedNumWithQuality(rarity)
    if rarity == 2 then
        return util.get_len(mc.decorateData.RedDotData.AData)
    elseif rarity == 3 then
        local num = util.get_len(mc.decorateData.RedDotData.SData)
        return num
    elseif rarity == 4 then
        return util.get_len(mc.decorateData.RedDotData.SPlusData)
    end
    return 0
end

function M.GetAllRedNum()
    if not mc.decorateData then
        return 0
    end
    local red_num_a = self.GetRedNumWithQuality(2)
    local red_num_s = self.GetRedNumWithQuality(3)
    local red_num_s_plus = self.GetRedNumWithQuality(4)
    return red_num_a + red_num_s + red_num_s_plus
end

function M.GetDecorateSumPower()
    return mc.decorateData.SumPower
end

--region 联盟帮助加速的玩家
function M.SetTempHelperInfo(data)
    mc.tempHelpPlayerInfo={}
    if not data.help or #data.help==0 then
        return
    end
    mc.tempHelpPlayerInfo={
        uSid = data.uSid or 0,
        help =data.help,
    }
    --抛出事件
    gw_ed.mgr:Trigger(gw_ed.GW_BUILDING_OPENGIFT_HELPER_RSP, mc.tempHelpPlayerInfo)
end

function M.ClearHelpPlayerInfo()
    mc.tempHelpPlayerInfo={}
end

function M.GetTempHelperInfo()
    return mc.tempHelpPlayerInfo
end

--用于额外判断建造队列是否显示
M.ShowConditionFunc = {
    [GWConst.enBuildingType.enBuildingType_DroneStar] = function ()
        local new_weapon_combat_crystal_data = require "new_weapon_combat_crystal_data"
        return new_weapon_combat_crystal_data.GetCanDisplayInBuildingList()
    end
    ,
}


--endregion

--endregion
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
--- 数据清理
function M.Dispose()
    _d.mde:clear()
    mn = _d.mde.net
    mc = _d.mde.const
    event.Unregister(event.UPDATE_MODULE_OPEN, self.CheckBuildingModuleOpen)
end
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
return M