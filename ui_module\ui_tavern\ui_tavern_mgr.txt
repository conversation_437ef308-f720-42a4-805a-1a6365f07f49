local table				= table
local ipairs            = ipairs
local pairs				= pairs
local require = require

local gw_common_util = require "gw_common_util"
local gw_sand_event_define = require "gw_sand_event_define"
local event	 = require "event"
local game_scheme = require "game_scheme"
local player_mgr		= require "player_mgr"
local net_route     = require "net_route"
local xManMsg_pb = require "xManMsg_pb"
local event_define  = require "event_define"
local net = require "net"
local GWAdmin = GWAdmin
local log = require "log"
local reward_mgr     = require "reward_mgr"
local ui_window_mgr = require "ui_window_mgr"
local GWConst = GWConst
local gw_hero_data = require "gw_hero_data"
local acornpub_pb = require "acornpub_pb"
local TransmitLuaFuncReq    = net.TransmitLuaFuncReqNew
local lua_pb = require "lua_pb"
local flow_text = require "flow_text"
local math = math
local lang = require "lang"
local string= string
local os = os
local GWG = GWG
local	time_util = require "time_util"
local red_const = require "red_const"
local red_system = require "red_system"
local ui_setting_util = require "ui_setting_util"
local tostring = tostring

local _M = {}
module("ui_tavern_mgr")

local tavernSeverData = {}
--刷新是则使用道具，否则用钻石
local m_useItem = true
local curStarLvelelCfg = {}
local m_selectHeroData = {}
local normalRefreshTasksTab = {}
--缓存已使用的英雄列表,优先以服务器回包为主，没回包过程中以客户端的为主
local usingHeroTab = {}
local m_taskHelpData = {}
local m_curTaskHelpData = {}
local canReviceTaksTab = {}
local m_allianceTasksGen = {}
local initAllHeroData = {}
local m_disPatchBool = true
local m_curTaskHeroCount = 0
local isOpenUI = false
local initRedPoint = false
--缓存数据
local cache_curLevel = nil
--属性加成
local increaseDispatch = 0
local increaseSupplyBox = 0

local isClickShowHistory = false
---------------------主界面相关 --------------------------
--隐秘机动队派遣队列数
function _M.SetIncreaseDispatch(value)
    increaseDispatch = value
end

function _M.GetIncreaseDispatch()
    return increaseDispatch
end

--获得当前可派遣的队伍数量
function _M.GetCurCanDisPatchCount()
    local cityProp = GWG.GWHomeMgr.commonData.GetCityProp()
    return cityProp.enCityProp_AcornPubCanSendTaskCount + _M.GetIncreaseDispatch()
end

--隐秘任务产出神秘补给箱数量
function _M.SetIncreaseSupplyBox(value)
    increaseSupplyBox = value
end

function _M.GetIncreaseSupplyBox()
    return increaseSupplyBox
end

function _M.ResetData()
    _M.RefreshCurStarLvelelCfg()
end

--获取我的任务对应任务的完成状态
--1 - 未派遣,2 - 已完成待领取,3 - 完美领取,4 - 正在进行,5 - 已领取部分被偷窃
function _M.GetpersonalTaskState(persionalData)
    if persionalData.doneTime == 0 then
        return 1
    elseif persionalData.doneTime <= os.server_time() and not persionalData.rewarded then
        return 2
    elseif persionalData.doneTime <= os.server_time() and persionalData.rewarded and #persionalData.robDbids == 0 then
        return 3
    elseif persionalData.doneTime > os.server_time() then
        return 4
    elseif persionalData.doneTime <= os.server_time() and persionalData.rewarded and #persionalData.robDbids > 0 then
        return 5
    end
    return -1
end

function _M.GetCfgIDbySID(taskSID)
    if tavernSeverData then
        for i = 1, #tavernSeverData.tasks do
            if tavernSeverData.tasks[i].taskSID == taskSID then
                return tavernSeverData.tasks[i].taskID
            end
        end
    end
end

--协助回包后,根据dbid刷新移除对应角色的taskSID任务
--returen taskID
function _M.RefreshLeagueData(dbid,taskSID)
    if tavernSeverData then
        for i = 1, #m_allianceTasksGen do
            if m_allianceTasksGen[i].dbid == dbid and m_allianceTasksGen[i].task.taskSID == taskSID  then
                local tempTaskID = m_allianceTasksGen[i].task.taskID
                table.remove(m_allianceTasksGen,i)
                return tempTaskID
            end
        end
    end
end

--获取盟友任务的完成状态 1-已完成待协助 2-正在进行
function _M.GetLeagueTaskState(doneTime)
    if doneTime <= os.server_time() then
        return 1
    else 
        return 2
    end
end

------------------刷新道具相关----------------

function _M.SetUseItemBool(useItem)
    m_useItem = useItem
end

function _M.GetUseItemBool()
    return m_useItem
end

------------------刷新道具相关----------------

--获取当前星级难度
function _M.GetCurStarLevlel()
    local gw_home_building_data = require "gw_home_building_data"
    local curLevel = gw_home_building_data.GetBuildingMainLevel()
    cache_curLevel = curLevel
    for i = 1, game_scheme:SecretStar_nums() do
        if i == game_scheme:SecretStar_nums() then
            return i
        end
        local minLevel = game_scheme:SecretStar_0(i).HeadquartersLevelRequired
        local maxLevel = game_scheme:SecretStar_0(i + 1).HeadquartersLevelRequired
        if minLevel <= curLevel and curLevel < maxLevel then
            return i
        end
    end
end

function _M.RefreshCurStarLvelelCfg()
    local gw_home_building_data = require "gw_home_building_data"
    local curLevel = gw_home_building_data.GetBuildingMainLevel()
    if not cache_curLevel or cache_curLevel ~= curLevel then
        curStarLvelelCfg = game_scheme:SecretStar_0(_M.GetCurStarLevlel())
    end
end

--获得当前星级对应的常规刷新道具信息
function _M.GetCurStarCostZuanShiCount()
    return curStarLvelelCfg.RefreshDiamondCost
end

--获得当前星级对应的常规刷新道具信息
function _M.GetCurStarCostItemContent()
    local costData = curStarLvelelCfg.RefreshCost.data
    local itemID = costData[0]
    local costItemCount = costData[1]
    return itemID,costItemCount
end

--获得当前星级对应的超级刷新道具信息
function _M.GetCurStarSuperCostItemContent()
    local costData = curStarLvelelCfg.MegaRefreshCost.data
    local itemID = costData[0]
    local costItemCount = costData[1]
    return itemID,costItemCount
end

function _M.GetCurStateMax()
    if not curStarLvelelCfg.AidLimit then
        _M.RefreshCurStarLvelelCfg()
    end
    return curStarLvelelCfg.AidLimit,curStarLvelelCfg.SnatchLimit
end

--获取超级刷新的消耗信息
--useItem是否使用道具 useItemCount 使用道具的数量 ,useZuanshi是否使用钻石 useZuanshiCount使用钻石的数量
function _M.GenSuperRefreshCostItem()
    local itemID,costItemCount = _M.GetCurStarSuperCostItemContent()
    local singleItemCostZuanshiCount = _M.GetCurStarCostZuanShiCount()
    local needRefreshCount = #_M.GetNormalPersonalTasksCount()
    local playerHasItemCount = player_mgr.GetPlayerOwnNum(itemID)
    local useItem =  playerHasItemCount > 0 
    local needCostItemCount = needRefreshCount * costItemCount
    local costItemCount = playerHasItemCount > needCostItemCount and needCostItemCount or playerHasItemCount
    local costZuanshiCount = playerHasItemCount > needCostItemCount and 0 or (needCostItemCount - playerHasItemCount) * singleItemCostZuanshiCount
    local useZuanshi = costZuanshiCount ~= 0
    return useItem,costItemCount,useZuanshi,costZuanshiCount
end

--获取盟友任务个数
function _M.GetCurLeagueTaskCount()
    return #m_allianceTasksGen
end

function _M.CheckHasOrangeTask()
    local hasOrangeTask = false
    for i = 1, #tavernSeverData.tasks do
        local taskRarity = game_scheme:SecretTask_0(tavernSeverData.tasks[i].taskID).TaskRarity
        if taskRarity == 3 and tavernSeverData.tasks[i].doneTime == 0 then
            hasOrangeTask = true
            return hasOrangeTask
        end
    end
    return hasOrangeTask
end

--派遣成功后修改数据,刷新界面
function _M.RrefreshSeverData(taskSID,doneTime)
    for i = 1, #tavernSeverData.tasks do
        if tavernSeverData.tasks[i].taskSID == taskSID then
            tavernSeverData.tasks[i].doneTime = doneTime
        end
    end
end

--派遣成功，添加客户端缓存
function _M.AddUsingHeroTab(palsID)
    for k,v in ipairs(palsID) do
        usingHeroTab[v] = true
    end
end

--记录正在派遣中的英雄，登录 & 领取后的数据刷新
function _M.RefreshUsingHeroIDBySever()
    --服务器缓存来了，清空客户端缓存，以服务器的为主
    usingHeroTab = {}
    for i = 1, #tavernSeverData.tasks do
        if tavernSeverData.tasks[i].palsID and #tavernSeverData.tasks[i].palsID > 0 then
            for j = 1, #tavernSeverData.tasks[i].palsID do
                usingHeroTab[tavernSeverData.tasks[i].palsID[j]] = true
            end
        end
    end
end

function SetCurTaskHelpData(taskHelpData)
    
    --region 任务假协助
    local isFirst = false
    local deleteArr = {}
    for i, v in ipairs(taskHelpData) do
        if v.dbid == -1 and not isFirst then
            local alliance_user_data = require "alliance_user_data"
            local alliance_mgr = require "alliance_mgr"
            local memberInfo = alliance_user_data.Get24HourOnlineRandomMember()
            if memberInfo then
                --假协助
                v.name = memberInfo.strName
                v.faceID = memberInfo.faceId
                v.frameID = memberInfo.frameID
                v.allianceShortName = alliance_mgr.GetUserAllianceShortName()
                v.faceStr = memberInfo.faceStr
                v.dbid = memberInfo.roleId
                isFirst = true
            end
        elseif v.dbid == -1 and isFirst then
            table.insert(deleteArr,i)
        end
    end
    
    -- 倒序删除记录的索引
    for i = #deleteArr, 1, -1 do
        table.remove(taskHelpData, deleteArr[i])
    end
    --endregion
    
    m_curTaskHelpData = taskHelpData
end

--获取当前任务奖励展示的协助数据
function _M.GetCurTaskHelpData()
    return m_curTaskHelpData
end

--获得所有已完成待领取的任务组
function _M.GetAllCanReviceTaskTab()
    canReviceTaksTab = {}
    for i = 1, #tavernSeverData.tasks do
        if tavernSeverData.tasks[i].doneTime ~= 0 and tavernSeverData.tasks[i].doneTime <= os.server_time() and not tavernSeverData.tasks[i].rewarded then
            table.insert(canReviceTaksTab,tavernSeverData.tasks[i].taskSID)
        end
    end
    return canReviceTaksTab
end

--获得占用任务个数(正在进行&已完成待领取)
function _M.GetDoingTaskCount()
    local count = 0
    for i = 1, #tavernSeverData.tasks do
        if tavernSeverData.tasks[i].doneTime ~= 0 and not tavernSeverData.tasks[i].rewarded then
            count = count + 1
        end
    end
    return count
end

--检查是否还存在可刷新的非橙色任务,超级刷新专属
function _M.CheckTaskRreshState()
    return #_M.GetNormalPersonalTasksCount() > 0
end

function _M.SetFirstEnterUI()
    isOpenUI = true
end

--      首次登录：显示可派遣 + 可领取 + 可协助
--      点入界面后：只显示可领取
--检测是否打开过界面
function _M.GetMainEnterRedPoint()
    local canDigCount = 0
    local tavern_treasure_data = require "tavern_treasure_data"
    if tavern_treasure_data.GetActOpen() then
        local tavern_treasure_mgr = require "tavern_treasure_mgr"
        canDigCount = tavern_treasure_mgr.GetChipData().canDigCount 
    end
    if not isOpenUI then
        return _M.GetFirstEnterRedPointCount() + canDigCount
    else
        return #_M.GetAllCanReviceTaskTab() + canDigCount
    end
end

function _M.GetFirstEnterRedPointCount()
    local count = 0
    count = count + _M.GetTavernMineRedCount()
    count = count + _M.GetTavernLeagueRedCount()
    return count
end

function _M.GetTavernMineRedCount()
    local tempCount = 0
    if not tavernSeverData.tasks then
        return tempCount
    end
    for i = 1, #tavernSeverData.tasks do
        --可派遣
        if tavernSeverData.tasks[i].doneTime ==0 then
            tempCount = tempCount + 1
            --已完成可领取
        elseif tavernSeverData.tasks[i].doneTime ~= 0 and tavernSeverData.tasks[i].doneTime <= os.server_time() and not tavernSeverData.tasks[i].rewarded then
            tempCount = tempCount + 1
        end
    end
    return tempCount
end

function _M.GetTavernLeagueRedCount()
    local tempCount = 0
    if not tavernSeverData.helpTimes then
        return tempCount
    end
    if not m_allianceTasksGen then
        return tempCount
    end
    for i = 1, #m_allianceTasksGen do
        if _M.GetLeagueTaskState(m_allianceTasksGen[i].task.doneTime) == 1 then
            tempCount = tempCount + 1
        end
    end
    local helpTimeMax,robTimeMax = _M.GetCurStateMax()
    tempCount = math.min(tempCount, helpTimeMax - tavernSeverData.helpTimes)
    return tempCount
end

--发送获取酒馆任务记录请求
function _M.SendGetRecord()
    _M.MSG_ACORNPUB_GET_RECORD_REQ()
end

--发送获取点赞记录请求
function _M.SendLikeRecord() 
    local net_click_liking = require "net_click_liking"
    net_click_liking.MSG_LIKE_GET_RECORD_REQ()
end

--打开酒馆页面时发送请求协议
function _M.SendReq()
    _M.SendGetRecord()
end
---------------------主界面相关 --------------------------

---------------------沙盘派遣界面相关 --------------------------
--task初始化玩家所有英雄的数据，只保留没有被占用的英雄（星级，等级，类型，职业，是否已被任务占用）
function _M.InitAllHeroData()
    initAllHeroData = {}
    --local heroPart = player_mgr.GetNonTrialPalPartData()
    local heroPart = gw_hero_data.GetAllHeroEntity()
    local count = 0
    for _, entity in pairs(heroPart) do
        local sid = entity:GetHeroSid()
        --local isUsed = isUsedHero(sid, entity.heroID)			--是被占用的英雄
        if not usingHeroTab[sid] and entity:GetIsUnLock() then                         --只保留没有占用的英雄
        --if entity:GetIsUnLock() then                         --只保留没有占用的英雄
            local cfg = game_scheme:Hero_0(entity:GetHeroID())
            if cfg then
                count = count + 1
                local data = {
                    lv = entity.serverData.heroLevel,
                    starLv = entity.serverData.heroStarLv,
                    entity = entity,
                    isUsed = false,
                    sid = entity:GetHeroSid(),
                    id = entity:GetHeroID(),
                    power = entity:GetPower(),
                    rarityType = cfg.rarityType
                }
                table.insert(initAllHeroData, data)
            end
        end
    end
    table.sort(initAllHeroData, function(a, b) 	--排序
        if a ~= nil and b ~= nil then
            if a.rarityType ~= b.rarityType then
                return a.rarityType > b.rarityType
            end
            return a.power > b.power
        end
    end)
    return initAllHeroData
end

function _M.GetSelectNum(selectData)
    local count = 0
    for pos, data in pairs(selectData) do
        count = count + 1
    end
    return count
end

function _M.GetTableLength(tbl)
    local count = 0
    for _ in pairs(tbl) do
        count = count + 1
    end
    return count
end

--获取上阵英雄列表
function _M.GetShangZhenTab(taskID)
    local tavernCfg = game_scheme:SecretTask_0(taskID)
    local allHeroListSkin = ui_setting_util.DeepCopy(initAllHeroData)

    -- 计算每个条件要求的个数
    local requiredCounts = {
        level = tavernCfg.HeroLevelRequired.data[1] or 0,
        rarity = tavernCfg.HeroRarityRequired.data[1] or 0,
        starLv = tavernCfg.HeroStarRequired.data[1] or 0
    }

    -- 按品质从低到高排序
    table.sort(allHeroListSkin, function(a, b)
        local heroCfgA = game_scheme:Hero_0(a.id)
        local heroCfgB = game_scheme:Hero_0(b.id)
        if heroCfgA and heroCfgB then
            return heroCfgA.rarityType < heroCfgB.rarityType
        end
        return false
    end) 

    local heroValidList = {} -- 缓存一下英雄是否符合某个条件的值
    -- 判断英雄是否符合某个条件
    local function isHeroValid(hero, condition)
        if heroValidList[hero.sid] and heroValidList[hero.sid][condition] then
            return heroValidList[hero.sid][condition]
        end
        if not heroValidList[hero.sid] then
            heroValidList[hero.sid] = {}
        end
        local heroEntity = gw_hero_data.GetHeroEntity(hero.sid)
        local heroCfg = game_scheme:Hero_0(hero.id)
        local isHave = 0

        if condition == "level" then
            if #tavernCfg.HeroLevelRequired.data > 0 then
                isHave = heroEntity:GetLevel() >= tavernCfg.HeroLevelRequired.data[0] and 1 or 0
            else
                isHave = 1
            end
        elseif condition == "rarity" then
            if #tavernCfg.HeroRarityRequired.data > 0 then
                isHave = (heroCfg.rarityType - 1) >= tavernCfg.HeroRarityRequired.data[0] and 1 or 0
            else
                isHave = 1
            end
        elseif condition == "starLv" then
            if #tavernCfg.HeroStarRequired.data > 0 then
                isHave = heroEntity:GetStar() >= (tavernCfg.HeroStarRequired.data[0] * 5) and 1 or 0
            else
                isHave = 1
            end
        end
        heroValidList[hero.sid][condition] = isHave
        return isHave
    end

    -- 计算英雄满足的条件数
    local function countValidConditions(hero)
        local count = 0
        if requiredCounts.level > 0 and isHeroValid(hero, "level") > 0 then count = count + 1 end
        if requiredCounts.rarity > 0 and isHeroValid(hero, "rarity") > 0 then count = count + 1 end
        if requiredCounts.starLv > 0 and isHeroValid(hero, "starLv") > 0 then count = count + 1 end
        return count
    end

    -- 计算剩余需要满足的条件数
    local function countValidMaxConditions()
        local count = 0
        if requiredCounts.level > 0 then count = count + 1 end
        if requiredCounts.rarity > 0 then count = count + 1 end
        if requiredCounts.starLv > 0 then count = count + 1 end
        return count
    end

    local validHeroes = {}
    while (requiredCounts.level > 0 or requiredCounts.rarity > 0 or requiredCounts.starLv > 0) and
            #validHeroes < tavernCfg.HeroAmountLimit do
        -- 找到符合条件最多的英雄
        local bestHero = nil
        local maxConditions = 0
        local bestHeroIndex = nil
        local maxValidConditions = countValidMaxConditions()

        for i, hero in ipairs(allHeroListSkin) do
            if not hero.isUsed then
                local validCount = countValidConditions(hero)
                if validCount > maxConditions then
                    maxConditions = validCount
                    bestHero = hero
                    bestHeroIndex = i
                end
                if maxConditions >= maxValidConditions then
                    break;
                end
            end
        end

        -- 没有找到合适的英雄
        if not bestHero then
            return nil
        end

        bestHero.isUsed = true
        table.insert(validHeroes, bestHero)

        -- 减少满足的条件计数
        if isHeroValid(bestHero, "level") > 0 and requiredCounts.level > 0 then
            requiredCounts.level = requiredCounts.level - 1
        end
        if isHeroValid(bestHero, "rarity") > 0 and requiredCounts.rarity > 0 then
            requiredCounts.rarity = requiredCounts.rarity - 1
        end
        if isHeroValid(bestHero, "starLv") > 0 and requiredCounts.starLv > 0 then
            requiredCounts.starLv = requiredCounts.starLv - 1
        end
    end

    -- 同步设置initAllHeroData中对应英雄的isUsed状态
    local function setHeroUsed(hero)
        -- 在initAllHeroData中找到对应的英雄并设置isUsed
        for _, origHero in ipairs(initAllHeroData) do
            if origHero.sid == hero.sid then
                origHero.isUsed = true
                break
            end
        end
    end

    -- 验证是否所有条件都满足
    if requiredCounts.level == 0 and requiredCounts.rarity == 0 and requiredCounts.starLv == 0 then
        local len = #validHeroes
        for i = 1, len do
            setHeroUsed(validHeroes[i])
        end
        -- 按品质从高到低排序
        table.sort(validHeroes, function(a, b)
            local heroCfgA = game_scheme:Hero_0(a.id)
            local heroCfgB = game_scheme:Hero_0(b.id)
            if heroCfgA and heroCfgB then
                return heroCfgA.rarityType > heroCfgB.rarityType
            end
            return false
        end) 
        return validHeroes
    end

    return nil -- 未满足所有条件，返回空表
end

--同步上阵的英雄数据
function _M.SetHeroDatas(selectdata)
    m_selectHeroData = selectdata
end

--存储沙盘实体面板是否已全部满足上阵条件
function _M.SetIsAllCond(isAllCond)
    m_disPatchBool = isAllCond
end

function _M.GetIsAllCond()
    return m_disPatchBool
end

function _M.SetCurTaskHeroCount(heroCount)
    m_curTaskHeroCount = heroCount
end

function _M.GetCurTaskHeroCount()
    return m_curTaskHeroCount
end

function _M.GetHeroDatas()
    return m_selectHeroData
end

--获取酒馆数据
function _M.GetTavernSeverData()
    return tavernSeverData
end

---@public 获取酒馆任务可以刷新的数量
function _M.CanTavernSeverCount()
    if tavernSeverData then
        for i, v in ipairs(tavernSeverData.tasks) do
            local state = _M.GetpersonalTaskState(v)
            if state == 1 then
                return true
            end
        end
    end
    return false
end

--获取酒馆个人任务列表
function _M.GetPersonalTasks()
    return tavernSeverData and tavernSeverData.tasks
end

--获取当前协助次数是否已用完
function _M.GetTavernCanHelp()
    local helpTimeMax, robTimeMax = _M.GetCurStateMax()
    return tavernSeverData.helpTimes < helpTimeMax
end

--获取当前掠夺次数是否已用完
function _M.GetTavernCanSteal()
    local helpTimeMax, robTimeMax = _M.GetCurStateMax()
    return tavernSeverData.robTimes < robTimeMax
end

--获取所有非橙色的普通任务 
--return normalRefreshTasksTab 任务列表
function _M.GetNormalPersonalTasksCount()
    normalRefreshTasksTab = {}
    for i = 1, #tavernSeverData.tasks do
        local taskRarity = game_scheme:SecretTask_0(tavernSeverData.tasks[i].taskID).TaskRarity
        if taskRarity < 3 and tavernSeverData.tasks[i].doneTime == 0 then
            table.insert(normalRefreshTasksTab,tavernSeverData.tasks[i].taskSID)
        end
    end
    return normalRefreshTasksTab
end

function _M.GetNormalPersonalTaskSIDTab()
    return normalRefreshTasksTab
end


--获取酒馆同盟任务列表
function _M.GetTavernAlliacneTaskS()
    return m_allianceTasksGen
end

function GenLeagueTasksData()
    m_allianceTasksGen = {}
    for i = 1, #tavernSeverData.allianceTasks do
        for j = 1, #tavernSeverData.allianceTasks[i].tasks do
            table.insert(m_allianceTasksGen,{dbid = tavernSeverData.allianceTasks[i].dbid,
                                            name = tavernSeverData.allianceTasks[i].name,
                                            faceID = tavernSeverData.allianceTasks[i].faceID,
                                            frameID = tavernSeverData.allianceTasks[i].frameID,
                                            task = tavernSeverData.allianceTasks[i].tasks[j],
                                            faceStr = tavernSeverData.allianceTasks[i].faceStr,
            })
        end
    end
end
---------------------沙盘派遣界面相关 --------------------------

---------------------沙盘实体相关 --------------------------
--可被掠夺的任务，需要在5级缩放层级显示（不可被掠夺的任务不会显示）
--他人任务 + 非同盟 + 已完成
function _M.GetMiniIcon1Visible(roleId,unionID,tavernDoneTime)
    return not _M.CheckIsSelfTask(roleId) and not _M.CheckIsSameUnionTask(unionID) and _M.CheckTaskIsFinsh(tavernDoneTime)
end

--是否自己任务
function _M.CheckIsSelfTask(roleId)
    return roleId == player_mgr.GetPlayerRoleID()
end

--是否同盟任务
function _M.CheckIsSameUnionTask(unionID)
    local alliance_mgr = require "alliance_mgr"
    local allianceID = alliance_mgr.GetUserAllianceId()
    local isSameAllicance = false
    --是否非同盟
    if unionID == 0 and allianceID == 0 then
        isSameAllicance = false
    else
        isSameAllicance = unionID == allianceID
    end
    return isSameAllicance
end

--判断是否已经偷过
function _M.CheckEntityIsSteal(tavernStealPlayers)
    --判断自己有没有偷过
    local selfSteal = string.find(tavernStealPlayers, tostring(player_mgr.GetPlayerRoleID()))
    return selfSteal ~= nil
end

--是否已完成
function _M.CheckTaskIsFinsh(tavernDoneTime)
    return tavernDoneTime > 0 and tavernDoneTime - os.server_time() < 0 or false
end

--这里抽离出来，hud没有次数时候是不显示，详情面板操作按钮是置灰
function _M.CheckEntityCanStealCount(tavernStealPlayers,taskCfgId)
    if taskCfgId == 0 then --服务器数据异常
        return false
    end
    return _M.GetStealPlayersCount(tavernStealPlayers) < game_scheme:SecretTask_0(taskCfgId).SnatchLimit
end

function _M.GetEntityCanStealCount(tavernStealPlayers,taskCfgId)
    if taskCfgId == 0 then -- 没有数据，直接返回0，0容错处理
        return 0,0
    end
    return game_scheme:SecretTask_0(taskCfgId).SnatchLimit - _M.GetStealPlayersCount(tavernStealPlayers),game_scheme:SecretTask_0(taskCfgId).SnatchLimit
end

function _M.GetStealPlayersCount(str)
    -- 去掉字符串两端的 { 和 }
    local cleanedStr = str:match("^{(.*)}$")
    if not cleanedStr then
        return 0 -- 如果格式不正确，返回 0
    end

    -- 如果字符串为空，直接返回 0
    if cleanedStr == "" then
        return 0
    end

    -- 使用 gmatch 按逗号分隔并计数
    local count = 0
    for _ in cleanedStr:gmatch("[^,]+") do
        count = count + 1
    end

    return count
end

---------------------沙盘实体相关 --------------------------

--------------------任务排序相关 ---------------------------
-- 假设 tavernSeverData 是包含 TAcronPubTaskItem 数据的表

-- 定义任务状态的优先级（数字越小优先级越高）
local taskStatePriority = {
    [2] = 1, -- 可领奖任务
    [1] = 2, -- 未完成任务
    [4] = 3, -- 进行中的任务
    [3] = 4, -- 完美完成的任务
    [5] = 5, -- 被偷窃任务
}

-- 排序规则函数
local function SortPersonalTasks(a, b)
    -- 获取任务状态
    local stateA = _M.GetpersonalTaskState(a)
    local stateB = _M.GetpersonalTaskState(b)

    -- 按任务状态优先级排序
    if taskStatePriority[stateA] ~= taskStatePriority[stateB] then
        return taskStatePriority[stateA] < taskStatePriority[stateB]
    end

    -- 获取任务星级和品质
    local cfgA = game_scheme:SecretTask_0(a.taskID)
    local cfgB = game_scheme:SecretTask_0(b.taskID)

    -- 按任务星级排序（高星级优先）
    if cfgA.TaskLevel ~= cfgB.TaskLevel then
        return cfgA.TaskLevel > cfgB.TaskLevel
    end

    -- 按任务品质排序（高品质优先）
    if cfgA.TaskRarity ~= cfgB.TaskRarity then
        return cfgA.TaskRarity > cfgB.TaskRarity
    end

    -- 按任务ID排序（大任务ID优先）
    return a.taskID > b.taskID
end

local function SortLeagueTasks(a, b)
    -- 获取任务状态
    local stateA = _M.GetLeagueTaskState(a.task.doneTime)
    local stateB = _M.GetLeagueTaskState(b.task.doneTime)

    -- 按任务状态优先级排序
    if stateA ~= stateB then
        return stateA < stateB
    end

    -- 获取任务星级和品质
    local cfgA = game_scheme:SecretTask_0(a.task.taskID)
    local cfgB = game_scheme:SecretTask_0(b.task.taskID)

    -- 按任务星级排序（高星级优先）
    if cfgA.TaskLevel ~= cfgB.TaskLevel then
        return cfgA.TaskLevel > cfgB.TaskLevel
    end

    -- 按任务品质排序（高品质优先）
    if cfgA.TaskRarity ~= cfgB.TaskRarity then
        return cfgA.TaskRarity > cfgB.TaskRarity
    end

    -- 按任务ID排序（大任务ID优先）
    return a.task.taskID > b.task.taskID
end


--------------------任务排序相关 ---------------------------

---------------------网络层-------------------------------
--获取酒馆信息请求
function _M.MSG_ACORNPUB_GET_INFO_REQ()
    local msg = acornpub_pb.TMSG_ACORNPUB_GET_INFO_REQ()
    local roleWorldID = gw_common_util.GetSandZoneSandBoxSid()
    TransmitLuaFuncReq(xManMsg_pb.MSG_ACORNPUB_GET_INFO_REQ, msg, lua_pb.MicroService_SandBox,nil,nil,roleWorldID)
    --net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ACORNPUB_GET_INFO_REQ, msg)
end


function CompareTavernData(oldData, newData)
    -- 如果旧数据为空，则认为有差异
    if not oldData then
        return true
    end

    -- 比较基本字段
    if oldData.helpTimes ~= newData.helpTimes or
            oldData.robTimes ~= newData.robTimes then
        return true
    end

    -- 比较任务数量
    if #oldData.tasks ~= #newData.tasks then
        return true
    end

    -- 比较同盟任务数量
    if #oldData.allianceTasks ~= #newData.allianceTasks then
        return true
    end

    -- 比较每个任务的详细信息
    for i, newTask in ipairs(newData.tasks) do
        local found = false
        for j, oldTask in ipairs(oldData.tasks) do
            if oldTask.taskSID == newTask.taskSID then
                found = true
                -- 比较任务状态和详情
                if oldTask.taskID ~= newTask.taskID or
                        oldTask.doneTime ~= newTask.doneTime or
                        oldTask.rewarded ~= newTask.rewarded then
                    return true
                end

                -- 比较派遣的英雄
                if #oldTask.palsID ~= #newTask.palsID then
                    return true
                end

                for k, newPalID in ipairs(newTask.palsID) do
                    local palFound = false
                    for l, oldPalID in ipairs(oldTask.palsID) do
                        if oldPalID == newPalID then
                            palFound = true
                            break
                        end
                    end
                    if not palFound then
                        return true
                    end
                end

                break
            end
        end
        if not found then
            return true
        end
    end

    -- 比较同盟任务
    for i, newTask in ipairs(newData.allianceTasks) do
        local found = false
        for j, oldTask in ipairs(oldData.allianceTasks) do
            if oldTask.dbid == newTask.dbid then
                found = true
                -- 比较任务数量
                if #oldTask.tasks ~= #newTask.tasks then
                    return true
                end
                -- 比较任务状态和详情
                for k, newSubTask in ipairs(newTask.tasks) do
                    if oldTask.tasks[k].taskSID ~= newSubTask.taskSID or  oldTask.tasks[k].doneTime ~= newSubTask.doneTime then
                        return true
                    end
                end
                break
            end
        end
        if not found then
            return true
        end
    end

    -- 如果所有比较都通过，说明没有差异
    return false
end

--获取酒馆信息回复
function MSG_ACORNPUB_GET_INFO_RSP(msg)
    if msg.errCode and msg.errCode ~= 0 then
        local flow_text = require 'flow_text'
        flow_text.Add(lang.Get(100000 + msg.errCode))
        return
    end
    --BuildSeverData(msg.data)
    -- 比较数据，只有在有差异时才更新
    local hasChanges = CompareTavernData(tavernSeverData, msg.data)
    if hasChanges then
        tavernSeverData = msg.data
        -- 对数据进行排序
        table.sort(tavernSeverData.tasks, SortPersonalTasks)
        GenLeagueTasksData()
        table.sort(m_allianceTasksGen, SortLeagueTasks)
        _M.RefreshUsingHeroIDBySever()
        event.Trigger(event_define.MSG_ACORNPUB_GET_INFO_RSP)
    end
   
    if not initRedPoint then
        red_system.TriggerRed(red_const.Enum.TavernTask)
        initRedPoint = true
    end    
end

-- 派遣任务请求
function _M.MSG_ACORNPUB_SENDTASK_REQ(taskSID,palsID,superSend)
    local msg = acornpub_pb.TMSG_ACORNPUB_SENDTASK_REQ()
    msg.taskSID = taskSID
    for i, v in ipairs(palsID) do
        table.insert(msg.palsID, v.sid)
    end
    msg.superSend = superSend
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ACORNPUB_SENDTASK_REQ, msg)
    --酒馆开始任务打点
    event.EventReport("SecretMission_Start",
            {
                sever_days = time_util.GetTimeOfZero(os.server_zone(), player_mgr:GetRoleOpenSvrTime()) - 86400,
                Mission_quality = game_scheme:SecretTask_0(_M.GetCfgIDbySID(taskSID)).TaskRarity,
                Mission_star = game_scheme:SecretTask_0(_M.GetCfgIDbySID(taskSID)).TaskLevel,
            })
end

--派遣任务回复
function MSG_ACORNPUB_SENDTASK_RSP(msg)
    if msg.errCode and msg.errCode ~= 0 then
        local flow_text = require 'flow_text'
        flow_text.Add(lang.Get(100000 + msg.errCode))
        return
    end
    if ui_window_mgr:IsModuleShown("ui_tavern_dispatch_tip") then
        ui_window_mgr:UnloadModule("ui_tavern_dispatch_tip")
    end

    if ui_window_mgr:IsModuleShown("ui_tavern_super_go") then
        ui_window_mgr:UnloadModule("ui_tavern_super_go")
    end
    flow_text.Add(lang.Get(668063))
    _M.RrefreshSeverData(msg.taskSID,msg.doneTime)
    _M.AddUsingHeroTab(msg.palsID)
    event.Trigger(event_define.MSG_ACORNPUB_GET_INFO_RSP)
    
    --region 酒馆派遣飘字
    --local heroData = {}
    --for i, v in ipairs(msg.palsID) do
    --    if i > 3 then
    --        break
    --    end
    --    local tempData = {
    --       heroID = v,
    --    }
    --    table.insert(heroData, tempData)
    --end
    --local flow_tavern_hero_tip = require "flow_tavern_hero_tip"
    --flow_tavern_hero_tip.Add(heroData)
    --endregion
    
end

--掠夺任务请求
function _M.MSG_ACORNPUB_ROBTASK_REQ(roleId, taskSID, worldID, taskID, taskEntitySID)
    local msg = acornpub_pb.TMSG_ACORNPUB_ROBTASK_REQ()
    msg.dbid = roleId
    msg.taskSID = taskSID
    msg.worldID = worldID
    msg.taskEntitySID = taskEntitySID
    TransmitLuaFuncReq(xManMsg_pb.MSG_ACORNPUB_ROBTASK_REQ, msg, lua_pb.MicroService_SandBox,nil,nil,gw_common_util.GetSandRoleSandBoxSid())
    --酒馆掠夺他人任务打点
    event.EventReport("SecretMission_Loot", {
        sever_days = time_util.GetTimeOfZero(os.server_zone(), player_mgr:GetRoleOpenSvrTime()) - 86400,
        Mission_quality = game_scheme:SecretTask_0(taskID).TaskRarity,
        Mission_star = game_scheme:SecretTask_0(taskID).TaskLevel,
        Role_ID = roleId,
        role_ID = player_mgr.GetPlayerRoleID()
    })
end

--掠夺任务回复
function MSG_ACORNPUB_ROBTASK_RSP(msg)
    if msg.errCode and msg.errCode ~= 0 then
        local flow_text = require 'flow_text'
        flow_text.Add(lang.Get(100000 + msg.errCode))
        return
    end
    tavernSeverData.robTimes = msg.robTimes
    local rewardID = game_scheme:SecretTask_0(msg.taskID).SnatchRewards
    if rewardID then
        --弹出恭喜获得弹窗
        local rewardData = reward_mgr.GetRewardGoodsList(rewardID)

        local showData = {
            [1] = {
                dataList = rewardData
            }
        }

        local ui_reward_result = require "ui_reward_result_new"
        ui_reward_result.SetInputParam(showData)			--设置数据
        ui_window_mgr:ShowModule("ui_reward_result_new")
    end
    event.Trigger(event_define.MSG_REFRESH_SANDHUD)
    --robTimes
end

--协助任务请求
function _M.MSG_ACORNPUB_HELPTASK_REQ(dbid,taskSID)
    local msg = acornpub_pb.TMSG_ACORNPUB_HELPTASK_REQ()
    msg.dbid = dbid
    msg.taskSID = taskSID
    TransmitLuaFuncReq(xManMsg_pb.MSG_ACORNPUB_HELPTASK_REQ, msg, lua_pb.MicroService_SandBox,nil,nil,gw_common_util.GetSandRoleSandBoxSid())
end

--协助任务回复
function MSG_ACORNPUB_HELPTASK_RSP(msg)
    if msg.errCode and msg.errCode ~= 0 then
        local flow_text = require 'flow_text'
        flow_text.Add(lang.Get(100000 + msg.errCode))
        return
    end
    tavernSeverData.helpTimes = msg.helpTimes
    local cfgIndex = _M.RefreshLeagueData(msg.dbid,msg.taskSID)
    local rewardID = game_scheme:SecretTask_0(cfgIndex).AidRewards
    if rewardID then
        --弹出恭喜获得弹窗
        local rewardData = reward_mgr.GetRewardGoodsList(rewardID)

        local showData = {
            [1] = {
                dataList = rewardData
            }
        }

        local ui_reward_result = require "ui_reward_result_new"
        ui_reward_result.SetInputParam(showData)			--设置数据
        ui_window_mgr:ShowModule("ui_reward_result_new")
    end
    event.Trigger(event_define.MSG_ACORNPUB_GET_INFO_RSP)
    event.Trigger(event_define.MSG_REFRESH_SANDHUD)

    --酒馆协助任务打点
    event.EventReport("SecretMission_Assist", {
        sever_days = time_util.GetTimeOfZero(os.server_zone(), player_mgr:GetRoleOpenSvrTime()) - 86400,
        Mission_quality = game_scheme:SecretTask_0(cfgIndex).TaskRarity,
        Mission_star = game_scheme:SecretTask_0(cfgIndex).TaskLevel,
    })
    --dbid
end

--刷新任务请求
function _M.MSG_ACORNPUB_REFRESHTASK_REQ()
    local msg = acornpub_pb.TMSG_ACORNPUB_REFRESHTASK_REQ()
    msg.useItem = m_useItem
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ACORNPUB_REFRESHTASK_REQ, msg)
end

--刷新任务回复
function MSG_ACORNPUB_REFRESHTASK_RSP(msg)
    if msg.errCode and msg.errCode ~= 0 then
        local flow_text = require 'flow_text'
        local item_cfg = game_scheme:Item_0(2)
        flow_text.Add(string.format(lang.Get(100000 + msg.errCode),lang.Get(item_cfg.nameKey)))
        return
    end
    event.Trigger(event_define.MSG_ACORNPUB_REFRESHTASK_RSP)
    for k,v in ipairs(msg.taskids) do
        --酒馆手动刷新打点
        event.EventReport("SecretMission_ManuallyRefresh", {
            sever_days = time_util.GetTimeOfZero(os.server_zone(), player_mgr:GetRoleOpenSvrTime()) - 86400,
            cost_item = m_useItem,
            cost_diamond = not m_useItem,
            Mission_quality = game_scheme:SecretTask_0(v).TaskRarity
        })
    end
end

--超级刷新任务请求
function _M.MSG_ACORNPUB_SUPERREFRESHTASK_REQ(costItemCount,costZuanshiCount)
    local msg = acornpub_pb.TMSG_ACORNPUB_SUPERREFRESHTASK_REQ()
    msg.itemCount = costItemCount
    msg.diamondCount = costZuanshiCount
    for k, v in ipairs(_M.GetNormalPersonalTasksCount()) do
        table.insert(msg.taskSID,v)
    end
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ACORNPUB_SUPERREFRESHTASK_REQ, msg)
    --酒馆超级刷新打点
    event.EventReport("SecretMission_SuperRefresh", {
        sever_days = time_util.GetTimeOfZero(os.server_zone(), player_mgr:GetRoleOpenSvrTime()) - 86400,
        cost_item_num = costItemCount,
        cost_diamond_num = costZuanshiCount,
    })
end

--超级刷新任务回复
function MSG_ACORNPUB_SUPERREFRESHTASK_RSP(msg)
    if msg.errCode and msg.errCode ~= 0 then
        local flow_text = require 'flow_text'
        flow_text.Add(lang.Get(100000 + msg.errCode))
        return
    end
    if ui_window_mgr:IsModuleShown("ui_tavern_super_refresh") then
        ui_window_mgr:UnloadModule("ui_tavern_super_refresh")
        flow_text.Add(lang.Get(668066))
    end
    event.Trigger(event_define.MSG_ACORNPUB_SUPERREFRESHTASK_RSP)
end

--批量领取任务奖励请求
function _M.MSG_ACORNPUB_BATCHGETREWARD_REQ(sandEnter,tasksSID)
    local msg = acornpub_pb.TMSG_ACORNPUB_BATCHGETREWARD_REQ()
    for k,v in ipairs( sandEnter and tasksSID or _M.GetAllCanReviceTaskTab()) do
        table.insert(msg.tasksSID,v)
        --酒馆收取任务打点
        event.EventReport("SecretMission_Finish", {
            sever_days = time_util.GetTimeOfZero(os.server_zone(), player_mgr:GetRoleOpenSvrTime()) - 86400,
            Mission_star = game_scheme:SecretTask_0(_M.GetCfgIDbySID(v)).TaskLevel,
            Mission_quality = game_scheme:SecretTask_0(_M.GetCfgIDbySID(v)).TaskRarity,
        })
    end
    local force_guide_event = require "force_guide_event"
    local force_guide_system = require "force_guide_system"
    force_guide_system.TriComEvent(force_guide_event.cEventTavernRewardClick)
    TransmitLuaFuncReq(xManMsg_pb.MSG_ACORNPUB_BATCHGETREWARD_REQ, msg, lua_pb.MicroService_SandBox,nil,nil,gw_common_util.GetSandRoleSandBoxSid())
end

--批量领取任务奖励回复
function MSG_ACORNPUB_BATCHGETREWARD_RSP(msg)
    if msg.errCode and msg.errCode ~= 0 then
        local flow_text = require 'flow_text'
        flow_text.Add(lang.Get(100000 + msg.errCode))
        return
    end
    local rewardData = {}
    local item_data = require "item_data"
    for i = 1, #msg.items do
        table.insert(rewardData,{nType = item_data.Reward_Type_Enum.Item,
            id = msg.items[i].itemID, num = msg.items[i].count,})
    end
    local showData = {
        [1] = {
            dataList = rewardData
        }
    }
    local closeFunc = function ()
        local force_guide_event = require "force_guide_event"
        local force_guide_system = require "force_guide_system"
        force_guide_system.TriEnterEvent(force_guide_event.tEventTavernRewardClick)
    end
    local ui_reward_result = require "ui_reward_result_new"
    local isTavernTask = true and #msg.helps > 0
    ui_reward_result.SetInputParam(showData,closeFunc,nil,nil,nil,nil,nil,nil,nil,nil,nil,isTavernTask)			--设置数据
    ui_window_mgr:ShowModule("ui_reward_result_new")
    
    SetCurTaskHelpData(msg.helps)
    event.Trigger(event_define.MSG_ACORNPUB_BATCHGETREWARD_RSP)
    --items
    --helps
end

--酒馆信息推送（比如跨天了一些数据的主动推送）
function MSG_ACORNPUB_INFO_NTF(msg)
    if msg.errCode and msg.errCode ~= 0 then
        local flow_text = require 'flow_text'
        flow_text.Add(lang.Get(100000 + msg.errCode))
        return
    end
    tavernSeverData = msg.data
    -- 对数据进行排序
    table.sort(tavernSeverData.tasks, SortPersonalTasks)
    GenLeagueTasksData()
    table.sort(m_allianceTasksGen, SortLeagueTasks)
    _M.RefreshUsingHeroIDBySever()
    event.Trigger(event_define.MSG_ACORNPUB_GET_INFO_RSP)
end

--获取酒馆任务记录请求
function _M.MSG_ACORNPUB_GET_RECORD_REQ(isClickBtn)
    local msg = acornpub_pb.TMSG_ACORNPUB_GET_RECORD_REQ()
    if isClickBtn then 
        isClickShowHistory = isClickBtn
    end
    TransmitLuaFuncReq(xManMsg_pb.MSG_ACORNPUB_GET_RECORD_REQ, msg, lua_pb.MicroService_SandBox,nil,nil,gw_common_util.GetSandRoleSandBoxSid())
end

--获取酒馆任务记录回复
function MSG_ACORNPUB_GET_RECORD_RSP(msg)
    if msg.errCode and msg.errCode ~= 0 then
        local flow_text = require 'flow_text'
        flow_text.Add(lang.Get(100000 + msg.errCode))
        return
    end
    -- log.Warning("MSG_ACORNPUB_GET_RECORD_RSP",isClickShowHistory)
    -- dump(msg.recordItems)
    if isClickShowHistory then 
        isClickShowHistory = false
        ui_window_mgr:ShowModule("ui_tavern_task_new",nil,nil,{recordData = msg.recordItems,isRobRes = false})
    else
        -- log.Warning("MSG_OPEN_ACORNPUB_HELP 5555555")
        event.Trigger(event_define.MSG_OPEN_ACORNPUB_HELP,msg.recordItems)
    end
end

--派遣任务前置沙盘操作请求(主界面中点击前往按钮)
function _M.MSG_ACORNPUB_SENDTASK_PRE_REQ(taskSID)
    local msg = acornpub_pb.TMSG_ACORNPUB_SENDTASK_PRE_REQ()
    msg.taskSID = taskSID
    TransmitLuaFuncReq(xManMsg_pb.MSG_ACORNPUB_SENDTASK_PRE_REQ, msg, lua_pb.MicroService_SandBox,nil,nil,gw_common_util.GetSandRoleSandBoxSid())
end

--派遣任务前置沙盘操作回复
function MSG_ACORNPUB_SENDTASK_PRE_RSP(msg)
    if msg.errCode and msg.errCode ~= 0 then
        local flow_text = require 'flow_text'
        flow_text.Add(lang.Get(100000 + msg.errCode))
        return
    end
    --event.Trigger(event_define.MSG_ACORNPUB_BATCHGETREWARD_RSP)
    --taskSID
    --sandboxSid
    --pos
    if msg.pos then
        local GWCommonUtil = require "gw_common_util"
        GWCommonUtil.JumpToGrid(msg.pos)
        --添加箭头提示
        local worldPos = GWG.GWSandMgr.GetPosByGrid(msg.pos)
        local common_tip_util = require "common_tip_util"

        local parent = GWAdmin.SandSceneUtil.GetTransformByName(GWConst.ESandOrderKey.Move1)
        common_tip_util.AddArrowTip(worldPos, 5, parent)
        ui_window_mgr:UnloadModule("ui_tavern_base")
        if GWG.GWMgr.curScene == GWConst.ESceneType.Home then
            GWG.GWMgr.AddSandSceneCallBack(function()
                ui_window_mgr:ShowModule("ui_tavern_dispatch_tip",nil,nil,{cfgIndex = _M.GetCfgIDbySID(msg.taskSID),taskSID = msg.taskSID})
            end)
        else
            ui_window_mgr:ShowModule("ui_tavern_dispatch_tip",nil,nil,{cfgIndex = _M.GetCfgIDbySID(msg.taskSID),taskSID = msg.taskSID})
        end
     
        --event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
    end
end

--请求掠夺任务记录
function _M.MSG_ACORNPUB_GET_TASK_ROB_RECORD_REQ(taskSID)
    local msg = acornpub_pb.TMSG_ACORNPUB_GET_TASK_ROB_RECORD_REQ()
    msg.taskSID = taskSID
    TransmitLuaFuncReq(xManMsg_pb.MSG_ACORNPUB_GET_TASK_ROB_RECORD_REQ, msg, lua_pb.MicroService_SandBox,nil,nil,gw_common_util.GetSandRoleSandBoxSid())
end

--请求掠夺任务记录回包
function MSG_ACORNPUB_GET_TASK_ROB_RECORD_RSP(msg)
    if msg.errCode and msg.errCode ~= 0 then
        local flow_text = require 'flow_text'
        flow_text.Add(lang.Get(100000 + msg.errCode))
        return
    end
    ui_window_mgr:ShowModule("ui_tavern_task_new",nil,nil,{recordData = msg.records,isRobRes = true,taskID = msg.taskID})
end

--感谢协助请求 点击感谢按钮 or 点击空白处返回 
function _M.MSG_ACORNPUB_HELP_RECORD_LIKED_REQ(data)
    local msg = acornpub_pb.TMSG_ACORNPUB_HELP_RECORD_LIKED_REQ()
    msg.doLike = data
    TransmitLuaFuncReq(xManMsg_pb.MSG_ACORNPUB_HELP_RECORD_LIKED_REQ, msg, lua_pb.MicroService_SandBox,nil,nil,gw_common_util.GetSandRoleSandBoxSid())
end
--感谢协助响应
function MSG_ACORNPUB_HELP_RECORD_LIKED_RSP(msg)
    if msg.errCode and msg.errCode ~= 0 then 
        local flow_text = require 'flow_text'
        flow_text.Add(lang.Get(100000 + msg.errCode))
        return
    end
end
---------------------网络层-------------------------------
function _M.OnLogout()
    isOpenUI = false
    initRedPoint = false
    cache_curLevel = nil
    tavernSeverData = {}
end

function RefreshEnterRedPoint()
    if not initRedPoint then
        _M.MSG_ACORNPUB_GET_INFO_REQ()
    end
end

function _M.Init()
    red_system.RegisterRedFunc(red_const.Enum.TavernTask, _M.GetMainEnterRedPoint)
    --event.Register(event.GW_SCENE_CHANGE_SUCCESS, RefreshEnterRedPoint)
    event.Register(gw_sand_event_define.GW_SAND_INIT_SELF_DATA_FINISH, RefreshEnterRedPoint)
    event.Register(event.ACCOUNT_LOGOUT, _M.OnLogout)
end

local MessageTable = {
    {xManMsg_pb.MSG_ACORNPUB_GET_INFO_RSP, MSG_ACORNPUB_GET_INFO_RSP, acornpub_pb.TMSG_ACORNPUB_GET_INFO_RSP},
    {xManMsg_pb.MSG_ACORNPUB_SENDTASK_RSP, MSG_ACORNPUB_SENDTASK_RSP, acornpub_pb.TMSG_ACORNPUB_SENDTASK_RSP},
    {xManMsg_pb.MSG_ACORNPUB_ROBTASK_RSP, MSG_ACORNPUB_ROBTASK_RSP, acornpub_pb.TMSG_ACORNPUB_ROBTASK_RSP},
    {xManMsg_pb.MSG_ACORNPUB_HELPTASK_RSP, MSG_ACORNPUB_HELPTASK_RSP, acornpub_pb.TMSG_ACORNPUB_HELPTASK_RSP},
    {xManMsg_pb.MSG_ACORNPUB_REFRESHTASK_RSP, MSG_ACORNPUB_REFRESHTASK_RSP, acornpub_pb.TMSG_ACORNPUB_REFRESHTASK_RSP},
    {xManMsg_pb.MSG_ACORNPUB_SUPERREFRESHTASK_RSP, MSG_ACORNPUB_SUPERREFRESHTASK_RSP, acornpub_pb.TMSG_ACORNPUB_SUPERREFRESHTASK_RSP},
    {xManMsg_pb.MSG_ACORNPUB_BATCHGETREWARD_RSP, MSG_ACORNPUB_BATCHGETREWARD_RSP, acornpub_pb.TMSG_ACORNPUB_BATCHGETREWARD_RSP},
    {xManMsg_pb.MSG_ACORNPUB_INFO_NTF, MSG_ACORNPUB_INFO_NTF, acornpub_pb.TMSG_ACORNPUB_INFO_NTF},
    {xManMsg_pb.MSG_ACORNPUB_GET_RECORD_RSP, MSG_ACORNPUB_GET_RECORD_RSP, acornpub_pb.TMSG_ACORNPUB_GET_RECORD_RSP},
    {xManMsg_pb.MSG_ACORNPUB_SENDTASK_PRE_RSP, MSG_ACORNPUB_SENDTASK_PRE_RSP, acornpub_pb.TMSG_ACORNPUB_SENDTASK_PRE_RSP},
    {xManMsg_pb.MSG_ACORNPUB_GET_TASK_ROB_RECORD_RSP, MSG_ACORNPUB_GET_TASK_ROB_RECORD_RSP, acornpub_pb.TMSG_ACORNPUB_GET_TASK_ROB_RECORD_RSP},
    {xManMsg_pb.MSG_ACORNPUB_HELP_RECORD_LIKED_RSP,MSG_ACORNPUB_HELP_RECORD_LIKED_RSP,acornpub_pb.TMSG_ACORNPUB_HELP_RECORD_LIKED_RSP},

}

net_route.RegisterMsgHandlers(MessageTable)

return _M

