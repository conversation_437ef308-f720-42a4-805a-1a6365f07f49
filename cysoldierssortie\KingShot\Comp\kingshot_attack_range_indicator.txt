---@class kingshot_attack_range_indicator
local indicator = bc_Class("kingshot_attack_range_indicator")
local typeof = typeof
local Disc = CS.Shapes.Disc
local Ring = CS.Shapes.DiscType.Ring
local Basic = CS.Shapes.DashType.Basic

indicator.transform = nil
indicator.discComponent = nil
indicator.gameObject = nil
indicator.isVisible = nil
indicator.currentRadius = nil
indicator.targetTransform = nil

local KingShot_Define = require "kingshot_define"

function indicator:__init(...)
    self.transform, self.targetTransform = ...
    self.gameObject = self.transform.gameObject
    self.isVisible = false
    self.currentRadius = 0
    
    -- 获取Shapes Disc组件
    --self.discComponent = self.transform:GetComponent(typeof(Disc))
    --urp不给用shape库 想下别的办法搞
    if self.discComponent then
        self:InitializeDisc()
    else
        print("Warning: AttackRangeIndicator prefab missing Shapes.Disc component!")
    end
end

function indicator:InitializeDisc()
    local params = KingShot_Define.Params.AttackRangeIndicator
    
    -- 设置基本属性
    --self.discComponent.type = Ring  -- 环形（只显示边框）
    self.discComponent.Radius = 0
    self.discComponent.Thickness = params.LineThickness
    
    -- 设置虚线样式
    self.discComponent.Dashed = true
    --self.discComponent.dashStyle.type = Basic
    --self.discComponent.dashStyle.size = params.DashLength
    --self.discComponent.dashStyle.spacing = params.DashSpacing
    
    -- 设置颜色
    local color = params.DefaultColor
    self.discComponent.Color = KingShot_Define.CS.Color(color.r, color.g, color.b, color.a)
    
    -- 设置渲染层级
    self.discComponent.SortingOrder = params.SortingOrder
    
    -- 初始隐藏
    self.gameObject:SetActive(false)
end

function indicator:SetRadius(radius)
    if self.currentRadius ~= radius then
        self.currentRadius = radius
        if self.discComponent then
            self.discComponent.Radius = radius
        end
    end
end

function indicator:SetVisible(visible)
    if self.isVisible ~= visible then
        self.isVisible = visible
        self.gameObject:SetActive(visible)
    end
end

function indicator:SetColor(color)
    if self.discComponent then
        self.discComponent.Color = color
    end
end

function indicator:SetPosition(position)
    if self.transform then
        -- 设置位置，稍微抬高一点避免Z-fighting
        local heightOffset = KingShot_Define.Params.AttackRangeIndicator.HeightOffset
        local newPos = KingShot_Define.CS.Vector3(position.x, position.y + heightOffset, position.z)
        self.transform.position = newPos
    end
end

function indicator:Update()
    -- 如果有目标Transform，跟随目标移动
    if self.targetTransform and self.isVisible then
        self:SetPosition(self.targetTransform.position)
    end
end

function indicator:SetTarget(targetTransform)
    self.targetTransform = targetTransform
end

function indicator:GetRadius()
    return self.currentRadius
end

function indicator:IsVisible()
    return self.isVisible
end

function indicator:__delete()
    if self.gameObject then
        KingShot_Define.CS.GameObject.Destroy(self.gameObject)
    end
end

return indicator
