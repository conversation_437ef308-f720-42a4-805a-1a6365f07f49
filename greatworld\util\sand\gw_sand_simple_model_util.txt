---
--- Created by: yuannan.
--- DateTime: 2025/4/17.
--- Desc: 沙盘简化模式Util

local require = require
local pairs = pairs
local string = string
local coroutine = coroutine
local xpcall = xpcall
local setmetatable = setmetatable

local UtilityManager = CS.UtilityManager
local UIUtil = UIUtil

local game_scheme = require "game_scheme"
local res_pool = require "res_pool"
local util = require "util"
local event = require "event"

local gw_admin = require "gw_admin"
local gw_sand_event_define = require "gw_sand_event_define"
local gw_sand_timer_mgr = require "gw_sand_timer_mgr"
local EntityManager = require "entity_manager"
local async_entity = require "async_entity"
local EntityHybridUtility = CS.Unity.Entities.EntityHybridUtility

local switchUtility = gw_admin.SwitchUtility
local function exceptionsHandler(err)
    switchUtility.OnSandLuaErr(err)
end

local function warnHandler(msg)
    switchUtility.Warn(msg)
end

local simpleLevelUtil = {}
local mc = nil

local simpleLevelState = {
    none = 0,
    simpleLevel1 = 1,
    simpleLevel2 = 2,
}
local simpleLevelCfg = nil

local function initCfg()
    if simpleLevelCfg then
        return
    end
    local device_level_controller = require "device_level_controller"
    local device_param_util = require "device_param_util"

    -- 预定义配置ID映射
    local LEVEL_CFG_MAP = {
        [device_param_util.eLowLevel] = 125,
        [device_param_util.eMidLevel] = 126,
        [device_param_util.eHighLevel] = 127
    }

    local levelCache = device_level_controller.GetOriginDeviceLevel()
    local constCfgId = LEVEL_CFG_MAP[levelCache] or LEVEL_CFG_MAP[device_param_util.eMidLevel]

    local cfgData = game_scheme:GWMapConstant_0(constCfgId)
    if not cfgData or not cfgData.szParam or not cfgData.szParam.data then
        exceptionsHandler("initSimpleCfg ERROR ! 无法获取对应 GWMapConstant 配置 cfgId : " .. constCfgId)
        return
    end

    local exitCfgData = game_scheme:GWMapConstant_0(130)
    if not exitCfgData or not exitCfgData.szParam or not exitCfgData.szParam.data then
        exceptionsHandler("initSimpleCfg ERROR ! 无法获取对应 GWMapConstant 配置 cfgId : " .. 130)
        return
    end

    simpleLevelCfg = {
        levelModelCount = cfgData.szParam.data[0],
        levelModelThreshold = cfgData.szParam.data[1],
        level2FPS = cfgData.szParam.data[2],
        level2FPSCheckTime = cfgData.szParam.data[3],
        level2FPSThreshold = cfgData.szParam.data[4],
        -- 退出简化模式检测时间
        levelExitCheckTime = exitCfgData.szParam.data[0],
    }
end

local function initData()
    mc = {
        simpleLevelState = simpleLevelState.none,
        loadModelCount = 0,
        exitCheckTime = 0,
        fpsCheckTime = 0,

        -- 创建弱键引用表
        responseTable = setmetatable({ }, { __mode = "v" }),
        responseChanged = false,
    }
end

local function getCurrentFps()
    if not simpleLevelUtil.utilityManager then
        simpleLevelUtil.utilityManager = UtilityManager.GetInstance()
    end
    if mc.fakeState then
        return mc.fakeFps
    end
    return simpleLevelUtil.utilityManager.CurrentFps or 0
end

local function getCurrentModelCount()
    if mc.fakeState then
        return mc.fakeModelCount
    else
        return mc.loadModelCount
    end
end

local function changeSimpleLevelState(levelState)
    if not mc or mc.simpleLevelState == levelState then
        return
    end
    mc.responseChange = true
    mc.simpleLevelState = levelState
    event.Trigger(gw_sand_event_define.GW_SAND_SIMPLE_LEVEL_CHANGED, levelState)
    warnHandler("ChangeSimpleLevelState " .. levelState)
end

---@private 退出简化模式检测
local function handleExitCheck(modelCount)
    mc.exitCheckTime = modelCount <= simpleLevelCfg.levelModelThreshold
            and mc.exitCheckTime + 1 or 0

    if mc.exitCheckTime >= simpleLevelCfg.levelExitCheckTime then
        mc.exitCheckTime = 0
        changeSimpleLevelState(simpleLevelState.none)
        return true
    end
    return false
end

---@private level 1 to 2 检测
local function handleLevel1To2Transition(currentFps)
    mc.fpsCheckTime = currentFps <= simpleLevelCfg.level2FPS
            and mc.fpsCheckTime + 1 or 0

    if mc.fpsCheckTime >= simpleLevelCfg.level2FPSCheckTime then
        mc.fpsCheckTime = 0
        changeSimpleLevelState(simpleLevelState.simpleLevel2)
    end
end

---@private level 2 to 1 检测
local function handleLevel2To1Transition(currentFps)
    mc.fpsCheckTime = currentFps >= simpleLevelCfg.level2FPSThreshold
            and mc.fpsCheckTime + 1 or 0

    if mc.fpsCheckTime >= simpleLevelCfg.level2FPSCheckTime then
        mc.fpsCheckTime = 0
        changeSimpleLevelState(simpleLevelState.simpleLevel1)
    end
end

---@private 获得沙盘模型地址(规则和金凯确认过)
local function getSandModelPath(cfg)
    if cfg then
        if cfg.modelsimplePath then
            return cfg.modelsimplePath
        elseif cfg.modelPath then
            return string.gsub(cfg.modelPath, "%.prefab$", "_simple.prefab")
        end
    end
    return nil
end

---@private 加载沙盘模型
local sandModelMeta = {
    __index = {
        SetParent = function(self, newParent, forced)
            if not util.IsObjNull(newParent) and (forced or self.parent ~= newParent) then
                self.parent = newParent
                if self._loaded then
                    if self.transform then
                        self.transform:SetParent(newParent, false)
                        UIUtil.ResetTransform(self.transform)
                    -- 如果是ECS模式，并且无 hybridTrans， attach to parent
                    elseif self.entity then
                        EntityHybridUtility.AttachToTransform(self.entity, newParent)
                    end
                end
            end
        end,
        Dispose = function(self)
            if self._disposed then
                return
            end
            self._loaded = false
            self._disposed = true
            simpleLevelUtil.DisposeSandModel(self)
        end
    }
}
local function createDefaultModel(path, parent, callback)
    local sandModel = setmetatable({
        _loaded = false,
        _disposed = false,
        parent = parent,
    }, sandModelMeta)

    if EntityManager.ModelEcs then
           sandModel.entityId = async_entity.RequestInstantiate(path, function(entity)
            if not entity then
                return
            end
             sandModel._loaded = true

            sandModel.entity = entity
            local hybridTrans  = EntityHybridUtility.GetRootTransform(entity)
            local gameObject = hybridTrans and hybridTrans.gameObject
            sandModel.gameObject = gameObject
            sandModel.transform = hybridTrans
            sandModel:SetParent(sandModel.parent, true)
            if callback then
                xpcall(callback, exceptionsHandler, gameObject, entity, path)
            end
        end)
    else
        sandModel.res_vo = res_pool.get_res(path, function(res_vo1)
            -- 防止回调时已被销毁
            if sandModel._disposed then
                res_vo1.cb = nil
                return
            end
            sandModel._loaded = true

            local gameObject, transform = res_vo1.go, res_vo1.go.transform
            sandModel.gameObject = gameObject
            sandModel.transform = transform
            sandModel:SetParent(sandModel.parent, true)
            gameObject:SetActive(true)
            if callback then
                xpcall(callback, exceptionsHandler, gameObject)
            end
        end)
    end
    return sandModel
end

function simpleLevelUtil.InitSand()
    initCfg()
    initData()
    -- 创建每秒检测的定时器
    if not simpleLevelUtil.checkTimerID then
        simpleLevelUtil.checkTimerID = gw_sand_timer_mgr.AddSandTimer(1, simpleLevelUtil.CheckSimpleLevelState)
    end
end

function simpleLevelUtil.LateUpdate()
    if not mc or not mc.responseChange then
        return
    end
    -- 分帧执行响应逻辑
    local done = simpleLevelUtil.ResponseSimpleLevelChange()
    if done then
        mc.responseChange = false
    end
end

function simpleLevelUtil.Dispose()
    if simpleLevelUtil.checkTimerID then
        gw_sand_timer_mgr.RemoveSandTimer(simpleLevelUtil.checkTimerID)
        simpleLevelUtil.checkTimerID = nil
    end

    if simpleLevelUtil.coroutineRunner then
        simpleLevelUtil.coroutineRunner = nil
    end

    changeSimpleLevelState(simpleLevelState.none)
end
---------------------------------------------  外部业务接口  ---------------------------------------------
function simpleLevelUtil.CheckSimpleLevelState()
    if mc.responseChange then
        return
    end

    local modelCount = getCurrentModelCount()
    if mc.simpleLevelState == simpleLevelState.none then
        if modelCount >= simpleLevelCfg.levelModelCount then
            changeSimpleLevelState(simpleLevelState.simpleLevel1)
        end
        return
    end
    -- 退出检测
    if handleExitCheck(modelCount) then
        return
    end

    local fps = getCurrentFps()
    if mc.simpleLevelState == simpleLevelState.simpleLevel1 then
        handleLevel1To2Transition(fps)
    elseif mc.simpleLevelState == simpleLevelState.simpleLevel2 then
        handleLevel2To1Transition(fps)
    end
end

function simpleLevelUtil.ResponseSimpleLevelChange()
    if not mc.responseTable then
        return true
    end

    if not simpleLevelUtil.coroutineRunner then
        simpleLevelUtil.coroutineRunner = coroutine.create(function()
            local frameRunCount = 16
            for _, entity in pairs(mc.responseTable) do
                local ok, err = xpcall(entity.OnSimpleLevelChanged, exceptionsHandler, entity, mc.simpleLevelState)
                frameRunCount = frameRunCount - 1
                if frameRunCount <= 0 then
                    frameRunCount = 16
                    coroutine.yield()
                end
            end
            return true
        end)
    end

    local ok, done = coroutine.resume(simpleLevelUtil.coroutineRunner)
    if not ok or (done == true) then
        simpleLevelUtil.coroutineRunner = nil
        return true
    end
    return false
end

function simpleLevelUtil.GetSimpleLevelState()
    if not mc then
        return simpleLevelState.none
    end
    return mc.simpleLevelState
end

function simpleLevelUtil.RegisterSimpleLevel(compId, entity)
    if not mc or not mc.responseTable then
        return
    end
    mc.responseTable[compId] = entity
end

function simpleLevelUtil.UnRegisterSimpleLevel(compId)
    if not mc or not mc.responseTable or not mc.responseTable[compId] then
        return
    end
    mc.responseTable[compId] = nil
end

---@public 加载一个沙盘英雄模型
local gw_hero_mgr
function simpleLevelUtil.CreateSandHeroModel(heroID, heroStar, skinID, parent, callback)
    if not heroID or not heroStar or not skinID then
        return
    end

    gw_hero_mgr = gw_hero_mgr or require "gw_hero_mgr"
    local cfg = gw_hero_mgr.GetCfgByModelIdAndStarLv(heroID, heroStar, skinID)
    local path = getSandModelPath(cfg)
    if not path then
        exceptionsHandler("Load HeroModel Fail, heroPath is nil! heroID = ", heroID, " heroStar = ", heroStar, " skinID = ", skinID)
        return
    end
    local model = createDefaultModel(path, parent, callback)
    mc.loadModelCount = mc.loadModelCount + 1
    return model
end

---@public 加载一个沙盘模型
function simpleLevelUtil.CreateSandModulModel(modulId, parent, callback)
    if not modulId then
        return
    end
    local cfg = game_scheme:Modul_0(modulId)
    local path = getSandModelPath(cfg)
    if not path then
        exceptionsHandler("Load ModulModel Fail, modulPath is nil! ModulId = ", modulId)
        return
    end

    local model = createDefaultModel(path, parent, callback)
    mc.loadModelCount = mc.loadModelCount + 1
    return model
end

---@public 加载一个沙盘模型
function simpleLevelUtil.DisposeSandModel(model)
    if not mc or not model then
        return
    end

    if model.res_vo then
        res_pool.return_res(model.res_vo)
        model.res_vo = nil
    elseif model.entityId then
        async_entity.Dispose(model.entityId)
    end
    model.gameObject = nil
    model.transform = nil
    model.entityId = nil
    model.entity = nil
    mc.loadModelCount = mc.loadModelCount - 1
end

-- 假数据接口
function simpleLevelUtil.SetFakeData(state, count, fps)
    if not mc then
        return
    end
    mc.fakeState = state
    mc.fakeModelCount = count
    mc.fakeFps = fps
end

return simpleLevelUtil