--- Created by fgy.
--- DateTime: 2024/7/11 14:10
--- Des:沙盘节点设置
local require = require

local util = require "util"
local gw_const = require "gw_const"
local ESandOrderKey = gw_const.ESandOrderKey
local gw_sand_scene_util = require "gw_sand_scene_util"
local GetTransformByName = gw_sand_scene_util.GetTransformByName

local sandNodeList = {}

---@class GWSandNode
local GWSandNode = {
    cityBgNode = function()
        if util.IsObjNull(sandNodeList.cityBgNode) then
            sandNodeList.cityBgNode = GetTransformByName(ESandOrderKey.Bg2)
        end
        return sandNodeList.cityBgNode
    end,
    lineNode = function()
        if util.IsObjNull(sandNodeList.lineNode) then
            sandNodeList.lineNode = GetTransformByName(ESandOrderKey.Bg3)
        end
        return sandNodeList.lineNode
    end,
    baseNode = function()
        if util.IsObjNull(sandNodeList.baseNode) then
            sandNodeList.baseNode = GetTransformByName(ESandOrderKey.Entity)
        end
        return sandNodeList.baseNode
    end,
    entityNode = function()
        if util.IsObjNull(sandNodeList.entityNode) then
            sandNodeList.entityNode = GetTransformByName(ESandOrderKey.Entity1)
        end
        return sandNodeList.entityNode
    end,
    entityNode1 = function()
        if util.IsObjNull(sandNodeList.entityNode1) then
            sandNodeList.entityNode1 = GetTransformByName(ESandOrderKey.Entity2)
        end
        return sandNodeList.entityNode1
    end,
    litNode = function()
        if util.IsObjNull(sandNodeList.litNode) then
            sandNodeList.litNode = GetTransformByName(ESandOrderKey.NoScale1)
        end
        return sandNodeList.litNode
    end,
    cityLitNode = function()
        if util.IsObjNull(sandNodeList.cityLitNode) then
            sandNodeList.cityLitNode = GetTransformByName(ESandOrderKey.NoScale3)
        end
        return sandNodeList.cityLitNode
    end,
    moveNode = function()
        if util.IsObjNull(sandNodeList.moveNode) then
            sandNodeList.moveNode = GetTransformByName(ESandOrderKey.Move)
        end
        return sandNodeList.moveNode
    end,
    -- 顶层区域用
    top1Node = function()
        if util.IsObjNull(sandNodeList.top1) then
            sandNodeList.top1 = GetTransformByName(ESandOrderKey.Top)
        end
        return sandNodeList.top1
    end,
    top2Node = function()
        if util.IsObjNull(sandNodeList.top2) then
            sandNodeList.top2 = GetTransformByName(ESandOrderKey.Top1)
        end
        return sandNodeList.top2
    end,
    top3Node = function()
        if util.IsObjNull(sandNodeList.top3) then
            sandNodeList.top3 = GetTransformByName(ESandOrderKey.Top2)
        end
        return sandNodeList.top3
    end,
    effect1Node = function()
        if util.IsObjNull(sandNodeList.effect1Node) then
            sandNodeList.effect1Node = GetTransformByName(ESandOrderKey.Effect1)
        end
        return sandNodeList.effect1Node
    end,
    effect2Node = function()
        if util.IsObjNull(sandNodeList.effect2Node) then
            sandNodeList.effect2Node = GetTransformByName(ESandOrderKey.Effect2)
        end
        return sandNodeList.effect2Node
    end,
    effect3Node = function()
        if util.IsObjNull(sandNodeList.effect3Node) then
            sandNodeList.effect3Node = GetTransformByName(ESandOrderKey.Effect3)
        end
        return sandNodeList.effect3Node
    end,
    ecsEffect1Node = function()
        if util.IsObjNull(sandNodeList.ecsEffect1Node) then
            sandNodeList.ecsEffect1Node = GetTransformByName(ESandOrderKey.ecsEffect1)
        end
        return sandNodeList.ecsEffect1Node
    end,
    InitSand = function()
        sandNodeList = {}
    end,
    Dispose = function()
        sandNodeList = {}
    end
}

return GWSandNode