local io = io
local require = require
local print = print
local os = os
local tonumber = tonumber
local string = string
local pairs = pairs
local ipairs = ipairs
local table = table
local math = math
local tostring = tostring
local typeof = typeof
local dump = dump

local UnityEngine = CS.UnityEngine
local GameObjectPool = CS.War.Base.GameObjectPool
local Application = CS.UnityEngine.Application
local Time = CS.UnityEngine.Time
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local IOSystem = require "iosystem_load"
local BattleEvent = CS.War.Battle.BattleEvent
local GameObject = CS.UnityEngine.GameObject
local Animator = CS.UnityEngine.Animator
local Transform = CS.UnityEngine.Transform
local DecompressBattle = CS.War.Script.Utility.DecompressBattle
local LogDebug = CS.War.Base.LogDebug
local Utility = CS.War.Script.Utility
local Episode = CS.War.Battle.Episode
local Camera = CS.UnityEngine.Camera
local AnimatorType = typeof(Animator)
local TransformType = typeof(Transform)
local CardType = typeof(CS.War.Battle.Card)
local CinemachineImpulseSource = CS.Cinemachine.CinemachineImpulseSource
local SpriteMeshInstanceReflection = CS.War.Battle.SpriteMeshInstanceReflection
local SoundEngine = CS.War.Script.SoundEngine
module("battle_manager")

local game_config = require "game_config"
local message_box = require "message_box"
local lang = require "lang"
local battle_message = require "battle_message"
local game_scheme = require "game_scheme"
local tbs_pb = require "tbs_pb"
local common_pb = require "common_new_pb"
local ui_in_battle = require "ui_in_battle"
local windowMgr = require "ui_window_mgr"
local event = require "event"
local game = require "game"
local util = require "util"
local player_mgr = require "player_mgr"
local battle_data = require "battle_data"
local log = require "log"
local battle_player_assistant = require "battle_player_assistant"
local gw_hero_mgr = require "gw_hero_mgr"
local battle_config = require "battle_config"
local version_mgr = require "version_mgr"
local prop_pb = require "prop_pb"
local battle_series_manager = require "battle_series_manager"
local time_scale_mgr = require "time_scale_mgr"
local battle_event_report = require "battle_event_report"
local scene_manager = require "scene_manager"
local ui_window_mgr = require "ui_window_mgr"
local menu_bot_data = require "menu_bot_data"
local ReviewingUtil = require "ReviewingUtil"
local weapon_data = require "weapon_data"
local ClearGameObjectPool = game.ClearGameObjectPool
local enableBossCommingEffectInterface = Utility.TimelineAssetOutputs ~= nil

--是否正在战斗
BattleRunning = false

local prepareBattle = nil
local finalizeBattle = {}
local finallizeCallBack = {}
local canSkipBattle = nil
local loadingWindow = nil
local bossCommingEffect = nil
local battleType = nil
local isReplay = false
local returnArean = true

--///上报数据相关
local lastStageType = nil   --记录战斗类型
local saveBattleMsgPath = nil
lastBattleID = nil--记录战斗ID
local stageTypeStr = ""
local lastLevel = nil       --记录战斗类型
local levelID = 0
local lastRound = 0         --记录回合数
local minspeed = 1.1
local maxspeed = 1.6
local lastBits = nil        -- 记录战斗数据
local lastMultiTbs = nil
local skipResult = true   -- 回放后是否要返回结算页面，竞技场需要特殊处理
local skipLoading = false  -- 不显示ui_loading
local skiped = nil
local isSaveBattleRecordMsg = false --是否保存战报协议消息

--加载原始战报，并且切入战斗的进度
--如果为-1时，表示不适用该方式
local loadAndCutOverBattleProgress = 0

--///战斗录像分享
local battleID = nil
local opponentName = nil
local powerID = nil

-- local battle_Controller = nil
local M = {}

local battle_player = nil
local _delayCall = nil

local curBattleEventHandler = nil

local BattleTypeInfo = {
    [common_pb.GameGoal] = { name = "位面战纪" },
    [common_pb.TheTowerOfIllusion] = { name = "星级通缉" }, -- 为方便数数后台数据比较，不修改为 “星际通缉”
    [common_pb.BraveManTried] = { name = "深空试炼" },
    [common_pb.Arena] = { name = "竞技场" },
    [common_pb.Ashdungeon] = { name = "遗落之境" },
    [common_pb.BrokenSpaceTime] = { name = "虚空主宰" },
    [common_pb.LeaActivityBoss] = { name = "活跃度Boss" },
    [common_pb.LeagueComp] = { name = "联盟争霸" },
    [common_pb.LeagueBoss] = { name = "联盟Boss" },
    [common_pb.LeagueTreasureBoss] = { name = "联盟探宝" },
    [common_pb.Compete] = { name = "切磋" },
    [common_pb.MateMonster] = { name = "时空幻域" },
}
function Initialize()
    BattleEvent.OnBattleDialog = battle_event_report.OnBattleDialog
    BattleEvent.OnBattleNarration = battle_event_report.OnBattleNarration
    BattleEvent.OnBattleSuspend = battle_event_report.OnBattleSuspend
    BattleEvent.OnClickBattleSuspend = OnClickBattleSuspend
    BattleEvent.OnBubbleShow = OnBubbleShow

    if Episode.TimeOutCallback then
        Episode.playEpisodeTimeOut = DelayPlayEpisodeTimeOut
    end
end

function PrepareBattleRunning()
    BattleRunning = true
    prepareBattle()
end

function FinalizeBattleRunning(victory, ispass, sType)

    scene_manager.instance():DestroySceneByName("scene_battle")

    local stageType = sType or battle_data.stageType

    if finalizeBattle and finalizeBattle[stageType] then
        finalizeBattle[stageType](victory, ispass)
    else
        --local sand_ui_event_define = require "sand_ui_event_define"
        --event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
    end
    BattleRunning = false
    local ui_point_slider_controller = require "ui_point_slider_controller"
    ui_point_slider_controller.ExitSaveMode(true)
end

function LoadingTimeOut()
    --超时跳过战斗，显示进入战斗前ui

    local ui_point_slider_controller = require "ui_point_slider_controller"
    ui_point_slider_controller.ExitSaveMode(true)
    if finalizeBattle ~= nil then
        local victory = battle_data.IsVictory()

        FinalizeBattleRunning(victory, true)
        finalizeBattle[battle_data.stageType] = nil
    else
        --event.Trigger(event.GW_OPEN_SAND_MAIN)
    end
end

function PlayEpisodeTimeOut(episode, des)
    log.Error(" 战斗资源加载超时:" .. des)

    local reportMsg = {
        -- system_memory_size = util.GetSystemMemorySize(),
    }
    event.EventReport("loading_out_time", reportMsg)

    local player = GetBattlePlayer()
    if player then
        player:Stop()
    end

    ui_window_mgr:CloseAll()
    ui_window_mgr:ShowModule("ui_lobby")

    local battle_manager = require "battle_manager"
    battle_manager.ClearLoadingEvent()
    message_box.Open(lang.Get(99), message_box.STYLE_YES, function()
    end, 0, lang.KEY_OK)
end

function DelayPlayEpisodeTimeOut(episode, des)
    util.DelayOneCall("PlayEpisodeTimeOut", function()
        PlayEpisodeTimeOut(episode, des)
    end, 0.1)
end

function StartWaitTimer()
    DisposeDelayCall()
    battle_message.SetBattleResult(true)
    _delayCall = util.DelayCall(600, function()
        local battle_data = require "battle_data"
        local isResultUI = ui_window_mgr:IsModuleShown("ui_battle_defeat_new") or ui_window_mgr:IsModuleShown("ui_battle_victory_new")
        if battle_data.skipBattle and not isResultUI then
            battle_message.SetBattleResult(nil)
        end
    end)
end

--销毁定时器
function DisposeDelayCall()
    if _delayCall then
        util.RemoveDelayCall(_delayCall)
        _delayCall = nil
    end
end

function OnBattleReportReceived(stageType, bits, multiTbs, finalizecallback, callPrepareBattle, playByTargetReport, battleID)
    util.PeekWatch("loading battle", "收到战报")
    event.Trigger(event.NEW_BATTLE_START, stageType)
    local ui_point_slider_controller = require "ui_point_slider_controller"
    ui_point_slider_controller.EnterSaveMode()
    --event.Trigger(event.POINT_SLIDER_ENTER_SAVE_MODE)
    local camp_trial_data = require "camp_trial_data"
    if camp_trial_data.GetFinalSkipBattle(stageType) then
        return
    end

    if battleID then
        log.Warning("BattleReportReceived battleType:", stageType, "battleId:", battleID, "roleId", player_mgr.GetPlayerRoleID(), "time", util.FormatTime(util.GetServerTime()))
        log.Warning("BattleReportReceived replay url:", GetBattleReplayWebUrl(battleID, stageType, player_mgr.GetPlayerRoleID()))
        if stageType == 49 then
            local property = {
                qiuri_battleId = battleID,
                qiuri_roleId = player_mgr.GetPlayerRoleID(),
                qiuri_time = util.FormatTime(util.GetServerTime()),
                qiuri_url = GetBattleReplayWebUrl(battleID, stageType, player_mgr.GetPlayerRoleID())
            }

            event.Trigger(event.GAME_EVENT_REPORT, "qiuri_battle_report", property)
        end
    end

    lastStageType = stageType
    lastBattleID = battleID

    --print("loading battle", "当前在连续战斗过程中！！！！！stageType:", stageType, "IsInSeriesBattle(stageType):", battle_series_manager.IsInSeriesBattle(stageType))
    if battle_series_manager.IsInSeriesBattle(stageType) then
        skipLoading = true
        battle_data.isInSeriesBattle = true
    else
        skipLoading = false
        battle_data.isInSeriesBattle = false
    end

    local battle_switch_manager = require "battle_switch_manager"
    --设置多战斗是否可跳过函数
    if battle_switch_manager.IsSwitchable(stageType) then
        local canSkipFun = battle_switch_manager.GetBattleFunction(stageType)
        if not canSkipFun then
            battle_switch_manager.SetBattleFunction(stageType, canSkipBattle)
        else
            canSkipBattle = canSkipFun
        end
    end

    lastBits = bits
    lastMultiTbs = multiTbs

    -- if stageType == common_pb.Maze then
    --     log.Error("OnBattleReportReceived收到战报===================", "battle_data.isInSeriesBattle", battle_data.isInSeriesBattle,battle_series_manager.IsInSeriesBattle(stageType))
    -- end

    local player = GetBattlePlayer()
    if not player then
        return
    end

    scene_manager.instance():SetSceneCallbackOnce("scene_battle", nil, nil, SceneBattleUnprocessedDestroyed)

    --print("a")
    bits = ParseRealBattleReport(bits)
    --print("b")
    --File.WriteAllBytes("Log/test2.bytes", bits)
    local pbMsg = tbs_pb.TbsReports()
    pbMsg:ParseFromString(bits)

    util.PeekWatch("loading battle", "战报反序列化完成")
    --连续战斗时，只清理敌方阵容
    if battle_data.isInSeriesBattle then
        if player.ClearBattleRightPals then
            player:ClearBattleRightPals()
        end

    else
        player:ClearBattle()
    end

    --战斗投影风格设置
    --低端机使用简化投影
    --中低级别使用
    local device_level_controller = require "device_level_controller"
    if device_level_controller.OpenShadowController() then
        if player.EnableSimpleShadow then
            player:EnableSimpleShadow(true)
        end
    end

    if isSaveBattleRecordMsg then
        local fileName = saveBattleMsgPath
        --如果没有指定保存的路径，则使用当前时间搓作为名字
        if not fileName then
            fileName = os.date("%Y_%m_%d_%H_%M_%S", os.time()) .. ".battleMsg"
        else
            saveBattleMsgPath = nil
        end
        SaveBattleRecord(util.pb2str(pbMsg), fileName)
    end
    SetBattlePoolActive(true)

    if not battle_data.skipBattle and lastStageType == common_pb.LeagueBoss and not isReplay then
        --针对联盟Boss频繁请求战斗（跳过战斗）时偶现进入战斗的情况
        battle_data.skipBattle = PlayerPrefs.GetInt('sociatyboss_skip_battle_' .. player_mgr.GetPlayerRoleID(), 2) == 1
        --print("PlayerPrefs.GetInt('sociatyboss_skip_battle_'..player_mgr.GetPlayerRoleID(), 2)",PlayerPrefs.GetInt('sociatyboss_skip_battle_'..player_mgr.GetPlayerRoleID(), 2))
    end
    if prepareBattle ~= nil and not battle_data.skipBattle then
        if not callPrepareBattle then
            PrepareBattleRunning()
        end
        prepareBattle = nil
        event.Trigger(event.HALL_SCENE_SILDING, false)
    end
    curBattleEventHandler = multiTbs and OnMultiTbsBattleEvent or OnBattleEvent
    player:RegisterBattleEventHandler(curBattleEventHandler)
    battle_player.ParseReport(pbMsg, playByTargetReport)
    util.PeekWatch("loading battle", "解析战报完成")

    if battle_data.skipBattle then
        if multiTbs then
            OnMultiTbsBattleEvent("BattleEnd")
        else
            OnBattleEvent("BattleEnd")
        end
    else
        if skipLoading == false then
            local ui_loading = require "ui_loading"
            --设置 battle 状态 为1
            ui_loading.SetBattleStageType(1)
            loadingWindow = ui_window_mgr:ShowModule("ui_loading")
            loadingWindow:SetLoadingProgress(0)
        end
        player:RegisterLoadingBeginHandler(OnLoadingBegin)
        player:RegisterLoadingCompleteHandler(OnLoadingCompleted)
        player:RegisterLoadingProgressChangedHandler(OnLoadingPorgressChanged)
        --监听角色加载完成事件
        if player.RegisterRoleLoadedHandler then
            player:RegisterRoleLoadedHandler(OnRoleLoaded)
        end

        local load_report_tool = require "load_report_tool"
        load_report_tool.RegisterBattleLoadHandler(true)

        util.PeekWatch("loading battle", "开始加载场景资源")
        player:Load()

        scene_manager.instance():LoadScene("scene_battle", true)
        util.PeekWatch("loading battle", "场景所需资源收集完成")
    end

    table.insert(finallizeCallBack, finalizecallback)
end

local LzmaMark = "INFWAR"
function ParseRealBattleReport(battleReport)
    local isCompress = true
    for i = 1, 6 do
        -- statements
        if string.byte(LzmaMark, i) ~= string.byte(battleReport, i) then
            isCompress = false
            break
        end
    end

    if isCompress then
        --print("isCompress",isCompress,#battleReport)
        battleReport = DecompressBattle(battleReport, 10)
    end
    return battleReport
end

function SetBattleRecordData(battleid, opponentname, powerid)
    battleID = battleid
    opponentName = opponentname
    powerID = powerid
end

function ClearBattleRecordData()
    battleID = nil
    opponentName = nil
    powerID = nil
end

-- 当前战斗回放功能
function StartBattleReplay()
    local pre = battle_data.skipBattle
    local preIsRePlay = GetIsReplay()
    battle_data.skipBattle = false
    
    SetIsReplay(true)
    if battle_series_manager.IsSeriesBattle(lastStageType) then
        battle_series_manager.PlayTargetWaveBattle(lastStageType, 1)
        return
    end
    -- 播放
    --if battleType == common_pb.CrystalCrown or battleType == common_pb.Advance then
    if battleType == common_pb.CrystalCrown
            or battleType == common_pb.BossSlave then
        skipResult = false
    end
    CloseSlgUI()
    OnBattleReportReceived(lastStageType, lastBits, lastMultiTbs, function()
        battle_data.skipBattle = pre
        SetIsReplay(preIsRePlay)
    end, nil, nil, lastBattleID)

end

--获取当前正在进行中的战斗剩余时间(单位秒)
function GetSurplusTimeOfCurrentBattle()
    local currentPlayTime = GetTimeOfCurrentBattle()    --当前战斗播放时间
    local totalTime = GetPredictTimeOfBattle(battle_data.stageType) --当前战斗总时长
    if totalTime <= currentPlayTime then
        return 0
    end
    --print("<color=#fff0ff>战斗时间：totalTime：</color>", totalTime, " 获取当前已进行时长：", currentPlayTime)
    return totalTime - currentPlayTime  --剩余战斗时长
end

--获取当前已进行时长(单位秒)
function GetTimeOfCurrentBattle()
    local player = GetBattlePlayer()
    if player.GetPlayingEpisode == nil or player:GetPlayingEpisode() == nil then
        return 0
    end
    local reportIndex = player:GetPlayingEpisode().reportIndex
    local currentPlayTime = 0
    if player.GetTimeByTargetReportIndex then
        currentPlayTime = player:GetTimeByTargetReportIndex(battle_data.stageType, reportIndex)
    end
    --print("<color=#fff0ff>战斗时间：获取当前已进行时长：</color>", currentPlayTime, " 当前战报index：", reportIndex)
    return currentPlayTime
end

--获取战斗（目标战斗类型的最后一场战斗）的预估总时长
function GetPredictTimeOfBattle(battleStageType)

    local player = GetBattlePlayer()
    if player.GetBattlePredictTime then
        return player:GetBattlePredictTime(battleStageType)
    end
    return 0
end

--多战斗：获取对应战斗结束时间（以servertime为基准）
function GetMultipleBattleEndTimeBy(battleStageType)
    if battle_data.multipleBattleEndTimes then
        return battle_data.multipleBattleEndTimes[battleStageType]
    end
    return 0
end

--多战斗：目标战斗是否为多队
function IsMultiTbsOfMultipleBattle(stageType)
    --多队战斗判断
    local reportData = battle_data.battleReports[stageType]
    local isMultiTbs = reportData and reportData.isMultiTbs
    return isMultiTbs
end

-- 多战斗：切出当前战斗
function CutOutCurrentBattle()
    local battleStageType = lastStageType
    local isSeriesBattle = battle_series_manager.IsSeriesBattle(battleStageType)
    local isMultiTbs = IsMultiTbsOfMultipleBattle(battleStageType)

    local speedScale = GetPlayerSpeedScale()
    --连续战斗、多队战斗处理
    if isSeriesBattle or isMultiTbs then
        local totalWave
        local curWave
        local surplusWave
        if isSeriesBattle then
            --切出连续战斗
            totalWave = battle_series_manager.GetToatlWave()
            curWave = battle_series_manager.GetWave()
            surplusWave = totalWave - curWave
        elseif isMultiTbs then
            --切出多队战斗
            local reportData = battle_data.battleReports[battleStageType]
            local multiTbsDatas = reportData and reportData.multiTbsDatas or {}
            totalWave = #multiTbsDatas --多队战斗总数
            curWave = battle_message.GetCurBattleIndexByBattle() --当前战斗波次
            surplusWave = totalWave - curWave   --剩余战斗波次
            --清空多战斗信息
            battle_message.SetIsMultiTbs(false, battleStageType)
            battle_message.ClearMultiTbs()
        end
        local curBattleTotalTime = 0
        if battle_data.currentBattleCompleteTotalTime and battle_data.currentBattleCompleteTotalTime > 0 then
            curBattleTotalTime = battle_data.currentBattleCompleteTotalTime / speedScale
            battle_data.currentBattleCompleteTotalTime = 0
        else
            curBattleTotalTime = GetPredictTimeOfBattle(battleStageType) / speedScale --战斗总时长
        end
        battle_data.multipleBattleSeriesPerTime[battleStageType] = curBattleTotalTime --设置多战斗中连续战斗(多队战斗)每次战斗预估时间
        battle_data.multipleBattleSeriesTotalWave[battleStageType] = surplusWave --设置多战斗中连续战斗总波次
        battle_data.multipleBattleSeriesCutOutWave[battleStageType] = curWave -- 切出战斗时的波次

        --local resetTotalTime = totalTime / speedScale
        battle_data.multipleBattleCutOutTimes[battleStageType] = util.GetServerTime()
        local currentPlayTime = GetTimeOfCurrentBattle()    --当前战斗已播放时间
        battle_data.multipleBattleCutOutPlayTimes[battleStageType] = currentPlayTime --切出战斗时已播放时长
        local curSurplusTime = GetSurplusTimeOfCurrentBattle() / speedScale --剩余时长需要乘以加速系数
        --剩余波次需要时长（剩余波次 * 当前战斗时长 * 加速系数）, 将当前战斗时长作为后续战斗的预估时间
        local surplusWaveTime = surplusWave * curBattleTotalTime
        local allWaveSurplusTime = curSurplusTime + surplusWaveTime --总剩余时间
        battle_data.multipleBattleEndTimes[battleStageType] = util.GetServerTime() + allWaveSurplusTime --结束时间=当前时间+剩余时长
        battle_data.multipleBattleSeriesNextWaveStartTime[battleStageType] = util.GetServerTime() + curSurplusTime --连续战斗下一场战斗开始时间

        if isSeriesBattle then
            battle_series_manager.CutOutBattle()
        end
        return
    end

    battle_data.multipleBattleCutOutTimes[battleStageType] = util.GetServerTime()
    local currentPlayTime = GetTimeOfCurrentBattle()    --当前战斗已播放时间
    battle_data.multipleBattleCutOutPlayTimes[battleStageType] = currentPlayTime --切出战斗时已播放时长
    local surplusTime = GetSurplusTimeOfCurrentBattle() / speedScale --剩余时长需要乘以加速系数
    battle_data.multipleBattleEndTimes[battleStageType] = util.GetServerTime() + surplusTime --结束时间=当前时间+剩余时长
end

--判断多战斗是否已结束
function IsMultipleBattleEnd(battleStageType)
    if util.GetServerTime() >= GetMultipleBattleEndTimeBy(battleStageType) then
        return true
    end
    return false
end

-- 多战斗：切入在后台播放中的战斗
-- battleStageType:战斗类型
function CutOverBackGroundBattle(battleStageType)
    local currentPlayTime, waveIndex, isProgress = GetBattleCurrentPlayTime(battleStageType)
    local endTime = battle_data.multipleBattleEndTimes[battleStageType]
    local offset = endTime - util.GetServerTime()
    --print("切入战斗加速处理：", "currentPlayTime:", currentPlayTime, " endTime:", endTime, " offset:", offset, "waveIndex", waveIndex, "isProgress", isProgress)
    CutOverBattle(battleStageType, currentPlayTime, waveIndex, isProgress)
end

function GetPlayerSpeedScale()
    local speedScale = 1
    --如果加速度，则乘以战斗加速系数
    local speed = GetPlaySpeed()
    if maxspeed - minspeed > 0.1 and speed > 0.5 * (minspeed + maxspeed) then
        speedScale = maxspeed / minspeed
    end
    --print("切入战斗加速处理：maxspeed:", maxspeed, " minspeed:", minspeed, " speed", speed, " speedScale:", speedScale)
    return speedScale
end

-- 多战斗：中途切入战斗播放
-- battleStageType:战斗类型
-- targetTime:切入的时间
function CutOverBattle(battleStageType, targetTime, waveIndex, isProgress)
    local battleReport = battle_data.battleReports[battleStageType]

    local isSeriesBattle = battle_series_manager.IsSeriesBattle(battleStageType)
    local isMultiTbs = IsMultiTbsOfMultipleBattle(battleStageType)

    --连续战斗和多战斗特殊处理
    if isSeriesBattle or isMultiTbs then
        if isSeriesBattle then
            battleReport = {}
            local battleDate = battle_series_manager.GetCurrentWaveBattleData() --获取当前战斗信息
            if not battleDate then
                print("当前战斗信息为空！！！")
                return
            end
            battleReport.rewards = battleDate.rewards
            battleReport.scores = battleDate.scores
            battleReport.battleBits = battleDate.reports --获取当前波次战报
            battleReport.battleID = battle_series_manager.GetCurrentBattleId() --当前战斗id
            battle_series_manager.CutOverBattle()
        elseif isMultiTbs and waveIndex ~= nil then
            local multiTbsDatas = battleReport and battleReport.multiTbsDatas
            --print("1设置多队战斗上下文数据!!!!!!!!!", #multiTbsDatas, battleStageType)
            --dump(multiTbsDatas)
            local battleInfo = multiTbsDatas[waveIndex]
            battleReport.battleBits = battleInfo.battleReport --获取当前波次战报
            battleReport.battleID = battleInfo.battleID --当前战斗id
            local totalWave = #multiTbsDatas
            local tbsPool = {}
            --构建剩余战报数组
            for i = waveIndex + 1, totalWave do
                table.insert(tbsPool, multiTbsDatas[i])
            end
            --设置多队战斗上下文数据
            battle_message.SetMultiTbs(multiTbsDatas)
            battle_message.SetTbsPool(tbsPool)
            --print("设置多队战斗上下文数据!!!!!!!!!", #tbsPool)
            --dump(tbsPool)
        end
        --通过播放进度切入战斗
        if isProgress then
            --使用此方式时，由于没有战斗时长数据，需要先加载完战斗资源，拿到战斗时长数据后，根据切入进度，得出具体的切入时间
            loadAndCutOverBattleProgress = targetTime
            --没有战斗时长信息，将切入时间设置为0
            targetTime = 0
        else
            loadAndCutOverBattleProgress = -1
        end

    end

    if not battleReport then
        print("多战斗切入:未找到目标战报信息!!!")
        return
    end

    local pre = battle_data.skipBattle
    battle_data.skipBattle = false

    if battleType == common_pb.CrystalCrown then
        skipResult = false
    end

    local targetReportIndex = 0;
    local player = GetBattlePlayer()
    if not util.IsObjNull(player) then
        targetReportIndex = player:GetReportIndexByTime(battleStageType, targetTime)
    end
    --进入战斗前设置消息的奖励和积分信息
    battle_data.rewards = battleReport.rewards
    battle_data.scores = battleReport.scores
    OnBattleReportReceived(battleStageType, battleReport.battleBits, isMultiTbs, function()
        battle_data.skipBattle = pre
    end, nil, targetReportIndex, battleReport.battleID)

end

-- 多战斗：获取后台进行战斗的当前播放时间
-- battleStageType:战斗类型
function GetBattleCurrentPlayTime(battleStageType)
    --通过战斗开始时间，计算当前播放时间

    --连续、多队战斗处理
    if battle_series_manager.IsSeriesBattle(battleStageType)
            or IsMultiTbsOfMultipleBattle(battleStageType) then

        local serverTime = util.GetServerTime()
        local cutOutTime = battle_data.multipleBattleCutOutTimes[battleStageType] or 0--切出战斗时间
        local cutOutPlayTime = battle_data.multipleBattleCutOutPlayTimes[battleStageType] or 0 --切出战斗时已播放时间
        local usedTime = serverTime - cutOutTime --切出战斗后经过的时间

        local nextWaveTime = battle_data.multipleBattleSeriesNextWaveStartTime[battleStageType] or 0
        local cutOutWaveIndex = battle_data.multipleBattleSeriesCutOutWave[battleStageType] or 0
        local timeScale = GetPlayerSpeedScale()
        if serverTime < nextWaveTime then
            local resetUsedTime = usedTime * timeScale --切出战斗后经过的时间,加速后的实际时间
            local currentPlayTime = resetUsedTime + cutOutPlayTime -- 当前播放时间=切出战斗后经过的时间+切出战斗时间
            -- print("连续战斗在当前回合 currentPlayTime", currentPlayTime, "cutOutWaveIndex", cutOutWaveIndex)
            return currentPlayTime, cutOutWaveIndex
        end

        local costTime = (serverTime - nextWaveTime) * timeScale
        local perBattleTime = battle_data.multipleBattleSeriesPerTime[battleStageType] or 1
        local costWave = math.ceil(costTime / perBattleTime) --切出战斗后已播放的波次
        local waveIndex = costWave + cutOutWaveIndex --当前播放波次
        local playTime = costTime % perBattleTime
        local playTimeProgress = playTime / perBattleTime --播放进度
        -- print("获取后台进行战斗的当前播放时间 waveIndex", waveIndex, "costWave", costWave, "cutOutWaveIndex", cutOutWaveIndex, "perBattleTime", perBattleTime, "costTime", costTime)
        return playTimeProgress, waveIndex, true
    end


    --通过 切出战斗后经过的时间+切出战斗时间，计算当前播放时间
    local serverTime = util.GetServerTime()
    local cutOutTime = battle_data.multipleBattleCutOutTimes[battleStageType]--切出战斗时间
    local cutOutPlayTime = battle_data.multipleBattleCutOutPlayTimes[battleStageType] --切出战斗时已播放时间
    local usedTime = serverTime - cutOutTime --切出战斗后经过的时间
    local resetUsedTime = usedTime * GetPlayerSpeedScale() --切出战斗后经过的时间,加速后的实际时间
    local currentPlayTime = resetUsedTime + cutOutPlayTime -- 当前播放时间=切出战斗后经过的时间+切出战斗时间
    -- print("获取后台进行战斗的当前播放时间 resetUsedTime:", resetUsedTime, " usedTime:", usedTime, " cutOutPlayTime:", cutOutPlayTime, " currentPlayTime:", currentPlayTime)
    return currentPlayTime

end

-- 2020-01-03新增：多条战斗回放，回放其中一条
function StartMultiBattleReplay(index)
    local multiTbs = battle_message.GetMultiTbs()
    if not multiTbs or not multiTbs[index] then
        return
    end

    local pre = battle_data.skipBattle
    battle_data.skipBattle = false

    SetIsReplay(true)
    -- 播放
    --if battleType == common_pb.CrystalCrown or battleType == common_pb.Advance then
    if battleType == common_pb.CrystalCrown then
        skipResult = false
    end

    OnBattleReportReceived(multiTbs[index].stageType, multiTbs[index].battleReport, true, function()
        battle_data.skipBattle = pre
    end, nil, nil, multiTbs[index].battleID)
end

function CloseResultWindow()
    -- print("CloseResultWindow")
    local ui_window_mgr = require "ui_window_mgr"
    ui_window_mgr:UnloadModule("ui_battle_defeat_new")
    ui_window_mgr:UnloadModule("ui_battle_victory_new")
    ui_window_mgr:UnloadModule("ui_in_battle")
    if ui_window_mgr:IsModuleShown("ui_halo_detail") then
        ui_window_mgr:UnloadModule("ui_halo_detail")
    end
    if ui_window_mgr:IsModuleShown("ui_halo_tips") then
        ui_window_mgr:UnloadModule("ui_halo_tips")
    end

    local player = GetBattlePlayer()
    if not util.IsObjNull(player) then
        player:ClearBattle()
    end
    local battle_data = require "battle_data"
    battle_data.SetRoleID()

    local ui_point_slider_controller = require "ui_point_slider_controller"
    ui_point_slider_controller.ExitSaveMode(true)
end


function OnNextWindowShowed(module_name)
    CloseResultWindow()
end

function SetBattleType(_type)
    battleType = _type
end

function UnregisterHandler(battleEventHandler)
    if not battleEventHandler then
        battleEventHandler = curBattleEventHandler
    end
    local player = GetBattlePlayer()
    player:UnRegisterBattleEventHandler(battleEventHandler)
    player:UnRegisterRoundEventHandler(OnRoundBegin)
    player:UnRegisterWeaponPowerEventHandler(OnWeaponProgressChanged)
    if player.UnRegisterOnLuaCallbackHandler then
        player:UnRegisterOnLuaCallbackHandler(OnLuaCallback)
    end
    if version_mgr.CheckSvnTrunkVersion(25380) then
        if player.UnRegisterOnHpChangedHandler then
            player:UnRegisterOnHpChangedHandler(OnBattleHpChanged)
        end
        if player.UnRegisterOnRoleDeadHandler then
            player:UnRegisterOnRoleDeadHandler(OnRoleDead)
        end
    end
    if version_mgr.CheckSvnTrunkVersion(55900) or version_mgr.CheckSvnBranchVersion(56823) then
        if player.UnRegisterRolePropChangedHandler then
            player:UnRegisterRolePropChangedHandler(OnRolePropChanged)
        end
    end
    if player.UnRegisterOnHitHandler then
        player:UnRegisterOnHitHandler(OnBattleBossHitInfo)
    end

    if player.UnRegisterRoleLoadedHandler then
        player:UnRegisterRoleLoadedHandler(OnRoleLoaded)
    end

    if player.UnRegisterOnBattleBuffChangeCallbackHandler then
        player:UnRegisterOnBattleBuffChangeCallbackHandler(OnBuffChange)
    end
end

function AutoPlayBattleRecord()
    --if Utility.IsInEditor() or game_config.ENABLE_Q1_DEBUG_MODE then
    --    local test_battle_mgr = require "test_battle_mgr"
    --    if test_battle_mgr.GetOpenState() then
    --        test_battle_mgr.AutoPlayBattleRecord()
    --    end
    --end
end
-- 战斗回放和单人竞技场
function OnBattleEvent(eventname, switchOutEnd, _stageType)

    local camp_trial_data = require "camp_trial_data"
    if camp_trial_data.GetFinalSkipBattle(_stageType) then
        return
    end

    if eventname == "BattleBegin" then
        local mainSlgMgr = require "main_slg_mgr"
        mainSlgMgr.EnterBattleState(true)
    end

    -- print("------------------OnBattleEvent--------------------------",eventname)
    if eventname == "BattleEnd" or eventname == "BattleSkip" then
        local mainSlgMgr = require "main_slg_mgr"
        mainSlgMgr.EnterBattleState(false)

        AutoPlayBattleRecord()
        local stageType = _stageType or lastStageType
        --切出战斗结算时设置lastStageType为结算战斗类型,
        if switchOutEnd then
            lastStageType = _stageType or lastStageType
        end

        local player = GetBattlePlayer()
        M.change_avatar = M.change_avatar or {}
        ---print("M.change_avatar",json.encode(M.change_avatar))
        for id, v in pairs(M.change_avatar) do
            Change_avatar(id, 1)
        end
        M.change_avatar = {}

        event.Trigger(event.BATTLE_END)

        ClearBattlePool()

        skiped = eventname == "BattleSkip"
        -- print("OnBattleEnd")


        local battle_data = require "battle_data"
        battle_data.ResetSummonedData()
        -- print("battle_data.winner:",battle_data.winner,"battle_data.loser:",battle_data.loser)
        local victory = battle_data.IsVictory()
        local rewards = battle_data.rewards
        local score = battle_data.scores

        UnregisterHandler(OnBattleEvent)
        local net = require "net"
        battle_data.currentBattleCompleteTotalTime = 0

        if camp_trial_data.GetIsTrialBattle(battleType) then
            local ui_camp_trial_mgr = require "ui_camp_trial_mgr"
            print("ui_camp_trial_mgr.TrialBattleEnd")
            time_scale_mgr.ResetTimeScale()
            ui_camp_trial_mgr.TrialBattleEnd(victory, rewards)
            return
        end

        --print("onbattleevent!!!!!!", stageType, battle_series_manager.IsLastWave(), " victory ", victory)
        if battle_series_manager.IsInSeriesBattle(stageType) and
                not battle_series_manager.IsLastWave() and victory == true --and not isReplay 
                and net.IsConnected() and not skiped then
            --连续战斗如果断线，则不请求下场，退出战斗
            if player.ClearBattleRightPals then
                player:ClearBattleRightPals()
            end
            --避免下回合死亡角色的血条被重置，需要跳过重置
            for i = 0, 5 do
                local heroData = battle_data.GetHeroByPos(i)
                if heroData and heroData.statistics and heroData.statistics.isDead then
                    local role = player:GetRole(heroData.palID)
                    if role then
                        if role.node and role.node.Hud.SetIsSkipResetHud then
                            role.node.Hud:SetIsSkipResetHud(true)
                        end
                    end
                end
            end
            --print("清空敌方数据！！！！！！！")
            local wave = battle_series_manager.GetWave()

            --如果不是最后一场连续战斗，需要请求战斗结果，才可以继续请求下一场战斗
            local battle_message = require "battle_message"

            battle_message.C2SBattleResult(battle_series_manager.GetCurrentBattleId())

            --print("开始请求下一场连续战斗！！！！！！！, 波次：", wave + 1, "battleid", battle_series_manager.GetCurrentBattleId(), "victory:",victory)
            battle_series_manager.PlayNextWaveBattle(stageType)
            return
        end

        if battle_series_manager.IsSeriesBattle(stageType) and not isReplay then
            --连续战斗结束后，将资源下载上限设置回缺省值
            local asset_load_mgr = require "asset_load_mgr"
            asset_load_mgr.SetMaxLoadingCount(2)

            --print("连续战斗结束！！！！！！！, victory:",victory, "battleid", battle_series_manager.GetCurrentBattleId())
            battle_message.C2SBattleResult(battle_series_manager.GetCurrentBattleId())
            --胜利或者失败后关闭界面才清除
            -- battle_series_manager.ClearBattleAssets()
            -- battle_series_manager.Clear()
        end

        --连续战斗跳过,计算最后一波战斗数据，覆盖重置参数
        if battle_series_manager.IsInSeriesBattle(stageType) and skiped then
            battle_series_manager.SetLastBattleData()
            victory = battle_data.IsVictory()
            rewards = battle_data.rewards
            score = battle_data.scores
        end


        --战斗播放结束，移除多战斗战报缓存数据
        local battle_switch_manager = require "battle_switch_manager"
        if battle_switch_manager.IsSwitchable(stageType) then
            battle_switch_manager.OnBattleEnd(stageType)
            if battle_data.battleReports[stageType] then
                --battle_data.battleReports[stageType] = nil
            end
        end

        ui_window_mgr:UnloadModule("ui_in_battle")
        if ui_window_mgr:IsModuleShown("ui_halo_detail") then
            ui_window_mgr:UnloadModule("ui_halo_detail")
        end
        if ui_window_mgr:IsModuleShown("ui_halo_tips") then
            ui_window_mgr:UnloadModule("ui_halo_tips")
        end
        -- if ui_window_mgr:IsModuleShown("ui_secret_place_relic") then
        --     ui_window_mgr:UnloadModule("ui_secret_place_relic")
        -- end
        -- if ui_window_mgr:IsModuleShown("ui_secret_place_package") then
        --     ui_window_mgr:UnloadModule("ui_secret_place_package")
        -- end
        -- if ui_window_mgr:IsModuleShown("ui_hero_challenge_pro") then
        --     ui_window_mgr:UnloadModule("ui_hero_challenge_pro")
        -- end
        time_scale_mgr.ResetTimeScale()
        -- 战斗回放（竞技场结算页面回放也要显示结算UI）
        if isReplay and battleType ~= nil then
            --if (battleType == common_pb.CrystalCrown or battleType == common_pb.AdvanceSingle or battleType == common_pb.Advance or battleType == common_pb.TopRace) and skipResult == false then
            if (battleType == common_pb.CrystalCrown
                    or battleType == common_pb.TopRace
                    or battleType == common_pb.WMTopRace
                    or battleType == common_pb.BossSlave) and skipResult == false then
                skipResult = true
            else
                if finalizeBattle[common_pb.Arena] ~= nil then
                    FinalizeBattleRunning(victory, true)
                    finalizeBattle[common_pb.Arena] = nil
                    battle_data.SetRoleID()
                end
                if finalizeBattle[common_pb.Carriage] ~= nil then
                    FinalizeBattleRunning(victory, true)
                    finalizeBattle[common_pb.Carriage] = nil
                    battle_data.SetRoleID()
                end
                if finalizeBattle[common_pb.Cutscene] ~= nil then
                    finalizeBattle[common_pb.Cutscene]()
                    finalizeBattle[common_pb.Cutscene] = nil
                end
                if finalizeBattle[common_pb.WeekendArena] ~= nil then
                    finalizeBattle[common_pb.WeekendArena]()
                    finalizeBattle[common_pb.WeekendArena] = nil
                end
                local ui_point_slider_controller = require "ui_point_slider_controller"
                ui_point_slider_controller.ExitSaveMode(true)
                player:ClearBattle()
                ClearGameObjectPool()
                return
            end
        end
        util.DelayCall(2, function()
            -- 只有主线剧情才需要发
            if lastStageType == common_pb.GameGoal then
                battle_message.SendBattleEndNTF()
            end
        end)
        --针对跳过战斗后，在结算面板打开前打开了其他面板，就不需要再次打开结算面板
        --print("针对跳过战斗后，在结算面板打开前打开了其他面板，就不需要再次打开结算面板",battle_message.GetBattleResult(),battle_data.skipBattle,"lastStageType",lastStageType)
        if lastStageType == common_pb.GameGoal then
            if not battle_message.GetBattleResult() then
                if finalizeBattle[common_pb.GameGoal] ~= nil then
                    ForceCall()
                    finalizeBattle[common_pb.GameGoal] = nil
                    battle_data.SetRoleID()
                end
                battle_message.SetBattleResult(true)

                local battle_data = require "battle_data"
                battle_data.Clear()
                ClearGameObjectPool()
                return
            end
        end
        local arenaType = battleType
        --[[打开结算面板]]
        -- 这是单人竞技场
        --if lastStageType == common_pb.Arena and battleType == common_pb.CrystalCrown or battleType == common_pb.AdvanceSingle then
        if lastStageType == common_pb.Arena and battleType == common_pb.CrystalCrown then
            local ui_match_battle_single_win_lose = require "ui_match_battle_single_win_lose"
            if battleType == common_pb.CrystalCrown and not isReplay then
                battle_event_report.ReportArenaBattleInfo(common_pb.CrystalCrown, battleID)
            end
            local heros = battle_data.GetHeros()
            ui_match_battle_single_win_lose.SetWinLoseRoleId(battle_data.attacker, battle_data.defender)
            for i = 0, 14 do
                local hero = heros[i]
                if hero ~= nil then
                    ui_match_battle_single_win_lose.SetHeroData(i, hero)
                end
            end
            ui_match_battle_single_win_lose.SetRewType(true)
            ui_match_battle_single_win_lose.SetCloseCallBack(function()

                if #finallizeCallBack > 0 then
                    for i, v in ipairs(finallizeCallBack) do
                        v()
                        finallizeCallBack[i] = nil
                    end

                    player:ClearBattle()
                    ClearGameObjectPool()
                    if not returnArean then
                        returnArean = true
                        return
                    end
                end
                if finalizeBattle[common_pb.Arena] ~= nil then
                    FinalizeBattleRunning(victory, true)
                    finalizeBattle[common_pb.Arena] = nil
                    battle_data.SetRoleID()

                end
                if arenaType == common_pb.CrystalCrown then
                    local ui_single_challenge = require "ui_single_challenge"
                    ui_single_challenge.InitialOffsetStep()
                    local net_arena_module = require "net_arena_module"
                    net_arena_module.InitNormalRankInfo = true
                    net_arena_module.Send_ARENA_ENTER(common_pb.CrystalCrown)
                    if net_arena_module.isLevelUp then
                        -- local ui_single_level_up = require "ui_single_level_up"
                        -- ui_single_level_up.SetInputParam(net_arena_module.levelUpId)
                        -- ui_window_mgr:ShowModule("ui_single_level_up")
                        net_arena_module.isLevelUp = false
                    end
                end
                player:ClearBattle()
                ClearGameObjectPool()
            end)

            local net_arena_module = require "net_arena_module"
            local battleRecord = net_arena_module.ArenaBattleRecord or nil
            if net_arena_module.ArenaBattleBattleId and net_arena_module.ArenaBattleBattleId == battleID and battleRecord then
                local rsp = {
                    arenaType = 1,
                    winnerScoreChange = battleRecord.winnerInfo.scoreChange,
                    loserScoreChange = battleRecord.loserInfo.scoreChange,
                    winnerCurScores = battleRecord.winnerInfo.curScore - battleRecord.winnerInfo.scoreChange,
                    loserCurScores = battleRecord.loserInfo.curScore - battleRecord.loserInfo.scoreChange,
                    curScoreHide = false,
                    hideLevelUp = true,
                }
                local rival = victory and battleRecord.loserInfo or battleRecord.winnerInfo
                local player = victory and battleRecord.winnerInfo or battleRecord.loserInfo
                ui_match_battle_single_win_lose.SetArenaRsp(rsp)
                ui_match_battle_single_win_lose.SetRivalInfo(rival.playerInfo, "RivalInfo.teamName", player.playerInfo)
            end
            ui_match_battle_single_win_lose.SetVictory(victory)
            -- print("battleID:",battleID,"opponentName:",opponentName)
            ui_match_battle_single_win_lose.SetBattleRecordData(battleID, opponentName)
            local arena_data = require "arena_data"
            arena_data.ReportBattleResult(arenaType, victory)
            local InArena = windowMgr:IsModuleShown("ui_match_senior_single_rank") or windowMgr:IsModuleShown("ui_match_single_rank") or isReplay
            -- if InArena then
            windowMgr:ShowModule("ui_match_battle_single_win_lose", nil, function()
                if  returnArean then
                    local arena_data = require "arena_data"
                    if arena_data.getSingleIntoBattleType() == arena_data.SingleIntoBattleType.Challenge or arena_data.getSingleIntoBattleType() == arena_data.SingleIntoBattleType.ChallengeReplay then
                        windowMgr:ShowModule("ui_match_single_rank")
                        if not net_arena_module.isLevelUp and not ui_window_mgr:IsModuleShown("ui_single_level_up") and not ui_window_mgr:IsModuleShown("ui_in_battle") then
                            ---策划需求关闭此界面，战斗回来
                            --ui_window_mgr:ShowModule("ui_single_challenge")
                        end
                    elseif arena_data.getSingleIntoBattleType() == arena_data.SingleIntoBattleType.History then
                        windowMgr:ShowModule("ui_match_single_rank")
                        ui_window_mgr:ShowModule("ui_match_battle_history")
                    end
                end
               
                arena_data.SetSingleIntoBattleType(arena_data.SingleIntoBattleType.None)
            end)
            -- end
            local ui_match_win_lose = require "ui_match_battle_single_win_lose"
            if rewards ~= nil and #rewards > 0 then
                ui_match_win_lose.SetRewardData(rewards)
            end
            battle_data.rewards = {}
            battleType = nil
            net_arena_module.ArenaBattleRecord = nil
            net_arena_module.ArenaBattleHolderid = nil
            net_arena_module.ArenaBattleBattleId = 0
            return
        end
        if (lastStageType == common_pb.TopRace and battleType == common_pb.TopRace) or
                (lastStageType == common_pb.WMTopRace and battleType == common_pb.WMTopRace) then
            -- print("show ui_top_game_win_lose!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
            local ui_top_game_win_lose = require "ui_top_game_win_lose"

            local heros = battle_data.GetHeros()
            for i = 0, 14 do
                local hero = heros[i]
                if hero ~= nil then
                    ui_top_game_win_lose.SetHeroData(i, hero)
                end
            end
            ui_top_game_win_lose.SetCloseCallBack(function()

                if #finallizeCallBack > 0 then
                    for i, v in ipairs(finallizeCallBack) do
                        v()
                        finallizeCallBack[i] = nil
                    end
                end
                if finalizeBattle[common_pb.TopRace] ~= nil then
                    finalizeBattle[common_pb.TopRace] = nil
                    battle_data.SetRoleID()
                end
                player:ClearBattle()
                ClearGameObjectPool()
            end)

            ui_top_game_win_lose.SetBattleRecordData(battleID, opponentName)

            windowMgr:ShowModule("ui_top_game_win_lose")

            battle_data.rewards = {}
            battleType = nil
            return
        end
        event.Trigger(event.ILLUSION_TOWER_BATTLE_END)
        if lastStageType and (lastStageType == common_pb.BrokenSpaceTime or lastStageType == common_pb.LeaActivityBoss) then
            local ui_win = nil
            local ui_win_name = ""
            if lastStageType == common_pb.LeaActivityBoss then
                -- ui_win = require "ui_sociaty_active_boss_win"
                -- ui_win_name = "ui_sociaty_active_boss_win"
            else
                -- ui_win = require "ui_domination_win"
                -- ui_win_name = "ui_domination_win"
            end
            if rewards ~= nil and #rewards > 0 then
                ui_win.SetRewardData(rewards)
                battle_data.rewards = {}
            end
            local heros = battle_data.GetHeros()
            for i = 0, 14 do
                local hero = heros[i]
                if hero ~= nil then
                    ui_win.SetHeroData(i, hero)
                end
            end
            ui_win.SetBattleRecordData(battleID, opponentName)
            local dominationWin = ui_window_mgr:ShowModule(ui_win_name)
            dominationWin.OnOkEvent = function()
                dominationWin.onOkEvent = nil
                player:ClearBattle()
                ClearGameObjectPool()
                battle_data.skipBattle = false
                ui_window_mgr:UnloadModule("ui_in_battle")
                if ui_window_mgr:IsModuleShown("ui_halo_detail") then
                    ui_window_mgr:UnloadModule("ui_halo_detail")
                end
                if ui_window_mgr:IsModuleShown("ui_halo_tips") then
                    ui_window_mgr:UnloadModule("ui_halo_tips")
                end
                time_scale_mgr.ResetTimeScale()
                if finalizeBattle[lastStageType] ~= nil and not battle_data.skipBattle then
                    FinalizeBattleRunning(victory, true)
                    finalizeBattle[lastStageType] = nil
                    battle_data.SetRoleID()
                    event.Trigger(event.HALL_SCENE_SILDING, true)
                end
                finalizeBattle[lastStageType] = nil
                if #finallizeCallBack > 0 then
                    for i, v in ipairs(finallizeCallBack) do
                        v()
                        finallizeCallBack[i] = nil
                    end
                end
            end
        else
            if battle_data.stageType == common_pb.BrokenSpaceTime or battle_data.stageType == common_pb.LeaActivityBoss then
                local ui_win_name = ""
                local ui_win = nil
                if battle_data.stageType == common_pb.LeaActivityBoss then
                    -- ui_win = require "ui_sociaty_active_boss_win"
                    -- ui_win_name = "ui_sociaty_active_boss_win"
                else
                    -- ui_win = require "ui_domination_win"
                    -- ui_win_name = "ui_domination_win"
                end
                if rewards ~= nil and #rewards > 0 then
                    ui_win.SetRewardData(rewards)
                    battle_data.rewards = {}
                end
                local heros = battle_data.GetHeros()
                for i = 0, 14 do
                    local hero = heros[i]
                    if hero ~= nil then
                        ui_win.SetHeroData(i, hero)
                    end
                end
                ui_win.SetBattleRecordData(battleID, opponentName)
                local dominationWin = ui_window_mgr:ShowModule(ui_win_name)
                dominationWin.OnOkEvent = function()
                    dominationWin.OnOkEvent = nil
                    player:ClearBattle()
                    ClearGameObjectPool()
                    battle_data.skipBattle = false
                    ui_window_mgr:UnloadModule("ui_in_battle")
                    if ui_window_mgr:IsModuleShown("ui_halo_detail") then
                        ui_window_mgr:UnloadModule("ui_halo_detail")
                    end
                    if ui_window_mgr:IsModuleShown("ui_halo_tips") then
                        ui_window_mgr:UnloadModule("ui_halo_tips")
                    end
                    time_scale_mgr.ResetTimeScale()

                    if finalizeBattle[battle_data.stageType] ~= nil and not battle_data.skipBattle then
                        FinalizeBattleRunning(victory, true)
                        finalizeBattle[battle_data.stageType] = nil
                        battle_data.SetRoleID()
                        event.Trigger(event.HALL_SCENE_SILDING, true)
                    end
                    finalizeBattle[battle_data.stageType] = nil
                    if #finallizeCallBack > 0 then
                        for i, v in ipairs(finallizeCallBack) do
                            v()
                            finallizeCallBack[i] = nil
                        end
                    end
                end
            else
                local InArena = windowMgr:IsModuleShown("ui_match_senior_single_rank") or windowMgr:IsModuleShown("ui_match_single_rank")
                if victory then
                    ShowBattleVictory(player, rewards, score, InArena, victory, battleType, nil, switchOutEnd)
                else
                    ShowBattleDefeat(player, rewards, score, InArena, victory, battleType, nil, switchOutEnd)
                end
            end
        end

        if not switchOutEnd and not isReplay then
            --可切出的战斗在正常结束时也要请求奖励
            local report = battle_data.battleReports[stageType]
            if report and report.battleID then
                battle_message.C2SBattleResult(report.battleID)
            end
        end
    end
end

function ShowBattleVictory(player, rewards, score, InArena, victory, battleType, noParam, switchOutEnd)
    local stageType = battleType or battle_data.stageType
    if not noParam then
        local ui_battle_victory = require "ui_battle_victory_new"
        SetBattleRewards(ui_battle_victory, rewards, score)
        battle_data.scores = 0
        SetBattleHeroes(ui_battle_victory)

        ui_battle_victory.SetShowType(stageType)
        ui_battle_victory.SetBattleRecordData(battleID, opponentName)

        local scene_manager_instance = scene_manager.instance()
        ui_battle_victory.SetSwitchMode(switchOutEnd and not battle_data.skipBattle and scene_manager_instance:IsNotInBattleScene())
    end
    local battlevictory = nil
    if not InArena then
        battlevictory = ui_window_mgr:ShowModule("ui_battle_victory_new")
        battlevictory.onOkEvent = function()
            battlevictory.onOkEvent = nil
            -- ui_window_mgr:UnloadModule("ui_battle_victory")
            player:ClearBattle()
            ClearGameObjectPool()
            -- Camera.main.transform.position = cameraPos
            -- cameraPos = nil
            battle_data.skipBattle = false
            ui_window_mgr:UnloadModule("ui_in_battle")
            if ui_window_mgr:IsModuleShown("ui_halo_detail") then
                ui_window_mgr:UnloadModule("ui_halo_detail")
            end
            if ui_window_mgr:IsModuleShown("ui_halo_tips") then
                ui_window_mgr:UnloadModule("ui_halo_tips")
            end
            time_scale_mgr.ResetTimeScale()

            if finalizeBattle[stageType] ~= nil and not battle_data.skipBattle then
                --print("ShowBattleVictory设置finalizeBattle！！！！！！",  victory)
                FinalizeBattleRunning(victory, true, stageType)
                finalizeBattle[stageType] = nil
                battle_data.SetRoleID()
                event.Trigger(event.HALL_SCENE_SILDING, true)
                battle_data.rewards = {}
            end
            finalizeBattle[stageType] = nil
            if #finallizeCallBack > 0 then
                for i, v in ipairs(finallizeCallBack) do
                    v()
                    finallizeCallBack[i] = nil
                end
            end
        end
    end
    --ui_window_mgr:ShowModule("ui_battle_victory")
    
end

function ShowBattleDefeat(player, rewards, score, InArena, victory, battleType, noParam, switchOutEnd)
    local stageType = battleType or battle_data.stageType
    if not noParam then
        local ui_battle_defeat = require "ui_battle_defeat_new"
        SetBattleRewards(ui_battle_defeat, rewards, score)
        battle_data.scores = 0
        SetBattleHeroes(ui_battle_defeat)

        ui_battle_defeat.SetStageType(stageType)
        ui_battle_defeat.SetBattleRecordData(battleID, opponentName)

        local scene_manager_instance = scene_manager.instance()
        ui_battle_defeat.SetSwitchMode(switchOutEnd and not battle_data.skipBattle and scene_manager_instance:IsNotInBattleScene())
    end
    if not InArena then
        local battledefeat = ui_window_mgr:ShowModule("ui_battle_defeat_new")
        battledefeat.onOkEvent = function(buttonId, params)
            battledefeat.onOkEvent = nil
            if not util.IsObjNull(player) then
                player:ClearBattle()
            end
            local showUI = true
            showUI = buttonId == 0

            battle_data.skipBattle = false
            ui_window_mgr:UnloadModule("ui_in_battle")
            if ui_window_mgr:IsModuleShown("ui_halo_detail") then
                ui_window_mgr:UnloadModule("ui_halo_detail")
            end
            if ui_window_mgr:IsModuleShown("ui_halo_tips") then
                ui_window_mgr:UnloadModule("ui_halo_tips")
            end
            time_scale_mgr.ResetTimeScale()

            if finalizeBattle[stageType] ~= nil and not battle_data.skipBattle then
                --print("ShowBattleDefeat设置finalizeBattle！！！！！！",  victory)
                if buttonId == 9 then 
                    battle_data.ShowMainPanel = true
                end
                FinalizeBattleRunning(victory, showUI, stageType)
                battle_data.SetRoleID()
                finalizeBattle[stageType] = nil

                event.Trigger(event.HALL_SCENE_SILDING, true)
                battle_data.rewards = {}
            end
            finalizeBattle[stageType] = nil

            local jumpHeroFunc = function(_needOpenHeorBase)
                local gw_hero_mgr = require "gw_hero_mgr"
                gw_hero_mgr.OpenHeroBagWindow(function()
                    if _needOpenHeorBase then
                        local ui_gw_hero_list_controller = require "ui_gw_hero_list_controller"
                        local heroList = ui_gw_hero_list_controller.GetAllHeroData()
                        local isRed = 0
                        local heroSid = nil
                        if heroList then
                            for k, v in pairs(heroList) do
                                if isRed > 0 then
                                    break
                                end
                                for i = 0, 3 do
                                    isRed = gw_hero_mgr.GetHeroEquipItemRed(v.heroSid, i)
                                    if isRed > 0 then
                                        heroSid = v.heroSid
                                        break
                                    end
                                end
                            end
                        end
                        if heroSid then
                            gw_hero_mgr.OpenHeroDetailUIBySid(heroSid)
                        end
                    end
                end)
                OnNextWindowShowed()
                for i, v in ipairs(finallizeCallBack) do
                    finallizeCallBack[i] = nil
                end
            end
            local flag = ""
            if buttonId == 0 then
                if #finallizeCallBack > 0 then
                    for i, v in ipairs(finallizeCallBack) do
                        v()
                        finallizeCallBack[i] = nil
                    end
                end
            elseif buttonId == 1 then
                flag = "slg_abh"
            elseif buttonId == 2 then
                flag = "slg_abi"
            elseif buttonId == 3 then
                flag = "slg_aax"
            elseif buttonId == 4 then
                flag = "slg_abj"
            elseif buttonId == 5 then
                flag = "slg_abk"
            elseif buttonId == 6 then
                flag = "slg_abl"
            elseif buttonId == 7 then
                flag = "slg_abm"
            elseif buttonId == 8 then
                if battle_data.ReturnSelectFunc then
                    battle_data.ReturnSelectFunc()
                    --event.Trigger(event.GW_OPEN_SAND_MAIN)
                    OnNextWindowShowed() 
                end
            elseif buttonId == 9 then
                flag = "slg_alg"
            end
            local module_jumping = require "module_jumping"
            if not string.empty(flag) then
                module_jumping.Jump(flag,params)
                --event.Trigger(event.GW_OPEN_SAND_MAIN)
                OnNextWindowShowed()
            end
            ClearGameObjectPool()
        end
    end
    --ui_window_mgr:ShowModule("ui_battle_defeat")
    
end

function OpenLobby()
    windowMgr:ShowModule("ui_lobby")
end

function ClearBattlePool()
    SetBattlePoolActive(false)
end

function SetBattlePoolActive(bActive)
    local objectPool = GameObjectPool.GetPrefabPool()
    if objectPool == nil then
        return
    end

    local const = require "const"
    local poolTypeSet = const.PoolTypeSet

    local i = 1
    for i = 1, #poolTypeSet do
        local poolType = poolTypeSet[i]
        if poolType ~= nil then
            objectPool:SetActive(poolType, bActive)
            if not bActive then
                objectPool:ClearPool(poolType)
            end
        end
    end

    if not bActive then
        GameObjectPool.ClearAssetPrefab()
    end
end

function OnMultiTbsBattleEvent(eventname, switchOutEnd, _stageType)
    if eventname == "BattleBegin" then
        local mainSlgMgr = require "main_slg_mgr"
        mainSlgMgr.EnterBattleState(true)
    end

    if eventname == "BattleEnd" or eventname == "BattleSkip" then
        local mainSlgMgr = require "main_slg_mgr"
        mainSlgMgr.EnterBattleState(false)

        AutoPlayBattleRecord()
        local stageType = _stageType or lastStageType
        local battle_switch_manager = require "battle_switch_manager"
        if battle_switch_manager.IsSwitchable(stageType) then
            battle_switch_manager.OnBattleEnd(stageType)
        end

        ClearBattlePool()
        battle_player_assistant.StopFPSCounter()
        skiped = eventname == "BattleSkip"
        local battle_data = require "battle_data"
        local player = GetBattlePlayer()

        battle_data.ResetSummonedData()
        UnregisterHandler(OnMultiTbsBattleEvent)

        --@region 判断胜负
        local victory = false
        -- TODO 修改 先处理我方和敌方
        if player_mgr.GetPlayerRoleID() == battle_data.winner then
            victory = true
        end
        -- 获取当前场次胜负，如果是最后一场，显示总胜负
        --@endregion


        ui_window_mgr:UnloadModule("ui_in_battle")
        if ui_window_mgr:IsModuleShown("ui_halo_detail") then
            ui_window_mgr:UnloadModule("ui_halo_detail")
        end
        if ui_window_mgr:IsModuleShown("ui_halo_tips") then
            ui_window_mgr:UnloadModule("ui_halo_tips")
        end
        time_scale_mgr.ResetTimeScale()
        local ui_maze_multi_battle_result = require "ui_maze_multi_battle_result"

        --针对跳过战斗后，在结算面板打开前打开了其他面板，就不需要再次打开结算面板，这里是用来处理什么？
        --[[打开结算面板，最后一场显示总结算]]
        local ui_multi_battle_result = require "ui_multi_battle_result"
        local ui_multi_win_lose = require("ui_multi_win_lose")
        if not isReplay then
            if battle_message.HasNextTbs() then
                if stageType ~= common_pb.Maze and stageType ~= common_pb.Legend then
                    ui_multi_win_lose.SetHeroData(battle_data.GetHeros())
                end
            end
            if stageType ~= common_pb.Maze and stageType ~= common_pb.Legend then
                ui_multi_battle_result.SetHeroData(battle_data.GetHeros())
            end
        end
        local rewards = battle_data.rewards
        local score = battle_data.scores
        --local InArena = windowMgr:IsModuleShown("ui_match_senior_single_rank") or windowMgr:IsModuleShown("ui_match_single_rank")
        local InArena = windowMgr:IsModuleShown("ui_match_single_rank")
        if battle_message.HasNextTbs() then
            if stageType == common_pb.Maze or stageType == common_pb.GameGoal
                    or stageType == common_pb.EquipEctypeZhuanShu then
                local tbsCount = battle_message.GetTbsCount()
                local hasNext = tbsCount >= 1
                local results, allVictory = ui_maze_multi_battle_result.GetMultiBattleResult()
                local count = results and #results or 0
                --TODO:星际迷航多队战斗，第一队输了则直接打开结算面板，第一队赢了才播放第二队战斗，然后再弹出结算面板
                local ui_maze_multi_battle_result = require "ui_maze_multi_battle_result"
                local heros = battle_data.GetHeros()
                for i = 0, 14 do
                    local hero = heros[i]
                    if hero ~= nil then
                        ui_maze_multi_battle_result.SetMultiHeroData(count + 1, i, hero)
                    end
                end
                ui_maze_multi_battle_result.SetMultiBattleResult(player, victory, score, battleID, opponentName, rewards, count + 1)
                --if (victory and hasNext) then
                if #battle_data.lastRewards == 0 then
                    battle_data.lastRewards = battle_data.rewards
                end
                if (hasNext) then
                    -- 播放下一场
                    --print("播放下一场==============================")
                    battle_data.Clear()
                    battle_message.MultiTbsNext()
                end
            elseif stageType == common_pb.Legend then
                local ui_legend_championships_battle_result = require "ui_legend_championships_battle_result"

                local tbsCount = battle_message.GetTbsCount()
                local hasNext = tbsCount >= 1

                local ui_legend_championships_battle_result = require "ui_legend_championships_battle_result"
                local heros = battle_data.GetHeros()

                local legend_championships_mgr = require "legend_championships_mgr"
                local leftInfo, rightInfo = legend_championships_mgr.GetCacheBattleInfo()
                local score = legend_championships_mgr.GetCacheScore()
                local battleIDs = legend_championships_mgr.GetCacheBattleIDs()

                if not isReplay then
                    ui_legend_championships_battle_result.AddBattleData(heros, battle_data.winner, battle_data.loser, nil, lastBattleID)
                end

                local show = true
                if battle_data.skipBattle and ui_window_mgr:IsModuleShown("ui_legend_championships_battle_result") then
                    --因为跳过战斗要连续解战报，可能多次打开
                    show = false
                end
                if show then
                    local win = ui_window_mgr:ShowModule("ui_legend_championships_battle_result")
                    win:SetInputParam(leftInfo, rightInfo, score and score[1], score and score[2], lastBattleID)
                end

                ui_legend_championships_battle_result.SetOKEvent(function()
                    if hasNext then
                        -- 播放下一场
                        player:ClearBattle()
                        SetIsReplay(false)
                        if not battle_data.skipBattle then
                            ui_window_mgr:UnloadModule("ui_legend_championships_battle_result")
                        end
                        battle_data.Clear()
                        ClearGameObjectPool()
                        battle_message.MultiTbsNext()
                    end
                end)
                if battle_data.skipBattle then
                    --跳过战斗自动打开下一场
                    ui_legend_championships_battle_result.TriggerOKEvent()
                end
                --[[elseif stageType == common_pb.WeekendArena then
                    local weekend_arena_mgr = require "weekend_arena_mgr"
                    weekend_arena_mgr.SetBattleRound()
                    local ui_weekend_arena_battle_result = require "ui_weekend_arena_battle_result"
                    local tbsCount = battle_message.GetTbsCount()
                    local hasNext = tbsCount >= 1
    
                    local heros = battle_data.GetHeros()
                    if not isReplay then
                        ui_weekend_arena_battle_result.AddBattleData(heros, battle_data.winner, battle_data.loser)
                    end
    
                    local leftInfo, rightInfo = weekend_arena_mgr.GetCacheBattleInfo()
                    local myScore,enemyScore = weekend_arena_mgr.GetBattleScore()
    
                    local show = true
                    if battle_data.skipBattle and ui_window_mgr:IsModuleShown("ui_weekend_arena_battle_result") then
                        --因为跳过战斗要连续解战报，可能多次打开
                        show = false
                    end
                    if show then
                        local win = ui_window_mgr:ShowModule("ui_weekend_arena_battle_result")
                        win:SetInputParam(leftInfo, rightInfo, myScore, enemyScore)
                    end
    
                    ui_weekend_arena_battle_result.SetOKEvent(function()
                        local tbsCount = battle_message.GetTbsCount()
                        hasNext = tbsCount >= 1
                        if hasNext then
                            -- 播放下一场
                            player:ClearBattle()
                            SetIsReplay(false)
                            if not battle_data.skipBattle then
                                ui_window_mgr:UnloadModule("ui_weekend_arena_battle_result")
                            end
                            battle_data.Clear()
                            battle_message.MultiTbsNext()
                        else
                            local hadNextRound = weekend_arena_mgr.HadNextRound()
                            if hadNextRound then
                                player:ClearBattle()
                                SetIsReplay(false)
                                if not battle_data.skipBattle then
                                    ui_window_mgr:UnloadModule("ui_weekend_arena_battle_result")
                                end
                                battle_data.Clear()
                                battle_message.MultiTbsNext()
                            else
                                local battle_manager = require "battle_manager"
                                local player = battle_manager.GetBattlePlayer()
                                if player then
                                    player:ClearBattle()
                                end
                                local battle_message = require "battle_message"
                                battle_message.SetIsMultiTbs(false)
                                battle_message.ClearMultiTbs()
                                local ui_weekend_arena_battle_result = require "ui_weekend_arena_battle_result"
                                ui_weekend_arena_battle_result.ResetBattleData()
                                ui_window_mgr:UnloadModule("ui_weekend_arena_battle_result")
                                ui_window_mgr:ShowModule("ui_matchplace_entrance",function ()
                                    ui_window_mgr:ShowModule('ui_weekend_arena')
                                end)
                            end
                        end
                        ClearGameObjectPool()
                    end)
                    if battle_data.skipBattle then
                        --跳过战斗自动打开下一场
                        ui_weekend_arena_battle_result.TriggerOKEvent()
                    end]]
            else

            end
        else
            if stageType == common_pb.Maze or stageType == common_pb.GameGoal
                    or stageType == common_pb.EquipEctypeZhuanShu then
                ui_maze_multi_battle_result.SetBattleType(stageType)
                if not isReplay or battle_data.skipBattle then
                    local results, allVictory = ui_maze_multi_battle_result.GetMultiBattleResult()
                    local count = battle_message.GetCurBattleIndexByBattle()--results and #results or 0
                    local heros = battle_data.GetHeros()
                    for i = 0, 14 do
                        local hero = heros[i]
                        if hero ~= nil then
                            ui_maze_multi_battle_result.SetMultiHeroData(count, i, hero)
                        end
                    end
                    ui_maze_multi_battle_result.SetMultiBattleResult(player, victory, score, battleID, opponentName, rewards, count)
                end
                local isMultiTbs = IsMultiTbsOfMultipleBattle(stageType)
                --print("OnMultiTbsBattleEvent!!!! isMultiTbs:", isMultiTbs, " stageType:", stageType)
                if isMultiTbs then
                    local teamIndex = 1
                    local results, allVictory = ui_maze_multi_battle_result.GetMultiBattleResult()
                    if results then
                        for i, v in ipairs(results) do
                            if not v.isVictory then
                                teamIndex = i
                            end
                        end
                    end
                    ui_maze_multi_battle_result.SwitchPage(teamIndex)
                else
                    if (victory) then
                        ShowBattleVictory(player, rewards, score, InArena, victory, stageType, nil, switchOutEnd)
                    elseif (not victory) then
                        ShowBattleDefeat(player, rewards, score, InArena, victory, stageType, nil, switchOutEnd)
                    end
                end
            elseif stageType == common_pb.Legend then
                local ui_legend_championships_battle_result = require "ui_legend_championships_battle_result"
                local heros = battle_data.GetHeros()
                if not isReplay then
                    ui_legend_championships_battle_result.AddBattleData(heros, battle_data.winner, battle_data.loser, true, lastBattleID)
                end

                local legend_championships_mgr = require "legend_championships_mgr"
                local leftInfo, rightInfo = legend_championships_mgr.GetCacheBattleInfo()
                local score = legend_championships_mgr.GetCacheScore()

                local win = ui_window_mgr:ShowModule("ui_legend_championships_battle_result")
                win:SetInputParam(leftInfo, rightInfo, score and score[1], score and score[2], lastBattleID)

                ui_legend_championships_battle_result.SetOKEvent(function()
                    player:ClearBattle()
                    local battle_message = require "battle_message"
                    battle_message.SetIsMultiTbs(false, common_pb.Legend)
                    battle_message.ClearMultiTbs()
                    ui_window_mgr:UnloadModule("ui_legend_championships_battle_result")
                    ui_legend_championships_battle_result.ResetBattleData()
                    if isReplay or not battle_data.skipBattle then
                        --[[local w = ui_window_mgr:ShowModule("ui_legend_championships_base")
                        w:SetInputParam(2)]]
                        legend_championships_mgr.ShowWindow()
                    end
                    if #finallizeCallBack > 0 then
                        for i, v in ipairs(finallizeCallBack) do
                            v()
                            finallizeCallBack[i] = nil
                        end
                    end
                    ClearGameObjectPool()
                end)
                --[[elseif stageType == common_pb.WeekendArena then
                    -- 回放
                    local ui_weekend_arena_battle_result = require "ui_weekend_arena_battle_result"
                    local tbsCount = battle_message.GetTbsCount()
                    local hasNext = tbsCount >= 1
                    local weekend_arena_mgr = require "weekend_arena_mgr"
    
                    weekend_arena_mgr.SetBattleRound() -- 记录回合数
    
                    local heros = battle_data.GetHeros()
    
                    local leftInfo, rightInfo = weekend_arena_mgr.GetCacheBattleInfo()
                    local myScore,enemyScore = weekend_arena_mgr.GetBattleScore()
    
                    local show = true
                    if battle_data.skipBattle and ui_window_mgr:IsModuleShown("ui_weekend_arena_battle_result") then
                        --因为跳过战斗要连续解战报，可能多次打开
                        show = false
                    end
                    if show then
                        local win = ui_window_mgr:ShowModule("ui_weekend_arena_battle_result")
                        win:SetInputParam(leftInfo, rightInfo, myScore, enemyScore)
                    end
                
                    if not isReplay then
                        local hadNextRound = weekend_arena_mgr.HadNextRound()
                        if hadNextRound then
                            ui_weekend_arena_battle_result.AddBattleData(heros, battle_data.winner, battle_data.loser,false)
                        else
                            ui_weekend_arena_battle_result.AddBattleData(heros, battle_data.winner, battle_data.loser,true)
                        end
                    end
    
                    ui_weekend_arena_battle_result.SetOKEvent(function()
                        if isReplay then
                            local player = GetBattlePlayer()
                            if player then
                                player:ClearBattle()
                            end
                            local battle_message = require "battle_message"
                            battle_message.SetIsMultiTbs(false)
                            battle_message.ClearMultiTbs()
                            local ui_weekend_arena_battle_result = require "ui_weekend_arena_battle_result"
                            ui_weekend_arena_battle_result.ResetBattleData()
                            ui_window_mgr:UnloadModule("ui_weekend_arena_battle_result")
                            ui_window_mgr:ShowModule("ui_matchplace_entrance",function ()
                                ui_window_mgr:ShowModule('ui_weekend_arena')
                            end)
                            ClearGameObjectPool()
                        else
                            local hadNextRound = weekend_arena_mgr.HadNextRound()
                            tbsCount = battle_message.GetTbsCount()
                            hasNext = tbsCount >= 1
                            weekend_arena_mgr.SetBattleRound() -- 记录回合数
                            if hasNext then
                                -- 有正式战斗
                                ui_weekend_arena_battle_result.AddBattleData(heros, battle_data.winner, battle_data.loser,false)
                            else
                                -- 无正式战斗
                                -- 判断有无虚假战斗
                                if hadNextRound then
                                    ui_weekend_arena_battle_result.AddBattleData(heros, battle_data.winner, battle_data.loser,true,true)
                                else
                                    local player = GetBattlePlayer()
                                    if player then
                                        player:ClearBattle()
                                    end
                                    local battle_message = require "battle_message"
                                    battle_message.SetIsMultiTbs(false)
                                    battle_message.ClearMultiTbs()
                                    local ui_weekend_arena_battle_result = require "ui_weekend_arena_battle_result"
                                    ui_weekend_arena_battle_result.ResetBattleData()
                                    ui_window_mgr:UnloadModule("ui_weekend_arena_battle_result")
                                    ui_window_mgr:ShowModule("ui_matchplace_entrance",function ()
                                        ui_window_mgr:ShowModule('ui_weekend_arena')
                                    end)
                                    ClearGameObjectPool()
                                end
                            end
                        end
    
                    end)
    
    
                    local leftInfo, rightInfo = weekend_arena_mgr.GetCacheBattleInfo()
                    local myScore,enemyScore = weekend_arena_mgr.GetBattleScore()
    
                    local show = true
                    if battle_data.skipBattle and ui_window_mgr:IsModuleShown("ui_weekend_arena_battle_result") then
                        --因为跳过战斗要连续解战报，可能多次打开
                        show = false
                    end
                    if show then
                        local win = ui_window_mgr:ShowModule("ui_weekend_arena_battle_result")
                        win:SetInputParam(leftInfo, rightInfo, myScore, enemyScore)
                    end]]

            -- elseif stageType == common_pb.RebirthSpace then
            --     local net_rebirth_space_module = require "net_rebirth_space_module"
            --     if skiped then
            --         net_rebirth_space_module.OnPlayBattleFinish()
            --     elseif isReplay then
            --         net_rebirth_space_module.PlayBattleReport()
            --     else

            --     end
            end

            if not switchOutEnd and not isReplay then
                --请求奖励
                local report = battle_data.battleReports[stageType]
                if report and report.battleID then
                    battle_message.C2SBattleResult(report.battleID)
                end
            end
        end
    end
end

function OnLoadingBegin()
    if loadingWindow then
        loadingWindow:SetLoadingProgress(0)
    end
end

function GetBattlePlayer()
    if battle_player == nil then
        battle_player = require "battle_player"
    end
    return battle_player.GetBattlePlayer()
end

function PlayBattle(player)
    if not player then
        player = GetBattlePlayer()
    end
    if player and not util.IsObjNull(player) then
        player:Play()
    end

    scene_manager.instance():LoadSceneComplete("scene_battle")
end

function BossCommingEffectComplete()
    local levelInfo = game_scheme:HookLevel_0(battle_data.stageLv)
    if not levelInfo then
        log.Error("BossCommingEffectComplete levelinfo is null", battle_data.stageLv)
        return
    end
    local bossHeroID = nil
    local monsterTeam = game_scheme:monsterTeam_0(levelInfo.ranksID.data[0])
    if monsterTeam and monsterTeam.BossNumber > 0 then
        bossHeroID = monsterTeam.MonsterId.data[monsterTeam.BossNumber - 1]
    end

    if bossHeroID and bossHeroID <= 0 then
        PlayBattle()
    else
        local heroCfg, heroLevel
        --从战报解析数据查找可能更快，数据与服务器一致
        local heroData = bossHeroID and battle_data.GetHeroByCfgId(bossHeroID)
        if heroData then
            heroCfg = heroData.cfg
            heroLevel = heroData.lv
        else
            --直接从英雄表查找
            heroCfg = bossHeroID and game_scheme:Hero_0(bossHeroID)
            heroLevel = heroCfg and heroCfg.monsterLevel
        end
        if heroCfg == nil then
            PlayBattle()
        else
            local modelPath = gw_hero_mgr.GetHeroModelAssetPath(heroCfg.heroID, heroCfg.starLv)
            if string.empty(modelPath) then
                PlayBattle()
            else
                local ui_boss_coming_new = require "ui_boss_coming_new"
                ui_boss_coming_new.SetHeroCfg(heroCfg, heroLevel)
                ui_boss_coming_new.SetCloseCallback(PlayBattle)

                local cfg_background = game_scheme:ActorBackground_1(1, heroCfg.type)

                ui_boss_coming_new.SetHeroAssetBundleName(heroCfg.heroID, heroCfg.starLv, cfg_background and cfg_background.bPath)
                ui_window_mgr:ShowModule("ui_boss_coming_new")
            end
        end
    end
end

function BindDirector(director, timeline, player)
    local binding = nil
    local output = util.TimelineAssetOutputs(timeline)
    local count = output.Count - 1
    for i = 0, count do
        binding = output[i]
        local bindingType = binding.sourceBindingType
        if bindingType == nil and binding.outputTargetType then
            -- unity 2018 使用 outputTargetType
            bindingType = binding.outputTargetType
        end
        if bindingType == AnimatorType then
            director:SetGenericBinding(binding.sourceObject, player.hub:GetComponent(AnimatorType))
        elseif bindingType == TransformType then
            director:SetGenericBinding(binding.sourceObject, player.transform)
        end
    end
end

function PlayBossCommingEffect()
    local const = require "const"
    if not enableBossCommingEffectInterface or not const.Open_Boss_Comming then
        -- log.Error("当前版本不支持开启 Boss 来袭")
        log.Warning("当前版本不支持开启 Boss 来袭")
        return
    end
    local EffectPath = battle_config.GetBossCommingEffectPath()
    IOSystem.LoadAssetAsync(EffectPath, nil, function(assetObj)
        if assetObj == nil then
            log.Error("PlayBossCommingEffect assetObj null !!!!!!!!!!!!!!!")
            if loadingWindow then
                ui_window_mgr:UnloadModule("ui_loading")
                loadingWindow = nil
            end
            return
        else
            if loadingWindow then
                ui_window_mgr:UnloadModule("ui_loading")
                loadingWindow = nil
            end
            if bossCommingEffect and not util.IsObjNull(bossCommingEffect) then
                -- asset 不需要销毁
                -- GameObject.Destroy(bossCommingEffect)
                bossCommingEffect = nil
            end
            local player = GetBattlePlayer()
            if player == nil or util.IsObjNull(player) then
                return
            end

            -- 为方便策划修改，不直接使用特效，改为 TimeLine 资源
            local duration = assetObj.duration
            bossCommingEffect = assetObj

            player.eventDirector.playableAsset = bossCommingEffect
            BindDirector(player.eventDirector, bossCommingEffect, player)
            player.eventDirector:Play()
            --使用特效显示时间预加载下界面资源
            IOSystem.LoadAssetAsync("ui/prefabs/uibosscomming.prefab", nil, nil, "battle_manager")
            util.DelayCall(duration, function()
                -- asset 不需要销毁
                -- GameObject.Destroy(bossCommingEffect)
                bossCommingEffect = nil

                BossCommingEffectComplete()
            end)
        end
    end, "battle_manager")
end

function LoadingWindowComplete()
    if loadingWindow and battle_data.stageType == common_pb.GameGoal then
        local levelInfo = game_scheme:HookLevel_0(battle_data.stageLv)
        if levelInfo == nil then
            log.Error("未能找到关卡数据:" .. battle_data.stageLv)
        else
            -- log.Error("关卡名："..levelInfo.Name)
            local const = require "const"
            if levelInfo.isbossComingEffect == 1 and enableBossCommingEffectInterface
                    and const.Open_Boss_Comming and (not isReplay) then

                local story_mgr = require "story_mgr"
                local isCanExcute = story_mgr.PlayStory(4, story_mgr.GetCacheLevel())
                if isCanExcute then

                    if loadingWindow then
                        ui_window_mgr:UnloadModule("ui_loading")
                        loadingWindow = nil
                    end
                    story_mgr.AddCompleteCallback(function()
                        PlayBossCommingEffect()
                    end)
                else
                    PlayBossCommingEffect()
                end
                return
            end
        end
    end
    if loadingWindow then
        ui_window_mgr:UnloadModule("ui_loading")
        loadingWindow = nil
    end

    event.Trigger(event.BOSS_COMMING_COMPLETE)
    PlayBattle()
end

local needCutOverBattle = true
function OnLoadingCompleted()
    local gw_popups_trigger_mgr = require "gw_popups_trigger_mgr"
    gw_popups_trigger_mgr.SetAllowShowPopus(false)
    
    --审核状态隐藏场外技能
    if ReviewingUtil.IsAndroidReviewing() then
        ReviewingUtil.BlockOusideSkill()
    end
    local shouldPlay = false
    if loadingWindow then
        --等待进度条动画完成，延时播放
        loadingWindow:OnLoadingCompleted(LoadingWindowComplete)
    else
        shouldPlay = true
    end
    local player = GetBattlePlayer()

    --由于1125 * 2436分辨率下level Text显示不下4位数，所以在战斗场景初始化后，修改text 的类型
    for i, v in pairs(battle_data.leftPals) do
        if v.palID then
            local role = player:GetRole(v.palID)
            if role and role.node.Hud then
                local text = role.node.Hud.level
                text.horizontalOverflow = UnityEngine.HorizontalWrapMode.Overflow
            end
        end
    end
    for i, v in pairs(battle_data.rightPals) do
        if v.palID then
            local role = player:GetRole(v.palID)
            if role.node.Hud then
                local text = role.node.Hud.level
                text.horizontalOverflow = UnityEngine.HorizontalWrapMode.Overflow
            end
        end
    end

    ClearGameObjectPool(2, true)
    util.StopWatch("loading battle")
    player:UnRegisterLoadingBeginHandler(OnLoadingBegin)
    player:UnRegisterLoadingCompleteHandler(OnLoadingCompleted)
    player:UnRegisterLoadingProgressChangedHandler(OnLoadingPorgressChanged)

    --没有提前计算战报时长数据，且需要中途切入战斗时，需要计算切入的时间
    if loadAndCutOverBattleProgress > 0 then
        local reportData = battle_data.battleReports[battle_data.stageType]
        local multiTbs = false
        if reportData then
            multiTbs = reportData.isMultiTbs
        end
        local curBattleEventHandler = multiTbs and OnMultiTbsBattleEvent or OnBattleEvent
        UnregisterHandler(curBattleEventHandler)

        local totalTime = GetPredictTimeOfBattle(battle_data.stageType) --战斗总时长
        battle_data.currentBattleCompleteTotalTime = totalTime --记录多战斗完整总时长
        local targetTime = totalTime * loadAndCutOverBattleProgress --根据进度计算出具体切入时间
        --print("根据进度切入战斗：totalTime", totalTime, "loadAndCutOverBattleProgress:", loadAndCutOverBattleProgress, "targetTime", targetTime)
        loadAndCutOverBattleProgress = 0
        util.DelayCallOnce(0, function()
            ClearBattlePool()
            player:ClearBattle()
            CutOverBattle(battle_data.stageType, targetTime)
        end)
        return
    end
    --解决看回放伤害记录翻倍的bug，需要解决快速点击回放导致多次请求战斗，然后导致监听重复添加问题，这里监听前先移除
    player:UnRegisterRoundEventHandler(OnRoundBegin)
    player:UnRegisterWeaponPowerEventHandler(OnWeaponProgressChanged)

    player:RegisterRoundEventHandler(OnRoundBegin)
    player:RegisterWeaponPowerEventHandler(OnWeaponProgressChanged)
    if player.RegisterOnLuaCallbackHandler then
        player:UnRegisterOnLuaCallbackHandler(OnLuaCallback)
        player:RegisterOnLuaCallbackHandler(OnLuaCallback)
    end

    if player.RegisterOnBattleBuffChangeCallbackHandler then
        player:UnRegisterOnBattleBuffChangeCallbackHandler(OnBuffChange)
        player:RegisterOnBattleBuffChangeCallbackHandler(OnBuffChange)
    end
    if version_mgr.CheckSvnTrunkVersion(25380) then
        if player.RegisterOnHpChangedHandler then
            player:UnRegisterOnHpChangedHandler(OnBattleHpChanged)
            player:RegisterOnHpChangedHandler(OnBattleHpChanged)
        end
        if player.RegisterOnRoleDeadHandler then
            player:UnRegisterOnRoleDeadHandler(OnRoleDead)
            player:RegisterOnRoleDeadHandler(OnRoleDead)
        end
    end
    local tv, bv = version_mgr.GetSvnVersion()
    if version_mgr.CheckSvnTrunkVersion(55900) or version_mgr.CheckSvnBranchVersion(56823) then
        if player.RegisterRolePropChangedHandler then
            player:UnRegisterRolePropChangedHandler(OnRolePropChanged)
            player:RegisterRolePropChangedHandler(OnRolePropChanged)
        end
    end
    if player.RegisterOnHitHandler then
        player:UnRegisterOnHitHandler(OnBattleBossHitInfo)
        player:RegisterOnHitHandler(OnBattleBossHitInfo)
    end

    if battle_series_manager.IsSeriesBattle(lastStageType) then
        for i = 0, 5 do
            local heroData = battle_data.GetHeroByPos(i)
            if heroData then
                local role = player:GetRole(heroData.palID)
                if role and role.node.Hud.SetIsSkipResetHud then
                    role.node.Hud:SetIsSkipResetHud(true)
                    --print("连续战斗我方阵容，在回合开始时，不需要重置血量!!!!")
                end
            end
        end
    end

    --变身英雄特殊处理
    -- local replaceHeroIdData = battle_data.GetReplaceHeroIdData()

    --强制显示英雄原始模型，隐藏变身后模型
    --使用技能后替换为变身模型

    if shouldPlay then
        PlayBattle(player)
    end

    local skip = battle_data.skipBattle
    if not skip then

        local vipLevel = player_mgr.GetPlayerVipLevel()
        local vipCfg = game_scheme:VipPrivilege_0(vipLevel)
        local playerLv = player_mgr.GetPlayerLV()
        local canSpeedUp = playerLv >= game_scheme:InitBattleProp_0(131).szParam.data[0]
        local canSkip = playerLv >= game_scheme:InitBattleProp_0(132).szParam.data[0]
        local playSpeed2 = game_scheme:InitBattleProp_0(153).szParam.data[0]
        local playSPeed1 = game_scheme:InitBattleProp_0(174).szParam.data[0]

        local IBPropData4 = game_scheme:InitBattleProp_0(1217)
        local IBPropData8 = game_scheme:InitBattleProp_0(1218)

        local playSpeed4 = IBPropData4 and IBPropData4.szParam and IBPropData4.szParam.data and IBPropData4.szParam.data[0] or 360
        local playSpeed8 = IBPropData8 and IBPropData8.szParam and IBPropData8.szParam.data and IBPropData8.szParam.data[0] or 780

        if vipCfg ~= nil then
            canSpeedUp = canSpeedUp or vipCfg.iTwiceSpeedFlag ~= 0
            canSkip = canSkip or vipCfg.iSkipBattleFlag ~= 0
        end

        local speed1 = (playSPeed1 * 0.01)
        local speed2 = canSpeedUp and (playSpeed2 * 0.01) or speed1
        local speed4 = canSpeedUp and (playSpeed4 * 0.01) or speed1
        local speed8 = canSpeedUp and (playSpeed8 * 0.01) or speed1
        time_scale_mgr.SetPlaySpeed(GetPlaySpeed())
        if not battle_data.isInSeriesBattle then
            local window = ui_window_mgr:ShowModule("ui_in_battle", function()
                -- 界面加载完毕才能设置 跳过按钮的状态
                local ui_in_battle = require "ui_in_battle"
                ui_in_battle.SetSkipBtn()
            end)
            if canSkipBattle ~= nil then
                canSkip = canSkipBattle() and canSkip
            end
            -- canSkip =true
            window:SetCanSkip(canSkip)
            window:SetSpeed(speed1, 1)
            window:SetSpeed(speed2, 2)
            window:SetSpeed(speed4, 3)
            window:SetSpeed(speed8, 4)
            window:SetStageType(battle_data.stageType)
        end

        --连续战斗特殊处理：资源加载完后开始预加载下一场战斗资源
        if battle_series_manager.IsSeriesBattle(battle_data.stageType) then
            --延迟1.2秒,防止在播放出战动画时卡顿
            util.DelayCallOnce(1.2, function()
                battle_series_manager.PreLoadResourceOfNextWaveBattle()
            end)
        end

    end

    --播放战斗音乐
    local music_contorller = require "music_contorller"
    music_contorller.PlayTempMusic(music_contorller.ID_BATTLE)
    --end)
end

function GetBattleTypeName(stageType)
    local stageTypeName
    local info = BattleTypeInfo[stageType]
    if info then
        stageTypeName = info.name
    else
        stageTypeName = tostring(stageType)
    end
    return stageTypeName
end

function ReportBattleLoading(_stageType, _loadingTime, _lastLevel)
    stageTypeStr = GetBattleTypeName(_stageType)
    levelID = (_stageType == common_pb.TheTowerOfIllusion or _stageType == common_pb.Ashdungeon or _stageType == common_pb.GameGoal) and _lastLevel or 0
    local reportMsg = {
        loading_time = _loadingTime,
        battle_type = stageTypeStr,
        dungeon_id = levelID,
        -- system_memory_size = util.GetSystemMemorySize(),
    }
    event.EventReport("battle_loading", reportMsg)
end

function OnRoleLoaded(role)
    if ReviewingUtil.IsAndroidReviewing() then
        local cur = role.node.gameObject
        if cur and not util.IsObjNull(cur) then
            local n = cur.transform.childCount
            if n and n > 0 then
                local child = cur.transform:GetChild(0)
                print("child name", child.gameObject.name)
                ReviewingUtil.LoadReviewingResWithName(child, child.gameObject.name)
            end
        end
    end
    --如果为敌方阵容,则显示出场特效
    --print("<color=#ffffff>role加载完成！！！！！</color>", role.position, lastStageType)
    if not role then
        return
    end
    if battle_series_manager.IsSeriesBattle(lastStageType) then
        --连续战斗敌方出现显示特效
        if role.position >= 6 then
            battle_series_manager.ShowEnmeyShowEffect(role.node.gameObject)
        end
    end

end

local bloadTime = 0
function OnLoadingPorgressChanged(progress)
    if loadingWindow then
        loadingWindow:SetLoadingProgress(progress)
    end
    --todo 上报事件 回放不用上报
    if not isReplay then
        if progress == 0 then
            bloadTime = os.time()
        end
        if progress == 1 then
            local loadtime = os.time() - bloadTime
            ReportBattleLoading(lastStageType, loadtime, lastLevel)
        end
    end
end

function ClearLoadingEvent()
    local player = GetBattlePlayer()
    if player and not util.IsObjNull(player) then
        player:ClearBattle()
        player:UnRegisterLoadingBeginHandler(OnLoadingBegin)
        player:UnRegisterLoadingCompleteHandler(OnLoadingCompleted)
        player:UnRegisterLoadingProgressChangedHandler(OnLoadingPorgressChanged)
    end
end

function OnRoundBegin(round)
    -- Debug.LogError("OnRoundBegin ".. round)
    ui_in_battle.ShowRound(round)
    event.Trigger(event.NEXT_ROUND, round)
    lastRound = round
    --todo 上报事件
    if not isReplay then
        battle_event_report.OnRoundBeginReport(maxspeed, minspeed, lastRound, stageTypeStr, levelID)
    end
end

function SetLastRound(round)
    lastRound = round
end

function GetLastRound()
    return lastRound
end

function OnBattleHpChanged(hp, from, to)
    if hp < 0 then
        event.Trigger(event.BATTLE_CAUSE_DAMAGE, math.abs(hp), from)
    end
end

function OnRoleDead(id)
    event.Trigger(event.BATTLE_NOTIFY_DEAD, id)
end

function OnRolePropChanged(casterId, propId, prop1, prop2, prop3)

    --print("<color=#ffff00>OnRolePropChanged:</color>", casterId, propId, prop1, prop2, prop3)
    --抵挡(护盾)次数 or 防御值
    if propId == 1000 or propId == prop_pb.PAL_PROP_DEFENCE then
        local curValue = prop1 --当前数量
        local max = prop2 --最大上限
        ui_in_battle.SetBossSkillProgress(casterId, propId, curValue, max)
    end

end

function OnWeaponProgressChanged(value)
    local array = string.split(value, '#', tonumber)
    local num = array[1]
    local sid = array[2]
    --print("<color=#8B4513>武器能量报告>>>>>>> SID ="..sid.."  Value = "..num.."</color>")
    local battle_data = require "battle_data"
    --dump(battle_data.euiptWeapon)
    if sid == battle_data.euiptWeapon.left then
        ui_in_battle.ChangeWeaoponProgress(num, 0)
    elseif sid == battle_data.euiptWeapon.right then
        ui_in_battle.ChangeWeaoponProgress(num, 1)
        -- else
        --     ui_in_battle.ChangeSummonedProgress(num, sid)
    end
end
--[[
    @desc:
    author:{author}
    time:2021-03-03 10:23:33
    --@param: timelineparam#roleid#targetid1,targetid2
    @return:
]]
function OnLuaCallback(param, bindObject)
    if param then
        local arr = util.SplitString(param, "#")
        local param1 = arr[1]
        local param2 = arr[2] and tonumber(arr[2]) or 0
        local param3 = arr[3]
        if param1 == "summonedBorn" then
            local battle_data = require "battle_data"
            local summoned = battle_data.GetSummonedByMasterId(param2)
            if summoned then
                if summoned.born == nil then
                    event.Trigger(event.BATTLE_NOTIFY_SUMMONED_BORN, param2)
                    summoned.born = true
                else
                    event.Trigger(event.BATTLE_NOTIFY_SUMMONED_REBORN, param2)
                end
            end
        elseif param1 == "summonedDie" then
            event.Trigger(event.BATTLE_NOTIFY_DEAD, param2)
        elseif param1 == "summonedAttack" then
            event.Trigger(event.BATTLE_NOTIFY_SUMMONED_ATTACK, param2)
        elseif param1 == "summonedProp" then
            event.Trigger(event.UPDATE_SUMMONED_PROP, param2)
        elseif param1 == "changeBody" then
            --print("onluaCallback:changeBody!!!!!!!!!!!!!!!!!!", param2)
            -- HeroChangeBodyInBattle(param2)
        elseif param1 == "restoreBody" then
            --print("onluaCallback:restoreBody!!!!!!!!!!!!!!!!!!", param2)
            -- HeroRestoreBodyInBattle(param2)
        elseif param1 == "hideBody" then
            --print("onluaCallback:hideBody!!!!!!!!!!!!!!!!!!", param3)
            --param3: 受击列表
            HideBodyInBattle(param3)
        elseif param1 == "change,1" then
            Change_avatar_call(param2, 1)
        elseif param1 == "change,2" then
            Change_avatar_call(param2, 2)

            --Buff消息转为OnBuffChange
            -- elseif param1 == "addBuff" then
        elseif param1 == "removeBuff" then
            --在移除指定buff时，显示对应的卡牌模型
            --print("<color=#8B4513>onluaCallback:removeBuff!!!!!!!!!!!!!!!!!!", "posId:", param2, "buffid:", param3, "</color>")
            --param3: buffId
            if param3 and tonumber(param3) and battle_data.IsShowBodyBuffId(tonumber(param3)) then
                local buffId = tonumber(param3)
                --log.Error("param3---->",param3)
                RemoveBuffInBttle(param2, buffId, 1)
            end
        elseif param1 == "OnCinemaChine" and bindObject then
            if bindObject and bindObject.shakeScreenCfg then
                SetCinemachineShake(bindObject)
            end
        end
    end
end

--震屏
local cinemachineSource = nil
function SetCinemachineShake(bindObject)
    local player = GetBattlePlayer()
    if player and player.cinemachineController then
        cinemachineSource = player.cinemachineController.impluseSource
    end
    if util.IsObjNull(cinemachineSource) then
        local goSource = battle_player.GetMainBattleZoneObj():Find("ComicBattlePlayer/CineMachtinContorll/CinemachineSource")
        if not util.IsObjNull(goSource) then
            cinemachineSource = goSource:GetComponent(typeof(CinemachineImpulseSource))
        end
    end
    if util.IsObjNull(cinemachineSource) then
        return
    end
    -- local CinemachineSourceCfg = goSource:GetComponent(typeof(CinemachineSourceCfg))
    -- CinemachineSourceCfg:SetAttackTime(bindObject.shakeScreenCfg.duration)
    -- CinemachineSourceCfg:SetAmplitudeGain(bindObject.shakeScreenCfg.amplitudeGain)
    -- CinemachineSourceCfg:SetFrequencyGain(bindObject.shakeScreenCfg.frequencyGain)
    -- CinemachineSourceCfg:SetSignalSource(bindObject.shakeScreenCfg.signalSource)
    -- CinemachineSourceCfg:GenerateImpulse()
    cinemachineSource.m_ImpulseDefinition.m_TimeEnvelope = { m_AttackTime = bindObject.shakeScreenCfg.duration } -- 结构体传参方法
    cinemachineSource.m_ImpulseDefinition.m_AmplitudeGain = bindObject.shakeScreenCfg.amplitudeGain
    cinemachineSource.m_ImpulseDefinition.m_FrequencyGain = bindObject.shakeScreenCfg.frequencyGain
    cinemachineSource.m_ImpulseDefinition.m_RawSignal = bindObject.shakeScreenCfg.signalSource
    cinemachineSource:GenerateImpulse()
end

--Buff
function OnBuffChange(roleid, buffid, bufflv, isActive)
    if bufflv <= 0 then
        bufflv = 1
    end
    if isActive then
        local battle_buff_data = require "battle_buff_data"
        battle_buff_data.ActiveBuffEffect(roleid, buffid, bufflv)
        --print("<color=#8B4513>onluaCallback:addBuff!!!!!!!!!!!!!!!!!!", "posId:", roleid, "buffid:", buffid, "</color>")
        -- if param3 then
        --     local buffId = tonumber(param3)
        --     AddBuffInBttle(param2, buffId)
        -- end
    else
        --在移除指定buff时，显示对应的卡牌模型
        --print("<color=#8B4513>onluaCallback:removeBuff!!!!!!!!!!!!!!!!!!", "posId:", roleid, "buffid:", buffid, "</color>")
        --param3: buffId
        RemoveBuffInBttle(roleid, buffid, bufflv)
    end
end

function Change_avatar_call(roleid, comInd)
    M.change_avatar = M.change_avatar or {}
    M.change_avatar[roleid] = 1
    Change_avatar(roleid, comInd)
end

function Change_avatar(roleid, comInd)
    -- print("change_avatar",roleid,comInd)
    local player = GetBattlePlayer()
    if not player then
        return
    end

    local sourceRole = player:GetRole(roleid)
    if sourceRole and sourceRole.roleGO and not util.IsObjNull(sourceRole.roleGO) then
        --获取卡牌信息
        local card = sourceRole.roleGO:GetComponent(CardType)
        if comInd == 2 then
            card.animator:SetBool("IsHuman", false)
        else
            card.animator:SetBool("IsHuman", true)
        end
    else
        log.Error("sourceRole is not find")
    end

end

--战斗中隐藏英雄（背景不隐藏）
--targets 英雄列表
function HideBodyInBattle(targets)
    if not targets then
        return
    end
    local player = GetBattlePlayer()
    if not player then
        return
    end

    local arr = util.SplitString(targets, ",")
    local id = -1

    local targetId = arr[1] and tonumber(arr[1])
    local sourceRole = targetId and player:GetRole(targetId) or nil
    if sourceRole and sourceRole.position then
        local targetPos = sourceRole.position
        local targetCount = 0
        --判断目标是敌人，还是我方
        if targetPos >= 6 then
            --敌人
            --targetCount = #battle_data.rightPals
            for i, v in pairs(battle_data.rightPals) do
                if v.palID then
                    local role = player:GetRole(v.palID)
                    if role and role.node and role.node.Hud then
                        local hp = role.node.Hud.hp
                        if hp > 0 then
                            targetCount = targetCount + 1
                        end
                    end
                end
            end
        else
            --我方
            --targetCount = #battle_data.leftPals
            for i, v in pairs(battle_data.leftPals) do
                if v.palID then
                    local role = player:GetRole(v.palID)
                    if role and role.node and role.node.Hud then
                        local hp = role.node.Hud.hp
                        if hp > 0 then
                            targetCount = targetCount + 1
                        end
                    end
                end
            end
        end
        --print("<color=#ff00ff>判断敌我；列举敌人位置</color>", targetPos)
        --吞噬目标数量为1时，不吞噬，（策划要求特殊处理）
        if targetCount <= 1 then
            --print("<color=#ff00ff>敌人数量为1时，不吞噬 count:</color>", targetCount)
            return
        end
    end

    for key, value in pairs(arr) do
        id = value and tonumber(value)
        local sourceRole = player:GetRole(id)
        if sourceRole ~= nil then
            --隐藏卡牌上的模型
            SetCardModelActive(sourceRole, false)
            --print("隐藏卡牌上的模型:", id, card)
        end
    end

end

function AddBuffInBttle(heroId, buffId)
    local player = GetBattlePlayer()
    if not player then
        return
    end

    --判断buffid是否为复活buff,则播放死亡状态
    if battle_data.IsReviveBuffId(buffId) then
        ChangeRoleAnimatorState(heroId, "Death")
    end

end

--战斗中移除buff事件
function RemoveBuffInBttle(heroId, buffId, bufflv)
    local player = GetBattlePlayer()
    if not player then
        log.Error("[buff]RemoveBuffInBttle can't find battlePlayer instance")
        return
    end
    --print("<color=#8B4513>removeBuff!!!!!!!!!!!!!!!!!!", "posId:", heroId, "buffid:", buffId, "</color>")
    --判断buffid是否为吞噬buff
    if battle_data.IsShowBodyBuffId(buffId) then
        ShowBodyInBattle(heroId)
        --print("<color=#8B4513>2removeBuff!!!!!!!!!!!!!!!!!!", "posId:", heroId, "buffid:", buffId, "</color>")
        --判断buffid是否为复活buff,则播放站立状态
    elseif battle_data.IsReviveBuffId(buffId) then
        --print("<color=#8B4513>3removeBuff!!!!!!!!!!!!!!!!!!", "posId:", heroId, "buffid:", buffId, "</color>")
        ChangeRoleAnimatorState(heroId, "ReSpawn")
    end

    --2022-4-1添加buff效果模块
    local battle_buff_data = require "battle_buff_data"
    battle_buff_data.RemoveBuffEffect(heroId, buffId, bufflv)
end

function ChangeRoleAnimatorState(heroId, state)
    --log.Error("1ChangeRoleAnimatorState!!!!", heroId, state)
    local player = GetBattlePlayer()
    if not player then
        return
    end
    local role = player:GetRole(heroId)
    if role ~= nil then
        --log.Error("2ChangeRoleAnimatorState!!!!", heroId, state)
        --获取卡牌信息
        local card = role.roleGO:GetComponent(CardType)
        if card and card.animator then
            --log.Error("3ChangeRoleAnimatorState!!!!", heroId, state)
            -- card.animator:ResetTrigger(state)
            card.animator:SetTrigger(state)
        end
    end
end


--战斗中显示英雄模型，(吞噬buff移除后，调用该函数)
--heroId 英雄id
function ShowBodyInBattle(heroId)
    local player = GetBattlePlayer()
    if not player then
        return
    end
    local id = heroId and tonumber(heroId)
    local sourceRole = player:GetRole(id)
    if sourceRole ~= nil then
        --显示卡牌上的模型
        SetCardModelActive(sourceRole, true)
    end
end

--设置战斗卡牌内的显示和隐藏
function SetCardModelActive(sourceRole, isShow)
    --获取卡牌信息
    local card = sourceRole.roleGO:GetComponent(CardType)
    if not card and not util.IsObjNull(card) then
        return
    end
    card.gameObject:SetActive(isShow)
    if card.standEffectObject and not util.IsObjNull(card.standEffectObject) then
        card.standEffectObject.gameObject:SetActive(isShow)
    end
    local node = sourceRole.node
    if node and not util.IsObjNull(node) then
        local hud = node.Hud
        if hud and not util.IsObjNull(hud) then
            hud.transform:GetChild(0).gameObject:SetActive(isShow)
        end
    end

end

function SetLayerActive(components, mask, enable)
    if components.Length <= 0 then
        return
    end
    local bit = require "bit"
    for index = 1, components.Length do
        local component = components[index - 1]
        local layerFlag = bit.lshift(1, component.gameObject.layer)
        if bit.band(layerFlag, mask) ~= 0 and component.gameObject.activeSelf ~= enable then
            component.gameObject:SetActive(enable)
        end
    end
end

function SetLayerActiveForSpriteMesh(sprite, mask, enable)
    if sprite.Length <= 0 then
        return
    end

    local bit = require "bit"
    for index = 1, sprite.Length do
        local component = sprite[index - 1]
        local layerFlag = bit.lshift(1, component.gameObject.layer)
        if bit.band(layerFlag, mask) ~= 0 then
            local shouldEnable = enable and not SpriteMeshInstanceReflection:GetCombined(component)
            if component.gameObject.activeSelf ~= shouldEnable then
                component.gameObject:SetActive(shouldEnable)
            end
        end
    end
end

--英雄变身还原
function HeroRestoreBodyInBattle(roleId)
    HeroChangeBodyInBattle(roleId, true)
end

--英雄变身
function HeroChangeBodyInBattle(roleId, isRestor)
    local player = GetBattlePlayer()

    if not player then
        return
    end

    local sourceRole = player:GetRole(roleId)
    local changeModleID = 0
    if isRestor then
        changeModleID = roleId
    else
        changeModleID = battle_data.GetReplaceModleIdBy(roleId)
    end

    if not changeModleID or changeModleID == 0 then
        return
    end
    local replaceModelGo = battle_data.GetReplaceHeroGoBy(changeModleID) --替身模型go
    if sourceRole ~= nil and sourceRole.roleGO ~= replaceModelGo then
        --变身后，将显示替身模型，隐藏原始模型
        if not util.IsObjNull(sourceRole.roleGO) then
            sourceRole.roleGO:SetActive(false)
        end
        if not util.IsObjNull(replaceModelGo) then
            --sourceRole:InternalLoad(replaceModelGo)
            ResetBattleRoleModleGo(sourceRole, replaceModelGo)
            --播放出场动画
            local card = replaceModelGo:GetComponent(CardType)
            if card and card.animator then
                card.animator:SetTrigger("chuchang")
            end
        end
        --print("changeBody!!!!!!!!!!!!sourceRole:", roleId, "changeModleID", changeModleID)
    else
        --print("changeBody!!!!!!!!!!!!sourceRole:变身后不执行")
    end

end

--重置战斗卡牌Role的模型go
function ResetBattleRoleModleGo(role, go)
    role.roleGO = go
    local player = GetBattlePlayer()
    role.node = player.hub.actorManager:GetBattleActionNode(role.position)
    role.node:AttachActor(go)
    --role.node.AutoAssignRenderOrder()
    role.node.role = role
    go:SetActive(true)
end

--虚空主宰Boss受击
function OnBattleBossHitInfo(deltahp, hp, maxhp)
    ui_in_battle.ChangeBossHp(hp, deltahp)
end

function GetPlaySpeedKey()
    --将PlaySpeed修改为PlaySpeedEx,速度配置被修改，不能再使用PlaySpeed数据
    local strIsSecret = ""
    local stageType = battle_data.stageType
    if stageType == common_pb.MultiKillingTower or stageType == common_pb.KillingTower then
        strIsSecret = "secretplace"
    elseif stageType == common_pb.RebirthSpace then
        strIsSecret = "rebirthspace"
    elseif stageType == common_pb.StarTemple then
        strIsSecret = "startemple"
    elseif stageType == common_pb.StarTempleRank then
        strIsSecret = "startemplerank"
    end

    return "PlaySpeedEx" .. strIsSecret .. player_mgr.GetPlayerRoleID()
end

function GetPlaySpeed()
    local speed = PlayerPrefs.GetFloat(GetPlaySpeedKey(), 1)
    --四舍五入一下，取一位小数
    speed = tonumber(string.format("%.1f", speed))
    return speed
end

function SetPlaySpeed(speed)
    -- if canSkipBattle then
    --     local skip, limiteSpeed = canSkipBattle()
    --     limiteSpeed = limiteSpeed or 2
    --     speed = math.min(speed, limiteSpeed)
    -- end
    Time.timeScale = speed
    PlayerPrefs.SetFloat(GetPlaySpeedKey(), speed)
    SoundEngine.SetFloat("Master/SFX/Battle", "PitchBattle", 1 / speed)
    --SoundEngine.SetVolumeOffset(2, -3)

    --print("Time.timeScale",Time.timeScale)
    return speed
end

function ResetTimeScale()
    Time.timeScale = 1
    SoundEngine.SetFloat("Master/SFX/Battle", "PitchBattle", 1)
    --SoundEngine.SetVolumeOffset(2, 0)
end

function Skip()
    ClearBattlePool()

    local player = GetBattlePlayer()
    if battleType == common_pb.Cutscene then
        player:SkipToPostlude()
    else
        player:Stop()
    end
    --
    --OpenSlgUI()
    event.Trigger(event.BATTLE_SKIP)
    --todo 上报事件
    if not isReplay and battleType ~= common_pb.Cutscene then
        battle_event_report.OnBattleSkipReport(lastRound, stageTypeStr, levelID)
    end
    --CutOutCurrentBattle()
end

function SaveBattleRecord(data, fileName)

    if Application.isEditor or game_config.ENABLE_Q1_DEBUG_MODE then
        --print("SaveBattleRecord")
        local timeStr = os.date("%Y_%m_%d_%H_%M_%S", os.time())
        local name = fileName or timeStr .. ".battle"
        local path
        local rootPath = LogDebug.Instance:GetLogFileRootPath()
        path = rootPath .. "/BattleRecord/" .. name
        util.WriteFile(path, data, "wb")
    end
end

function PlaybackRecord(datapath)
    prepareBattle = function()
        menu_bot_data.CloseAllPage()
    end

    finalizeBattle[common_pb.Replay] = function(victory, showui)
        ui_window_mgr:ShowModule("ui_lobby")
    end

    canSkipBattle = function()
        return true
    end

    if isSaveBattleRecordMsg then
        local list = util.SplitString(datapath, "/")
        local fileName = list[#list]
        print("文件名字：", datapath, fileName)
        saveBattleMsgPath = fileName .. "msg"
    end

    util.DelayCall(0, function()
        local file = io.open(datapath, "rb")
        local str = file:read("*a")
        io.close(file)
        CloseSlgUI()
        OnBattleReportReceived(common_pb.Replay, str, nil, function()
            OpenSlgUI()
        end)
    end)
end

function SetBattleHeroes(win)
    local battle_data = require "battle_data"
    -- local common_new_pb = require "common_new_pb"

    local heros = battle_data.GetHeros()
    for i = 0, 14 do
        local hero = heros[i]
        if hero ~= nil then
            win.SetHeroData(i, hero)
        end
    end
end

function SetStarTempleRewards(window, rewards, score)
    window.ResetReward()
    for i = 1, #rewards do
        local reward = rewards[i]
        if reward.id then
            window.AddReward(reward.id, reward.num, reward.rewardID, reward.sid, reward.level, reward.rare)
        end
        if reward.heroid then
            window.AddHeroReward(reward.heroid, reward.num, reward.sid, reward.starLv, reward.lv, reward.rare)
        end

        if reward.rewardID then
            window.AddDelayReward(reward.rewardID, reward.num)
        end

    end
    window.SetScore(score)
end

function SetBattleRewards(window, rewards, score)
    window.ResetReward()
    for i = 1, #rewards do
        local reward = rewards[i]
        window.AddReward(reward.id, reward.num, reward.rewardID, reward.sid,reward.ItemFlag)
    end
    window.SetScore(score)
end

function OnCloseScene()

    local player = GetBattlePlayer()
    if not util.IsObjNull(player) then
        player:ClearBattle()
    end
    local gw_popups_trigger_mgr = require "gw_popups_trigger_mgr"
    gw_popups_trigger_mgr.SetAllowShowPopus(true)
end

function OnLogOut()
    --Debug.LogError("Leaving." ..  state)
    --if state == game.STATE_GAME then
    local ui_window_mgr = require "ui_window_mgr"
    ui_window_mgr:UnloadModule("ui_in_battle")
    ui_window_mgr:UnloadModule("ui_battle_defeat_new")
    ui_window_mgr:UnloadModule("ui_battle_victory_new")
    if ui_window_mgr:IsModuleShown("ui_halo_detail") then
        ui_window_mgr:UnloadModule("ui_halo_detail")
    end
    if ui_window_mgr:IsModuleShown("ui_halo_tips") then
        ui_window_mgr:UnloadModule("ui_halo_tips")
    end
    --end
end
event.Register(event.ACCOUNT_LOGOUT, OnLogOut)
event.Register(event.CLOSE_ALL_SCENE, OnCloseScene)


---description 保存阵容,默认下标要从0开始，如果需要从1开始，则设置isFromIndexOne为true
function SaveHeroFormation(hData, mark,isFromIndexOne)
    local heroListString = ""
    local heroIDString = ""
    local heroRowString = ""
    local heroColString = ""
    for pos, hero in pairs(hData) do
        heroListString = heroListString .. hero.heroSid
        heroIDString = heroIDString .. hero.heroID
        local row,col = battle_data.GetRowAndColByIndex(isFromIndexOne and pos or pos + 1)
        heroRowString = heroRowString .. row
        heroColString = heroColString .. col

        heroListString = heroListString .. "#"
        heroIDString = heroIDString .. "#"
        heroRowString = heroRowString .. "#"
        heroColString = heroColString .. "#"
    end
    local roleId = player_mgr.GetPlayerRoleID()
    PlayerPrefs.SetString(roleId .. mark .. "FormationsSid", heroListString)
    PlayerPrefs.SetString(roleId .. mark .. "FormationsID", heroIDString)
    PlayerPrefs.SetString(roleId .. mark .. "FormationsRow", heroRowString)
    PlayerPrefs.SetString(roleId .. mark .. "FormationsCol", heroColString)
    PlayerPrefs.SetInt(player_mgr.GetPlayerRoleID() .. mark .. "FormationsSaveTime", os.server_time())

end

--获取本地保存的英雄阵容
function GetHeroFormation(mark, GetParPartDataBySid)
    local roleId = player_mgr.GetPlayerRoleID()

    local heroListString = PlayerPrefs.GetString(roleId .. mark .. "FormationsSid")
    local heroIDString = PlayerPrefs.GetString(roleId .. mark .. "FormationsID")
    local heroRowString = PlayerPrefs.GetString(roleId .. mark .. "FormationsRow")
    local heroColString = PlayerPrefs.GetString(roleId .. mark .. "FormationsCol")

    local heroList = {}
    if heroListString ~= nil and heroListString ~= "" then
        local arrPalId = util.SplitString(heroListString, "#")
        local arrheroID = util.SplitString(heroIDString, "#")
        local arrRow = util.SplitString(heroRowString, "#")
        local arrCol = util.SplitString(heroColString, "#")

        for i, id in ipairs(arrPalId) do
            local sid = tonumber(id)
            if sid ~= 0 then
                local entity = (GetParPartDataBySid and GetParPartDataBySid(sid)) or player_mgr.GetPartDataCacheBySid(sid)
                if entity and entity.heroID == tonumber(arrheroID[i]) then
                    local row = tonumber(arrRow[i])
                    local col = tonumber(arrCol[i])
                    local index = battle_data.GetIndexByRowAndCol(row,col)
                    heroList[index] = entity
                end
            end
        end
    end
    return heroList
end

function SetUpRebirthBattle()
    canSkipBattle = function()
        return true
    end
end

function RegisteBattle(stageType)
    if stageType == common_pb.LeagueComp then
        SetPrepareBattle(function()

            -- ui_window_mgr:UnloadModule("ui_socialty_fight_main")
            -- ui_window_mgr:UnloadModule("ui_socialty_fight_record")

        end)
        SetFinalizeBattle(stageType, function(victory, showui)
            if showui then
                -- local net_leagueComp_module = require "net_leagueComp_module"

                -- net_leagueComp_module.Send_LEAGUE_COMP_BASE_INFO_REQ()
                -- net_leagueComp_module.Send_LEAGUE_COMP_UPDATE_PART_REQ()
            else
                -- local ui_socialty_fight_record = require "ui_socialty_fight_record"
                -- ui_socialty_fight_record.handClose = true
            end

        end)
        SetCanSkipBattle(function()
            return true
        end)
    end
end

--- 圆月之战
function SetupFestivalBossChallengeV2(skipBattle, monsterTeamID, activityID, isNewbie, battleStageType)
    -- local activity_boss_challenge = require "activity_boss_challenge"
    -- local heroData = activity_boss_challenge.GetHeroFormation()
    -- local ui_select_hero = require "ui_select_hero"
    -- ui_select_hero.SetSaveHeroData(heroData, true)
    -- ui_select_hero.SetEnemyByMonsterTeam(monsterTeamID, nil, true)

    -- local wID = weapon_data.GetLocalizedWeaponData(battleStageType)
    -- local weaponData = {
    --     stage = battleStageType,
    --     weaponId = wID,
    --     lineupIdx = 0,
    --     arenaType1 = 0,
    -- }
    -- ui_select_hero.SetSaveWeaponData(weaponData)--传送武器数据

    -- battle_data.skipBattle = skipBattle or false
    -- local window = ui_window_mgr:ShowModule("ui_select_hero")
    -- if window ~= nil then
    --     window.onCloseEvent = function(win)
    --         if win ~= nil then
    --             win.onCloseEvent = nil
    --             win.onFightEvent = nil
    --             ui_window_mgr:UnloadModule("ui_select_hero")
    --         end
    --     end

    --     window.onFightEvent = function(win)
    --         if win ~= nil then
    --             win.onCloseEvent = nil
    --             win.onFightEvent = nil

    --             local selectedHero = win:GetSelectedHero()
    --             activity_boss_challenge.SetHeroFormation(selectedHero)

    --             local battleData = {
    --                 stageType = battleStageType,
    --                 skipBattle = skipBattle,
    --                 heroData = selectedHero,
    --                 ActivityID = activityID,
    --                 multiHeroData = {
    --                     [1] = {
    --                         hero = selectedHero,
    --                         weaponId = wID
    --                     }
    --                 }
    --             }
    --             battle_message.SendBattleChallengeMessage(battleData)
    --             ui_window_mgr:UnloadModule("ui_select_hero")
    --             if skipBattle == false then
    --                 event.Trigger(event.ACTIVITY_TIME_UP, true)
    --             end
    --         end
    --     end

    --     prepareBattle = function()

    --     end

    --     finalizeBattle[battleStageType] = function(victory, showui)
    --         if showui then
    --             OnNextWindowShowed()
    --             local ui_festival_activity_base = require "ui_festival_activity_base"
    --             if isNewbie then
    --                 ui_festival_activity_base.SetInputParam(nil, isNewbie, 2)
    --                 ui_festival_activity_base.OpenByActivityID(activityID)
    --             else
    --                 local festival_activity_cfg = require "festival_activity_cfg"
    --                 local activityCfg = festival_activity_cfg.GetActivityCfgByAtyID(activityID)
    --                 ui_festival_activity_base.SetInputParam(activityCfg.actType, isNewbie)
    --                 ui_festival_activity_base.OpenByActivityID(activityID)
    --             end
    --         else
    --             event.Trigger(event.ACTIVITY_TIME_UP, false)
    --         end
    --     end

    --     canSkipBattle = function()
    --         return true
    --     end
    -- end
end

function SetupNewPlayerCutscene(assetBundleName, callback)
    IOSystem.LoadAssetAsync(assetBundleName, "",
            function(data)
                --skipLoading = true
                isReplay = true
                battleType = common_pb.Cutscene

                prepareBattle = function()
                end

                finalizeBattle[common_pb.Cutscene] = function(victory, showui)
                    skipLoading = false
                    isReplay = false
                    battleType = nil
                    prepareBattle = nil
                    finalizeBattle[common_pb.Cutscene] = nil
                    canSkipBattle = nil
                    callback()
                end

                canSkipBattle = function()
                    return false
                end

                OnBattleReportReceived(nil, data.bytes)
                CountConsume()
            end, "battle_manager")
end

--统计新手剧情加载耗时
local comFun
function CountConsume()
    local startTime = Time.time
    local player = GetBattlePlayer()
    if not player then
        return
    end
    comFun = function()
        battle_event_report.NewPlayerLoadedReport(Time.time, Time.time - startTime)
        local player = GetBattlePlayer()
        if player ~= nil and comFun then
            player:UnRegisterLoadingCompleteHandler(comFun)
        end
    end
    player:RegisterLoadingCompleteHandler(comFun)
end

function SendBattleChallengeMessage(battleData)
    battle_message.SendBattleChallengeMessage(battleData)
end


--回调设置
function SetPrepareBattle(callBack)
    prepareBattle = callBack
end

function SetFinalizeBattle(stageType, callBack)
    if not stageType then
        return
    end
    finalizeBattle[stageType] = callBack
end

function SetCanSkipBattle(callBack)
    canSkipBattle = callBack
end

function GetBattleReplayWebUrl(battleReport, stageType, leftId)
    local net_arena_module = require "net_arena_module"
    local url = net_arena_module.GetReplayWebUrl(battleReport)--GetReplayWebUrl()
    local hasField = stageType or leftId;
    if hasField then
        url = url .. "?"
    end
    if stageType then
        url = url .. "stageType=" .. stageType
    end
    if leftId then
        if stageType then
            url = url .. "&"
        end
        url = url .. "roleId=" .. leftId
    end
    return string.format(url, battleReport)
end

function ShowBattle(battleReport, stageType, name, leftId, multiTbs, finalizecallback, prepareCallback)
    SetBattleRecordData(battleReport, name or "")
    local http_inst = require "http_inst"
    local net_arena_module = require "net_arena_module"
    local url = net_arena_module.GetReplayWebUrl(battleReport)--GetReplayWebUrl()
    local rUrl = string.format(url, battleReport)
    print("战斗录像url：", rUrl, "!   :", url, "! battleReport:", battleReport);
    http_inst.Req_Timeout(rUrl, 4, function(str, hasError, bytes)
        if hasError then
            local flow_text = require "flow_text"
            local lang = require "lang"
            flow_text.Add(lang.Get(18514))
            if finalizecallback then
                finalizecallback()
                finalizecallback = nil
            end
            return
        end
        if prepareCallback then
            prepareCallback()
            prepareCallback = nil
        end
        local ui_loading = require "ui_loading"
        ui_loading.SetBattleStageType(stageType)
        ui_window_mgr:ShowModule("ui_loading")
        local battle_data = require 'battle_data'
        if leftId then

            battle_data.SetRoleID(leftId)
        end
        local preSkip = battle_data.skipBattle;
        battle_data.skipBattle = false
        RegisteBattle(stageType)
        SetIsReplay(true)
        SetSkipResult(false)
        local battle_extern = require "battle_extern"
        -- 主要是回放完以后重新打开的UI不一样
        if stageType == common_pb.Team then
            battle_extern.RegisterTeamArenaBattle(false, stageType)
        elseif stageType == common_pb.WeekendArena then
            battle_extern.RegisterWeekendArenaBattle(false, stageType)
        elseif stageType == common_pb.Carriage then
            battle_extern.RegisterCarriageBattle(false, stageType)
        else
            battle_extern.RegisterSingleArenaBattle(false, stageType)
        end
        OnBattleReportReceived(stageType, bytes, multiTbs, function()
            SetIsReplay(false)
            battle_data.skipBattle = preSkip
            if finalizecallback then
                finalizecallback()
            end
        end, stageType == common_pb.TheTowerOfIllusion)
    end)
end

function SceneBattleUnprocessedDestroyed()
    log.Warning("scene_manager, SceneBattleUnprocessedDestroyed, 没有主动销毁场景")

    -- 其它场景加载完成时，scene_manager 根据条件判断是否需要销毁其它场景
    -- 此处在 scene_battle 场景在未正常销毁时提供处理方式
    -- 主动销毁战斗
    local player = GetBattlePlayer()
    if player and (not util.IsObjNull(player)) then
        player:Stop()
        player:ClearBattle()
    end

    local battle_manager = require "battle_manager"
    battle_manager.ClearLoadingEvent()

    local battle_data = require "battle_data"
    battle_data.Clear()

    ClearBattlePool()
    ClearGameObjectPool()
    battle_data.skipBattle = false
    ui_window_mgr:UnloadModule("ui_in_battle")
end

function SetIsReplay(val)
    isReplay = val
end

function GetIsReplay()
    return isReplay
end

function SetSkipResult(val)
    skipResult = val
end

function SetReturnArean(val)
    returnArean = val
end

function IsSkiped()
    return skiped
end

function GetBattleID()
    return battleID
end

function GetLastStageType()
    return lastStageType
end

function OnClickBattleSuspend(heroSID)
    if heroSID then
        print("<color=#00FFFF>On Luanch Skill>>>>>>>>>>>>>>>>></color>", heroSID)

        local music_contorller = require "music_contorller"
        music_contorller.PlayFxAudio(300022)
    end
end

function OnBubbleShow(soundID)
    if soundID and soundID > 0 then
        local music_contorller = require "music_contorller"
        music_contorller.PlayHeroAudio(soundID)
    end
end

function GetLastBattle()
    return lastStageType, lastLevel
end

function SetLastBattle(level)
    lastLevel = level
end
function ForceCall()
    if lastStageType == common_pb.TheTowerOfIllusion or (lastStageType >= common_pb.FactionType1 and lastStageType <= common_pb.FactionType4) then
        -- local ui_illusion_tower = require "ui_illusion_tower"
        -- ui_illusion_tower.SetWaitForData(true)
        -- ui_illusion_tower.MSG_PLAYER_INPUT_CHALLENGE_REQ(ui_illusion_tower.GetTypeMap(lastStageType), false, true)
    end
end

function GetPowerID()
    return powerID
end

function IsSaveBattleRecordMsg(value)
    isSaveBattleRecordMsg = value
end

---@ 星河神殿
function SetupStarTempleBalttle(battleData, victoryCallBack, defeatCallBack)

    -- local star_temple_battle_data = require "star_temple_battle_data"
    -- star_temple_battle_data.SetBattleType(star_temple_battle_data.StarTempleBattleType.Normal)
    -- battle_message.SendBattleChallengeMessage(battleData)
    -- battle_data.skipBattle = battleData.skipBattle or false

    -- prepareBattle = function()
    --     ui_window_mgr:UnloadModule("ui_star_temple_battle")
    --     ui_window_mgr:UnloadModule("ui_star_temple_base")
    --     --local menu_bot_data = require "menu_bot_data"
    --     --menu_bot_data.CloseAllPage()
    -- end

    -- finalizeBattle[common_pb.StarTemple] = function(victory, showui, callBackData)
    --     if showui then
    --         --[[local scene_manager = require "scene_manager"
    --         local scene_manager_instance = scene_manager.instance()
    --         if scene_manager_instance:IsCurrentScene("scene_battle") then
    --             ui_window_mgr:ShowModule("ui_lobby")
    --         end
    --         ]]--
    --         if victory then
    --             if victoryCallBack then
    --                 victoryCallBack(callBackData)
    --                 victoryCallBack = nil
    --             end
    --         else
    --             if defeatCallBack then
    --                 defeatCallBack(callBackData)
    --                 defeatCallBack = nil
    --             end
    --         end
    --         --ReportBattleInfo(common_pb.StarTemple)
    --     end
    --     battle_data.Clear()
    -- end
    -- canSkipBattle = function()
    --     return true
    -- end
end
---@see 星河神殿排行榜战斗
function SetupStarTempleRankBalttle(battleData)
    -- battle_message.SendBattleChallengeMessage(battleData)
    -- battle_data.skipBattle = battleData.skipBattle or false
    -- prepareBattle = function()
    --     ui_window_mgr:UnloadModule("ui_star_temple_arean")
    --     ui_window_mgr:UnloadModule("ui_star_temple_base")
    --     --ui_window_mgr:UnloadModule("ui_star_temple_base")
    --     --local menu_bot_data = require "menu_bot_data"
    --     --menu_bot_data.CloseAllPage()
    -- end
    -- local resultCallBack = function()
    --     local star_temple_arean_cache_data = require "star_temple_arean_cache_data"
    --     local tempData = star_temple_arean_cache_data.PopChallengeBuffer()
    --     if tempData then
    --         if tempData.result == 1 then
    --             if tempData.rRankIdBefore and tempData.rRankIdBefore > 3 then
    --                 ui_window_mgr:ShowModule("ui_star_temple_arean_victory")
    --             else
    --                 ui_window_mgr:ShowModule("ui_star_temple_arean_victory2")
    --             end
    --         end
    --     end
    -- end
    -- finalizeBattle[common_pb.StarTempleRank] = function(victory, showui)

    --     if showui then
    --         --ReportBattleInfo(common_pb.StarTempleRank)
    --         if victory then
    --             ui_window_mgr:ShowModule("ui_star_temple_arean")
    --             resultCallBack()
    --             resultCallBack = nil
    --         else
    --             ui_window_mgr:ShowModule("ui_star_temple_arean")
    --         end


    --     end
    -- end
    -- canSkipBattle = function()
    --     return true
    -- end
end

function RegisteCommonBattle(tab)
    prepareBattle = function()
        if tab.prepareBattle then
            tab.prepareBattle()
        end
    end
    finalizeBattle[tab.stageType] = function(victory, showui)
        if showui then
            if tab.comFunc then
                tab.comFunc()
            end
        else
            if tab.endFunc then
                tab.endFunc()
            end
        end
    end
end

function OpenSlgUI()
    --local sand_ui_event_define = require "sand_ui_event_define"
    --event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
end

function CloseSlgUI()
    local sand_ui_event_define = require "sand_ui_event_define"
    event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_MAIN)
end

Initialize()
