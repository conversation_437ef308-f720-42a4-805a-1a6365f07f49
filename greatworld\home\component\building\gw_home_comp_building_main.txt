--- Des:建筑-大本营
local require = require
local pairs = pairs
local table = table
local string = string
local typeof = typeof
local gw_home_effect_object = require "gw_home_effect_object"
local gw_gpu_animation_uitl = require "gw_gpu_animation_uitl"
local gw_home_novice_chapter_data = require "gw_home_novice_chapter_data"
local gw_bomberman_define = require "gw_bomberman_define"
local gw_bomberman_mgr = require "gw_bomberman_mgr"
local buildingBaseClass = require "gw_home_comp_building_survivor_base"
local newclass = newclass
local gw_ed = require "gw_ed"
local event_personalInfo = require "event_personalInfo"
local event = event
local GWG = GWG
local GWConst = GWConst
local os = require "os"
local util = require "util"
local ui_window_mgr = require "ui_window_mgr"
local ShaderCollectionWarmUp = CS.War.Render.ShaderCollectionWarmUp
local SpriteRenderer         = CS.UnityEngine.SpriteRenderer
local log = log
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

module("gw_home_comp_building_main")
---@class GWHomeCompBuildingMain: gw_home_comp_building_base
--当前统一都用GWHomeCompBuilding
local GWHomeCompBuilding = newclass("gw_home_comp_building_main", buildingBaseClass)

--- 构造器
---@see  override
function GWHomeCompBuilding:ctor()
    buildingBaseClass.ctor(self)
end
--- 初始化数据 
---@see  override
function GWHomeCompBuilding:Init()
    buildingBaseClass.Init(self)
end
function GWHomeCompBuilding:OnLoaded()
    buildingBaseClass.OnLoaded(self)
    self:OnEffectChange()
    self:RefreshBubbleData()
    -- 重置动画播放
    gw_gpu_animation_uitl.ResetGpuAnimatorStandState(self.transform)
    self:CreateSpriteBuildingShader()
end
---@public 刷新气泡
function GWHomeCompBuilding:RefreshBubbleData()
    local bomberOpen = gw_bomberman_mgr.CheckOpen()
    local noviceEventPass = gw_home_novice_chapter_data.CheckPassLevel()
    if bomberOpen and noviceEventPass then
        self:CreateBubbleEntity(GWG.GWConst.EHomeBubbleEntityType.HostageRescue, function()
            self:BubbleOnClick()
        end, nil)
    else
        self:DisposeBubbleEntity(GWG.GWConst.EHomeBubbleEntityType.HostageRescue)
    end
end
---@public 点击气泡回调
function GWHomeCompBuilding:BubbleOnClick()
    local unforced_guide_mgr = require "unforced_guide_mgr"
    if unforced_guide_mgr.GetCurGuide() == 5 then
        local unforced_guide_event_define = require "unforced_guide_event_define"
        event.Trigger(unforced_guide_event_define.click_bubble_rescue)
    end
    --打开解救人质界面
    ui_window_mgr:ShowModule("ui_bomberman")
end
---@public 设置建筑贴图表现
---@see  override
function GWHomeCompBuilding:SetBuildingTextureInfo()
    buildingBaseClass.SetBuildingTextureInfo(self)
    self.soldierNode = {};
    self.soldierObj = {};
    self.soldierData = {};
    self.soldierModels = {};
    local node = self.transform:Find("node/icon_root/soldierSlot")
    local childCount = node.childCount
    for i = 0, childCount - 1 do
        self.soldierNode[i + 1] = node:GetChild(i)
    end
    --todo 下面是把欢迎的模型放出来的方法。
    --self:CreateSoldier()
end

function GWHomeCompBuilding:CreateSpriteBuildingShader()
    if not GWG.GWHomeMgr.CreatedSpriteBuildingShader then
        local iconTrs = self.transform:Find("node/icon_root/icon")
        if not util.IsNullOrEmpty(iconTrs) then
            local spriteRenderer =  iconTrs:GetComponent(typeof(SpriteRenderer))
            if not util.IsNullOrEmpty(spriteRenderer) then
                local mat = spriteRenderer.sharedMaterial
                if mat.name == "Sprites-Default" then
                    mat.shader = ShaderCollectionWarmUp.Find("Custom/Sprites/SpritesBuildingNoZWrite");
                end
            end
        end
        GWG.GWHomeMgr.CreatedSpriteBuildingShader = true
    end
end

function GWHomeCompBuilding:CreateSoldier()
    local curTime = os.server_time() --获取当前服务器时间戳，以当前时间戳为基准计算等待时间。
    for i, v in pairs(self.soldierNode) do
        local model = self:CreateHeroAtTrans(i, v)
        --TODO 关于行动队列暂时没有特别好的主意，先手写控制吧。
        local actionQueue = {
            [1] = {
                action = function()

                end,
                finishTime = curTime + 2
            },
            [2] = {
                action = function()
                    model:OnHideBubble();
                    local pos = {
                        x = 4,
                        y = 0,
                        z = 4
                    }
                    model:SetUpdateLocalTween(pos, 2, function()
                        model:CheckAction()
                    end)
                end,
                waiting = -1
            },
            [3] = {
                action = function()
                    self.soldierModels[i] = nil;
                    model:Dispose()
                end,
                waiting = -1
            }
        }
        model:OnSetActionQueue(actionQueue)

    end
end

function GWHomeCompBuilding:CreateHeroAtTrans(index, trans)
    local res = "art/greatworld/home/<USER>/soldier/311.prefab";
    local sendValue = {
        text = "欢迎回来，指挥官！"
    }
    local modelId = GWG.GWHomeMgr.modelMgr.CreatedModel("gw_home_comp_model_worker", res, trans, GWG.GWConst.EHomeBubbleEntityType.Dialog, sendValue);
    self.soldierModels[index] = modelId;
    --table.insert(self.soldierModels,modelId);
    local model = GWG.GWHomeMgr.modelMgr.GetModelById(modelId);

    return model;
end

---@see 设置能否升级
---@see override
function GWHomeCompBuilding:SetCanUpgrade(canUpgrade, needCheck)
    buildingBaseClass.SetCanUpgrade(self, canUpgrade, needCheck)
end
function GWHomeCompBuilding:RegisterListener()
    buildingBaseClass.RegisterListener(self)
    self.onPlayerSchlossInfoChange = function()
        self:OnRoleSchlossChange()
    end
    event.Register(event_personalInfo.UPDATE_PERSONALISED_INFO, self.onPlayerSchlossInfoChange)
    self.bombermanUpdate = function()
        self:RefreshBubbleData()
    end
    event.Register(gw_bomberman_define.BOMBERMAN_DATA_UPDATE, self.bombermanUpdate)
    gw_ed.mgr:Register(gw_ed.GW_HOME_EVENT_UPDATE, self.bombermanUpdate)
end
function GWHomeCompBuilding:UnregisterListener()
    buildingBaseClass.UnregisterListener(self)
    event.Unregister(event_personalInfo.UPDATE_PERSONALISED_INFO, self.onPlayerSchlossInfoChange)
    event.Unregister(gw_bomberman_define.BOMBERMAN_DATA_UPDATE, self.bombermanUpdate)
end

function GWHomeCompBuilding:OnRoleSchlossChange()
    local bData = self.serData
    GWG.GWAdmin.PushBSComponent(self)
    if bData then
        gw_ed.mgr:Trigger(gw_ed.GW_HOME_INTERNAL_DATA_CHANGE, "BuildingNTF", bData)
    end
end

---@public 更新特效
function GWHomeCompBuilding:OnEffectChange()
    local helper_personalInfo = require "helper_personalInfo"
    local effectPath,effectId,effectScale,effectPos,effectBottomPath = helper_personalInfo.GetSchlossEffectPrefabPath(helper_personalInfo.SchlossPrefabType.building)
    self:PlayBottomEffect(effectBottomPath,effectId,effectScale)
    --判断路径
    if string.IsNullOrEmpty(effectPath) then
        return
    end
    --特效路径
    if self.effectPath and self.effectPath == effectPath then
        return
    end
    self.effectPath = effectPath
    --删除特效
    if self.effectEntity then
        self.effectEntity:Dispose()
        self.effectEntity = nil
    end
    --特效参数
    local effectParams = {
        path = effectPath,
        position = effectPos,
        scale = { x = effectScale, y = effectScale, z = effectScale },
        rotation = { x = 0, y = 315, z = 0 },
    }
    self.effectEntity = gw_home_effect_object.CM("Home_gw_home_effect_object_" .. effectId):Init(effectId, self.effect_root, effectParams)
end

function GWHomeCompBuilding:PlayBottomEffect(path, effectId, effectScale)
    --删除特效
    if self.effectBottomEntity then
        self.effectBottomEntity:Dispose()
        self.effectBottomEntity = nil
    end
    if string.IsNullOrEmpty(path) then
        return
    end
    --特效参数
    local effectParams = {
        path = path,
        position = effectPos,
        scale = { x = effectScale, y = effectScale, z = effectScale },
        rotation = { x = 0, y = 315, z = 0 },
    }
    self.effectBottomEntity = gw_home_effect_object.CM("Home_gw_home_effect_object_" .. effectId .. "_bottom"):Init(effectId, self.effect_root, effectParams)
end

--- 回收
---@see  override
function GWHomeCompBuilding:Recycle()
    buildingBaseClass.Recycle(self)
end

--- 弃用
---@see  override
function GWHomeCompBuilding:Dispose()
    self.effectPath = nil
    if self.effectEntity then
        self.effectEntity:Dispose()
        self.effectEntity = nil
    end
    --删除特效
    if self.effectBottomEntity then
        self.effectBottomEntity:Dispose()
        self.effectBottomEntity = nil
    end
    if self.soldierModels then
        for i, v in pairs(self.soldierModels) do
            local model = GWG.GWHomeMgr.modelMgr.GetModelById(v);
            if model then
                model:Dispose()
            end
            v = nil;
        end
    end
    buildingBaseClass.Dispose(self)
end
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

return GWHomeCompBuilding