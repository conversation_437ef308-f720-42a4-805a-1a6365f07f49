﻿local GWG = GWG
local game_scheme = require "game_scheme"

local M = {}

-- 配置ID常量
local CONFIG_IDS = {
    LORD_PATROL_RADIUS = 8401,      -- 领主巡逻范围区间（最小半径和最大半径）
    LORD_PATROL_START = 8402,       -- 领主巡逻起点坐标（x#y）
    LORD_PATROL_REST_TIME = 8403,   -- 领主巡逻随机待机时间区间（单位毫秒）
    CHAT_PATROL_LANG_IDS = 8404,    -- 巡逻时的闲聊语言ID
    CHAT_REST_LANG_IDS = 8405,      -- 休息时的闲聊语言ID
    CHAT_INTERACT_LANG_IDS = 8406,  -- 交互时的闲聊语言ID
    CHAT_MOVE_LANG_IDS = 8407,      -- 寻路时的闲聊语言ID
    LORD_BUBBLE_PARAMS = 8408,      -- 闲聊气泡参数（持续时长#触发cd）
    LORD_MODEL_PARAMS = 8411,       -- 领主模型缩放#位移速度#狗的模型缩放#狗的移速（除以100）
}

-- 默认配置（硬编码配置，作为备用值）
local DEFAULT_CONFIG = {
    -- 领主配置
    lord = {
        -- 移动相关
        moveSpeed = 2.0,                    -- 移动速度
        rotationTime = 0.3,                 -- 转向时间
        minMoveDistance = 0.1,              -- 最小移动距离
        
        -- 巡逻相关（将从配置读取）
        patrolMinRadius = 3,                -- 巡逻最小半径
        patrolMaxRadius = 8,                -- 巡逻最大半径
        patrolBornX = 50,                  -- 出生起点X坐标
        patrolBornY = 50,                  -- 出生起点Y坐标
        patrolStartX = 50,                  -- 巡逻起点X坐标（这里解释下，出生之后先往patrolStartX patrolStartY走，走到之后再开始后续的巡逻）
        patrolStartY = 45,                  -- 巡逻起点Y坐标
        patrolMinRestTime = 1,           -- 最小休息时间
        patrolMaxRestTime = 5,           -- 最大休息时间
        
        -- 模型相关（将从配置读取）
        modelScale = 1.0,                   -- 模型缩放
        
        -- 交互相关
        interactRadius = 2,                 -- 交互半径
        interactTime = 2.0,                 -- 交互时间
    },
    
    -- 流浪狗配置
    strayDog = {
        -- 移动相关
        moveSpeed = 1.8,                    -- 移动速度
        rotationTime = 0.25,                -- 转向时间
        
        -- 跟随相关
        followDistance = 1,                 -- 跟随距离
        followCheckInterval = 0.3,          -- 跟随检查间隔（秒）
        maxFollowDistance = 8,              -- 最大跟随距离
        
        -- 巡逻相关
        patrolMinRadius = 4,                -- 巡逻最小半径
        patrolMaxRadius = 8,                -- 巡逻最大半径
        patrolMinRestTime = 1.5,            -- 最小休息时间（秒）
        patrolMaxRestTime = 4.0,            -- 最大休息时间（秒）
        
        -- 生成相关
        spawnRadius = 3,                    -- 生成半径
        spawnMaxAttempts = 20,              -- 最大生成尝试次数
        
        -- 交互相关
        interactRadius = 1.5,               -- 交互半径
        interactTime = 1.5,                 -- 交互时间

        -- 模型相关（将从配置读取）
        modelScale = 1.0,                   -- 模型缩放
    },
    
    -- 寻路配置
    pathfinding = {
        -- 路径优化
        simplifyThreshold = 0.1,            -- 路径简化阈值
        smoothRadius = 0.4,                 -- 路径平滑半径
        smoothAngleThreshold = 90,          -- 平滑角度阈值（度）
        
        -- 性能相关
        maxPathLength = 100,                -- 最大路径长度
        maxSearchNodes = 1000,              -- 最大搜索节点数
        searchTimeout = 100,                -- 搜索超时（毫秒）
    },
    
    -- 路径特效配置
    pathEffect = {
        interval = 1.5,                     -- 特效间隔
        density = 1.0,                      -- 特效密度系数
        startOffset = 0.2,                  -- 起点偏移系数
        effectLifetime = 5.0,               -- 特效生命周期（秒）
    },
    
    -- 气泡配置
    bubble = {
        offsetX = 89,                       -- X偏移
        offsetY = 60,                       -- Y偏移
        fadeTime = 0.5,                     -- 淡入淡出时间

        -- 显示配置
        showDuration = 5.0,                 -- 气泡显示时长（秒）
        cooldownTime = 10.0,                -- 触发CD时间（秒）
        patrolCooldown = 10.0,              -- 巡逻触发CD（秒）
        restCooldown = 10.0,                -- 休息触发CD（秒）

        -- 闲聊文本配置（将从配置读取）
        chatLangIds = {
            -- 巡逻时的闲聊（配置ID: 8404）
            patrol = {},
            -- 休息时的闲聊（配置ID: 8405）
            rest = {},
            -- 交互时的闲聊（配置ID: 8406）
            interact = {},
            -- 寻路时的闲聊（配置ID: 8407）
            move = {}
        },
    },
    
    -- 调试配置
    debug = {
        enablePathDebug = false,            -- 启用路径调试
        enableMoveDebug = false,            -- 启用移动调试
        enableAIDebug = false,              -- 启用AI调试
        enableEffectDebug = false,          -- 启用特效调试
    }
}

-- 运行时配置（合并默认配置和策划配置）
local runtimeConfig = {}
local init = false
-- 初始化配置
function M.InitConfig()
    if init then
        return
    end
    GWG.GWAdmin.SwitchUtility.HomeLog("初始化家园系统配置")
    
    -- 复制默认配置
    runtimeConfig = M.DeepCopy(DEFAULT_CONFIG)
    
    -- 读取策划配置并覆盖默认值
    M.LoadConfig()
    
    GWG.GWAdmin.SwitchUtility.HomeLog("家园系统配置初始化完成")
    init = true
end

-- 读取策划配置
function M.LoadConfig()
    -- 读取领主巡逻范围配置
    local patrolRadiusCfg = game_scheme:InitBattleProp_0(CONFIG_IDS.LORD_PATROL_RADIUS)
    if patrolRadiusCfg and patrolRadiusCfg.szParam and patrolRadiusCfg.szParam.data then
        runtimeConfig.lord.patrolMinRadius = patrolRadiusCfg.szParam.data[0] or runtimeConfig.lord.patrolMinRadius
        runtimeConfig.lord.patrolMaxRadius = patrolRadiusCfg.szParam.data[1] or runtimeConfig.lord.patrolMaxRadius
        GWG.GWAdmin.SwitchUtility.HomeLog("读取领主巡逻范围配置: "..runtimeConfig.lord.patrolMinRadius.."-"..runtimeConfig.lord.patrolMaxRadius)
    end
    
    -- 读取领主巡逻起点配置
    local patrolStartCfg = game_scheme:InitBattleProp_0(CONFIG_IDS.LORD_PATROL_START)
    if patrolStartCfg and patrolStartCfg.szParam and patrolStartCfg.szParam.data then
        runtimeConfig.lord.patrolBornX = patrolStartCfg.szParam.data[0] or runtimeConfig.lord.patrolBornX
        runtimeConfig.lord.patrolBornY = patrolStartCfg.szParam.data[1] or runtimeConfig.lord.patrolBornY
        runtimeConfig.lord.patrolStartX = patrolStartCfg.szParam.data[2] or runtimeConfig.lord.patrolStartX
        runtimeConfig.lord.patrolStartY = patrolStartCfg.szParam.data[3] or runtimeConfig.lord.patrolStartY
        GWG.GWAdmin.SwitchUtility.HomeLog("读取领主巡逻起点配置: ("..runtimeConfig.lord.patrolStartX..","..runtimeConfig.lord.patrolStartY..")")
    end
    
    -- 读取领主巡逻休息时间配置
    local patrolRestCfg = game_scheme:InitBattleProp_0(CONFIG_IDS.LORD_PATROL_REST_TIME)
    if patrolRestCfg and patrolRestCfg.szParam and patrolRestCfg.szParam.data then
        runtimeConfig.lord.patrolMinRestTime = tonumber(string.format("%.1f", patrolRestCfg.szParam.data[0]/100)) or runtimeConfig.lord.patrolMinRestTime
        runtimeConfig.lord.patrolMaxRestTime = tonumber(string.format("%.1f", patrolRestCfg.szParam.data[1]/100)) or runtimeConfig.lord.patrolMaxRestTime
        GWG.GWAdmin.SwitchUtility.HomeLog("读取领主巡逻休息时间配置: "..runtimeConfig.lord.patrolMinRestTime.."-"..runtimeConfig.lord.patrolMaxRestTime.."ms")
    end
    
    -- 读取气泡参数配置
    local bubbleCfg = game_scheme:InitBattleProp_0(CONFIG_IDS.LORD_BUBBLE_PARAMS)
    if bubbleCfg and bubbleCfg.szParam and bubbleCfg.szParam.data then
        runtimeConfig.bubble.showDuration = tonumber(string.format("%.1f", bubbleCfg.szParam.data[0]/100)) or runtimeConfig.bubble.showDuration
        runtimeConfig.bubble.cooldownTime = tonumber(string.format("%.1f", bubbleCfg.szParam.data[1]/100))  or runtimeConfig.bubble.cooldownTime
        runtimeConfig.bubble.patrolCooldown = runtimeConfig.bubble.cooldownTime
        runtimeConfig.bubble.restCooldown = runtimeConfig.bubble.cooldownTime
        GWG.GWAdmin.SwitchUtility.HomeLog("读取气泡参数配置: 持续"..runtimeConfig.bubble.showDuration.."ms CD"..runtimeConfig.bubble.cooldownTime.."ms")
    end
    
    -- 读取其他参数配置
    local modelCfg = game_scheme:InitBattleProp_0(CONFIG_IDS.LORD_MODEL_PARAMS)
    if modelCfg and modelCfg.szParam and modelCfg.szParam.data then
        runtimeConfig.lord.modelScale = modelCfg.szParam.data[0] / 100.0 or runtimeConfig.lord.modelScale
        runtimeConfig.lord.moveSpeed = modelCfg.szParam.data[1] / 100.0 or runtimeConfig.lord.moveSpeed

        runtimeConfig.strayDog.modelScale = modelCfg.szParam.data[2] / 100.0 or runtimeConfig.strayDog.modelScale
        runtimeConfig.strayDog.moveSpeed = modelCfg.szParam.data[3] / 100.0 or runtimeConfig.strayDog.moveSpeed
      
        GWG.GWAdmin.SwitchUtility.HomeLog("读取领主模型参数配置: 缩放"..runtimeConfig.lord.modelScale.." 速度"..runtimeConfig.lord.moveSpeed)
        GWG.GWAdmin.SwitchUtility.HomeLog("读取流浪狗模型参数配置: 缩放"..runtimeConfig.strayDog.modelScale.." 速度"..runtimeConfig.strayDog.moveSpeed)
    end

    -- 读取闲聊文本配置
    M.LoadChatLangIds()
end

-- 读取闲聊文本配置
function M.LoadChatLangIds()
    -- 读取巡逻时的闲聊语言ID
    local patrolChatCfg = game_scheme:InitBattleProp_0(CONFIG_IDS.CHAT_PATROL_LANG_IDS)
    if patrolChatCfg and patrolChatCfg.szParam and patrolChatCfg.szParam.data then
        runtimeConfig.bubble.chatLangIds.patrol = {}
        for i = 0, #patrolChatCfg.szParam.data do
            local langId = patrolChatCfg.szParam.data[i]
            if langId and langId > 0 then
                table.insert(runtimeConfig.bubble.chatLangIds.patrol, langId)
            end
        end
        GWG.GWAdmin.SwitchUtility.HomeLog("读取巡逻闲聊配置: "..#runtimeConfig.bubble.chatLangIds.patrol.."个语言ID")
    end

    -- 读取休息时的闲聊语言ID
    local restChatCfg = game_scheme:InitBattleProp_0(CONFIG_IDS.CHAT_REST_LANG_IDS)
    if restChatCfg and restChatCfg.szParam and restChatCfg.szParam.data then
        runtimeConfig.bubble.chatLangIds.rest = {}
        for i = 0, #restChatCfg.szParam.data do
            local langId = restChatCfg.szParam.data[i]
            if langId and langId > 0 then
                table.insert(runtimeConfig.bubble.chatLangIds.rest, langId)
            end
        end
        GWG.GWAdmin.SwitchUtility.HomeLog("读取休息闲聊配置: "..#runtimeConfig.bubble.chatLangIds.rest.."个语言ID")
    end

    -- 读取交互时的闲聊语言ID
    local interactChatCfg = game_scheme:InitBattleProp_0(CONFIG_IDS.CHAT_INTERACT_LANG_IDS)
    if interactChatCfg and interactChatCfg.szParam and interactChatCfg.szParam.data then
        runtimeConfig.bubble.chatLangIds.interact = {}
        for i = 0, #interactChatCfg.szParam.data do
            local langId = interactChatCfg.szParam.data[i]
            if langId and langId > 0 then
                table.insert(runtimeConfig.bubble.chatLangIds.interact, langId)
            end
        end
        GWG.GWAdmin.SwitchUtility.HomeLog("读取交互闲聊配置: "..#runtimeConfig.bubble.chatLangIds.interact.."个语言ID")
    end

    -- 读取寻路时的闲聊语言ID
    local moveChatCfg = game_scheme:InitBattleProp_0(CONFIG_IDS.CHAT_MOVE_LANG_IDS)
    if moveChatCfg and moveChatCfg.szParam and moveChatCfg.szParam.data then
        runtimeConfig.bubble.chatLangIds.move = {}
        for i = 0, #moveChatCfg.szParam.data do
            local langId = moveChatCfg.szParam.data[i]
            if langId and langId > 0 then
                table.insert(runtimeConfig.bubble.chatLangIds.move, langId)
            end
        end
        GWG.GWAdmin.SwitchUtility.HomeLog("读取寻路闲聊配置: "..#runtimeConfig.bubble.chatLangIds.move.."个语言ID")
    end
end

-- 获取配置的公共接口
function M.GetLordConfig()
    return runtimeConfig.lord
end

function M.GetStrayDogConfig()
    return runtimeConfig.strayDog
end

function M.GetPathfindingConfig()
    return runtimeConfig.pathfinding
end

function M.GetPathEffectConfig()
    return runtimeConfig.pathEffect
end

function M.GetBubbleConfig()
    return runtimeConfig.bubble
end

function M.GetDebugConfig()
    return runtimeConfig.debug
end

-- 获取完整配置
function M.GetAllConfig()
    return runtimeConfig
end

-- 设置调试配置
function M.SetDebugConfig(debugType, enabled)
    if runtimeConfig.debug[debugType] ~= nil then
        runtimeConfig.debug[debugType] = enabled
        GWG.GWAdmin.SwitchUtility.HomeLog("设置调试配置 "..debugType..": "..tostring(enabled))
    end
end

-- 深拷贝函数
function M.DeepCopy(original)
    local copy
    if type(original) == 'table' then
        copy = {}
        for key, value in pairs(original) do
            copy[key] = M.DeepCopy(value)
        end
    else
        copy = original
    end
    return copy
end

-- 重新加载配置
function M.ReloadConfig()
    GWG.GWAdmin.SwitchUtility.HomeLog("重新加载家园系统配置")
    M.LoadConfig()
end

-- 打印当前配置（调试用）
function M.PrintCurrentConfig()
    GWG.GWAdmin.SwitchUtility.HomeLog("=== 家园系统当前配置 ===")
    GWG.GWAdmin.SwitchUtility.HomeLog("领主移动速度: "..runtimeConfig.lord.moveSpeed)
    GWG.GWAdmin.SwitchUtility.HomeLog("领主巡逻范围: "..runtimeConfig.lord.patrolMinRadius.."-"..runtimeConfig.lord.patrolMaxRadius)
    GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗跟随距离: "..runtimeConfig.strayDog.followDistance)
    GWG.GWAdmin.SwitchUtility.HomeLog("路径特效间隔: "..runtimeConfig.pathEffect.interval)
    GWG.GWAdmin.SwitchUtility.HomeLog("气泡持续时间: "..runtimeConfig.bubble.duration.."ms")
end


return M
