local AsyncEntity = {}

local EntityHybridUtility = CS.Unity.Entities.EntityHybridUtility
local table  = table
local asset_loader = require "asset_loader"
local xpcall = xpcall
local pairs  = pairs
local log = require "log"
local LRU = require "lru"
local max = math.max
local floor = math.floor
local BoxCollider = CS.UnityEngine.BoxCollider

-- 对象池
local entryPool = {}
local infoPool  = {}
local metaPool  = {}

-- 分配一个 entry，直接作为列表使用
local function allocEntry()
    return table.remove(entryPool) or {}
end

-- 回收 entry，清空列表
local function freeEntry(entry)
    for i = #entry, 1, -1 do
        entry[i] = nil
    end
    table.insert(entryPool, entry)
end

-- 分配 info 对象
local function allocInfo()
    return table.remove(infoPool) or {}
end

-- 清理并回收 info
local function freeInfo(info)
    info.onComplete = nil
    table.insert(infoPool, info)
end

-- 分配 meta 对象
local function allocMeta()
    return table.remove(metaPool) or {}
end

-- 清理并回收 meta
local function freeMeta(meta)
    -- 显式清除字段
    meta.entry = nil
    meta.index = nil
    table.insert(metaPool, meta)
end
local function freeMeta(meta)
    table.insert(metaPool, meta)
end

-- 请求 ID 自增
local nextReqId = 1
-- 请求元数据映射
local reqMap = {}
-- 存储已创建的实体或实体列表
local createdMap = {}

-- pendingRequests[path] = entry 列表
local pendingRequests = {}
-- 资源加载后缓存 prefab
local prefabCache = {}
-- 路径是否正在加载的标记
local loadingRequests = {}

local asset_loader_cache = {}
local loader_path_map = {}

-- 发起异步加载
local function startLoad(path)
    loadingRequests[path] = true
    local loader = asset_loader(path, "async_entity")
    loader:load(function(prefabGameObject)
        if not prefabGameObject or not loadingRequests[path] then
            loadingRequests[path] = nil
            loader:Dispose()
            return
        end

        loadingRequests[path] = nil
        if prefabGameObject.asset then
            prefabCache[path] = prefabGameObject.asset
            local instanceId = prefabGameObject.asset:GetInstanceID()
            asset_loader_cache[instanceId] = loader
            loader_path_map[instanceId] = path
        end
    end)
end

--- 提交异步 Instantiate 请求，返回 reqId
-- @param path
-- @param onComplete
-- @param layer 可选
-- @param sortingOrder 可选
function AsyncEntity.RequestInstantiate(path, onComplete, sortingOrder,zTestAlways,layer)
    -- 获取或创建 entry（列表）
    local entry = pendingRequests[path]
    if not entry then
        entry = allocEntry()
        pendingRequests[path] = entry
    end
    local list = entry

    -- 创建 info
    local info = allocInfo()
    local id = nextReqId
    info.id = id
    info.onComplete = onComplete
    info.layer = layer or 0
    info.sortingOrder = sortingOrder or 0
    info.path = path
    info.zTestAlways = zTestAlways or false
    list[#list+1] = info
    local index = #list
    -- 保存请求元数据
    local meta = allocMeta()
    meta.entry = entry
    meta.index = index
    reqMap[id] = meta

    nextReqId = id + 1
    -- 如果资源未加载且未加载中，则发起加载
    if not prefabCache[path] and not loadingRequests[path] then
        startLoad(path)
    end
    return id
end

local function DisposeLoader(instanceId)
    local loader = asset_loader_cache[instanceId]
    if loader then
        loader:Dispose()
        local path = loader_path_map[instanceId]
        if path then
            prefabCache[path] = nil
            loadingRequests[path] = nil
            loader_path_map[instanceId] = nil
        end
        asset_loader_cache[instanceId] = nil
    end
end

--- 取消并销毁请求，包括尚未执行的和已创建的实体
-- @param requestId
function AsyncEntity.Dispose(requestId)
    -- 取消 pending
    local meta = reqMap[requestId]
    if meta then
        local entry = meta.entry
        local list  = entry
        local idx   = meta.index
        local info = list[idx]
        if idx < #list then
            local last  = list[#list]
            list[idx] = last
            -- 更新被替换 info 的索引
            local movedMeta = reqMap[last.id]
            if movedMeta then movedMeta.index = idx end
        end
        list[#list] = nil
        freeInfo(info)
        reqMap[requestId] = nil
        freeMeta(meta)
    end
    -- 销毁已创建实体
    local created = createdMap[requestId]
    if created then
        local instanceID = EntityHybridUtility.DestroyEntity(created)
        if instanceID ~= 0 then
            DisposeLoader(instanceID)
        end
        createdMap[requestId] = nil
    end
end

local outputEntities = {}

local function ExceptionsHander(err)
    log.Error(err)
end

--- 每帧调用，处理所有 pendingRequests
function AsyncEntity.Update()
    for path, entry in pairs(pendingRequests) do
        local prefab = prefabCache[path]
        if prefab then
            pendingRequests[path] = nil
            local list = entry
            if #list > 0 then
                local first = list[1]
                if #list == 1 then
                    -- 单实例化
                    local e = EntityHybridUtility.Instantiate(prefab, first.layer, first.sortingOrder,first.zTestAlways)
                    createdMap[first.id] = e
                    xpcall(first.onComplete, ExceptionsHander,e)
                    local m = reqMap[first.id]
                    reqMap[first.id] = nil
                    freeMeta(m)
                    freeInfo(first)
                else
                    -- 批量实例化
                    local count = EntityHybridUtility.Instantiate(prefab, #list, first.layer, first.sortingOrder,first.zTestAlways, outputEntities)
                    local e
                    for i, info in ipairs(list) do
                        e = outputEntities[i]
                        createdMap[info.id] = e
                        xpcall(info.onComplete, ExceptionsHander, e)
                        local m = reqMap[info.id]
                        reqMap[info.id] = nil
                        freeMeta(m)
                        freeInfo(info)
                    end

                    for i=count, 1, -1 do 
                         outputEntities[i] = nil
                    end

                end
            end
            freeEntry(entry)
        end
    end
end

function AsyncEntity.GetPrefabFromCache(path)
    return prefabCache[path]
end

local ColliderExtentsCache = {}
function AsyncEntity.GetColliderIntSize(entity, path, gameobject, volatileSize)
    local extent = ColliderExtentsCache[path]
    if extent then
        if volatileSize then
            return type(extent) == "table" and extent[1] * volatileSize / extent[2]
        end
        return extent
    else
        if entity then
            local x, y, z = EntityHybridUtility.GetColliderSize(entity)
            if x and z then
                extent = floor(max(x, z) * 0.5 + 0.5)
            end
        elseif gameobject then
            local collider = gameobject:GetComponentInChildren(typeof(BoxCollider), true)
            if not collider then
                extent = 0
            else
                local size = collider.bounds.size
                extent = floor(max(size.x, size.z) * 0.5 + 0.5)
            end
        end
        if not volatileSize then
            ColliderExtentsCache[path] = extent
        else
            ColliderExtentsCache[path] = { extent, volatileSize }
        end
        return extent
    end
end

event.Register(event.CSUpdate, AsyncEntity.Update)

return AsyncEntity
