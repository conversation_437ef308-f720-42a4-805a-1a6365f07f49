﻿--region FileHead
--- ui_speed_up_panel.txt
-- author:  nyz
-- ver:     1.0
-- desc:    
-------------------------------------------------
--endregion 

--region Require
local require = require
local type = type
local pairs = pairs
local ipairs = ipairs
local typeof = typeof
local string = string
local table = table
local tostring = tostring
local ui_util = require "ui_util"

local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local Image = CS.UnityEngine.UI.Image
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local enum_define = require "enum_define"
local class = require "class"
local ui_base = require "ui_base"
local module_scroll_list = require "scroll_list"
local gw_totalrecharge_mgr = require "gw_totalrecharge_mgr"
local Common_Util = CS.Common_Util.UIUtil
local log = require "log"
local e_handler_mgr = require "e_handler_mgr"
local lang 			        = require "lang"
local iui_item_detail = require "iui_item_detail"
local item_data = require "item_data"
local card_assets = require "card_sprite_asset"
local math = math
local util = require "util"
local goods_item = require "goods_item_new"
local Color                 = CS.UnityEngine.Color
local CanvasGroup = CS.UnityEngine.CanvasGroup

local technology_data = require "technology_data"
local game_scheme = require "game_scheme"
local net_login_module = require "net_login_module"
local ScrollRect = CS.UnityEngine.UI.ScrollRect
local reward_item = require "reward_item"
local time_util = require "time_util"
local os = os
local player_mgr = require "player_mgr"
local GWG = GWG
local GWConst = GWConst
local gw_attribute_bonus = require "gw_attribute_bonus"
--endregion 

--region ModuleDeclare
module("ui_speed_up_panel")
local ui_path = "ui/prefabs/gw/gw_technologysystem/uispeeduppanel.prefab"
local window = nil
local UISpeedUpView = {}
local needTime = 0;
local needDiamond = 0;
local diamondTxt = nil;
local selectIndex = -1;
local curTime = 0;
local technologySpriteAsset = nil;
local spriteAsset = nil;
local soldierSpriteAsset = nil;
local buildingAsset = nil;
--endregion 

local _GiftItemBgIndex = {
    Yellow = 0,
    Blue = 1,
}

local _CountdownBgIndex = {
    Green = 0,
    Blue = 1,
}

local _CountdownTextColor = {
    Green = { r = 135 / 255, g = 244 / 255, b = 37 / 255, a = 1 },
    Blue = { r = 91 / 255, g = 203 / 255, b = 255 / 255, a = 1 },
}

local _BuyBtnTextColor = {
    Normal = { r = 255 / 255, g = 254 / 255, b = 202 / 255, a = 1 },
    Gray = { r = 255 / 255, g = 255 / 255, b = 255 / 255, a = 1 },
}

local _BuyBtnTextOutlineColor = {
    Normal = { r = 153 / 255, g = 51 / 255, b = 0 / 255, a = 1 },
    Gray = { r = 90 / 255, g = 90 / 255, b = 90 / 255, a = 1 },
}

local _GiftListItemSize = { x = 628, y = 143 }

GiftType = {
    --- 普通礼包（日周月）
    ActivityContent = "ActivityContent",
    --- 特惠礼包
    SpecialGift = "SpecialGift",
    --- 每日印记&材料礼包
    DailyMaterialRewardGift = "DailyMaterialRewardGift",
}

--region WidgetTable
UISpeedUpView.widget_table = {
    Btn_Ad_1 = { path = "contentBg/MainPanel/Content/Auto_Ad_1", type = "Button", event_name = "OnBtn_Ad_1ClickedProxy" },
    Btn_Ad_2 = { path = "contentBg/MainPanel/Content/Auto_Ad_2", type = "Button", event_name = "OnBtn_Ad_2ClickedProxy" },
    Btn_closeBtn = { path = "Auto_closeBtn", type = "Button", event_name = "OnCloseBtnClick" },
    Btn_OneClickToSpeedUp = { path = "contentBg/MainPanel/Auto_OneClickToSpeedUp", type = "Button", event_name = "OnBtn_OneClickToSpeedUpClickedProxy",0.5 },

    rect_table = {path = "contentBg/MainPanel/Content/ScrollList/Viewport/Content",type = ScrollRectTable},
    gift_rect_table = { path = "contentBg/MainPanel/Content/Auto_GiftList/Auto_ScrollView/GiftViewport/Auto_Content", type = ScrollRectTable },

    queue01Icon = {path = "contentBg/MainPanel/ClockDown/Info/Icon",type = "Image"},
    hospitalIcon = {path = "contentBg/MainPanel/ClockDown/Info/hospitalIcon",type = "Image"},
    buildingIcon = {path = "contentBg/MainPanel/ClockDown/Info/buildingIcon",type = "Image"},
    queue01TimeSlider = {path = "contentBg/MainPanel/ClockDown/Info/Slider",type = "Slider"},
    queue01TimeTxt = {path = "contentBg/MainPanel/ClockDown/Info/Slider/contentText",type = "Text"},
    queue01TimeDesc = {path = "contentBg/MainPanel/ClockDown/Info/Desc",type = "Text"},

    Img_GiftList = { path = "contentBg/MainPanel/Content/Auto_GiftList", type = "Image", event_name = "" },
    Rtsf_RelativeList = { path = "contentBg/MainPanel/Content/ScrollList", type = "RectTransform" },
    Rtsf_ScrollView = { path = "contentBg/MainPanel/Content/Auto_GiftList/Auto_ScrollView", type = "RectTransform", event_name = "" },
    canvas_group = {path = "", type = "CanvasGroup"},

}
--endregion 

--function 设置View-Controller模式的UI
-- return type  ---- 未定义/VC/纯V   
-- 注意，View-Controller模式的ui必须要重写这个接口
function UISpeedUpView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end
--endregion 

--region WindowInit
--[[窗口初始化]]
function UISpeedUpView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)
    self:SubscribeEvents()
    self.queue01TimeTicker = nil;
    
    needTime = 0;
    needDiamond = 0;
    diamondTxt = nil;
    spriteAsset = spriteAsset or card_assets.CreateSpriteAsset()--card_assets.CreateTechnologyIconAsset()
    technologySpriteAsset = technologySpriteAsset or card_assets.CreateTechnologyIconAsset()
    soldierSpriteAsset = soldierSpriteAsset or card_assets.CreateSoldierHeadAsset()
    buildingAsset = buildingAsset or card_assets.CreateBuildingAsset()
    
    self:InitScrollRectTable()
    
    self.oriParam = {
        RelativeListSize = self.Rtsf_RelativeList.sizeDelta,
        GiftListSize = self.Img_GiftList.rectTransform.sizeDelta
    }
    
    --region User
    --endregion 
end --///<<< function

--endregion 


--region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UISpeedUpView:OnShow()
    self.__base:OnShow()
    self:OnPlayAShowAni(nil, nil)
end --///<<< function

--endregion 

--region WindowOnHide
--[[界面隐藏时调用]]
function UISpeedUpView:OnHide()
    self.__base:OnHide()    
end --///<<< function

--endregion 


--region WindowClose
function UISpeedUpView:Close()
    --log.Error("Close")
    local force_guide_system = require "force_guide_system"
    local force_guide_event = require "force_guide_event"
    force_guide_system.TriComEvent(force_guide_event.cEventSpeedUpPanelClick)
    
    if self.rect_table then
        self.rect_table:ItemsDispose()
    end
    if self.gift_rect_table then
        self.gift_rect_table:ItemsDispose()
    end
    self.notFirstOpen1 = nil
    self.notFirstOpen2 = nil

    if self.queue01TimeTicker then
        util.RemoveDelayCall(self.queue01TimeTicker)
        self.queue01TimeTicker = nil
    end
    if spriteAsset then
        spriteAsset:Dispose()
        spriteAsset = nil
    end
    if technologySpriteAsset then
        technologySpriteAsset:Dispose()
        technologySpriteAsset = nil;
    end
    if soldierSpriteAsset then
        soldierSpriteAsset:Dispose()
        soldierSpriteAsset = nil;
    end
    if buildingAsset then
        buildingAsset:Dispose()
        buildingAsset = nil
    end
    self.__base:Close()
    window = nil
    needTime = 0;
    needDiamond = 0;
    diamondTxt = nil;
    selectIndex = -1;
    curTime = 0;
    --region User
    --endregion 
end --///<<< function
--endregion 

function UISpeedUpView:SubscribeEvents()    
    -- 注意 事件建议使用self:RegisterEvent(event_name,func)来注册，不需要手动维护注销了    
end

---********************功能函数区**********---
--region 礼包相关
--- 获取倒计时文本
---@param giftType string
---@param remainingTime number
---@return string
local function GetCountdownText(remainingTime)
    -- 剩余时间大于1天
    local template = nil
    if (remainingTime > 86400) then
        -- 天 小时
        template = ("#D " .. lang.Get(36061) .. " #H " .. lang.Get(15801))
    else
        -- 时 分 秒
        template = "#H:#M:#S"
    end
    local text = time_util.FormatTime5(remainingTime)

    -- 特惠礼包显示礼包过期时间
    --if (giftType == GiftType.SpecialGift) then
    --    --return (lang.Get(209) .. text)     -- "剩余时间："
    --    return text
    --end

    -- 日周月礼包显示刷新时间
    --return string.format(lang.Get(80313), text) -- "%s后刷新"
    return text
end

--获取关联礼包剩余倒计时
local function GetRemainingTime(timeStamp)
    local remainingTime = timeStamp - net_login_module.GetServerTime() --获取服务器时间
    remainingTime = math.max(math.floor(remainingTime), 0)
    return remainingTime
end

function UISpeedUpView:InitScrollRectTable()
    self.rect_table.onItemRender = OnItemRender;
    self.rect_table.onItemDispose = function(scroll_rect_item,index)
        if scroll_rect_item then
            if scroll_rect_item.data and scroll_rect_item.data[3] then
                scroll_rect_item.data[3]:Dispose()
            end
            local useDiamond = scroll_rect_item:Get("UseDiamond");
            useDiamond.onClick:RemoveAllListeners();
            local useItem = scroll_rect_item:Get("UseItem");
            useItem.onClick:RemoveAllListeners();
        end
    end
    
    self.gift_rect_table.onItemRender = function(...)
        self:OnGiftListItemRender(...)
    end
    self.gift_rect_table.onItemDispose = function(...)
        self:OnGiftListItemDispose(...)
    end
end

function UISpeedUpView:OnGiftListItemDispose(item,index)
    if not item then
        return
    end
    if (not item.data) then
        return
    end

    if item.data.countdownTicker then
        util.RemoveDelayCall(item.data.countdownTicker)
        item.data.countdownTicker = nil
    end

    if item.data.goodsItems then
        for i, v in pairs(item.data.goodsItems) do
            if v then
                v:Dispose()
            end
        end
        item.data.goodsItems = nil
    end
    item.data = nil
end

function UISpeedUpView:OnGiftListItemRender(item, index, data)
    --log.Log("ff:OnGiftListItemRender" .. index)
    --local ui_gift_gw_mgr = require("ui_gift_gw_mgr")
    --ui_gift_gw_mgr:OnGiftListItemRender(item, data, self.VData)
    item.data = item.data or { index = 0, data = nil, goodsItems = {}, countdownTicker = nil }
    item.data.index = index
    item.data.data = data
    -- 组件
    local bgSS = item:Get("BgSS")
    local nameText = item:Get("NameText")
    local goodsListScrollRect = item:Get("GoodsListScrollRect")
    local goodsListContent = item:Get("GoodsListContent")
    local tag = item:Get("Tag")
    local tagText = item:Get("TagText")
    local countdownSS = item:Get("CountdownSS")
    local countdownText = item:Get("CountdownText")
    local limitText = item:Get("LimitText")
    local buyBtn = item:Get("BuyBtn")
    local buyBtnSS = item:Get("BuyBtnSS")
    local buyBtnText = item:Get("BuyBtnText")
    local comRechargeScore = item:Get("ComRechargeScore")
    -- 设置
    gw_totalrecharge_mgr.SetRechargeScore(data.rechargeId,comRechargeScore,self)
    local purchaseLimit = data.limitNum
    local purchasedTimes = data.limitNum - data.curCanBuyNum
    local endTime = data.freshTime
    nameText.text = lang.Get(data.name)
    local isShowTag = data.multiples ~= 0
    tag:SetActive(isShowTag)
    if isShowTag then
        tagText.text = string.format2("{%s1}\n{%s2}%", lang.Get(9185), data.multiples)
        tagText.fontSize = 18
        tagText.transform.sizeDelta = { x = 50, y = 50 }
    end

    -- 是否可以购买
    local hasLimit = (purchaseLimit <= 15)
    local canBuy = ((not hasLimit) or (purchasedTimes < purchaseLimit))

    -- 背景样式
    if (index == 1) and canBuy then
        bgSS:Switch(_GiftItemBgIndex.Yellow)
    else
        bgSS:Switch(_GiftItemBgIndex.Blue)
    end

    countdownSS:Switch(_CountdownBgIndex.Blue)
    countdownText.color = _CountdownTextColor.Blue
    -- 倒计时文本大小
    if (lang.USE_LANG == lang.EN) or (lang.USE_LANG == lang.RU) then
        countdownText.fontSize = 14
    else
        countdownText.fontSize = 16
    end

    -- 取消旧的倒计时
    if item.data.countdownTicker then
        self:RemoveTimer(item.data.countdownTicker)
        item.data.countdownTicker = nil
    end
    -- 设置倒计时
    local remainingTime = 0

    if (endTime > 0) then
        remainingTime = GetRemainingTime(endTime)

    end
    countdownSS.gameObject:SetActive(remainingTime > 0)
    if (remainingTime > 0) then
        countdownText.text = GetCountdownText(remainingTime)

        item.data.countdownTicker = self:CreateTimer(1,
                function()
                    remainingTime = GetRemainingTime(endTime)
                    countdownText.text = GetCountdownText(remainingTime)
                end)
    end

    -- 限购次数
    limitText:SetActive(hasLimit ~= nil)
    if hasLimit then
        local limitStr = (tostring(purchasedTimes) .. "/" .. tostring(purchaseLimit))
        --limitText.text = string.format(lang.Get(81783), limitStr)
        limitText.text = limitStr
    end

    -- 购买按钮
    buyBtnSS:Switch(canBuy and 0 or 1)
    buyBtnText.color = canBuy and _BuyBtnTextColor.Normal or _BuyBtnTextColor.Gray
    buyBtnText.curOutlineColor = canBuy and _BuyBtnTextOutlineColor.Normal or _BuyBtnTextOutlineColor.Gray
    buyBtnText.text = canBuy and data.priceText or lang.Get(15128)   -- "已售罄"

    -- 物品图标
    local goodsItems = item.data.goodsItems
    local rewardList = data.rewardList
    local count = math.max(#goodsItems, #rewardList)

    if goodsItems then
        for i, v in ipairs(goodsItems) do
            if v then
                v:Dispose()
            end
        end
    end

    for i = 1, count do
        local goodsItem = goodsItems[i]
        local reward = rewardList[i]
        if reward then
            -- 展示/生成物品图标
            if goodsItem and not util.IsObjNull(goodsItem.gameObject) then
                goodsItem.gameObject:SetActive(true)
            else
                --goodsItem = goods_item.CGoodsItem()
                --goodsItem:Init(goodsListContent, nil, 0.5)
                --goodsItems[i] = goodsItem

                goodsItem = reward_item.new()
                goodsItem:Init(goodsListContent, true, 0.5, function()
                    goodsItem:SetObjSize({ x = 140, y = 140 })
                end)
                goodsItems[i] = goodsItem
            end

            local goodsID = reward.id
            local goodsNum = reward.num
            goodsItem:SetRewardData(goodsID, goodsNum, reward.nType)
            --goodsItem:SetGoods(nil, goodsID, goodsNum, true, reward)
            --goodsItem:SetCountEnable(util.PriceConvertGiftDiamond(goodsNum))
            --goodsItem:GoodsEffectEnable(true, self.curOrder + 1, 1, 0.45)
            --goodsItem:SetFrameBg(3)

        elseif goodsItem then
            -- 隐藏物品图标
            goodsItem.gameObject:SetActive(false)
        end
    end
    -- 物品列表滚动
    goodsListScrollRect.enabled = (#rewardList > 4)
    goodsListScrollRect.normalizedPosition = { x = 0, y = 0 }   -- 恢复到初始位置

    -- 按钮回调
    item.InvokeFunc = function(name)
        if data[name] then
            data[name](data)
        end
    end
end

function OnItemRender(scroll_rect_item,index,dataItem)
    
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index;
    scroll_rect_item.data[2] = dataItem;
    --log.Error(index)
    --local itemList = {scroll_rect_item:Get("item_1"),scroll_rect_item:Get("item_2"),scroll_rect_item:Get("item_3")}
    Common_Util.SetActive(scroll_rect_item.gameObject,true)

    local icon = scroll_rect_item:Get("Icon");
    local desc = scroll_rect_item:Get("desc");
    local name = scroll_rect_item:Get("name");
    local OneClickUseTen = scroll_rect_item:Get("OneClickUseTen");
    local useDiamond = scroll_rect_item:Get("UseDiamond");
    local useItem = scroll_rect_item:Get("UseItem");
    local itemDesc = scroll_rect_item:Get("ItemDescObj");
    local diamondDesc = scroll_rect_item:Get("DiamondDescObj");
    local diamondCount = scroll_rect_item:Get("DiamondText");
    local oneClickUseItemCount = scroll_rect_item:Get("OneClickTenText");
    
    local OneClickUseHundred = scroll_rect_item:Get("OneClickUseHundred");
    local OneClickUseHundredText = scroll_rect_item:Get("OneClickUseHundredText");
    
    local shortBubble = scroll_rect_item:Get("ShortBubble");
    local longBubble = scroll_rect_item:Get("longBubble");
    
    --scroll_rect_item.iconUI = scroll_rect_item.iconUI or goods_item.CGoodsItem():Init(icon_1)
    --滚动的回调，没有对应需求所以先干掉。
    --e_handler_mgr.TriggerHandler(window.controller_name,"OnScorllRectItemRender",index,dataItem)
    --绑定上点击等事件，主要是为了抛给controller层进行分离   
    scroll_rect_item.InvokeFunc = function(funcname)
        --注意  这里的事件是存在多种的clickItemEvent 
        if dataItem[funcname] then
            dataItem[funcname](index,dataItem)
        end
    end
    --log.Error(dataItem.TechnologyTypeName)
    --log.Error(dataItem.name)
    name.text = lang.Get(dataItem.nameKey);
    Common_Util.SetActive(OneClickUseTen.gameObject,false)
    Common_Util.SetActive(OneClickUseHundred.gameObject,false)
    if dataItem.isDiamond then
        Common_Util.SetActive(diamondDesc,true)
        Common_Util.SetActive(itemDesc,false)

        Common_Util.SetActive(shortBubble,false)
        Common_Util.SetActive(longBubble,false)
        
        Common_Util.SetActive(useItem.gameObject,false)
        Common_Util.SetActive(useDiamond.gameObject,true)
        
        diamondCount.text = technology_data:formatNumber(needDiamond);
        
        if needDiamond > dataItem.count then
            diamondCount.color = Color.red
        else
            diamondCount.color = {r = 121/255,g=91/255,b=39/255,a=1}
        end
        
        diamondTxt = diamondCount;

        local item = scroll_rect_item.data[3]
        if not item then
            item = goods_item.CGoodsItem():Init(icon, nil, 0.6)
            scroll_rect_item.data[3] = item
        end
        
        item:SetGoods(nil, 2, nil, function()
            iui_item_detail.Show(2, nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil)
        end)
        --spriteAsset:GetSprite(dataItem.icon, function(sp)
        --    icon.sprite = sp
        --end)
        
        useDiamond.onClick:RemoveAllListeners();
        useDiamond.onClick:AddListener(
            function()
                --拦截钻石数量。
                local _useDiamond = technology_data:GetDiamondCountByTime(curTime);
                e_handler_mgr.TriggerHandler("ui_speed_up_panel_controller","OnBtn_UseDiamond",_useDiamond)
            end
        )
    else
        Common_Util.SetActive(diamondDesc,false)
        Common_Util.SetActive(itemDesc,true)
        
        Common_Util.SetActive(useItem.gameObject,true)
        Common_Util.SetActive(useDiamond.gameObject,false);
        desc.text = dataItem.count;
        local needCount = math.floor(needTime / dataItem.time);
        e_handler_mgr.TriggerHandler("ui_speed_up_panel_controller","SetItemNeedCount",dataItem.id,needCount)
        local showHundredBtn = needCount > 10 and dataItem.count > 10;
        local showTenBtn = needCount > 1 and dataItem.count > 1;
        if needCount > 10 then
            if dataItem.count > 10 then
                oneClickUseItemCount.text = string.format2("x{%s1}",10);
            else
                oneClickUseItemCount.text = string.format2("x{%s1}",dataItem.count);
            end
        else
            if dataItem.count > needCount then
                oneClickUseItemCount.text = string.format2("x{%s1}",needCount);
            else
                oneClickUseItemCount.text = string.format2("x{%s1}",dataItem.count);
            end
        end
        if needCount > 100 then
            if dataItem.count > 100 then
                OneClickUseHundredText.text = string.format2("x{%s1}",100);
            else
                OneClickUseHundredText.text = string.format2("x{%s1}",dataItem.count);
            end
        else
            if dataItem.count > needCount then
                OneClickUseHundredText.text = string.format2("x{%s1}",needCount);
            else
                OneClickUseHundredText.text = string.format2("x{%s1}",dataItem.count);
            end
        end
        --if needCount > dataItem.count then
        --    oneClickUseItemCount.color = Color.red
        --    OneClickUseHundredText.color = Color.red
        --else
        --    oneClickUseItemCount.color = {r = 58/255, g = 114/255, b = 34/255, a = 1 }
        --    OneClickUseHundredText.color = {r = 58/255, g = 114/255, b = 34/255, a = 1 }
        --end
        
        local item = scroll_rect_item.data[3]
        if not item then
            item = goods_item.CGoodsItem():Init(icon, nil, 0.6)
            scroll_rect_item.data[3] = item
        end

        item:SetGoods(nil, dataItem.id, nil, function()
            iui_item_detail.Show(dataItem.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil)
        end)
        
        --spriteAsset:GetSprite(dataItem.icon, function(sp)
        --    icon.sprite = sp
        --end)
        
        Common_Util.SetActive(OneClickUseTen.gameObject,showTenBtn and selectIndex == index)
        Common_Util.SetActive(OneClickUseHundred.gameObject,showHundredBtn and selectIndex == index)
        Common_Util.SetActive(shortBubble,showTenBtn and selectIndex == index and not showHundredBtn)
        Common_Util.SetActive(longBubble,showTenBtn and selectIndex == index and showHundredBtn)
        
        useItem.onClick:RemoveAllListeners();
        useItem.onClick:AddListener(
            function()
                e_handler_mgr.TriggerHandler("ui_speed_up_panel_controller","SetSelectIndex",index)
                e_handler_mgr.TriggerHandler("ui_speed_up_panel_controller","OnBtn_UseItem",dataItem,1)
            end
        )
    end
end

function UISpeedUpView:UpdateList(data,_selectIndex,begin)
    if self.rect_table == nil then
        return
    end
    if not data then
        return
    end
    selectIndex = _selectIndex;
    local dataSize = #data
    self.rect_table.pageSize = (dataSize > 40 and 40 or dataSize)
    self.rect_table.data = data
    if self.notFirstOpen1 then
        self.rect_table:Refresh(0, -1)
    else
        self.rect_table:Refresh(-1, -1)
        self.notFirstOpen1 = true
    end
    if begin then
        self.rect_table:ScrollTo(begin - 1)
    end
end

function UISpeedUpView:UpdateQueue(data,_needDiamond)
    if not data then
        return;
    end
    local BasicResearchTime = 0;
    local cfg = nil;--data.cfgData.data;
    local ui_speed_up_panel_controller = require "ui_speed_up_panel_controller"
    --local technologyEffectList = technology_data:GetTechnologyEffectList()
    if data.type == ui_speed_up_panel_controller.SpeedUpTypeEnum.Technology then
        cfg = data.nextData.data;
        local nextData = technology_data:GetTargetTechnologyByIndex(cfg.TechnologyTypeID,cfg.ScientificID).data[data.nextData.data.Level];
        BasicResearchTime = data.curResearchAllTime-- - data.curResearchStartTime--nextData.data.BasicResearchTime / (1+ technology_data:GetTechnologySpeedUpRate(data.buildID))
        
        data.curResearchCompleteTime = math.ceil(data.doneTime - os.server_time())
        _needDiamond = technology_data:GetDiamondCountByTime(data.curResearchCompleteTime);
    elseif data.type == ui_speed_up_panel_controller.SpeedUpTypeEnum.Building then
        local nextData = game_scheme:Building_0(data.buildID,data.level+1)
        local value = 0
        local rate = gw_attribute_bonus.GetEffectValue(504)--technologyEffectList[504]  or 0
        local workerBuildingData = GWG.GWHomeMgr.buildingData.GetMaxLevelBuildingDataByBuildingID(currentResearchBuildingId)
        if workerBuildingData then
            local workerBuilding = GWG.GWHomeMgr.survivorData.GetSurvivorAdditionListByBuildingSid(workerBuildingData.uSid)
            value = workerBuilding[506] or 0
        end
        
        BasicResearchTime = nextData.time / (1 + rate / 10000) - value
        cfg = data.cfgData;
    elseif data.type == ui_speed_up_panel_controller.SpeedUpTypeEnum.TrainingSoldier then
        cfg = data.cfgData;
        local value = gw_attribute_bonus.GetEffectValue(402)--technologyEffectList[402] or 0
        BasicResearchTime = cfg.TrainTime * data.count / (1 + value/ 10000);
    elseif data.type == ui_speed_up_panel_controller.SpeedUpTypeEnum.UpgradeSoldier then
        cfg = data.cfgData;
        local nextSoldier = game_scheme:Soldier_0(cfg.soldierID + 1);
        if nextSoldier ~= nil then
            local value = gw_attribute_bonus.GetEffectValue(402)--technologyEffectList[402]  or 0
            BasicResearchTime = data.count * (nextSoldier.TrainTime - cfg.TrainTime)/ (1 + value/ 10000);
        else
            log.Error("士兵已经满级，但仍旧尝试晋级！")
        end
    elseif data.type == ui_speed_up_panel_controller.SpeedUpTypeEnum.TreatmentSoldier then
        BasicResearchTime = 0
        for i,v in pairs(data.soldiersList) do
            if v.nCount and v.nSoldierID then
                local temp = game_scheme:Soldier_0(v.nSoldierID);
                if temp then
                    BasicResearchTime = BasicResearchTime + (temp.CureTime * v.nCount) / (1 + GWG.GWHomeMgr.soldierData.GetHospitalSpeed() / 10000);
                end
            end
        end
    elseif data.type == ui_speed_up_panel_controller.SpeedUpTypeEnum.EquipFactory then
        --TODO 这里要获取装备合成的总时长
        BasicResearchTime = data.curResearchAllTime
    end

    needTime = data.curResearchCompleteTime;
    needDiamond = _needDiamond;
    --听说这样拼接效率会高一些
    local strings = {time_util.FormatTime5(data.curResearchCompleteTime),"/",time_util.FormatTime5(BasicResearchTime)}
    self.queue01TimeTxt.text = table.concat(strings);
    local strings2 = {lang.Get(209),time_util.FormatTime5(data.curResearchCompleteTime)};--technology_data:SecondsToTime(data.curResearchCompleteTime)};
    self.queue01TimeDesc.text = table.concat(strings2);

    self.queue01TimeSlider.value = 1-(data.curResearchCompleteTime / BasicResearchTime);
    if self.queue01TimeTicker then
        util.RemoveDelayCall(self.queue01TimeTicker)
        self.queue01TimeTicker = nil
    end
    if data.curResearchCompleteTime <= 0 then
        self:Queue01FinishStudy()
    else
        self:Countdown01(data.curResearchCompleteTime,BasicResearchTime,function()
            self:Queue01FinishStudy()
        end)
        Common_Util.SetActive(self.buildingIcon,false)
        if cfg ~= nil then
            Common_Util.SetActive(self.queue01Icon,true)
            Common_Util.SetActive(self.hospitalIcon,false)
            if cfg.iconID ~= nil then
                if data.type == ui_speed_up_panel_controller.SpeedUpTypeEnum.Technology then
                    technologySpriteAsset:GetSprite(cfg.iconID, function(sp)
                        self.queue01Icon.sprite = sp
                    end)
                else
                    spriteAsset:GetSprite(cfg.iconID, function(sp)
                        self.queue01Icon.sprite = sp
                    end)
                end

            else
                if data.type == ui_speed_up_panel_controller.SpeedUpTypeEnum.Building then
                    Common_Util.SetActive(self.queue01Icon,false)
                    Common_Util.SetActive(self.buildingIcon,true)
                    if data.buildID == GWConst.ScientificBuildingId.Building_2 then
                        data.buildID = GWConst.ScientificBuildingId.Building_1
                    end
                    buildingAsset:GetSprite(data.buildID,function(sp)
                        self.buildingIcon.sprite = sp
                    end)
                else
                    soldierSpriteAsset:GetSprite(cfg.soldierID, function(sp)
                        self.queue01Icon.sprite = sp
                    end)
                end
            end
        else
            --TODO 医院治疗是没有icon的，所以这里要填医院的icon。
            if data.type == ui_speed_up_panel_controller.SpeedUpTypeEnum.TreatmentSoldier then
                Common_Util.SetActive(self.queue01Icon,false)
                Common_Util.SetActive(self.hospitalIcon,true)
            elseif data.type == ui_speed_up_panel_controller.SpeedUpTypeEnum.EquipFactory then
                Common_Util.SetActive(self.queue01Icon,true)
                Common_Util.SetActive(self.hospitalIcon,false)
                technologySpriteAsset:GetSprite(data.equipId, function(sp)
                    self.queue01Icon.sprite = sp
                end)
            end
        end
    end
end

function UISpeedUpView:Queue01FinishStudy()
    e_handler_mgr.TriggerHandler(self.controller_name,"OnFinishStudy");
end

function GetCurTime()
    return curTime;
end

function UISpeedUpView:UpdateDiamondCount(value)
    local diamondCount = technology_data:GetDiamondCountByTime(value)
    needDiamond = diamondCount
    if diamondTxt then
        diamondTxt.text = technology_data:formatNumber(needDiamond);
        local allDiamond = player_mgr.GetPlayerAllDiamond()
        if needDiamond > allDiamond then
            diamondTxt.color = Color.red
        else
            diamondTxt.color = {r = 121/255,g=91/255,b=39/255,a=1}
        end
    end
end

function UISpeedUpView:Countdown01(_curTime,totalTime,callback)
    curTime = _curTime;
    local strings = {time_util.FormatTime5(curTime),"/",time_util.FormatTime5(totalTime)}
    self.queue01TimeTxt.text = table.concat(strings);
    if self.queue01TimeTicker then
        util.RemoveDelayCall(self.queue01TimeTicker)
        self.queue01TimeTicker = nil
    end
    self.queue01TimeTicker = util.IntervalCall(1,function()
        curTime = curTime - 1;
        if math.floor(curTime) % 30 == 0 then
            self:UpdateDiamondCount(curTime)
        end
        local strings = {time_util.FormatTime5(curTime),"/",time_util.FormatTime5(totalTime)}
        self.queue01TimeTxt.text = table.concat(strings);
        local strings2 = {lang.Get(209),time_util.FormatTime5(curTime)};
        self.queue01TimeDesc.text = table.concat(strings2);
        self.queue01TimeSlider.value = 1-(curTime / totalTime);
        
        if curTime <= 0 then
            if self.queue01TimeTicker then
                util.RemoveDelayCall(self.queue01TimeTicker)
                self.queue01TimeTicker = nil
                if callback then
                    callback()
                end
            end
        end
    end)
end

function UISpeedUpView:SetGiftList(giftData)
    if not giftData then
        Common_Util.SetActive(self.Img_GiftList.gameObject,false)
        self.Rtsf_RelativeList.sizeDelta = {x=660,y=732}
        return;
    end
    local len = #giftData;
    Common_Util.SetActive(self.Img_GiftList.gameObject,len > 0)
    if len <= 0 then
        self.Rtsf_RelativeList.sizeDelta = {x=660,y=732}
    end
    self.gift_rect_table:SetData(giftData,len)
    if self.notFirstOpen2 then
        self.gift_rect_table:Refresh(0, -1)
    else
        self.gift_rect_table:Refresh(-1, -1)
        self.notFirstOpen2 = true
    end
    self.Rtsf_ScrollView:GetComponent(typeof(ScrollRect)).enabled = (len > 2)

    local tmpRelativeListSize = { x = self.oriParam.RelativeListSize.x, y = self.oriParam.RelativeListSize.y + (2 - len) * _GiftListItemSize.y }
    local tmpGiftListSize = { x = self.oriParam.GiftListSize.x, y = self.oriParam.GiftListSize.y - (2 - len) * _GiftListItemSize.y }
    self.Rtsf_RelativeList.sizeDelta = tmpRelativeListSize
    self.Img_GiftList.rectTransform.sizeDelta = tmpGiftListSize
end
---********************end功能函数区**********---

--region WindowInherited
local CUISpeedUpView = class(ui_base, nil, UISpeedUpView)
--endregion 

--region static ModuleFunction 
-- 特别注意，当前并不是由controller层来驱动ui的生命流程的 
-- 当前因为需要view层 也就是ui_base来驱动ui的init  加载完成，show等流程，所以流程仍然保留，而controller层的流程逻辑受view流程影响，
-- view对应的Init/Show 加载完后，当前会通过事件同步调用controller层的Init/Show流程，controller层的流程逻辑才会执行。
--当前仍然保留了静态的Show，Close接口流程
function Show(data)
     if  data  and data["uipath"] then
        ui_path = data["uipath"];   
    end  
    if window == nil then
        window = CUISpeedUpView()
        window._NAME = _NAME
        window.delayOpenMain = 0
        window.delayCloseMain = 0
        window:LoadUIResource(ui_path, nil, nil, nil)
    end
    --这里调用window:Show()  会造成多次调用 但hide后却又需要 uiwindowmgr有问题 TODO
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end
--endregion
