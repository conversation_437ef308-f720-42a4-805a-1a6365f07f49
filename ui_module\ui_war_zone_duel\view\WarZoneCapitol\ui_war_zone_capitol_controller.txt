local print = print
local require = require
local pairs = pairs
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type
local tonumber = tonumber

local cfg_util = require "cfg_util"
local util = require "util"
local war_zone_duel_const = require "war_zone_duel_const"
local os = require "os"
local event = require "event"
local gw_task_data = require "gw_task_data"
local gw_task_const = require "gw_task_const"
local event_task_define = require "event_task_define"
local player_mgr = require "player_mgr"
local war_zone_duel_data = require "war_zone_duel_data"
local net_war_zone_duel_module = require "net_war_zone_duel_module"
local log = require "log"
local time_util = require "time_util"
local reward_mgr = require "reward_mgr"
local game_scheme = require "game_scheme"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"
local war_zone_duel_mgr = require "war_zone_duel_mgr"
local war_zone_duel_define = require "war_zone_duel_define"
local gw_common_util = require "gw_common_util"

--region Controller Life
module("ui_war_zone_capitol_controller")
local controller = nil
local UIController = newClass("ui_war_zone_capitol_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)

    local id = war_zone_duel_data.GetActivityID()
    self.pointsGoodsId = war_zone_duel_data.warZonePointsGoodsID
    self.showDownCfg = game_scheme:SrverShowdown_1(id)
    
    self:RefreshStageData() --阶段倒计时，及进攻按钮的刷新
    self:GetWinRewardData() --连胜奖励数据init
    self:InitGoToAtkBtnCD()
    self:InitTreasureBoxListData() --个人积分宝箱Init
    self:RefreshTreasureData() --个人积分宝箱数据刷新
    self:GetPointsList() --个人积分列表展示（纯读表）
    self:GetBoxProgress() --个人积分宝箱进度条
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents()
    self._GET_CONGRESS_POS_RSP_Handler = function(evtName, msg)
        self:GET_CONGRESS_POS_RSP_Handler(msg)
    end
    self:RegisterEvent(war_zone_duel_define.Evt_MSG_ZONEBATTLEDUEL_GET_CONGRESS_POS_RSP_Handler, self._GET_CONGRESS_POS_RSP_Handler)

    self.receiveRewardFun = function(_,msg)

        if msg.errCode ~= 0 then
            return
        end
        --按钮置灰
        self:TriggerUIEvent("SetReceiveRewardBtn", 0)
        local rewardID = self.showDownCfg.WinningStreakRewards--奖励ID
        local rewardIDList = reward_mgr.GetRewardGoodsList(rewardID)
        local selfWorldID = player_mgr.GetCreateRoleWorldID()
        if rewardIDList and #rewardIDList >= 1 then
            local worldData = war_zone_duel_data.GetWorldItemByWorldID(selfWorldID)
            local multiplier = worldData.continueWinTimes or 0
            local listData = { title = "", dataList = rewardIDList,multiplier = multiplier}
            local showData = {}
            table.insert(showData, listData)
            local ui_reward_result = require "ui_reward_result_new"
            ui_reward_result.SetInputParam(showData)
            ui_window_mgr:ShowModule("ui_reward_result_new")
        end
    end --领取连胜奖励回复
    self:RegisterEvent(war_zone_duel_define.Evt_MSG_ZONEBATTLEDUEL_GET_ROUNDWIN_REWARD_RSP_Handler, self.receiveRewardFun)


    self.updateChestTaskData = function(eventName,taskData,moduleId,modulList)
        if modulList[gw_task_const.TaskModuleType.WarZoneDuel] then
            self:RefreshTreasureData()
        end
    end --任务数据刷新事件
    self:RegisterEvent(event_task_define.REFRESH_TASK,self.updateChestTaskData)


    --道具补充监听
    self.goodsChangeFun = function(e,id,sid,num)
        if id == self.pointsGoodsId then
            self:GetBoxProgress()
            local red_system = require "red_system"
            local red_const = require "red_const"
            red_system.TriggerRed(red_const.Enum.WarZoneRewardBtn)
            self:RefreshReceiveBtn()
        end
    end
    self:RegisterEvent(event.UPDATE_GOODS_NUM_CHANGE, self.goodsChangeFun)

    self.refreshStage = function(_, msg)
        self:RefreshStageData()

        local isAtyEnd = war_zone_duel_data.IsInStage(war_zone_duel_define.enZoneBattleDuelStage.AtyEnd)
        if isAtyEnd then
            --self:RefreshTreasureData()
            self:RefreshReissueBoxReward()
        end
    end

    self:RegisterEvent(war_zone_duel_define.Evt_ALLIANCE_BATTLE_DUEL_CHANGE_NTF_Handler, self.refreshStage)
end

function UIController:AutoUnsubscribeEvents()
end
--endregion

--region Controller Logic
function UIController:OnBtnReceiveClickedProxy()
    if self.isCanReceive or self.isRewardResult then
        net_war_zone_duel_module.MSG_ZONEBATTLEDUEL_GET_ROUNDWIN_REWARD_REQ()
    end
end

--前往进攻按钮
function UIController:OnBtnGoToAtkClickedProxy()
    war_zone_duel_mgr.GET_CONGRESS_POS_REQ()
    war_zone_duel_mgr.SetWarZoneAttackBtnRed()
end

--前往防守按钮
function UIController:OnBtnGoToDefClickedProxy()
    war_zone_duel_mgr.GET_CONGRESS_POS_REQ()
    war_zone_duel_mgr.SetWarZoneAttackBtnRed()
end

function UIController:OnSliderPointsValueChange(value)
    
end

function UIController:OnBtnContentTipsCloseBtnClickedProxy()
    self:TriggerUIEvent("CloseTipsPanel", false)
end

---@public function 点击奖励按钮
function UIController:OnBtnRewardClickedProxy()
    ui_window_mgr:ShowModule("ui_war_zone_reward_rank")
end

---@public 获取双方对决的战区数据
function UIController:GetWarZoneData()
    local viewData = {}
    local selfWorldID = player_mgr.GetCreateRoleWorldID()
    local vsItems = war_zone_duel_data.vsItems
    local enemyWorldID = 0
    if vsItems.worldIDA == selfWorldID then
        enemyWorldID = vsItems.worldIDB
    else
        enemyWorldID = vsItems.worldIDA
    end

    --local playerInfoArr = war_zone_duel_data.GetPlayPresInfo(worldIDArr)
    local leftPlayInfo = war_zone_duel_data.GetPlayPresInfo({selfWorldID}) 
    local rightPlayInfo = war_zone_duel_data.GetPlayPresInfo({enemyWorldID})
    local leftWarZoneData = self:SetWarZoneData(selfWorldID,leftPlayInfo and leftPlayInfo[1] or nil, true)
    local rightWarZoneData = self:SetWarZoneData(enemyWorldID,  rightPlayInfo and rightPlayInfo[1] or nil)

    viewData.leftWarZoneData = leftWarZoneData
    viewData.rightWarZoneData = rightWarZoneData
    local atkWorldID = war_zone_duel_data.GetSelfZoneDuelAtkWorldID()
    --local defWorldID = war_zone_duel_data.GetSelfZoneDuelDefWorldID()
    if selfWorldID == atkWorldID then
        --自己是进攻方
        viewData.isSelfAtk = true
    end
    self:TriggerUIEvent("RefreshCapitolWarZone", viewData)
end

---@public function 设置国会争夺双方的数据
function UIController:SetWarZoneData(worldID, playerInfo, isSelf)
    local warZoneData = {}
    warZoneData.bgTyp = isSelf and 1 or 2
    warZoneData.worldID = worldID
    warZoneData.isTopHead = true
    if playerInfo then
        warZoneData.faceID = playerInfo.faceID
        warZoneData.faceStr = playerInfo.faceStr
        warZoneData.frameID = playerInfo.frameID
        warZoneData.playerName = playerInfo.name
    end
    return warZoneData
end


---@public function 进攻回包处理
function UIController:GET_CONGRESS_POS_RSP_Handler(msg)
    if msg.errCode ~= 0 then
        return
    end
    ui_window_mgr:UnloadModule(self.view_name)
    ui_window_mgr:UnloadModule("ui_war_zone_duel_main")
    local pos = { x = msg.x, y = msg.y }
    local sandbox_pb = require "sandbox_pb"
    
    gw_common_util.SwitchToSand(function()
        if self.isSelfAtk then
            local gw_admin = require "gw_admin"
            gw_admin.SandRelocationEntityUtil.SetRelocationEntitySid(0, { x = pos.x, y = 0, z = pos.y }, function(state, gridPos)
                if not state then
                    gw_common_util.SwitchToSand()
                end
            end, sandbox_pb.enSandboxCrossMoveType_ZoneDuel_Battle)
        else
            local sand_operate_command = require "sand_operate_command"
            sand_operate_command.ClickNoneEvent(pos)
        end
        -- 返回迁城默认打开迁城界面
        --self:CloseView()
    end, pos, msg.sandboxSid, true)
end

--endregion

--region Controller Function

---@public function 获取连胜奖励
function UIController:GetWinRewardData()
    if not self.showDownCfg then
        return
    end
    local data = {}
    local rewardID = self.showDownCfg.WinningStreakRewards--奖励ID
    local rewardData = reward_mgr.GetRewardGoodsList(rewardID)
    self:TriggerUIEvent("RefreshWinRewardList", rewardData)
    
    local selfWorldID = player_mgr.GetCreateRoleWorldID()
    local myWorld = war_zone_duel_data.GetWorldItemByWorldID(selfWorldID)
    local winReward = {}
    if myWorld then

        if self.showDownCfg then
            local serverGroup_str = cfg_util.StringToArray(self.showDownCfg.WinningStreakMultiplier, ";", "#")
            local multiple = 1
            for i, v in ipairs(serverGroup_str) do
                if myWorld.continueWinTimes >= tonumber(v[1]) then
                    multiple = tonumber(v[2])
                end
            end
            --倍数
            winReward.multiple = multiple <= 1 and 0 or multiple
        end
    end
    
    local getPointsTime = war_zone_duel_data.GetTimeTypePoint(war_zone_duel_define.enZoneBattleDuelTimeType.CrossMoveBegin) 
    getPointsTime = getPointsTime ~= 0 and getPointsTime or war_zone_duel_data.GetGainPointsTime() 
    winReward.getPointsTime = getPointsTime
    self:TriggerUIEvent("RefreshRewardArea", winReward)
    --个人积分达到5000
    --倒计时

end

---@public function 初始化进攻按钮CD显示
function UIController:InitGoToAtkBtnCD()
    local moveCityTime = war_zone_duel_data.crossMoveCDTime
    if moveCityTime then
        self:TriggerUIEvent("SetAttackBtnCD", moveCityTime)
    end
end

---@public 获取箱子列表数据
function UIController:InitTreasureBoxListData()
    --local showDownCfg = game_scheme:SrverShowdown_1(601)
    if not self.showDownCfg then
        return
    end
    
    local boxCount = self.showDownCfg.ProgressRewards.count
    local boxDataArr = self.showDownCfg.ProgressRewards.data

    local viewData = {}
    for i = 0, boxCount - 1, 2 do
        local taskID = boxDataArr[i]
        local taskCfg = game_scheme:TaskMain_0(taskID)
        local points = taskCfg.ConditionValue1
        local rewardID = taskCfg.TaskReward
        local diamond = boxDataArr[i + 1]

        local tempData = {
            taskID = taskID,
            points = points,
            rewardID = rewardID,
            diamond = diamond
        }
        table.insert(viewData, tempData)
    end
    
    self:TriggerUIEvent("InitTreasureArea", viewData)
    self.InitTreasureBoxData = viewData
    
    --viewData 里在获取所有宝箱任务数据
    
end

function UIController:RefreshTreasureData()
    local boxTaskDataArr = {}
    if self.InitTreasureBoxData then
        for i, boxData in ipairs(self.InitTreasureBoxData) do
            local taskData = gw_task_data.GetTaskData(boxData.taskID)
            if taskData then
                local tempData = {
                    status = taskData.status,
                    rate = taskData.rate,
                    taskID = boxData.taskID,
                }
                table.insert(boxTaskDataArr, tempData)
            end
        end
        self:TriggerUIEvent("RefreshTreasureTask",boxTaskDataArr)
    end
end

---@public function 获取箱子进度
function UIController:GetBoxProgress()
    local pointsNum = war_zone_duel_data.GetSelfCapitalPoints()
    
    local boxViewData = {}
    boxViewData.boxItemNum = pointsNum
    local boxCount = self.showDownCfg.ProgressRewards.count
    local boxDataArr = self.showDownCfg.ProgressRewards.data
    
    local pointsArr = {}
    for i = 0, boxCount - 1,2 do
        local tempID = boxDataArr[i]
        local taskCfg = game_scheme:TaskMain_0(tempID)
        local points = taskCfg.ConditionValue1
        
        local tempData = {}
        tempData.taskID = tempID
        tempData.points = points
        table.insert(pointsArr, tempData)
    end
    
    local lastTaskID = boxDataArr[boxCount - 2]
    local taskCfg = game_scheme:TaskMain_0(lastTaskID)
    boxViewData.MaxBoxItemNum = taskCfg.ConditionValue1 --进度上限
    
    boxViewData.pointsArr = pointsArr
    
    self:TriggerUIEvent("RefreshTreasureProgress",boxViewData)
end

---@public 获取积分列表数据
function UIController:GetPointsList()
    if not self.showDownCfg then
        return
    end

    local taskIDArr = self.showDownCfg.CompetitivePointsTasks.data
    local taskCount = self.showDownCfg.CompetitivePointsTasks.count

    local viewData = {}

    for i = 0, taskCount - 1 do
        local id = taskIDArr[i]
        local taskCfg = game_scheme:TaskMain_0(id)
        if taskCfg then
            local taskType = taskCfg.ConditionType
            local index = taskCfg.ConditionValue2.data[0] or 0
            viewData[taskType] = viewData[taskType] or {}
            viewData[taskType][index] = viewData[taskType][index] or {}
            local tempData = {
                strLangID = taskCfg.TaskLang,
                addPoints = taskCfg.PointsRewardID.data[1],
                soldierLevel = taskCfg.ConditionValue2[1][0],
            }
            viewData[taskType][index] = tempData
        end
    end

    self:TriggerUIEvent("CreatePointsShowDown", viewData)
end

---@public 刷新阶段数据,在转换阶段时调用一次
function UIController:RefreshStageData()

    local isPreview = war_zone_duel_data.IsInStage(war_zone_duel_define.enZoneBattleDuelStage.PreNotice)
    if isPreview then
        --预告阶段
        self:TriggerUIEvent("SetAttackBtnActive",false)
        local startTime = 0
        if war_zone_duel_data.cfgData then
            startTime = war_zone_duel_data.cfgData.activityServerCross.TimeStart
        end
        self:TriggerUIEvent("RefreshStageArea", startTime,war_zone_duel_const.WarZoneCapitolState.Preview)
        self:TriggerUIEvent("SetReceiveRewardBtn", 0)
    else
        --奖励结算
        self.isRewardResult = war_zone_duel_data.IsInStage(war_zone_duel_define.enZoneBattleDuelStage.CongressBattleEnd)
        --是否可以领奖
        self.isCanReceive = war_zone_duel_data.IsInStage(war_zone_duel_define.enZoneBattleDuelStage.CongressBattleSettlement)
        --国会争夺
        local isCapitolStart = war_zone_duel_data.IsInStage(war_zone_duel_define.enZoneBattleDuelStage.CongressBattleStart)
        
        if self.isRewardResult or self.isCanReceive then
            local activityEndTime = war_zone_duel_data.GetTimeTypePoint(war_zone_duel_define.enZoneBattleDuelTimeType.RoundTimeEnd)
            self:TriggerUIEvent("RefreshStageArea", activityEndTime, war_zone_duel_const.WarZoneCapitolState.RewardResult)
        elseif isCapitolStart then
            local endTime = war_zone_duel_data.GetTimeTypePoint(war_zone_duel_define.enZoneBattleDuelTimeType.CongressAtkEnd)
            self:TriggerUIEvent("RefreshStageArea", endTime, war_zone_duel_const.WarZoneCapitolState.Capitol)
        else
            local battleStart = war_zone_duel_data.GetTimeTypePoint(war_zone_duel_define.enZoneBattleDuelTimeType.CongressAtkBegin)
            self:TriggerUIEvent("RefreshStageArea", battleStart, war_zone_duel_const.WarZoneCapitolState.PointsRace)
        end
        
        self:RefreshReceiveBtn() --刷新领取奖励按钮
        self:RefreshGoAtkBtn()   --刷新进攻按钮
        self:GetWarZoneData()    
    end
end

---@public 刷新领取奖励按钮
function UIController:RefreshReceiveBtn()
    --还需要判断一下积分是否足够
    if self.showDownCfg then
        local limitPoints = self.showDownCfg.CompetitivePointsConditions
        local pointsNum = war_zone_duel_data.GetSelfCapitalPoints()
        self.isPointsReached = pointsNum >= limitPoints
    end
    
    --还需要判读一下是否领过奖
    if self.isPointsReached  and (self.isRewardResult or self.isCanReceive) then
        local activityEndTime = war_zone_duel_data.GetTimeTypePoint(war_zone_duel_define.enZoneBattleDuelTimeType.RoundTimeEnd)
        self:TriggerUIEvent("RefreshStageArea", activityEndTime, war_zone_duel_const.WarZoneCapitolState.RewardResult)
        self:TriggerUIEvent("SetReceiveRewardBtn", war_zone_duel_data.bSkipRoundWinReward == 1 and 1 or 0)
    else
        self:TriggerUIEvent("SetReceiveRewardBtn", 0)
    end
end

---@public 刷新进攻按钮的显示
function UIController:RefreshGoAtkBtn()
    --region 跨服按钮的判断
    local moveStartTime = war_zone_duel_data.GetTimeTypePoint(war_zone_duel_define.enZoneBattleDuelTimeType.CrossMoveBegin)
    local moveEndTime = war_zone_duel_data.GetTimeTypePoint(war_zone_duel_define.enZoneBattleDuelTimeType.CrossMoveEnd)
    local curTime = os.server_time()
    if curTime >= moveStartTime and curTime < moveEndTime then

        local selfWorldID = player_mgr.GetCreateRoleWorldID()
        local atkWorldID = war_zone_duel_data.GetSelfZoneDuelAtkWorldID()
        if selfWorldID == atkWorldID then
            self.isSelfAtk = true
            self:TriggerUIEvent("SetAttackBtnActive",true)
            self:TriggerUIEvent("SetGoToDef",false)
        else
            self.isSelfAtk = false
            self:TriggerUIEvent("SetAttackBtnActive",false)
            self:TriggerUIEvent("SetGoToDef",true)
        end
    else
        self:TriggerUIEvent("SetAttackBtnActive",false)
        self:TriggerUIEvent("SetGoToDef",false)
    end
end

---@public function 刷新补发宝箱显示
function UIController:RefreshReissueBoxReward()
    if self.InitTreasureBoxData then
        local boxTaskDataArr = {}
        for i, boxData in ipairs(self.InitTreasureBoxData) do
            local taskCfg = game_scheme:TaskMain_0(boxData.taskID)
            local points = taskCfg.ConditionValue1 or 0
            local taskData = gw_task_data.GetTaskData(boxData.taskID)
            local isState = taskData.rate >= points
            if taskData then
                local tempData = {
                    status = isState,
                    rate = isState and -1 or taskData.rate,
                    taskID = boxData.taskID,
                }
                table.insert(boxTaskDataArr, tempData)
            end
        end
        self:TriggerUIEvent("RefreshTreasureTask",boxTaskDataArr)
    end
end

--endregion


--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
