--- net_weapon_combat_crystal_module.txt  --------------------------------------
--- author: 陶英焕
--- Date:   2025/5/20 14:43
--- ver:    1.0
--- desc:   神兽进阶及神兽结晶Net层处理
-------------------------------------------------------------
local require = require
local string = string
local xManMsg_pb = require "xManMsg_pb"
local net = require "net"
local net_route = require "net_route"
local log = require "log"
local lang = require "lang"
local pairs = pairs
local flow_text = require "flow_text"
local DroneCenter_pb = require "DroneCenter_pb"
local game_scheme = require "game_scheme"
local new_weapon_combat_crystal_define = require "new_weapon_combat_crystal_define"
local event = require "event"
local ipairs = ipairs
local table = table
module("net_weapon_combat_crystal_module")

-- 无描述
function Error_Code(msg)
    if not msg.errorCode then
        log.Error("#### errorCode==nil ####")
        return false;
    end
    if msg.errorCode ~= 0 then
        -- 搜索失败
        flow_text.Add(lang.Get(100000 + msg.errorCode))
        return false
    end
    return true
end

--请求战斗进阶等级提升
local CombatAdvanceReportData = {}
function MSG_DRONEADVANDE_UPGRADE_REQ(data)
    local gw_home_drone_data = require"gw_home_drone_data"
    local msg = DroneCenter_pb.TMSG_DRONEADVANDE_UPGRADE_REQ()

    for k,v in pairs(data.starSidCost) do
        msg.starSidCost:append(v)
    end

    for k,v in ipairs(data.itemCost) do
        local itemCost = msg.itemCost:add()
        itemCost.ItemID = v.ItemID
        itemCost.ItemNum = v.ItemNum
    end
    msg.nDroneId = gw_home_drone_data.GetDroneId()

    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_DRONEADVANDE_UPGRADE_REQ, msg)
end

-- 返回战斗进阶等级提升（结晶消耗数据变更会通过物品那边通知）
function MSG_DRONEADVANDE_UPGRADE_RSP(msg)
    if Error_Code(msg) then
        event.Trigger(new_weapon_combat_crystal_define.MSG_DRONEADVANDE_UPGRADE_RSP)
        local red_system = require "red_system"
        local red_const = require "red_const"
        red_system.TriggerRed(red_const.Enum.BuildList, true)
        red_system.TriggerRed(red_const.Enum.BuildListTogAffair, true)
        red_system.TriggerRed(red_const.Enum.BuildListTogEco, true)
        red_system.TriggerRed(red_const.Enum.BuildListTogDecor, true)
    end
end


--请求无人机结晶信息
function MSG_DRONESTAR_INFO_REQ (data)
    local msg = DroneCenter_pb.TMSG_DRONESTAR_INFO_REQ()

    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_DRONESTAR_INFO_REQ, msg)
end
--返回无人机结晶信息
function MSG_DRONESTAR_INFO_RSP(msg)
    if Error_Code(msg) then
        local new_weapon_combat_crystal_data = require "new_weapon_combat_crystal_data"
        new_weapon_combat_crystal_data.SetCrystalSchemeData(msg)
    end
end

--更换装备结晶
function MSG_DRONESTAR_EQUIP_REQ (data)
    local msg = DroneCenter_pb.TMSG_DRONESTAR_EQUIP_REQ()
    msg.planId = data.planId
    msg.place = data.place
    msg.oldStarSid = data.oldStarSid
    msg.newStarSid = data.newStarSid
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_DRONESTAR_EQUIP_REQ, msg)
end
--装备结晶返回
function MSG_DRONESTAR_EQUIP_RSP(msg)
    if Error_Code(msg) then
        flow_text.Add(lang.Get(600394))

        --打点
        local reportData = {}
        local new_weapon_combat_crystal_data = require "new_weapon_combat_crystal_data"
        local oldCfg = new_weapon_combat_crystal_data.GetCrystalCfgBySid(msg.oldStarSid)
        local newCfg = new_weapon_combat_crystal_data.GetCrystalCfgBySid(msg.starSid)
        reportData.Beforechange = oldCfg and string.format("%s#%s",oldCfg.crystalID,oldCfg.starLevel) or "0#0"
        reportData.Afterchange = newCfg and string.format("%s#%s",newCfg.crystalID,newCfg.starLevel) or "0#0"
        reportData.PlanID = msg.planId
        event.Trigger(event.GAME_EVENT_REPORT, "CrystalEquipChange",reportData)
    end
end

--请求结晶升星
function MSG_DRONESTAR_UPGRADE_REQ (data)
    local msg = DroneCenter_pb.TMSG_DRONESTAR_UPGRADE_REQ()
    msg.starSid = data.starSid
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_DRONESTAR_UPGRADE_REQ, msg)
end
--结晶升星返回
function MSG_DRONESTAR_UPGRADE_RSP(msg)
    if Error_Code(msg) then
        local ui_window_mgr = require "ui_window_mgr"
        ui_window_mgr:ShowModule("ui_new_magic_weapon_crystal_upgrade_result",nil,nil,{sid = msg.starSid })
        event.Trigger(new_weapon_combat_crystal_define.MSG_DRONESTAR_UPGRADE_RSP)
        
        --打点
        local new_weapon_combat_crystal_data = require "new_weapon_combat_crystal_data"
        local cfg = new_weapon_combat_crystal_data.GetCrystalCfgBySid(msg.starSid)
        if cfg then
            local reportData = {}
            reportData.star = string.format("%s#%s",cfg.crystalID,cfg.starLevel)
            local lastLevelCfg = game_scheme:MagicWeaponCrystal_0(cfg.crystalID,cfg.starLevel - 1)
            local starRisingCost = lastLevelCfg.starRisingCost.data
            reportData.cost = string.format("%s#%s#%s",starRisingCost[0],starRisingCost[1],starRisingCost[2])
            event.Trigger(event.GAME_EVENT_REPORT, "CrystalStar",reportData)
        end
    end
end

--请求重置结晶
local resetCache = {}
local reportCrystalReset = {}
function MSG_DRONESTAR_RESET_REQ (data)
    local item_data = require "item_data"
    resetCache = {}
    local msg = DroneCenter_pb.TMSG_DRONESTAR_RESET_REQ()

    for k,v in pairs(data) do
        msg.starSidList:append(v)
    end

    local new_weapon_combat_crystal_data = require "new_weapon_combat_crystal_data"
    -- local cfgMap = {}
    -- for k,sid in ipairs(msg.starSidList) do
    --     local num ,cfg = new_weapon_combat_crystal_data.GetResetCrystal(sid)
    --     if cfg then
    --         local itemID = cfg.crystalID
    --         local level = cfg.starLevel
    --         if not cfgMap[itemID.."_"..level] then
    --             cfgMap[itemID.."_"..level] = {num = 0}
    --         end
    --         cfgMap[itemID.."_"..level].id = itemID
    --         local itemCfg = game_scheme:Item_0(itemID)
    --         cfgMap[itemID.."_"..level].nType = item_data.Reward_Type_Enum.Item
    --         cfgMap[itemID.."_"..level].num = cfgMap[itemID.."_"..level].num + num
    --     end
    -- end
    -- for k,v in pairs(cfgMap) do
    --     table.insert(resetCache,v)
    -- end
    local resetStartString = ""
    local resetEndString = ""
    for k,v in pairs(new_weapon_combat_crystal_data.GetCrystalListBySidList(data)) do
        local num,cfg = new_weapon_combat_crystal_data.GetResetCrystal(v.sidList[1])
        if cfg then
            table.insert(resetCache,{id=cfg.crystalID,nType = item_data.Reward_Type_Enum.Item,num = #v.sidList*num})
            resetEndString = resetEndString..cfg.crystalID.."#"..cfg.starLevel.."#"..#v.sidList*num..";"
        end
        resetStartString = resetStartString..v.crystalID.."#"..v.level.."#"..#v.sidList..";"
    end
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_DRONESTAR_RESET_REQ, msg)

    --打点
    reportCrystalReset = {ResetStart = resetStartString,ResetEnd = resetEndString}
end
--重置结晶返回
function MSG_DRONESTAR_RESET_RSP(msg)
    if Error_Code(msg) then
        local ui_reward_result = require "ui_reward_result_new"
        ui_reward_result.SetInputParam({ { dataList = resetCache } })
        local ui_window_mgr = require "ui_window_mgr"
        ui_window_mgr:ShowModule("ui_reward_result_new", nil, nil)
        event.Trigger(new_weapon_combat_crystal_define.MSG_DRONESTAR_RESET_RSP)
        event.Trigger(event.GAME_EVENT_REPORT, "CrystalReset",reportCrystalReset)
    end
end

--请求设置军队结晶方案
function MSG_DRONESTAR_TROOP_REQ (data)
    local msg = DroneCenter_pb.TMSG_DRONESTAR_TROOP_REQ()
    msg.teamIndex = data.teamIndex
    msg.planId = data.planId
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_DRONESTAR_TROOP_REQ, msg)

end
--设置军队结晶方案返回
function MSG_DRONESTAR_TROOP_RSP(msg)
    if Error_Code(msg) then
        event.Trigger(new_weapon_combat_crystal_define.TMSG_DRONESTAR_TROOP_RSP)
    end
end


--无人机结晶信息NTF(// 登录推送, 装备, 结晶升星, 设置军队)
function MSG_DRONESTAR_INFO_NTF(msg)
    local new_weapon_combat_crystal_data = require "new_weapon_combat_crystal_data"
    new_weapon_combat_crystal_data.SetCrystalSchemeData(msg)
end

local MessageTable = {
    { xManMsg_pb.MSG_DRONEADVANDE_UPGRADE_RSP, MSG_DRONEADVANDE_UPGRADE_RSP, DroneCenter_pb.TMSG_DRONEADVANDE_UPGRADE_RSP },
    { xManMsg_pb.MSG_DRONESTAR_INFO_RSP, MSG_DRONESTAR_INFO_RSP, DroneCenter_pb.TMSG_DRONESTAR_INFO_RSP },
    { xManMsg_pb.MSG_DRONESTAR_TROOP_RSP, MSG_DRONESTAR_TROOP_RSP, DroneCenter_pb.TMSG_DRONESTAR_TROOP_RSP },
    { xManMsg_pb.MSG_DRONESTAR_EQUIP_RSP, MSG_DRONESTAR_EQUIP_RSP, DroneCenter_pb.TMSG_DRONESTAR_EQUIP_RSP },
    { xManMsg_pb.MSG_DRONESTAR_UPGRADE_RSP, MSG_DRONESTAR_UPGRADE_RSP, DroneCenter_pb.TMSG_DRONESTAR_UPGRADE_RSP },
    { xManMsg_pb.MSG_DRONESTAR_RESET_RSP, MSG_DRONESTAR_RESET_RSP, DroneCenter_pb.TMSG_DRONESTAR_RESET_RSP },
    { xManMsg_pb.MSG_DRONESTAR_INFO_NTF, MSG_DRONESTAR_INFO_NTF, DroneCenter_pb.TMSG_DRONESTAR_INFO_NTF },
}
net_route.RegisterMsgHandlers(MessageTable)