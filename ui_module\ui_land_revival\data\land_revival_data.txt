local table = table
local require = require
local string = string
local ipairs = ipairs
local tonumber = tonumber
local math = math
local gw_task_data = require "gw_task_data"
local reward_mgr = require "reward_mgr"
local game_scheme = require "game_scheme"
local os = require "os"
local time_util = require "time_util"
local festival_activity_mgr = require "festival_activity_mgr"
local data_mgr = require "data_mgr"
local log = require "log"
local util = require "util"


module("land_revival_data")


local _d = data_mgr:CreateData("hero_first_charge_data")
---非服务器数据存储
local mc = _d.mde.const

local hammerItemID = 8038
--region 活动相关
---@public function 设置主活动ID
function SetActivityId(activityID)
    mc.activityID = activityID
end

---@public function 获取主活动ID
function GetActivityID()
    return mc.activityID
end

---@public function 设置活动任务ID
function SetActivityTaskID(activityTaskID)
    mc.activityTaskID = activityTaskID
end

---@public function 获取活动任务ID
function GetActivityTaskID()
    return mc.activityTaskID
end


---@public function 获取活动数据
function GetActivityData()
    if not mc.activityData then
        mc.activityData = festival_activity_mgr.GetActivityDataByActivityID(mc.activityID)
    end
    return mc.activityData
end

---@public function 获取活动任务数据
function GetActivityTaskIDData()
    if not mc.activityData then
        mc.activityData = festival_activity_mgr.GetActivityDataByActivityID(mc.activityTaskID)
    end
    return mc.activityData
end

---@deprecated 获取当前活动开启的天数 
function GetCurDayNew()
    local activityData = GetActivityData()
    local startTime = 0
    if activityData then
        startTime = activityData.startTimeStamp or 0
    end
    local curDay = math.ceil((os.server_time() - startTime)/86400)
    return curDay or 0
end

---@public function 获取锤子道具ID
function GetHammerItemID()
    return hammerItemID
end

---@public function 活动是否开启
function IsLandRevivalActivityOpen()
    local activityID = GetActivityID()
    local isOpen = festival_activity_mgr.GetIsOpenByActivityID(activityID, true)
    return isOpen
end
--endregion

--region 任务数据相关

---@public function 获取复兴任务数据
function GetRevivalTaskData()
    if not mc.vipTaskDataArr then
        mc.vipTaskDataArr = {}
        local num = game_scheme:ChallengeReward_nums()
        for i = 0, num - 1 do
            local cfg = game_scheme:ChallengeReward(i)
            if cfg then
                if cfg.AtyID == mc.activityTaskID then
                    local tempData = {}
                    tempData.taskID = cfg.TaskID
                    tempData.vipTaskID = cfg.VipTaskID
                    tempData.vipLevel = cfg.viplevel
                    table.insert(mc.vipTaskDataArr, tempData)
                end
            end
           
        end
    end
    return mc.vipTaskDataArr
end

---@public function 获取自己锤子数量
function GetSelfHammerCount()
    local player_mgr = require "player_mgr"
    local sid = player_mgr.GetGoodsSidById(hammerItemID)
    local itemCount = 0
    if sid and sid >0 then
        itemCount = player_mgr.GetGoodsNumberBySid(sid)
    end
    return itemCount
end

---@public function 获取vip任务总进度
function GetVipTaskTotalProgress()
    local vipTaskData = GetRevivalTaskData()
    local curProgress = GetSelfHammerCount()
    if vipTaskData then
        local len = #vipTaskData
        local lastTask = vipTaskData[len]
        if lastTask then
            local taskCfg = game_scheme:TaskMain_0(lastTask.taskID)
            if taskCfg then
                local totalProgressRate = taskCfg.ConditionValue1
                return curProgress, totalProgressRate
            end
        end
    end
    return curProgress,0
end

---@public function 获取vip任务合并的奖励
function GetVipTaskMergeReward()
    local vipTaskData = GetRevivalTaskData()
    local rewardIdList = {}
    if vipTaskData then
        for _, v in ipairs(vipTaskData) do
            local taskCfg1 = game_scheme:TaskMain_0(v.taskID)
            local taskCfg2 = game_scheme:TaskMain_0(v.vipTaskID)
            table.insert(rewardIdList, taskCfg1.TaskReward)
            table.insert(rewardIdList, taskCfg2.TaskReward)
        end
    end
    local rewardList = reward_mgr.GetRewardGoodsMergers(rewardIdList)
    return rewardList
end

---@public function 获取7天任务配置
function GetDayTaskData()
    if not mc.dayTaskDataArr then
        mc.dayTaskDataArr = {}
        local num = game_scheme:ChallengeListNew_nums()
        for i = 0, num - 1 do
            local cfg = game_scheme:ChallengeListNew(i)
            if cfg then
                if cfg.AtyID == mc.activityTaskID then
                    table.insert(mc.dayTaskDataArr, cfg)
                end
            end
        end
    end
    return mc.dayTaskDataArr
end

---@public function 获取所有天的任务数据
function GetAllDayTaskListData()
    if not mc.AllDayTaskData then
        GetDayTaskData()
        if mc.dayTaskDataArr then
            mc.AllDayTaskData = {}
            for i, v in ipairs(mc.dayTaskDataArr) do
                --固定3个内容
                local tempDayData = { [1] = {}, [2] = {}, [3] = {} }
                for index = 1, 3 do
                    tempDayData[index].titleLang = v["ListName"..index]
                    tempDayData[index].taskIDList = {}
                    local count = v["nContentID"..index].count
                    local data = v["nContentID"..index].data
                    for n = 0, count do
                        local nContentID = data[n]
                        local contentCfg = game_scheme:ChallengeContent_0(nContentID)
                        if contentCfg then
                            local taskID = contentCfg.nParameter.data[0]
                            table.insert(tempDayData[index].taskIDList, taskID)
                        end
                    end
                end
                mc.AllDayTaskData[v.nDayNum] = tempDayData
            end
        end
    end
    return mc.AllDayTaskData
end

---@public function 获取某一天的toggle数据
function GetHammerToggleData(dayIndex)
    if not mc.AllDayTaskData  then
        GetAllDayTaskListData()
    end
    local dayData = mc.AllDayTaskData[dayIndex]
    return dayData
end

--endregion

--region UI表现相关

function SetMainJumpDay(dayIndex)
    mc.NextOpenDay = dayIndex
end

function GetJumpDay()
    return mc.NextOpenDay
end

--endregion

function Clear()
    _d.mde:clear()
    mc = _d.mde.const
end