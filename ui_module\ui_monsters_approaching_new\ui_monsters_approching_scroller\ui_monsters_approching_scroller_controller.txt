local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type
local Edump = Edump

local controller_base = require "controller_base"
local monsters_approaching_group_define = require "monsters_approaching_group_define"
local GWMgr = require "gw_mgr"
local GWConst = require "gw_const"
local event = require "event"
local screen_util = require "screen_util"
local monsters_approaching_group_data = require "monsters_approaching_group_data"
local monsters_approaching_group_mgr = require "monsters_approaching_group_mgr"
local util = require "util"
local log = require "log"
--region Controller Life
module("ui_monsters_approching_scroller_controller")
local controller = nil
local UIController = newClass("ui_monsters_approching_scroller_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)
    self.curIndex = 1
end

function UIController:OnShow()
    self.__base.OnShow(self)
    self:UpdateScrollTable()
    self:OnRefreshMonsterUI()    
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.CData = nil
    if self.delayAni then
        util.RemoveDelayCall(self.delayAni)
        self.delayAni = nil
    end
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

-- 刷新列表
function UIController:UpdateScrollTable()
    local scrollTable = {}--构建用于显示页面的数据结构
    if  GWMgr.curScene ~= GWConst.ESceneType.Storm then
        local iconStr = ""
        if  GWMgr.curScene == GWConst.ESceneType.Sand then 
            iconStr = "_sand"
        elseif  GWMgr.curScene == GWConst.ESceneType.Home then
            iconStr = "_home"
        end
        if monsters_approaching_group_data.GetIsOpenActivity() then
            local datas = monsters_approaching_group_data.GetOpenActList()
            local taskData
            for index, serverData in ipairs(datas) do
                local isRed = monsters_approaching_group_mgr.UpdateRedInfoByID(serverData.nActiveStateID)
                if not taskData or isRed then -- 策划改动：取消滚动，优先展示有红点的，都没有则按排序展示
                    local data = {
                        serverData = serverData
                    }
                    local curdata, allNum, curNum = monsters_approaching_group_mgr.GetTaskData(serverData)
                    if allNum and allNum ~= 0 then
                        data.allNum = allNum
                    end
                    data.tarTime = monsters_approaching_group_data.GetEndTime(serverData)
                    data.curNum = curNum or 0
                    local cfg = serverData.cfg
                    if cfg then
                        data.iconName = cfg.IconName .. iconStr
                        data.MainSlgEffect = cfg.MainSlgEffect
                        data.ActivityName = cfg.ActivityName
                        data.time = cfg.TimeResidue -- 滚动计时
                        data.OnBtnClickEvent = function()
                            -- log.Warning("OnBtnClickEvent", index)
                            monsters_approaching_group_mgr.OpenMonstersUI(index)
                        end
                        taskData = data
                        if isRed then
                            break
                        end
                    end
                end
            end
            
            if taskData then
                table.insert(scrollTable, taskData)
            end
        end
    end
    if #scrollTable > 1 then
        table.insert(scrollTable, scrollTable[1])--（最后一个复用第一个实现循环播放）
    end
    local red_system = require "red_system"
    local red_const = require "red_const"
    red_system.TriggerRed(red_const.Enum.MonsterApprochingEnterRed)
    self.scrollTable = scrollTable
    -- log.Warning("[gwlx] data", Edump(scrollTable))
    self:TriggerUIEvent("UpdateScrollTable", scrollTable)
    -- self:CheckSrollToIndex()
    self:UpdateBubbleUI()
end
-- -- 检测注册列表定时滚动
-- function UIController:CheckSrollToIndex()
--     local len = #self.scrollTable
--     if len <= 1 then
--         return
--     end
--     local data = self.scrollTable[self.curIndex]
--     if data then
--         local time = data.time or 5
--         if self.curIndex == len or self.curIndex == 1 then -- 假头尾循环列表，所以需要第一个和最后一个之间时间各占一半
--             time = time / 2
--         end
--         -- log.Warning("[gwlx] time", time, self.curIndex)
--         if self.delayAni then
--             util.RemoveDelayCall(self.delayAni)
--             self.delayAni = nil
--         end
--         if time > 0 then
--             self.delayAni = util.DelayCallOnce(time, function ()
--                 self:SrollToIndex()
--             end)
--         else
--             self:SrollToIndex()
--         end
--     end
-- end
-- -- 列表滚动动画
-- function UIController:SrollToIndex()
--     if self.stopScroller then
--         return
--     end
--     local index = self.curIndex + 1
--     local len = #self.scrollTable
--     if index > len then
--         index = 1
--     end
--     local isPlayAni = index ~= 1
--     self.curIndex = index
--     self:TriggerUIEvent("SrollToIndex", index - 1, isPlayAni)
--     self:CheckSrollToIndex()
-- end

-- 设置对话气泡
function UIController:SetMonsterAttackTalk(stage, desId)
    local data = {}
    -- local monsters_approaching_group_data = require "monsters_approaching_group_data"
    self:HideTalk()
    data.stage = stage
    data.desId = desId
    self:TriggerUIEvent("SetMonsterAttackTalk", data)

    -- local dataStage = monsters_approaching_group_data.GetDataStage()
    -- if dataStage == monsters_approaching_group_define.DataStage.Doing then
    -- end
end

function UIController:HideTalk()
    local util = require "util"
    if self.ShowDialogueTime then
        util.RemoveDelayCall(self.ShowDialogueTime)
        self.ShowDialogueTime = nil
    end
    self.ShowDialogueTime = util.DelayCallOnce(monsters_approaching_group_define.MonsterApproachingTalkDuration, function()
        monsters_approaching_group_data.SetCurPopStage(monsters_approaching_group_define.PopStage.Hide)
        self:TriggerUIEvent("SetMonsterAttackTalk")
        local data = self.scrollTable[self.curIndex]
        local id
        if data then
            id = data.serverData.nActiveStateID
            -- log.Warning("设置气泡对话刷新计时器2 id", id)
            event.Trigger(monsters_approaching_group_define.START_BUBBLE_TALK_GROUP, id)
        end
    end)
end

function UIController:UpdateBubbleUI()
    local bubbleData = monsters_approaching_group_mgr.GetShowBossData(true)
    if self.BubbleData and bubbleData and self.BubbleData.nActiveStateID ~= bubbleData.nActiveStateID then -- 气泡变化了
        event.Trigger(monsters_approaching_group_define.UPDATE_MONSTER_BUBBLE_GROUP)
    -- elseif self.BubbleData and bubbleData and self.BubbleData.bReward ~= bubbleData.bReward then -- 任务状态变化了
    --     event.Trigger(monsters_approaching_group_define.UPDATE_MONSTER_BUBBLE_GROUP)
    elseif not self.BubbleData and bubbleData then -- 首次气泡
        event.Trigger(monsters_approaching_group_define.UPDATE_MONSTER_BUBBLE_GROUP)
    end
    self.BubbleData = bubbleData
    self:TriggerUIEvent("UpdateBubbleUI", self.BubbleData, self.BubbleData and monsters_approaching_group_mgr.GetIsCanChallenge(self.BubbleData.nActiveStateID))
end

-- 屏幕尺寸变化监听
function UIController:OnRefreshMonsterUI()
    local new = screen_util.GetScreenAspect()
    local isSmall = new >= 720/1280
    self:TriggerUIEvent("OnRefreshMonsterUI", isSmall)
end
--怪物攻城 End

function UIController:AutoSubscribeEvents() 
    --怪物攻城事件 start
    self.RefreshMonsterAttackTalk = function(eventName, stage, desId)
        self:SetMonsterAttackTalk(stage, desId)
    end
    --刷新怪物攻城对话
    self:RegisterEvent(monsters_approaching_group_define.MONSTER_ATTACK_POP_TALK_GROUP, self.RefreshMonsterAttackTalk)

    self.UnlockMonsterAttack = function(eventName)
        self:UpdateScrollTable()
        self:OnRefreshMonsterUI()
    end
    --解锁怪物攻城时刷新入口
    self:RegisterEvent(monsters_approaching_group_define.UNLOCK_MONSTER_ATTACK_GROUP, self.UnlockMonsterAttack)

    -- 屏幕尺寸变化监听
    self.OnUPDATE_SCREEN_SIZE = function(event, new, old)
        self:OnRefreshMonsterUI()
	end
    self:RegisterEvent(event.SCREEN_SIZE_CHANGED, self.OnUPDATE_SCREEN_SIZE)

    --语言变换监听
    self.languageChange = function()
         self:UpdateScrollTable()
    end
    self:RegisterEvent(event.LANGUAGE_SETTING_CHANGED, self.languageChange)
    -- --怪物攻城滚动列表刷新
    -- self.ContinueScroller = function(event, index)
    --     self.stopScroller = false
    --     self.curIndex = index - 1
    --     self:SrollToIndex()
    -- end
    -- self:RegisterEvent(monsters_approaching_group_define.CONTINUE_MONSTERS_SCROLLER, self.ContinueScroller)
    -- --怪物攻城停止滚动
    -- self.StopScroller = function(event, index)
    --     self.curIndex = index - 1
    --     self:SrollToIndex()
    --     self.stopScroller = true
    -- end
    -- self:RegisterEvent(monsters_approaching_group_define.STOP_MONSTERS_SCROLLER, self.StopScroller)
     -- 模块开关
    self:RegisterEvent(event.UPDATE_MODULE_OPEN, self.UnlockMonsterAttack)
end
function UIController:OnBtnBubbleClickedProxy() -- 点击气泡
    monsters_approaching_group_mgr.OnTrunToMonster(self.BubbleData)
end
function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic
--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
