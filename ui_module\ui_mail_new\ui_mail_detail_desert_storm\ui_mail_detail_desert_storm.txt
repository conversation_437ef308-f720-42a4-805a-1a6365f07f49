local print = print
local typeof = typeof
local require = require
local pairs = pairs
local string = string
local tostring = tostring
local stringFormat = string.format
local type = type
local table = table
local ipairs = ipairs
local GameObject = CS.UnityEngine.GameObject
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem

local face_item_new = require "face_item_new"
local alliance_data = require "alliance_data"
local helper_personalInfo = require "helper_personalInfo"
local ui_util = require "ui_util"
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_mail_detail_desert_storm_binding"

--region View Life
module("ui_mail_detail_desert_storm")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil
    self.mvpItems = nil
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic
--刷新界面
function UIView:RefreshTitleInfo(data)
    if not data then
        return
    end
    local mailTypeUIDataMap = data.mailTypeUIDataMap
    self.txt_MailType.text = lang.Get(mailTypeUIDataMap.TxtName)

    self.txt_title.text = data.txtDesTitle
    self.txt_desc.text = data.DesData
end

--刷新战斗结果结果内容
function UIView:RefreshStormBattleAllianceResult(data)
    if not data then
        return
    end
    local selfAllianceData = data.allianceInfo
    self.ss_bg:Switch(selfAllianceData.isWin and 0 or 1)
    self.obj_victory:SetActive(selfAllianceData.isWin and true or false)

    self:SetAllianceItem(selfAllianceData, self.scrItem_myAlliance)
    self:SetAllianceItem(data.attackAllianceInfo, self.scrItem_enemyAlliance)
end

--刷新MVP列表
function UIView:RefreshStormBattleMvpResult(data)
    if self.mvpItems or not data or not data.mvpScoreRole or #data.mvpScoreRole == 0 then
        self.obj_result:SetActive(false)
    else
        self.obj_result:SetActive(true)

        self.mvpItems = {}
        local length = #data.mvpScoreRole
        for i, v in ipairs(data.mvpScoreRole) do
            local entityObj = GameObject.Instantiate(self.item_mvpState.gameObject, self.rtf_mvpLayout)
            if entityObj then
                entityObj.gameObject:SetActive(true)
                self.mvpItems[i] = entityObj:GetComponent(typeof(ScrollRectItem))
            end
            self:SetMvpItem(v, self.mvpItems[i])
            -- 最后一条没有下划线
            local line = self.mvpItems[i]:Get("line")
            line:SetActive(i ~= length)
        end
    end
end

--刷新个人列表
function UIView:RefreshStormBattleSingleRank(data)
    if self.rankItems or not data or not data.battleResults or #data.battleResults == 0 then
        self.obj_singleRank:SetActive(false)
    else
        self.obj_singleRank:SetActive(true)
        self.rankItems = {}
        for i, v in ipairs(data.battleResults) do
            local entityObj = GameObject.Instantiate(self.item_rankItem.gameObject, self.rtf_rankLayout)
            if entityObj then
                entityObj.gameObject:SetActive(true)
                self.rankItems[i] = entityObj:GetComponent(typeof(ScrollRectItem))
            end
            self:SetRankItem(v, self.rankItems[i])
        end
    end
end

function UIView:SetAllianceItem(data, scrollItem)
    if not scrollItem then
        return
    end

    local flag = scrollItem:Get("flag")
    if flag and data and data.flag and data.flag > 0 then
        local flagData = alliance_data.GetFlagIdData(data.flag)
        if flagData then
            self:CreateSubSprite("CreateLeagueAsset", flag, 'qizhi' .. tostring(flagData.iconID))
        end
    end

    local world = scrollItem:Get("world")
    if world then
        if data and data.worldID and data.worldID > 0 then
            local str = helper_personalInfo.SetWorldAndServerNameToShow(stringFormat("#%s", ui_util.GetWorldIDToShowWorldID(data.worldID, nil, ui_util.WorldIDRangeType.Normal))) -- 可传入第二个参数改变是否显示当前服的名字 默认关闭显示
            world.text = str
        else
            world.text = "#0"
        end
    end

    local name = scrollItem:Get("name")
    if name then
        if data and not string.IsNullOrEmpty(data.shortName) and not string.IsNullOrEmpty(data.allianceName) then
            name.text = util.SplicingUnionShortName(data.shortName, data.allianceName)
        else
            name.text = "[]"
        end
    end

    local power = scrollItem:Get("power")
    if power then
        if data and data.score and data.score > 0 then
            power.text = tostring(data.score)
        else
            power.text = "0"
        end
    end
end

local mvpStateLangTable = {
    [1] = 1003638,
    [2] = 1003639,
    [3] = 1003640,
    [4] = 1003641,
    [5] = 1003642
}

function UIView:SetMvpItem(data, scrollItem)
    if not data or not scrollItem then
        return
    end

    local name = scrollItem:Get("name")
    if name then
        name.text = data.name
    end

    local score = scrollItem:Get("score")
    if score then
        score.text = tostring(data.mvpScore)
    end

    local state = scrollItem:Get("state")
    if state then
        state.text = lang.Get(mvpStateLangTable[data.mvpType])
    end

    local face = scrollItem:Get("face")
    if face then
        local faceItem = face_item_new.CFaceItem()
        faceItem:Init(face, nil, 1)
        faceItem:SetFaceInfo(data.roleFaceStr, nil)
        faceItem:SetFrameID(data.frameid, true)
        table.insert(self.VData, faceItem)
    end

end

function UIView:SetRankItem(data, scrollItem)
    if not data or not scrollItem then
        return
    end

    local rankingIcon = scrollItem:Get("rankingIcon")
    if rankingIcon then
        if data.rankIndex <= 3 then
            rankingIcon.gameObject:SetActive(true)
            rankingIcon:Switch(data.rankIndex - 1)
        else
            rankingIcon.gameObject:SetActive(false)
        end
    end

    local rankingText = scrollItem:Get("rankingText")
    if rankingText then
        if data.rankIndex > 3 then
            rankingText.gameObject:SetActive(true)
            rankingText.text = tostring(data.rankIndex)
        else
            rankingText.gameObject:SetActive(false)
            rankingText.text = ""
        end
    end

    local name = scrollItem:Get("name")
    if name then
        name.text = data.name
    end

    local Power = scrollItem:Get("Power")
    if Power then
        Power.text = tostring(data.score)
    end

    local face = scrollItem:Get("face")
    if face then
        local faceItem = face_item_new.CFaceItem()
        faceItem:Init(face, nil, 1)
        faceItem:SetFaceInfo(data.roleFaceStr, nil)
        faceItem:SetFrameID(data.frameid, true)
        table.insert(self.VData, faceItem)
    end
end

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
            window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
            window:LoadUIResource(ui_path, nil, nil, nil, nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
