local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local Common_Util = CS.Common_Util.UIUtil

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_desert_storm_activity_tips_panel_binding"
local ui_window_mgr = require "ui_window_mgr"
local time_util = require "time_util"
local gw_storm_mgr = require "gw_storm_mgr"
local card_sprite_asset = require "card_sprite_asset"
local os = os
local typeof = typeof
local SortingGroup = CS.UnityEngine.Rendering.SortingGroup
local Canvas = CS.UnityEngine.Canvas

--region View Life
module("ui_desert_storm_activity_tips_panel")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
    self.timeTicker = nil
    self.alliance_sprite_asset = card_sprite_asset.CreateLeagueAsset()
    self.canvas = self.UIRoot:GetComponent(typeof(Canvas))
    self.sortGroup = self.UIRoot:GetComponent(typeof(SortingGroup))
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:UpdateUI()
    local mainSlg = ui_window_mgr:GetWindowObj("ui_main_slg")
    if mainSlg then
        local curOrder = mainSlg.curOrder > 10000 and mainSlg.curOrder or self.curOrder
        if self.canvas then
            self.canvas.sortingOrder = curOrder + 15
        end
    end
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    if self.timeTicker then
        util.RemoveDelayCall(self.timeTicker)
        self.timeTicker = nil;
    end
    if self.alliance_sprite_asset then
        self.alliance_sprite_asset:Dispose()
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

function UIView:ShowPanel(timeList)
    self.timeList = timeList

    self:UpdatePanel()
    self:UpdateScore()
end

function UIView:UpdateScore()
    local activityData = gw_storm_mgr.GetStormDataActivity()
    local scoreData = require "gw_storm_data_score"
    local matchInfo = activityData.OnGetMatchInfo()
    if matchInfo and matchInfo.target then
        self.txt_enemyScore.text = matchInfo.target.alliance_score 
        self.txt_enemyMemberCount.text = matchInfo.target.map_nums
    else
        self.txt_enemyScore.text = 0
    end
    if matchInfo and matchInfo.own then
        self.txt_myScore.text = matchInfo.own.alliance_score
        self.txt_myMemberCount.text = matchInfo.own.map_nums
    else
        local alliance_data = require("alliance_data")
        local userAllianceData = alliance_data.GetUserAllianceData()
        if userAllianceData and userAllianceData.allianceId ~= nil then
            local myScore,mySpeed = scoreData.GetAlliScoreCurTime(userAllianceData.allianceId)
            self.txt_myScore.text = myScore
        end
    end

end

function UIView:UpdatePanel()
    if self.timeTicker then
        util.RemoveDelayCall(self.timeTicker)
        self.timeTicker = nil;
    end
    local startTime = self.timeList.battleTime - os.server_time()
    local endTime = self.timeList.endTime - os.server_time()
    
    if startTime > 0 then
        Common_Util.SetActive(self.obj_TimeTickerObj,true)
        Common_Util.SetActive(self.obj_showScoreBoard,false)
        Common_Util.SetActive(self.obj_hideScoreBoard,false)
        self.timeTicker = util.IntervalCall(1,function()
            startTime = startTime - 1;
            if startTime < 0 then
                if self.timeTicker then
                    util.RemoveDelayCall(self.timeTicker)
                    self.timeTicker = nil;
                end
                self:UpdatePanel() --立即检测一次状态
            end
            self.txt_Timer.text = time_util.FormatTime5(startTime)
        end)
    elseif endTime > 0 then
        Common_Util.SetActive(self.obj_TimeTickerObj,false)
        Common_Util.SetActive(self.obj_showScoreBoard,false)
        Common_Util.SetActive(self.obj_hideScoreBoard,true)
        local activityData = gw_storm_mgr.GetStormDataActivity()
        local matchInfo = activityData.OnGetMatchInfo()
        local alliance_data = require("alliance_data")
        if matchInfo.target then
            local allianceData = matchInfo.target.alliance
            local flagData = alliance_data.GetFlagIdData(allianceData.flag)
            if flagData then
                local allianceIconId = flagData.iconID;
                if self.alliance_sprite_asset then
                    local flagPath = string.format2("qizhi{%s1}",allianceIconId)
                    self.alliance_sprite_asset:GetSprite(flagPath, function(sprite)
                        if sprite then
                            self.img_enemyFlag.sprite = sprite
                        end
                    end)
                end
            end
        end

        local userAllianceData = alliance_data.GetUserAllianceData()
        if not userAllianceData or userAllianceData.allianceId == nil or userAllianceData.allianceId == 0  then

        else
            local flagData = alliance_data.GetFlagIdData(userAllianceData.flag)
            if flagData then
                local allianceIconId = flagData.iconID;
                if self.alliance_sprite_asset then
                    local flagPath = string.format2("qizhi{%s1}",allianceIconId)
                    self.alliance_sprite_asset:GetSprite(flagPath, function(sprite)
                        if sprite then
                            self.img_enemyFlag.sprite = sprite
                        end
                    end)
                end
            end
        end
        local checkTime = 10
        self.timeTicker = util.IntervalCall(1,function()
            endTime = endTime - 1;
            self.txt_startTime.text = time_util.FormatTime5(endTime)
            checkTime = checkTime - 1;
            if checkTime <= 0 then
                checkTime = 10;
                gw_storm_mgr.OnGetMatchInfo()
            end
            if endTime <= 0 then
                if self.timeTicker then
                    util.RemoveDelayCall(self.timeTicker)
                    self.timeTicker = nil;
                end
                ui_window_mgr:UnloadModule("ui_desert_storm_activity_tips_panel")
            end
        end)
    else
        ui_window_mgr:UnloadModule("ui_desert_storm_activity_tips_panel")
    end

end

function UIView:ShowScoreBoard()
    Common_Util.SetActive(self.obj_showScoreBoard,true)
    Common_Util.SetActive(self.obj_hideScoreBoard,false)
end

function UIView:HideScoreBoard()
    Common_Util.SetActive(self.obj_showScoreBoard,false)
    Common_Util.SetActive(self.obj_hideScoreBoard,true)
end
--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    if window then
        window:Show()
    end
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
