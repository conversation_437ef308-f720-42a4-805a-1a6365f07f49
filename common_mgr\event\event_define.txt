---@module event_define
module("event_define")

CSUpdate = "csupdate"
CSUpdateEx = "csupdateex"
CSFixedUpdate = "csfixedupdate"
CSLateUpdate = "cslateupdate"
CSEndOfFrame = "csendofframe"

LOW_MEMORY = "LOW_MEMORY"
CUT_DOWN_PRELOAD_LEVEL = "CUT_DOWN_PRELOAD_LEVEL"
BEFORE_SEND_BUG_REPORT = "BEFORE_SEND_BUG_REPORT"

-- 进度条部分事件
MARK_PROGRESS_PRELOAD_RESOURCES = "MARK_PROGRESS_PRELOAD_RESOURCES"
MARK_PROGRESS_LOGIN_EVENT = "MARK_PROGRESS_LOGIN_EVENT"

--- 屏幕尺寸变化（预处理线路，业务模块勿用）
SCREEN_SIZE_CHANGED_PREPASS = "SCREEN_SIZE_CHANGED_PREPASS"
--- 屏幕尺寸变化
SCREEN_SIZE_CHANGED = "SCREEN_SIZE_CHANGED"
--- 检测全屏遮罩
CHECK_FULL_SCREEN_SHIELD = "CHECK_FULL_SCREEN_SHIELD"
--- 设置展示小游戏全屏遮罩
SET_MINI_GAME_SHIELD = "SET_MINI_GAME_SHIELD"

UNIT_TEST = "unit_test"
SHOW_FLOW_MESSAGE = "show_flow_message"
SHOW_FLOW_GOOD = "show_flow_good"
SHOW_MODULE = "show_module"
SHOW_RESULT_MODULE = "show_result_module"

NET_CONNECTED = "net_connected"
NET_MSG_RECEIVE = "net_msg_receive"

STATE_ENTER = "state_enter"
STATE_LEAVE = "state_leave"

KICKOUT_CLIENT = "KICKOUT_CLIENT" --服务器踢人
SYNC_SERVER_TIME = "sync_server_time" --同步服务器时间
PING_VALUE_CAHNGED = "PING_VALUE_CAHNGED"
SERVER_CROSS_DAY = "SERVER_CROSS_DAY" --服务器跨天事件

ANDROID_BACK = "android_back"
ON_ANDROID_BACK_RESULT = "on_android_back_result"

--退出战场
STATE_EXIT_BATTLE = "state_exit_battle"
RESULT_WINDOW_CLOSE = "result_window_closed"
BATTLE_SKIP = "battle_skip"

-- state cache set
SET_FLOW_STATE_CACHE = "SET_FLOW_STATE_CACHE"

--返回大厅
STATE_COMEBACK_LOBBY = "state_return_lobby"
UPDATE_LOBBY_ARROW = "update_lobby_arrow"
RETURN_LOGIN = "return_login"
CLEAR_ROLE_DATA = "clear_role_data"
OPEN_LOBBY_XINYUAN = "open_lobby_xinyuan" --打开新手心愿活动界面
OPEN_LOBBY_WISHLISTLOTTERYWEEK = "open_lobby_wish_list_lottery_week" -- 打开周活动心愿抽界面

--返回登录界面清除数据
LOADING_BATTLE_TIME_OUT = "loading_battle_time_out"

SCENE_DESTROY = "scene_destroy"
SCENE_DESTROY_NEW = "scene_destroy_new"
-- 2022/1/25 此事件更改为切换账号时触发（原因：原有数据依赖此事件初始化数据，从生命周期角度看 是不合理的）
SCENE_LOADING = "scene_loading"
SCENE_LOADED = "scene_loaded"
SCENE_LOADING_COMPLETE = "scene_loading_complete"
SCENE_SHOW = "scene_show"
SCENE_HIDE = "scene_hide"
ALL_UI_POP_UP_FINISH = "ALL_UI_POP_UP_FINISH"
CHECK_HERO_REWARD = "check_hero_reward"

--更新福利界面
UPDATE_GIFT_NEW_UI = "UPDATE_GIFT_NEW_UI"

-- 暂无使用 关闭所有界面,不要触发任何异步回调,单纯完成关闭操作
CLOSE_ALL_WINDOW = "CLOSE_ALL_WINDOW"

BEFORE_SCENE_LOAD = "BEFORE_SCENE_LOAD"
PLAYER_START = "player_start"

CLOSE_SELECT_SERVERS = "close_select_servers"

ENABLE_ROW_BUTTON = "enable_row_button"
UPDATE_ROLE_FACE_PART_DATA = "update_role_face_part_data"

NEXT_ROUND = "next_round"
SORT_MODULE = "sort_module"
DECOMPOSE_ZIP_FILE_ASYNC = "decompose_zip_file_async"

-- 服务器紧急处理模块开关
UPDATE_MODULE_ON_OFF = "update_module_on_off"
-- 模块分区功能开关
UPDATE_MODULE_OPEN = "update_module_open"
-- 刷新服务器列表
UPDATE_SERVER_LIST_INFO = "update_server_list_info"
-- 重新请求更新服务器数据
REQ_SERVER_LIST = "req_server_list"
-- 聊天
CHAT_MSG_ARRIVED_NTF = "chat_msg_arrived_NTF"
CHAT_UI_OPEN_NTF = "chat_ui_open_ntf"
CHAT_NEW_MSG_NTF = "chat_new_msg_ntf"
CHAT_LOGIN_MSG_NTF = "chat_login_msg_ntf"
CHAT_SEND_SUCCESSFULLY = "chat_send_successfully"
CHAT_SETTING_SUCCESSFULLY = "chat_setting_successfully"
CHAT_UPDATE_MSG = "chat_update_msg"
CHAT_BLOCK_PLAYER = "chat_block_player"
CHAT_BLOCK_PLAYER_UNLOCK_CLICK = "chat_block_player_unlock_click"
CHAT_BLOCK_PLAYER_UNLOCK_RSP = "chat_block_player_unlock_rsp"
CHAT_BLOCK_PLAYER_LIST_RSP = "chat_block_player_list_rsp"
CHAT_BLOCK_PLAYER_LIST_NTF = "chat_block_player_list_ntf"
UPDATE_PLAYER_INFO_DETAIL = "update_player_info_detail"
UPDATE_CHAT_TIPS = "update_chat_tips"

UPDATE_CHAT_MSG = "UPDATE_CHAT_MSG"
UPDATE_PRIVATE_CHAT_MSG = "UPDATE_PRIVATE_CHAT_MSG"
UPDATE_SESSION_PROP = "UPDATE_SESSION_PROP"
UPDATE_CHANNEL_DOT = "UPDATE_CHANNEL_DOT"
UPDATE_CUR_CHANNEL = "UPDATE_CUR_CHANNEL"
UPDATE_CHANNEL_CONFIG = "UPDATE_CHANNEL_CONFIG"
UPDATE_CHAT_PAGE_CHANGE = "UPDATE_CHAT_PAGE_CHANGE"
UPDATE_CHAT_VIEW = "update_chat_view"
CHAT_SET_TOP_TIPS_AND_SHOW = "CHAT_SET_TOP_TIPS_AND_SHOW"
CHAT_TOP_TIPS_HIDE = "CHAT_TOP_TIPS_HIDE"
CHAT_MSG_READED = "CHAT_MSG_READED"--消息已读
CHAT_SHOW_GO2BOTTOM_BTN = "CHAT_SHOW_GO2BOTTOM_BTN" --聊天界面显示回到底部按钮
--更新当前页面

RES_CHANGE_CHANNEL = "RES_CHANGE_CHANNEL"

AT_STATE_CHANGE = "AT_STATE_CHANGE" --@状态改变

TRANSLATE_RSP = "TRANSLATE_RSP" --翻译响应
UPDATE_SCENE_TIME = "update_scene_time"

UPDATE_BATTLE_PLAYBACK = "update_battle_playback"

-- 封号禁言相关
COMMAND_FREEZE = "command_freeze"
COMMAND_MUTE = "command_mute"
COMMAND_REMOVE_MUTE = "command_remove_mute"

CREATEACTOR_DATA_UPDATE = "createactor_data_update"
BASEMODEL_DATA_UPDATE = "basemodel_data_update"
BASEMODEL_SEX_UPDATE = "basemodel_sex_update"

-- 光环系统相关
HALO_UPGRADE_UPDATE_DATA = "halo_upgrade_update_data"
UPDATE_GET_HALO = "update_get_halo"
UPDATE_HALO_UI = "update_halo_ui"
HALO_LEVEL_UPGRADE = "halo_level_upgrade"

-- 摄像机位置变化
CAMERA_POSITION_CHANGE = "camera_position_change"
UI_MENU_BOT_SELECT_TAG = "ui_menu_bot_select_tag"

-- 语音kUserVolumeIndicationEvent
VOICE_VOLUME_INDICATION = "voice_volume_indication" -- 语音说话广播事件
VOICE_PHONE_HANDESET_CHANGESTATE = "voice_phone_handeset_changestate" -- 语音耳机插拔状态改变
VOICE_EXIT_ROOM = "voice_exit_room" -- 退出语音房间

ACTOR_LOGIN_NTF = "actor_login_ntf" --大厅角色登陆消息

ACTOR_LOGIN_FINISH = "actor_login_finish" --大厅登陆完成(地图也加载完了)

LOGIN_RESPONSE_ERROR = "LOGIN_RESPONSE_ERROR"

CONNECT_FAILED_LOBBY = "connect_failed_lobby"
CONNECT_FAILED_DISCONNECT = "connect_failed_disconnect"
CONNECT_SUCCESS = "connect_success"

ACCOUNT_LOGOUT = "account_logout" -- 账号退出登录
ACCOUNT_QUEUE_NTF = "account_queue_ntf" -- 登录账号排队通知
AUTOLOGIN_CLOSED = "autologin_closed"

-- 邮件
MAIL_UPDATE = "mail_update" --更新邮件
OPEN_DETAIL_MAIL_RES = "open_detail_mail_res" --打开详细邮件结果
RECEIVE_MAIL_GOODS_RES = "receive_mail_goods_res" --领取物品结果
RECEIVE_ALL_MAIL_GOODS_RES = "receive_all_mail_goods_res"
DEL_MAIL_RES = "del_mail_res" --删除邮件结果
DEL_SPECIAl_MAIL_RES = "del_special_mail_res" --删除特定邮件结果
MAIL_RESET_UI = "mail_reset_ui" --重置UI数据
HAS_UNREAD_MAIL = "has_unread_mail" --通知是否有未读邮件
UPDATE_MAIL_RECEIVE_STATE = "update_mail_receive_state"
UPDATE_MAIL_READ_STATE = "update_mail_read_state" --更新邮件阅读状态
--更新邮件领取状态
UPDATE_PLAYER_MAIL = "update_player_mail"
HAS_NEW_MAIL = "has_new_mail"
RefreshMainMailBubble_SystemImportant = "RefreshMainBubble_SystemImportant" --刷新主界面气泡_系统重要
RefreshMainMailBubble_R5 = "RefreshMainBubble_R5" --刷新主界面气泡_R5
RefreshMainMailBubble_President = "RefreshMainBubble_President" --刷新主界面气泡_总统
RefreshMainMailBubble_Strategy = "RefreshMainBubble_Strategy" --刷新主界面气泡_攻略
RefreshMain_Questionnaire = "RefreshMain_Questionnaire" --刷新主界面调查问卷
RefreshMain_Download = "RefreshMain_Download" --刷新主界面下载
RefreshMain_ChangePackage = "RefreshMain_ChangePackage" --刷新主界面换包好礼

-- net 层
NET_DISCONNECT_EVENT = "net_disconnect_event" -- 断线事件通知
NET_CONNECT_EVENT = "net_connect_event" -- 连接事件通知
NET_HANDSHAKE_EVENT = "net_handshake_event" -- 握手事件通知
NET_HANDSHAKE_SUCCESS_EVENT = "net_handshake_success_event" -- 握手成功事件通知
NET_MESSAGE_RECIVE_NTF = "net_message_recive_ntf" -- 客户端接收到服务器消息事件通知
NET_MESSAGE_SEND_RSP = "net_message_send_rsp" -- 客户端发送消息请求

NET_HANDSHAKE_LOGINSERVER_EVENT = "NET_HANDSHAKE_LOGINSERVER_EVENT" -- 登录握手事件
NET_CLIENT_RECONNECT_EVENT = "NET_CLIENT_RECONNECT_EVENT" -- 客户端重连成功事件

LOGIN_OVER_TIME = "login_over_time" --连接超时（弹窗）
NET_RECONNECT_SUCCESS_EVENT = "NET_RECONNECT_SUCCESS_EVENT" -- 重连成功事件通知
-- 绑定帐号
BIND_ACCOUNT_SUCCESS_NTF = "bind_account_success_ntf" -- 绑定站好成功事件
RECEIVE_ACTORINFO_MSG = "receive_actorinfo_msg" -- 收到服务器角色数据

-- 语言修改
LANGUAGE_SETTING_CHANGED = "language_setting_changed"

--UI窗口加载完毕
UI_MODULE_LOAD_COMPLETE = "UI_MODULE_LOAD_COMPLETE"
UI_MODULE_INIT_BASE = "UI_MODULE_INIT_BASE"
UI_MODULE_SHOW = "UI_MODULE_SHOW"
UI_MODULE_INIT = "UI_MODULE_INIT"
UI_MODULE_HIDE = "UI_MODULE_HIDE"
UI_MODULE_CLOSE = "UI_MODULE_CLOSE"
UI_MODULE_PREPARE_CLOSE = "UI_MODULE_prepare_CLOSE"
UI_MODULE_REMOVE_OBJ = "UI_MODULE_REMOVE_OBJ"

--UI窗口打开耗时上报
UI_MODULE_BEGIN_REPORT = "ui_module_begin_report"
UI_MODULE_LOAD_BEGIN_REPORT = "ui_module_load_begin_report"
UI_MODULE_LOAD_END_REPORT = "ui_module_load_end_report"
UI_MODULE_CREATE_REPORT = "ui_module_create_report"
UI_MODULE_INIT_REPORT = "ui_module_init_report"
UI_MODULE_SHOW_REPORT = "ui_module_show_report"
UI_MODULE_END_REPORT = "ui_module_end_report"

--界面全局事件
UI_GLOBAL_INPUT_ENABLE = "UI_GLOBAL_INPUT_ENABLE"
UI_GLOBAL_INPUT_DISABLE = "UI_GLOBAL_INPUT_DISABLE"
UI_POINTER_DOWN = "UI_POINTER_DOWN"
UI_POINTER_UP = "UI_POINTER_UP"
UI_POINTER_EVENT_CLEAN = "UI_POINTER_EVENT_CLEAN"

--撮合国战
NATIONAL_WAR_STATE = "national_war_state"

--和谐文本事件
HARMONIOUS_SHOWTEXT = "harmonious_showtext"
HARMONIOUS_CLEARTEXT = "harmonious_cleartext"

--设置地图位置
SET_MAP_LOCATION = "SET_MAP_LOCATION"

--涟漪引导关闭
BUTTON_TIPS_TRIGGER_CLOSE = "button_tips_trigger_close"

-- 主角数值属性变更事件
HERO_NUM_PROP_CHANGE_NTF = "hero_num_prop_change_ntf"
--更新英雄数值属性
UPDATE_HERO_NUM_PROP = "update_hero_num_pro"
--更新英雄战斗数值属性
UPDATE_HERO_BATTLE_PROP = "update_hero_battle_pro"
--更新物品属性
UPDATE_GOOD_PROP = "update_good_prop"
--获得道具
ADD_ITEM = "add_item"
-- 销毁英雄
DESTORY_HERO_ENTITY = "destory_hero_entity"
-- 回退英雄
RESET_HERO_ENTITY = "reset_hero_entity"
-- 更新上阵英雄数据
UPDATE_HERO_FORMATION = "update_hero_formation"
--重新计算所有英雄战力
RECALCULATE_ALL_HERO_POWER = "RECALCULATE_ALL_HERO_POWER"
--所有英雄战力计算完毕
ALL_HERO_POWER_CAL_FINISHED = "ALL_HERO_POWER_CAL_FINISHED"
--打开提示
OPEN_MESSAGE_BOX = "open_message_box"

BATTLE_RESULT_CLOSE = "battle_result_close"

--大厅场景滑动
HALL_SCENE_SILDING = "hall_scene_sliding"

--（放置手游）英雄操作相关
HERO_SELECT_SHOW = "hero_select_show"

SET_CUR_CAMERA = "set_cur_camera"

--战斗开始
BATTLE_BEGIN = "battle_begin"
--战斗结束
BATTLE_END = "battle_end"
--消耗物品事件
ITEM_CONSUME = "item_consume"

--播放个人竞技场晋升特效
PLAY_SINGLE_FIGHT_EFFECT = "PLAY_SINGLE_FIGHT_EFFECT"

CHECK_FORCE_GUIDE_SYSTEM = "check_force_guide_system"

BOSS_COMMING_COMPLETE = "boss_comming_complete"
-----------任务模块-------------
UPDATE_TASK_REFRESH_TIME = "update_task_refresh_time"
UPDATE_TASK_DATA = "update_task_data"
UPDATE_TASK_FINISHEF_STATE = "update_task_finished_state"
UPDATE_WEEK_TASK_FINISHEF_STATE = "update_week_task_finished_state"
UPDATE_MONTH_TASK_FINISHEF_STATE = "update_month_task_finished_state"
UPDATE_ACHI_FINISHEF_STATE = "update_achi_finished_state"
UPDATE_TASK_REDDOT = "update_task_reddot"
UPDATE_ACHI_REDDOT = "update_achi_reddot"
UPDATE_ACHI_DATA = "UPDATE_ACHI_DATA" --只成就数据更新时会触发
UPDATE_WEEK_TASK_REFRESH_TIME = "update_week_task_refresh_time"
UPDATE_WEEK_TASK_REDDOT = "update_week_task_reddot"
UPDATE_MONTH_TASK_REDDOT = "update_month_task_reddot"
TOP_ACHIEVEMENT_REWARD = "top_achievement_reward"
--收取成就顶部奖励触发
TASK_ACITIVITY_REWARD = "task_acticity_reward"
--收取每日任务宝箱奖励触发
WEEK_TASK_ACITIVITY_REWARD = "week_task_acticity_reward"
--完成每日任务获取成就
TASK_ACTIVITY_GET = "task_acticity_get"
--完成每周任务获取成就
WEEK_TASK_ACTIVITY_GET = "week_task_acticity_get"
--收取每周任务宝箱奖励触发
--七日挑战任务
UPDATE_SEVEN_SCORE = "UPDATE_SEVEN_SCORE" --积分
UPDATE_SEVEN_REWARD_STATE = "UPDATE_SEVEN_REWARD_STATE" --奖励状态
SEVEN_RED_DOT = "seven_red_dot"
--七天挑战状态更新
UPDATE_SEVEN_NTF = "update_seven_ntf"
--七天挑战数据刷新
UPDATE_SEVEN_PAGE = "update_seven_page"
-- 七天挑战切换切页

UPDATE_TASK_SUNDRY = "update_task_sundry" --刷新任务杂项

MONTH_TASK_MODE_SWITCH = "month_task_mode_switch" --月任务模式切换
MONTH_TASK_REWARD_UPDATE = "month_task_reward_update" --月任务奖励更新

------------背包模块-------------
UPDATE_PACKAGE_LIST = "update_package_list"
ITEM_OPERATION_DONE = "item_operation_done"
ITEM_CHECK_SOURCE = "item_check_source"
UPDATE_FRAGEMENT_NUM = "update_fragement_num"
UPDATE_GOODS_NUM = "update_goods_num"
UPDATE_GOODS_NUM_CHANGE = "update_goods_num_change"
UPDATE_SKILL_NUM = "update_skill_num"
UPDATE_SIGIL_NUM = "update_sigil_num"
UPDATE_CHEST_NUM = "update_chest_num"
UPDATE_SURVIVOR_SPLINTER_NUM = "update_survivor_splinter_num"
LOTTERY_BOX_REAWRD_PREVIEW_SHOW = "lottery_box_reawrd_preview_show"
GOODS_PROP_UPDATE_FINISH = "goods_prop_update_finish" --单次下发的所有道具属性更新完成
------------主线剧情试炼-------------
UPDATE_HOOK_BOOTY = "updata_hook_booty" --挂机战利品
CHANGE_HOOKING_LEVEL = "change_hooking_level" --当前挂机关卡改变
CHANGE_HOOK_CONDITION = "change_hook_condition" --挂机条件发生变化
CHANGE_PASS_LEVEL = "change_pass_level" --通过关卡发生变化
CONFIRM_GET_REWARD = "onfirm_get_reward" --获取挂机奖励
ENTER_LAY_MAIN = "enter_lay_main" --进入狩猎界面
LEAVE_LAY_MAIN = "leave_lay_main" --离开挂机界面
UPDATE_STAGE_TYPE = "update_stage_type" --玩家战斗结束后的通知战斗类型
INIT_LAYMAIN_DATA = "init_laymain_data" --初始化挂机界面的数据
UPDATE_IDLE_TIME = "update_idle_time" --刷新挂机时间
UPDATE_GET_HOOK_REWARD = "update_get_hook_reward" --收取挂机收益回复
UPDATE_ENEMY_CERATE = "update_enemy_ceRate" --更新当前挑战关卡敌方战力
CLOSE_UI_COMPLETE_CHAPTER = "close_ui_complete_chapter"
--关闭章节通关提示面板
UPDATE_AUTO_YIELD_REWARDS = "update_auto_yield_reward" --关卡通关原因引起的挂机产出速度变化

------------幻境之塔-------------
UPDATE_ILLUSION_TOWER_DATA = "update_ilusion_tower_data" --幻境之塔数据更新
ILLUSION_TOWER_PASS = "illusion_tower_pass"
ILLUSION_TOWER_BATTLE_BEGIN = "illusion_tower_battle_begin"
ILLUSION_TOWER_BATTLE_END = "illusion_tower_battle_end"
ILLUSION_TOWER_PROP_UPGRADE = "illusion_tower_prop_upgrade"
ILLUSION_TOWER_LEVEL_UPDATE = "ILLUSION_TOWER_LEVEL_UPDATE"
--属性改变
ILLUSION_TOWER_PASS_REQUEST = "illusion_tower_pass_request"

------------英雄相关-----------------
CLICK_HERO_ITEM = "click_hero_item" -- 选中某个英雄
UPDATE_HERO_EQUIEPMENT = "update_hero_equipment" --更新英雄装备
HERO_ADVANCE_RSP_EVENT = "hero_advance_rsp_event" --  英雄进阶结果
HERO_AWAKEN_RSP = "hero_awaken_rsp" --英雄觉醒结果
UPDATE_HERO_BASE_PANEL = "update_hero_base_panel"
--更新英雄面板
UPDATE_BUY_HERO_LIMIT_NUM = "update_buy_hero_limit_num"
--更新玩家购买英雄列表容量上限的次数
SHOW_HERO_DETAIL = "show_hero_detail" --  显示英雄详情
SKILL_UPGRADE_SUCCESSFULLY = "skill_upgrade_successfully" --技能升级成功
SKILL_UPDATE = "skill_update" -- 技能更改
HERO_REPLACE_RSP = "hero_replace_rsp" --英雄置换回复
HERO_RECOVER_RSP = "hero_recover_rsp" --英雄退还回复
HERO_NUMBER_CHANGE = "hero_number_change" --英雄数量发生变化的情况
HERO_UPDATE = "hero_update"
--英雄数据更新
HERO_UPGRAD_RSP = "hero_upgrad_rsp"
--英雄升级回复
HERO_ICON_RED = "hero_icon_red"
--英雄icon红点
HERO_BIODATA_UPDATE = "hero_biodata_update"
--英雄剧情红点提示
HERO_BIODATA_DATA_UPDATE = "hero_biodata_data_update"
--英雄图鉴等级更新
HERO_BIODATA_LIST_UPDATE = "hero_biodata_list_update"
--英雄图鉴等级更新(登陆下发)
HERO_BIODATA_SINGLE_UPDATE = "hero_biodata_single_update"
--英雄图鉴等级更新(单条更新)
HERO_BIODATA_REFRESH = "hero_biodata_refresh"
--英雄图鉴刷新
HERO_BIODATA_UPDATEITEM = "hero_biodata_updateitem"
--刷新英雄图鉴
CREATE_ARR_UPDATE = "create_arr_update"
--创造法阵更新
SUPPER_EFFECT_ENABLE = "supper_effect_enable"
--无法控制的特效
PLAYER_BATTLE_POWER_UPDATE = "player_battle_power_update" --玩家英雄战斗力排名前六总战力值更新
PLAYER_ALL_HERO_POWER_UPDATE = "player_all_hero_power_update" --玩家所有英雄总战力值更新
SET_HERO_TALENT_SKILL = "set_hero_talent_skill" --设置英雄天赋技能
DELETE_GOODS_UPDATE_EVENT = "delete_goods_update_event" --物品删除更新
HERO_RESONANCE_UPDATE = "hero_resonance_update" --英雄共鸣更新
HERO_ADVANCE_CAREER_SELECT_RES = "advance_career_select_res" --t3进阶职业回复
EQUIPMENT_RESONANCE_CHANGE_RES = "equipment_resonance_change_res" --t2共鸣属性转换
EQUIPMENT_RESONANCE_RES = "equipment_resonance_res" --英雄进阶回复
CALCULATE_HERO_PROP = "calculate_hero_prop"
--计算英雄属性
CLICK_WEAR_COMPLETE = "click_wear_complete"
--一键穿装完成

HERO_AWAKEN_SKILL_AWAKE_SUCCESSFULLY = "hero_awaken_skill_awake_successfully" --英雄觉醒技觉醒成功
UPDATE_HERO_AWAKEN_SKILL_DATA = "update_hero_awaken_skill_data"

HERO_FAST_UPGRADE_RSP = "hero_fast_upgrade_rsp"
--一键升级成功

HERO_PROP_SET_DIRTY = "hero_prop_set_dirty"--英雄属性标记脏

UPDATE_TRIAL_HERO_GET = "UPDATE_TRIAL_HERO_GET" --试用英雄领取成功
DESTROY_TRIALHERO = "DESTROY_TRIALHERO" --试用英雄回收销毁成功

------------主角相关-------------
CREATE_PLAYER_ENTITY = "create_player_entity"
CREATE_ROGUE_ENTITY = "create_rogue_entity" -- 创建实体事件
CREATE_PLAYER_CHALLENGE = "player_challenge_rsp"
REC_ROLE_CREATE_TIME = "rec_role_create_time"
UPDATE_PLAYER_VIP_LEVEL = "update_player_vip_level"
UPDATE_VIP_EXP = "update_vip_exp"
PLAYER_NAME_UPDATE = "player_name_update"
--玩家名字更新
VIP_CLICKED_REDPOT = "vip_clicked_redpot" --vip点击红点刷新
------------抽奖模块-------------
LOTTERY_UPDATEUI_EVENT = "lottery_updateui_event"
LOTTERY_RSP = "lottery_rsp"
LOTTERY_SETCHOICE_RSP = "lottery_setchoice_rsp"
ENTER_OPTIONAL_HERO = "enter_optional_hero"
END_ENTER_OPTIONAL_HERO = "end_enter_optional_hero"
ENTER_HERO_SUMMON_OPTIONAL = "enter_hero_summon_optional"
END_ENTER_HERO_SUMMON_OPTIONAL = "end_enter_hero_summon_optional"

------------商店模块-------------
UPDATE_GOODS_ENABLE_STATE = "update_goods_enable_state" -- 更新商品的可购买状态
UPDATE_REFRESH_GOODS = "update_refreshGoods" -- 刷新随机商店物品
RESET_DIAMOND_ORDER = "reset_diamond_order" -- 设置钻石UI的层级
GENERAL_SHOP_RSP = "general_shop_rsp" -- 通用商店购买回复
UPDATE_SHOP_REFRESHTIME = "update_shop_refreshtime" -- 更新商店倒计时
GENERAL_SHOP_LUA_RSP = "general_shop_lua_rsp" -- 更新商店倒计时
MARKET_GOODS_NUM_CHANGED = "MARKET_GOODS_NUM_CHANGED"
UPDATE_SUB_GOODS_NUM = "update_sub_goods_num"
BUY_GOODS_SUCCESS = "BUY_GOODS_SUCCESS"--购买商品成功
BUY_GOODS_FAIL = "BUY_GOODS_FAIL"--购买商品失败

ENTER_MARKET_GUIDE = "enter_market_guide" --引导进去购物中心
CLICK_MARKET_GUIDE = "click_market_guide" --点击购物中心
TREASURE_RARE_SHOP_GUIDE_START = "treasure_rare_shop_guide_start" --进入购物中心
CLICK_TREASURE_RARE_SHOP_TOGGLE = "click_treasure_rare_shop_toggle" --点击任意商店页或退出购物中心

----------产出-------------------
CREATE_HERO_AWARD = "create_hero_award" --合成英雄奖励
DECOMPOSE_HERO_AWARD = "decompose_hero_award" --分解英雄奖励

CREATE_SPECIALGOODS_ENTITY = "create_specialgoods_entity" -- 创建特殊物品通知

------------竞技场------------
ARENA_TIME_UPDATE = "arena_time_update"
ARENA_READY_FOR_BATTLE = "arena_ready_for_battle"
ARENA_RIVAL_LIST = "arena_rival_list"
ARENA_LINEUP_SUCCESS = "arena_lineup_success"
ARENA_BATTLERECORD_RECV = "arena_battlerecord_recv"
ARENA_LASTRANKING_RECV = "arena_lastranking_recv"
ARENA_PLAYER_DETAILINFO = "arena_player_detailinfo"
ARENA_DEFEND_LINEUP_RECV = "arena_defend_lineup_recv"
UPDATE_LEAGUE_PLAYER_DETAILINFO = "update_league_player_detailinfo"
WEEKEND_ARENA_PLAYER_DETAILINFO = "weekend_arena_player_detailinfo"
UPDATE_WEEKEND_ARENA_INFO = "update_weekend_arena_info"

TEAM_RECV_APPLYLIST = "team_recv_applylist"
TEAM_ARENA_ENROLL = "team_arena_enroll"
ARENA_RANKINGLIST_RECV = "arena_rankinglist_recv"
TEAM_HALL_MSG_RECV = "team_hall_msg_recv"
TEAMMEMBER_CHANGED = "teammember_changed"
TEAM_HAVE_NEW_APPLY = "team_have_new_apply"
TEAM_RECV_INVITELIST = "team_recv_invitelist"
TEAM_HAVE_NEW_INVITE = "team_have_new_invite"
TEAM_APPLY_LIST_CHANGE = "team_apply_list_change"
TEAM_SYNC_LINEUPS = "team_sync_lineups"
TEAMMEMBER_QUEUE_CHANGE = "teammember_queue_change"
ARENA_SYNC_REPLAYID = "arena_sync_replayid"

ARENA_FRIENDINVITELIST_UPDATE = "arena_friendinvitelist_update"
ARENA_TEAMINVITELIST_UPDATE = "arena_teaminvitelist_update"
ARENA_FRIENDINVITE_STATE = "arena_teaminvite_state"
ARENA_TICKET_CREATE = "arena_ticket_create"
AREAN_GET_HANGUP_REWARD = "arena_get_hangup_reward"

ARENA_SEASON_START = "arena_season_start"
ARENA_GET_CURRENT_RIVAL = "arena_get_current_rival"
ARENA_GET_LEVEL_REWARD = "arena_get_level_reward"
ARENA_GET_LEVEL_REWARD_INFO = "arena_get_level_reward_info"
ARENA_GET_VICTORY_REWARD = "arena_get_victory_reward"
ARENA_REFRESH_BTN_STATE = "arena_refresh_btn_state"
----------------装备系统------------------
ON_GEM_UPGRADE_RSP = "ON_GEM_UPGRADE_RSP" --普通装备强化回复

ON_MSG_GEMSTONE_UPGRADE_RSP = "ON_MSG_GEMSTONE_UPGRADE_RSP" --神装强化回复
ON_MSG_HERO_EXCLUSIVE_ACTIVE_RSP = "ON_MSG_HERO_EXCLUSIVE_ACTIVE_RSP" --专属装备激活回复
ON_MSG_HERO_EXCLUSIVE_UPGRADE_RSP = "ON_MSG_HERO_EXCLUSIVE_UPGRADE_RSP" --专属装备强化回复

ON_HERO_UPDATE_GEMSTONE_RSP = "ON_HERO_UPDATE_GEMSTONE_RSP" --装备交换回复
UPDATE_GOODS_ENTITY_DATA = "UPDATE_GOODS_ENTITY_DATA" --物体实体数据更新
UPDATE_GODEQUIP_RESOUND_DATA = "UPDATE_GODEQUIP_RESOUND_DATA" --神器回响数据更新
UPDATE_GODEQUIP_ENTITY_DATA = "UPDATE_GODEQUIP_ENTITY_DATA" --神器实体数据更新
--装备实体创建事件
CREATE_GODEQUIP_ENTITY = "CREATE_GODEQUIP_ENTITY"

--------签到----------------
SIGNIN_DATA_UPDATE = "SIGNIN_DATA_UPDATE" --签到数据更新
SIGNIN_RSP = "SIGNIN_RSP" --签到回复
UPDATE_SEVEN_SIGN_DATA = "update_seven_sign_data"
--七天签到数据更新
SEVEN_SIGNIN_RSP = "seven_sign_rsp"
--七天签到数据更新
UPDATE_ZERO_TIME = "update_zero_time"
--七天登录时间刷新

-------------酒馆-------------
TAVERN_UPDATE_TASKLIST = "tavern_updata_tasklist" --任务列表数据刷新
TAVERN_SORT_TASKLIST = "tavern_sort_tasklist" --刷新时任务列表排序
TAVERN_ENTER_TASKlIST = "tavern_enter_tasklist" --登录时发下来的数据

-------------场景--------------
SCENE_GAMEITEMREDDOT = "scene_gameitem_reddot"
SCENE_GAME_IS_SHOW_ITEM_TYPE = "SCENE_GAME_IS_SHOW_ITEM_TYPE"
SCENE_MATCHPLACEREDDOT = "scene_matchplace_reddot"
SCENE_TOPGAME_TIP = "scene_topgame_reddot"
SHOW_TAVERN_SCENE_UPGRADE_TIPS = "show_tavern_scene_upgrade_tips"
SCENE_BATTLESTATE = "scene_battlestate"
SCENE_WEEKEND_ARENA_TIP = "scene_weekend_arena_tip"

-------------地牢-------------
ASH_UPDATE_DATA = "ash_update_data" -- 地牢数据刷新
ASH_UPDATE_STAGE = "ash_update_stage" -- 地牢关卡更新
ASH_SET_STAGE = "ash_set_stage" -- 地牢设置关卡
ASH_HIDE_EFFECT = "ash_hide_effect" -- 地牢隐藏光效

ON_S2C_SELECT_DRUG = "ON_S2C_SELECT_DRUG"
ON_S2C_RANK_UPDATE = "ON_S2C_RANK_UPDATE"
ON_FIGHT_RESULT = "ON_FIGHT_RESULT"
ON_S2C_SWEEP_RES = "ON_S2C_SWEEP_RES"
ON_GET_HERP_RSP = "ON_GET_HERP_RSP"

-----------活动----------------
ACTIVITY_TYPR_DATA_UPDATE = "ACTIVITY_TYPR_DATA_UPDATE" --某一类活动数据更新(定位到某类活动 删除和新增活动走这条)
ACTIVITY_ONE_DATA_UPDATE = "ACTIVITY_ONE_DATA_UPDATE" --活动的数据更新(定位到某个活动 某条活动重置走这条)
ACTIVITY_CONTENT_UPDATE = "ACTIVITY_CONTENT_UPDATE" --活动内容发生变化（定位到某个内容）
ACTIVITY_BUY_RSP = "ACTIVITY_BUY_RSP" --活动界面的购买回复
ACTIVITY_PER_SECOND = "ACTIVITY_PER_SECOND" --活动界面计时器每秒事件
ACTIVITY_ITEM_TIMER_END = "ACTIVITY_ITEM_TIMER_END" --活动界面计时器倒计时结束
ACTIVITY_REFREASH = "activity_refreash" --活动更新
ACTIVITY_REDDOT_REFREASH = "activity_reddot_refreash" --活动更新
ACTIVITY_MOPUP_REWARD = "activity_mopup_reward" --活动扫荡奖励回复
ACTIVITY_DISPLACE_RSP = "activity_displace_resp" --部分活动回复通用事件
ACTIVITY_BADGESNUM_UPDATE = "activity_badgesnum_update"
ACTIVITY_REQUEST_FAIL = "activity_request_fail" --通用活动请求失败
--更新徽章数量
ACTIVITY_WARM_UP_RSP = "activity_warm_up_rsp"
--活动前瞻数据恢复
SCENE_GAMEITEM_ACTIVITY_TIPS = "scene_gameitem_activity_tips"
--活动标签显示
SCENE_CLEAN_ACTIVITY_TIPS = "scene_clean_activity_tips"
--清空活动标签
SCENE_OPEN_MODULE = "scene_open_module"
--主城打开模块事件
ACTIVITY_CLOSE_ACTIVITY = "activity_close_activity"
--关闭活动页面事件
CUMULATIVE_REFRESH = "cumulative_refresh"
--累计充值刷新
ACTIVITY_DATA_INIT = "activity_data_init"
--活动数据下发
SCENE_GAMEITEM_REBORN_ACTIVITY_TIPS = "scene_gameitem_reborn_activity_tips"
--商店重生活动标志

ACTIVITY_TIME_UP = "activity_time_up"
--活动时间结束，需要退出界面

WISH_LIST_LOTTERY = "wish_list_lottery" --心愿单抽奖
WISH_LIST_LOTTERY_TIMES_UPDATE = "wish_list_lottery_times_update" --心愿单抽奖
WISH_LIST_SELECT_REWARD = "wish_list_select_reward" --心愿单选择奖励

FESTIVAL_SUPER_MONTH_REEWARD = "FESTIVAL_SUPER_MONTH_REEWARD" -- 超级月卡领取奖励

BLESSING_ACTIVITY_LOTTERY = "blessing_activty_lottery" --圣物活动抽奖
BLESSING_ACTIVITY_LOTTERY_TIMES_UPDATE = "blessing_activty_lottery_times_update" --圣物活动更新次数
BLESSING_ACTIVITY_LOTTERY_SELECT_REWARD = "blessing_activty_lottery_select_reward" --圣物活动选择奖励
-----------勇者试炼-------------
START_MOVE = "start_move" -- 可以开始移动的事件
STOP_MOVE = "stop_move" -- 移动结束事件
UPDATE_GRID_VISABLE = "update_grid_visable" -- 更新事附件视野
UPDATE_SELECT_TEAMUI = "UPDATE_SELECT_TEAMUI" -- 设置派遣UI的显示
SELECT_TEAM_EVENT = "select_team_event" -- 选中队伍事件
TRIAL_START_EVENT = "trial_start_event" -- 勇者试炼活动开始或关闭通知
UPDATE_TRIAL_LEVEL = "update_trial_level" -- 关卡更新通知
UPDATE_TRIAL_TEAMINFO = "update_trial_teaminfo" -- 队伍数据更新
UPDATE_TRIAL_TEAMMATE = "update_trial_teammate" -- 队员更新
UPDATE_EVENT_STATE = "update_event_state" -- 更新事件状态
FINISH_TRIAL_BATTLE = "finish_trial_battle" -- 结束勇者试炼某场战斗
START_TRIAL_BATTLE = "start_trial_battle" -- 开始勇者试炼某场战斗
SWIPE_EVENT = "swipe_event" -- 滑动屏幕事件
UPDATE_TEAM_POS = "update_team_pos" -- 更新队伍的位置
MOVE_TO_TEAMPOS = "move_to_teampos" -- 移动摄像机到队伍位置
LOCK_TEAM_RSP = "lock_team_rsp" -- 锁定队伍回复
TRIAL_IS_OVER = "trial_is_over" -- 试炼结束返回大厅
SHOW_PASS_REWARD = "show_pass_reward" -- 显示通关奖励
UPDATE_ENERGY_TIP = "update_energy_tip" -- 更新体力的提示
LOAD_BRAVE_SCENE = "load_brave_scene" -- 加载勇者试炼场景
CLICK_TEAM_ENTITY = "click_team_entity" -- 选中场景中的英雄图像
UPDATE_TRIAL_TEAMBUFF = "update_trial_teambuff" -- 更新队伍的buff信息
TASK_EVENT_RSP = "task_event_rsp" -- 勇者试炼事件回复

--改版

ON_MSG_PEAK_UPDATE_NTF = "ON_MSG_PEAK_UPDATE_NTF" --更新通知
ON_MSG_PEAK_LEVEL_RSP = "ON_MSG_PEAK_LEVEL_RSP" --进入关卡响应
ON_MSG_PEAK_RESET_RSP = "ON_MSG_PEAK_RESET_RSP" --重置响应
ON_MSG_PEAK_MOVE_RSP = "ON_MSG_PEAK_MOVE_RSP" --移动响应
ON_MSG_PEAK_CARRIAGE_RSP = "ON_MSG_PEAK_CARRIAGE_RSP" --灵魂马车响应
ON_MSG_PEAK_SPRING_RSP = "ON_MSG_PEAK_SPRING_RSP" --温泉响应
ON_MSG_PEAK_REVIVE_RSP = "ON_MSG_PEAK_REVIVE_RSP" --复活响应
ON_MSG_PEAK_RELIC_RSP = "ON_MSG_PEAK_RELIC_RSP" --选择遗物响应

ON_PEAK_SCENE_EVENT = "ON_PEAK_SCENE_EVENT" --深空试炼场景事件
RESET_PEAK_MAP = "RESET_PEAK_MAP" --深空试炼重置
ON_PEAK_SCENE_FINISH = "ON_PEAK_SCENE_FINISH" --进入关卡响应完成


------------角色信息------------
ACTOR_NEW_FACE_NTF = "actor_new_face_ntf" -- 新增一个头像
ACTOR_FACE_UPDATE = "actor_face_update" -- 头像更新
ACTOR_FACE_PROP_UPDATE = "actor_face_prop_update" -- 角色属性更新
ACTOR_LEVEL_UPGRADE = "actor_level_upgrade" -- 角色升级
ACTOR_FRAME_UPDATE = "actor_frame_update" -- 头像框更新
ACTOR_FRAME_ATTR_UPDATE = "actor_frame_attr_update" -- 头像框属性更新
ACTOR_TITLE_UPDATE = "actor_title_update" -- 称号更新
ACTOR_TITLE_ATTR_UPDATE = "actor_title_attr_update" -- 称号属性更新
ACTOR_CUSTOM_TITLE_DATA_UPDATE = "actot_custom_title_data_update" -- 自定义称号数据更新
ACTOR_NEW_POWER_UPDATE = "actor_new_power_update" -- 战力更新
ACTOR_NEW_POWER_UP = "actor_new_power_up" -- 战力提升

------------设置系统------------
SETTING_ACCOUNT_LIST_RSP = "setting_account_list_rsp" -- 收到所有服务器数据
SETTING_UPDATE_SERVER_LIST_DATA = "setting_update_server_list_data" -- 更新服务器列表
ACCOUNT_CHANGE_WORLD_RSP = "ACCOUNT_CHANGE_WORLD_RSP" -- 成功换区回复事件

------------联盟系统------------
UPDATE_SOCIATY_REDTIP = "update_sociaty_redtip" -- 更新联盟红点提示
UPDATE_SOCIATY_SIGNIN_TIME = "update_sociaty_signin_time" -- 更新签到倒计时
UPDATE_SOCIATY_BASEDATA = "update_sociaty_basedata" -- 更新联盟基础数据
UPDATE_SOCIATY_PERSONDATA = "update_sociaty_persondata" -- 更新联盟个人数据
UPDATE_SOCIATY_MEMBER = "update_sociaty_member" -- 添加或者删除成员数据
UPDATE_SOCIATY_MEMBERINFO = "update_scoiaty_memberinfo" -- 修改成员列表的属性
UPDATE_SOCIATY_APPLYINFO = "update_scoiaty_applyinfo" -- 修改申请列表
UNION_TECHNOLOGY_RESET_RSP = "UNION_TECHNOLOGY_RESET_RSP" -- 联盟科技重置回复
UNION_TECHNOLOGY_UPGRADE_RSP = "UNION_TECHNOLOGY_UPGRADE_RSP" -- 联盟科技升级回复
UPDATE_ORDER_REFRESH_TIME = "update_order_refresh_time" -- 更新获取订单的重置时间
RETURN_SOCIATY_SCENE = "return_sociaty_scene" -- 返回到联盟大厅主场景
UPDATE_RECOMMEND_LEAGUE = "update_recommend_league" -- 更新推荐联盟
UPDATE_SEARCH_LEAGUE = "update_search_league" -- 更新搜索到的联盟
UPDATE_SOCIATY_OEDER_DATA = "update_sociaty_order_data" -- 更新订单数据
UPDATE_ORDER_REMAIN_TIME = "update_order_remain_time" -- 更新订单剩余时间
UPDATE_CONTRI_DATA = "update_contri_data" -- 更新贡献数据
UPDATE_SOCIATY_ORDER_STATE = "update_sociaty_order_state" -- 更新订单完成状态
DEL_SOCIATY_ORDER = "del_sociaty_order" -- 领取完成订单奖励后删除订单
MODIFY_LEAGUEBASE_SUCCESS = "modify_leaguebase_success" -- 修改联盟数据成功
UPGRADE_SOCIATY_ORDER_SUCCESS = "upgrade_sociaty_order_success" -- 升级联盟订单成功

UPDATE_SOCIATY_FIGHT_CLICK_STATE = "update_sociaty_fight_click_state" -- 联盟争霸点击地块
UPDATE_SOCIATY_FIGHT_EVENT = "update_sociaty_fight_event" -- 联盟争霸事件
UPDATE_SOCIATY_FIGHT_RANK = "update_sociaty_fight_rank" -- 联盟争霸排行刷新
UPDATE_SOCIATY_FIGHT_GUIDE = "update_sociaty_fight_guide" -- 联盟争霸指引刷新
UPDATE_SOCIATY_FIGHT_MAP = "update_sociaty_fight_map"
UPDATE_SOCIATY_FIGHT_BATTLE_RECORD = "update_sociaty_fight_battle_record"
SHARE_CELL_SEND_SUCCESSFULLY = "share_cell_send_successfully"
UPDATE_SOCIATY_BOSS_ACTIVE_TIME = "update_sociaty_boss_active_time" -- 联盟Boss复活战斗倒计时
UPDATE_SOCIATY_BOSS_HURT_RANK_DATA = "update_sociaty_boss_hurt_rank_data" -- 联盟Boss数据通知
UPDATE_SOCIATY_BOSS_FIGHT_COUNT = "update_sociaty_boss_fight_count" -- 联盟Boss攻击次数
UPDATE_SOCIATY_BOSS_RANK_DATA = "update_sociaty_boss_rank_data" -- 联盟Boss已死Boss的排行榜数据
UPDATE_PLAYER_TREASURE_DATA = "update_player_treasure_data" -- 更新联盟成员探宝数据
UPDATE_PLAYER_TREASURE_INTERFACE = "update_player_treasure_interface" -- 更新联盟成员探宝界面
EXPLORE_UI_PARTICLE = "explore_ui_particle" -- 探宝界面播放光效
OPEN_PLAYER_TREASURE_DATA = "open_player_treasure_data" -- 打开联盟探宝界面请求数据
UPDATE_FLOWER_RECORD = "update_flower_record" -- 送花数据更新
UPDATE_EXPLORE_STATE = "update_explore_state" -- 探宝状态更新
UPDATE_AFTER_BOSS_BATTLE = "update_after_boss_battle" -- 联盟探宝挑战BOSS数据更新
UPDATE_STATE_OR_PHYSICAL = "update_state_or_physical" -- 联盟探宝体力或探宝状态更新
UPDATE_BOSS_RANK_DATA = "update_boss_rank_data" -- 联盟探宝BOSS排行榜数据
UPDATE_FLOWER_RANK_DATA = "update_flower_rank_data" -- 联盟探宝送花排行榜数据
UPDATE_SOCIATY_BOSS_REFRESH_REWARD = "update_sociaty_boss_refresh_reward" -- 联盟Boss领取奖励刷新
UPDATE_LEAGUE_INFO = "update_league_info" --更新请求的联盟信息
UPDATE_SOCIATY_ICONID = "update_sociaty_iconid" -- 创建联盟时图标刷新
CREATE_SOCIATY_SUCCESS = "create_sociaty_success"
UPDATE_LEAGUE_INVITED_INFO = "update_league_invited_info" --更新请求的联盟信息
CREATE_SOCIATY_SUCCESS = "create_sociaty_success"
LOAD_SOCIATY_SCENE = "load_sociaty_scene" --联盟场景相机位置
LEAGUE_APPLY_SUCCESS = "league_apply_success" --申请加入联盟成功
LEAGUE_APPLY_SETTING_SUCCESS = "league_apply_setting_success"
--申请设置成功
LEAGUE_CLEAR_SETTING_SUCCESS = "league_clear_setting_success"
--清理设置成功
LEAGUE_AutoOfficer_SETTING_SUCCESS = "league_autoofficer_setting_success"
--晋升设置成功
UPDATE_LEAGUE_INVITED_RSP = "update_league_invited_rsp"
--联盟邀请回复
UPDATE_LEAGUE_RECRUIT = "update_league_recruit" --联盟招募同步
LEAGUE_RECRUIT_SAVE = "league_recruit_save" --联盟招募信息保存
UPDATE_LEAGUEWAR_ATTACK_INFO_DATA = "UPDATE_LEAGUEWAR_ATTACK_INFO_DATA" --公会战攻击信息请求

UPDATE_SOCIATY_TITLEID = "update_sociaty_titleid" --更新联盟称号
UPDATE_SOCIATY_ICON_TITLE_DATA = "update_sociaty_icon_title_data" --更新联盟图标和称号

--------appsFlyer回调事件----------
DID_RECEIVE_CONVERSION_DATA = "didReceiveConversionData" --AppsFlyerTrackerCallbacks:: got conversion data
DID_RECEIVE_CONVERSION_DATA_WITH_EROOR = "didReceiveConversionDataWithError" --AppsFlyerTrackerCallbacks:: got conversion data error
DID_FINISH_VALIDATE_RECEIPT = "didFinishValidateReceipt" --AppsFlyerTrackerCallbacks:: got didFinishValidateReceipt
DID_FINISH_VALIDATE_RECEIPT_WITH_ERROR = "didFinishValidateReceiptWithError" --AppsFlyerTrackerCallbacks:: got idFinishValidateReceiptWithError error
ON_APP_OPEN_ATTRIBUTION = "onAppOpenAttribution" --AppsFlyerTrackerCallbacks:: got onAppOpenAttribution
ON_APP_OPEN_ATTRIBUTION_FAILURE = "onAppOpenAttributionFailure" --AppsFlyerTrackerCallbacks:: got onAppOpenAttributionFailure error
ON_IN_APP_BILLING_SUCCESS = "onInAppBillingSuccess" --AppsFlyerTrackerCallbacks:: got onInAppBillingSuccess succcess
ON_IN_APP_BILLING_FAILURE = "onInAppBillingFailure" --AppsFlyerTrackerCallbacks:: got onInAppBillingFailure error
ON_INVITE_LINK_GENERATED = "onInviteLinkGenerated" --AppsFlyerTrackerCallbacks:: generated userInviteLink
FIREBASE_TOKEN_RECEIVED = "firebase_token_received"

-------- 充值 ----------
RECHARGE_SYNC_PURCHASED = "recharge_sync_purchased"
GOLD_FINGER_STATE = "gold_finger_state"

ON_LOGIN_CALLBACK = "ON_LOGIN_CALLBACK" --登陆回调事件

RESET_MENUTOP_ORDER = "reset_memutop_order" -- 重新设置order
HIDE_DIAMOND_UI = "hide_diamond_ui" -- 隐藏钻石UI
RESET_SOCIATYUI_ORDER = "reset_sociatyui_order" -- 重新设置联盟二级页面层级
RESET_MENUBOT_ORDER = "reset_memubot_order"
VIPLVREWARD = "vip_lv_reward"
VIPEXPREWARD = "vip_exp_reward"
INIT_GOLD_FINGER_DATA = "init_gold_finger_data" --初始化点金手界面数据
UPDATE_GOLD_FINGER_DATA = "update_gold_finger_data" --更新点金手数据

UPDATE_RECHARGE_NUM = "update_recharge_num"
--更新充值金额
UPDATE_NEW_RECHARGE_NUM = "update_new_recharge_num"
--------- 新手引导按钮提示 -----
ON_GUIDE_BUTTON_TIPS = "on_guide_button_tips" --提示效果开关
UPDATE_GETREWARD_TIME = "updata_getreward_time" --收取挂机奖励时间

GUIDE_START = "GUIDE_START" --开始引导
FORCE_GUIDE_ENTER = "FORCE_GUIDE_ENTER" --进入强制引导
FORCE_GUIDE_EXIT = "FORCE_GUIDE_EXIT" --退出强制引导

FORCE_GUIDE_THROW_ERROR = "FORCE_GUIDE_THROW_ERROR" --引导异常
FORCE_GUIDE_CLICK_MASK = "FORCE_GUIDE_CLICK_MASK" --点击遮罩

-----------------羁绊树屋------------------
ON_MSG_MATE_UPDATE_NTF = "ON_MSG_MATE_UPDATE_NTF" --数据更新通知
ON_MSG_MATE_RECOMMEND_RSP = "ON_MSG_MATE_RECOMMEND_RSP"
--羁绊推荐响应
ON_MSG_MATE_APPLY_RSP = "ON_MSG_MATE_APPLY_RSP"
--羁绊申请响应
ON_MSG_MATE_HANDLE_APPLY_RSP = "ON_MSG_MATE_HANDLE_APPLY_RSP"
--处理羁绊申请响应
ON_MSG_MATE_FIND_RSP = "ON_MSG_MATE_FIND_RSP"
--搜索响应
ON_MSG_MATE_END_RSP = "ON_MSG_MATE_END_RSP"
--解除羁绊响应
ON_MSG_MATE_WATER_RSP = "ON_MSG_MATE_WATER_RSP"
--浇水响应
ON_MSG_MATE_REPAIR_RSP = "ON_MSG_MATE_REPAIR_RSP"
--修理武器响应
ON_MSG_MATE_LOG_RSP = "ON_MSG_MATE_LOG_RSP"
--请求日志响应
ON_MSG_MATE_LOG_REWARD_RSP = "ON_MSG_MATE_LOG_REWARD_RSP"
--请求日志响应
ON_MSG_MATE_LOG_REDDOT_NTF = "ON_MSG_MATE_LOG_REDDOT_NTF"
--日志红点

NEW_MSG_FLAG_CHANGE = "NEW_MSG_FLAG_CHANGE" --是否有新消息状态改变
MATE_CHAT_DATA_CHANGE = "MATE_CHAT_DATA_CHANGE" --聊天数据有变动

-----------------假想时轴、奇迹农场------------------
UPDATE_FARM_MAIN_CITY_SUCCESS = "update_farm_main_city_success" --假想时轴升级成功
UPDATE_FARM_SOIL_UNLOCK = "update_farm_soil_unlock" --地块解锁通知
UPDATE_FARM_BOTANY_SUCCESS = "update_farm_botany_success" --树木种植成功
UPDATE_FARM_TREE_SUCCESS = "update_farm_tree_success" --树木升级成功
UPDATE_FARM_COLLECT_SUCCESS = "update_farm_collect_success" --资源收取成功
UPDATE_FARM_DISMANTLE_SUCCESS = "update_farm_dismantle_success" --树木拆除成功
UPDATE_FARM_PRODUCT_MAX_LIMIT = "update_farm_product_max_limit" --树木产出达到产出上限值
UPDATE_FARM_PRODUCT_SOURCE = "update_farm_product_source" --更新树木产量
UPDATE_TREE_INFO = "update_tree_info" -- 更新地块的树木信息
UPDATE_REPAIR_INFO = "update_repair_info" -- 更新修理信息，是否显示
UPDATE_WARER_INFO = "update_water_info" -- 更新浇水信息
UPDATE_MONSTER_INFO = "update_monster_info" -- 更新怪物信息

UPDATE_TIMEAREA_REDTIP = "update_timearea_redtip" --时空幻域主场景红点

----------------武器研究室-------------------------------
UPDATE_WEAPON_LIST = "update_weapon_list" --更新武器列表
UNLOCK_WEAPON_NTF = "unlock_weapon_ntf" --武器解锁通知
UNLOCK_WEAPON_PART_NTF = "unlock_weapon_part_ntf" --武器部件解锁通知
ACTIVE_WEAPON_RSP = "active_weapon_rps" --武器激活通知
UPGRADE_WEAPON_RSP = "upgrade_weapon_rsp" --武器升级回复
DISMANTLE_WEAPON_RSP = "dismantle_wepaon_rsp" --武器拆解回复
CHOOSE_WEAPON_RSP = "choose_weapon_rsp" --选择武器回复
WEAPON_DATA_UPDATED = "weapon_data_updated" --武器数据更新
WEAPON_UPDATE_NTF = "weapon_update_ntf" --武器升级通知
WEAPON_GET_CHOOSE_RSP = "weapon_get_choose_rsp" --获得武器装备信息恢复

----------------非强制引导---------------------------------
ENTER_HERO_INFO = "enter_hero_info"
CLICK_HERO_INFO_DESCRIPTION = "click_hero_info_description"
UI_MENU_BOT_SHOW = "ui_menu_bot_show" --大厅底部菜单UI显示
UI_MENU_BOT_HIDE = "ui_menu_bot_hide" --大厅底部菜单UI隐藏
UI_MENU_BOT_CHANGE_TAG = "ui_menu_bot_change_tag"
ENTER_WANTED = "enter_wanted" --进入星际通缉
CLICK_WANTED_TREASURE_DESCRIPTION = "click_wanted_treasure_description" --点击宝箱介绍气泡
CLOSE_WANTED_TREASURE_DESCRIPTION = "close_wanted_treasure_description" --关闭宝箱介绍气泡
CLICK_WANTED_TREASURE = "click_wanted_treasure" --点击星际通缉宝箱
CLOSE_WANTED_TREASURE = "close_wanted_treasure" --关闭星级通缉宝箱
CLICK_WANTED_BATTLE = "click_wanted_battle" --点击星级通缉挑战
ENTER_WANTED_BATTLE = "enter_wanted_battle" --打开星际通缉挑战界面
CLICK_WANTED_BATTLE_2 = "click_wanted_battle_2" --点击星际通缉挑战界面挑战按钮
ENTER_SELECT_HERO = "enter_select_hero" --进入出战界面
CLICK_SELECT_HERO = "click_select_hero" --选择英雄
SELECT_HERO_COMPLETE = "select_hero_complete" --选择英雄完毕
CLICK_SELECT_HERO_BATTLE = "click_select_hero_battle" --点击出战界面战斗按钮
ENTER_MARKET = "enter_market" --进入旅者集市
CLICK_MARKET_DESCRIPTION = "click_market_description" --点击旅者集市介绍
ENTER_ARENA = "enter_arena" --进入竞技场
CLICK_ARENA_BATTLE = "click_arena_battle" --点击竞技场挑战
ENTER_CASINO = "enter_casino" --进入地下娱乐城
CLICK_CASINO_PLAY = "click_casino_play" --点击地下娱乐城转动
ENTER_TRIAL = "enter_trial" --进入深空试炼
CLICK_TRIAL_BATTLE = "click_trial_battle" --点击深空试炼挑战
ENTER_TRIAL_MAP = "enter_trial_map"
CLICK_FACE_ITEM = "click_face_item"
SELECT_FACE_ITEM = "select_face_item"
CLICK_MAP_CELL = "click_map_cell"
MOVE_MAP_CELL = "move_map_cell"
CLICK_TRIAL_DESCRIPTION_1 = "click_trial_description_1"
CLOSE_TRIAL_DESCRIPTION_1 = "close_trial_description_1"
CLICK_TRIAL_DESCRIPTION_2 = "click_trial_description_2"
CLOSE_TRIAL_DESCRIPTION_2 = "close_trial_description_2"
CLICK_TRIAL_DESCRIPTION_3 = "click_trial_description_3"
ENTER_CYBER_BAR = "enter_cyber_bar" --进入赛博酒吧
CLICK_CYBER_BAR_ACCEPT = "click_cyber_bar_accept" --点击赛博酒吧接取

SELECT_CYBER_BAR_HERO_BTN = "select_cyber_bar_hero_btn" --一键上阵
CLICK_CYBER_BAR_HERO_BTN = "click_cyber_bar_hero_btn" -- 点击上阵按钮
CLICK_CYBER_BAR_START_BTN = "click_cyber_bar_start_btn" -- 引导任务开始按钮
CLICK_DOWN_CYBER_BAR_START_BTN = "click_down_cyber_bar_start_btn" -- 点击任务开始按钮
CLICK_CYBER_BAR_RECIVE_BTN = "click_cyber_bar_recive_btn" -- 引导任务收取按钮
CLICK_DOWN_CYBER_BAR_RECIVE_BTN = "click_down_cyber_bar_recive_btn" -- 点击任务收取按钮

ENTER_ACHIEVEMENT = "enter_achievement" --进入成就
CLICK_ACHIEVEMENT_DESCRIPTION = "click_achievement_description" --点击成就介绍
ENTER_SYNERGY_ARRAY = "enter_synergy_array" --进入协同阵列
CLICK_SYNERGY_ARRAY_DESCRIPTION = "click_synergy_array_description" --点击协同阵列介绍
ENTER_CORE_CRYSTAL = "enter_core_crystal" --进入核晶淬取
CLICK_CORE_CRYSTAL_DESCRIPTION = "click_core_crystal_description" --点击核晶淬取介绍
ENTER_TIME_MAGIC_FIELD = "enter_time_magic_field" --进入时空幻域
CLICK_TIME_MAGIC_FIELD_DESCRIPTION = "click_time_magic_field_description" --点击时空幻域介绍
CLOSE_TIME_MAGIC_FIELD_DESCRIPTION = "close_time_magic_field_description" --关闭时空幻域介绍
CLICK_TIME_MAGIC_FIELD_DESCRIPTION_1 = "click_time_magic_field_description_1" --点击时空幻域介绍1
CLOSE_TIME_MAGIC_FIELD_DESCRIPTION_1 = "close_time_magic_field_description_1" --关闭时空幻域介绍1
CLICK_TIME_MAGIC_FIELD_HOLE = "click_time_magic_field_hole" --点击时空幻域树洞
ENTER_TIME_MAGIC_FIELD_PLANT = "enter_time_magic_field_plant" --进入时空幻域种植界面
CLICK_TIME_MAGIC_FIELD_PLANT = "click_time_magic_field_plant" --点击时空幻域种植
CLICK_TIME_MAGIC_FIELD_PLANT_COMPLETE = "click_time_magic_field_plant_complete" --时空幻域种植完毕
CLICK_TIME_MAGIC_FIELD_DESCRIPTION_2 = "click_time_magic_field_description_2" --点击时空幻域介绍2
CLOSE_TIME_MAGIC_FIELD_DESCRIPTION_2 = "close_time_magic_field_description_2" --关闭时空幻域介绍2
CLICK_TIME_MAGIC_FIELD_TREE_HOUSE = "click_time_magic_field_tree_house" --点击时空幻域树屋
ENTER_TIME_MAGIC_FIELD_TREE_HOUSE = "enter_time_magic_field_tree_house" --进入时空幻域树屋
CLICK_TIME_MAGIC_FIELD_DESCRIPTION_3 = "click_time_magic_field_description_3" --点击时空幻域介绍3
CLOSE_TIME_MAGIC_FIELD_DESCRIPTION_3 = "close_time_magic_field_description_3" --关闭时空幻域介绍3
CLICK_TIME_MAGIC_FIELD_RETURN = "click_time_magic_field_return" --树屋返回时空幻域主界面
RETURN_TIME_MAGIC_FIELD = "return_time_magic_field" --返回时空幻域主界面
CLICK_TIME_MAGIC_FIELD_COLLECT = "click_time_magic_field_collect" --点击时空幻域收集
CLICK_TIME_MAGIC_FIELD_COLLECT_COMPLETE = "click_time_magic_field_collect_complete" --点击时空幻域收集完毕
CLICK_TIME_MAGIC_FIELD_DESCRIPTION_4 = "click_time_magic_field_description_4" --点击时空幻域介绍4
CLOSE_TIME_MAGIC_FIELD_DESCRIPTION_4 = "close_time_magic_field_description_4" --关闭时空幻域介绍4
CLICK_TIME_MAGIC_FIELD_DESCRIPTION_5 = "click_time_magic_field_description_5" --点击时空幻域介绍5
CLOSE_TIME_MAGIC_FIELD_DESCRIPTION_5 = "CLOSE_TIME_MAGIC_FIELD_DESCRIPTION_5" --关闭时空幻域介绍4
CLICK_TIME_MAGIC_FIELD_DESCRIPTION_6 = "click_time_magic_field_description_6" --点击时空幻域介绍5
ENTER_UNION = "enter_union" --进入联盟
CLICK_UNION_DESCRIPTION = "click_union_description" --点击联盟介绍
CLOSE_UNION_DESCRIPTION = "close_union_description" --关闭联盟介绍
CLICK_UNION_HALL = "click_union_hall" --点击联盟大厅
JOIN_UNION = "join_union"
CLICK_UNION_JOINBTN = "click_union_joinBtn"
ENTER_UNION_HALL = "enter_union_hall" --进入联盟大厅界面
CLICK_UNION_DESCRIPTION_1 = "click_union_description_1" --点击联盟介绍1
RETURN_UNION = "return_union" --返回联盟
CLICK_UNION_EXPLORE = "click_union_explore" --点击联盟探索
ENTER_UNION_EXPLORE = "enter_union_explore" --进入联盟探索界面
CLICK_UNION_DESCRIPTION_2 = "click_union_description_2" --点击联盟介绍2
ENTER_CONVERSION_CABIN = "enter_conversion_cabin" --进入位面转换舱
CLICK_CONVERSION_CABIN_DESCRIPTION = "click_conversion_cabin_description" --点击位面转换舱介绍
ENTER_BABEL = "enter_babel" --进入位面之塔
CLICK_BABEL_PLAY = "click_babel_play" --点击位面之塔抽奖
ENTER_UNION_WARZONE = "enter_union_warzone"
CLOSE_UNION_WARZONE_DESCRIPTION_1 = "close_union_warzone_description_1"
CLOSE_UNION_WARZONE_DESCRIPTION_2 = "close_union_warzone_description_2"
CLOSE_UNION_WARZONE_DESCRIPTION_3 = "close_union_warzone_description_3"
ENTER_UNION_WARZONE_TARGET = "enter_union_warzone_target"
CLOSE_UNION_WARZONE_TARGET = "close_union_warzone_target"
ENTER_UNION_WARZONE_DETAIL = "enter_union_warzone_detail"
CLOSE_UNION_WARZONE_DETAIL = "close_union_warzone_detail"
CLICK_UNION_WARZONE_DESCRIPTION_1 = "click_union_warzone_description_1"
CLICK_UNION_WARZONE_DESCRIPTION_2 = "click_union_warzone_description_2"
CLICK_UNION_WARZONE_DESCRIPTION_3 = "click_union_warzone_description_3"
CLICK_UNION_WARZONE_TARGET = "click_union_warzone_target"
CLICK_UNION_WARZONE_DESCRIPTION_4 = "click_union_warzone_description_4"
CLICK_UNION_WARZONE_DESCRIPTION_5 = "click_union_warzone_description_5"
CLICK_UNION_WARZONE_DETAIL_BATTLE = "click_union_warzone_detail_battle"
CLICK_UNION_WARZONE_DESCRIPTION_6 = "click_union_warzone_description_6"
ENTER_UNION_WARZONE_DEFEND_DETAIL = "enter_union_warzone_defend_detail"
CLICK_UNION_WARZONE_DEFEND_DETAIL_BATTLE = "click_union_warzone_defend_detail_battle"
ENTER_MAZE = "enter_maze"
CLICK_MAZE_DESCRIPTION_1 = "click_maze_description_1"
CLOSE_MAZE_DESCRIPTION_1 = "close_maze_description_1"
CLICK_MAZE_DESCRIPTION_2 = "click_maze_description_2"
CLOSE_MAZE_DESCRIPTION_2 = "close_maze_description_2"
CLICK_MAZE_DESCRIPTION_3 = "click_maze_description_3"
ENTER_SOUL_CONNECT_BIN = "enter_soul_connect_bin"
CLICK_SOUL_CONNECT_BIN_ADD = "click_soul_connect_bin_add"
ENTER_SOUL_CONNECT_BIN_ADD = "enter_soul_connect_bin_add"
CLICK_SOUL_CONNECT_BIN_SELECT = "click_soul_connect_bin_select"
CLICK_SOUL_CONNECT_BIN_SELECT_COMPLETE = "click_soul_connect_bin_select_complete"
CLICK_SOUL_CONNECT_BIN_CONFIRM = "click_soul_connect_bin_confirm"
CLICK_SOUL_CONNECT_BIN_CONFIRM_COMPLETE = "click_soul_connect_bin_confirm_complete"
CLOSE_SOUL_CONNECT_BIN_DESCRIPTION = "close_soul_connect_bin_description"
CLICK_SOUL_CONNECT_BIN_UNLOCK = "click_soul_connect_bin_unlock"
ENTER_SOUL_CONNECT_BIN_UNLOCK = "enter_soul_connect_bin_unlock"
CLOSE_MAZE_DESCRIPTION_3 = "close_maze_description_3"
CLICK_MAZE_CARRIAGE = "click_maze_carriage"
ENTER_MAZE_CARRIAGE = "enter_maze_carriage"
CLICK_MAZE_CARRIAGE_HERO = "click_maze_carriage_hero"
CLICK_MAZE_CARRIAGE_CONFIRM = "click_maze_carriage_confirm"
CLICK_MAZE_CARRIAGE_CONFIRM_COMPLETE = "click_maze_carriage_confirm_complete"
ENTER_RECYCLE = "enter_recycle"
CLICK_RECYCLE_DESCRIPTION_1 = "click_recycle_description_1"
CLOSE_RECYCLE_DESCRIPTION_1 = "close_recycle_description_1"
CLICK_RECYCLE_DESCRIPTION_2 = "click_recycle_description_2"
ENTER_HERO = "enter_hero"
CLICK_HERO = "click_hero"
ENTER_HERO_UPGRADE = "enter_hero_upgrade"
CLICK_HERO_UPGRADE = "click_hero_upgrade"
CLICK_HERO_UPGRADE_COMPLETE = "click_hero_upgrade_complete"
CLICK_HERO_ADVANCE = "click_hero_advance"
ENTER_HERO_ADVANCE = "enter_hero_advance"
CLICK_HERO_ADVANCE_COMPLETE = "click_hero_advance_complete"
ENTER_CREATE_ARRAY = "enter_create_array"
CLICK_HERO_CREATE = "click_hero_create"
WORLDMAP_BUTTON_CLICK_GUIDE = "WORLDMAP_BUTTON_CLICK_GUIDE" --解锁新世界地图 世界地图按钮的点击引导
ENTER_EQUIP_INSTANCE = "enter_equip_instance"
ENTER_EQUIP_INSTANCE_BATTLE = "enter_equip_instance_battle"
CLICK_EQUIP_INSTANCE_BATTLE_BTN = "click_equip_instance_battle_btn"
CLICK_EQUIP_INSTANCE_BATTLE = "click_equip_instance_battle"
ENTER_HERO_EQUIP = "enter_hero_equip"
CLICK_HERO_WEAR = "click_hero_wear"
CLICK_HERO_WEAR_COMPLETE = "click_hero_wear_complete"
CLICK_HERO_EQUIP = "click_hero_equip"
ENTER_EQUIP_DETAIL = "enter_equip_detail"
CLICK_EQUIP_UPGRADE = "click_equip_upgrade"
ENTER_EQUIP_ENHANCE = "enter_equip_enhance"
CLICK_EQUIP_ENHANCE = "click_equip_enhance"
ENTER_HERO_BASE = "enter_hero_base"
CLICK_HERO_BASE_ENHANCE = "click_hero_base_enhance"
ENTER_SINGLE_RANK = "enter_single_rank"
CLICK_SINGLE_RANK_BATTLE = "click_single_rank_battle"
ENTER_SINGLE_CHALLENGE = "enter_single_challenge"
CLICK_SINGLE_CHALLENGE_BATTLE = "click_single_challenge_battle"

ENTER_TRIAL_WORLD_MAP = "enter_trial_world_map"
CLICK_TRIAL_WORLD_MAP = "click_trial_world_map"
ENTER_TRIAL_MAP_DESCRIPTION = "enter_trial_map_description"
CLICK_TRIAL_MAP_ENTER = "click_trial_map_enter"

ENTER_WISH_POOL = "enter_wish_pool"
CLICK_WISH_POOL_SET = "click_wish_pool_set"
WISH_POOL_SET_COMPLETE = "wish_pool_set_complete"
CLICK_WISH = "click_wish"

ENTER_HOMELAND_BASE = "enter_homeland_base"
CLICK_WEAPON = "click_weapon"
ENTER_WEAPON = "enter_weapon"
CLICK_WEAPON_ACTIVE = "click_weapon_active"
WEAPON_ACTIVE_COMPLETE = "weapon_active_complete"
CLICK_WEAPON_DETAIL = "click_weapon_detail"
ENTER_WEAPON_DETAIL = "enter_weapon_detail"
CLICK_WEAPON_UPGRADE = "click_weapon_upgrade"

ENTER_INSTANLL_WEAPON = "enter_install_weapon"
CLICK_WEAPON_BTN = "click_weapon_btn"
INSTALL_WEAPON = "install_weapon"
CLICK_INSTALL_WEAPON = "click_install_weapon"

WEAPON_UPGRADE_FAIL = "weapon_upgrade_fail"
CLICK_RETURN_WEAPON = "click_return_weapon"
RETURN_WEAPON_COMPLETE = "return_weapon_complete"
CLICK_LOTTERY = "click_lottery"

ENTER_LOTTERY = "enter_lottery"
CLICK_LOTTERY_DESCRIPTION = "click_lottery_description"
CLOSE_LOTTERY_DESCRIPTION = "close_lottery_description"
CLICK_LOTTERY_1 = "click_lottery_1"
COMPLETE_LOTTERY_1 = "complete_lottery_1"
CLICK_LOTTERY_2 = "click_lottery_2"
COMPLETE_LOTTERY_2 = "complete_lottery_2"
CLICK_LOTTERY_3 = "click_lottery_3"
COMPLETE_LOTTERY_3 = "complete_lottery_3"

ENTER_ATTACK = "enter_attack"
CLICK_ATTACK = "click_attack"
ATTACK_COMPLETE = "attack_complete"
CLICK_COLLECT = "click_collect"

ENTER_DIG = "enter_dig"
CLICK_DIG_DESCRIPTION = "click_dig_description"
CLOSE_DIG_DESCRIPTION = "close_dig_description"
CLICK_DIG_1 = "click_dig_1"
COMPLETE_DIG_1 = "complete_dig_1"
CLICK_DIG_2 = "click_dig_2"
COMPLETE_DIG_2 = "complete_dig_2"

ENTER_HOMELAND = "enter_homeland"
CLICK_CITY = "click_city"
ENTER_CITY_DETAIL = "enter_city_detail"
CLICK_CITY_UPGRADE = "click_city_upgrade"
COMPLETE_CITY_UPGRADE = "complete_city_upgrade"
CLICK_CITY_COLLECT = "click_city_collect"
COMPLETE_MAIN_CITY_COLLECT = "complete_main_city_collect"
CLICK_BUILD = "click_build"
ENTER_BUILD_DETAIL = "enter_build_detail"
CLICK_BUILD_UPGRADE = "click_build_upgrade"

COMPLETE_MAZE = "complete_maze"
CLICK_MAZE_REWARD = "click_maze_reward"
ENTER_MAZE_REWARD = "enter_maze_reward"
RECEIVE_MAZE_REWARD = "receive_maze_reward"
CLOSE_MAZE_REWARD = "close_maze_reward"
CLICK_MAZE_SHOP = "click_maze_shop"
ENTER_MAZE_COMPLETE = "enter_maze_complete"

START_MAZE_RELIVE = "start_maze_relive"
CLICK_MAZE_RELIVE = "click_maze_relive"
ENTER_MAZE_RELIVE = "enter_maze_relive"
CLICK_RELIVE = "click_relive"

ENTER_TASK = "enter_task"
CLICK_MONTH_TASK = "click_month_task"
ENTER_MONTH_TASK = "enter_month_task"
ENTER_MONTH_TASK_SELECT = "enter_month_task_select"

ENTER_INSTANCE_BASE = "enter_instance_base"
CLICK_SIGIL_SPACE_TOGGLE = "click_sigil_space_toggle"
ENTER_SIGIL_SPACE = "enter_sigil_space"
CLICK_SIGIL_SPACE_INSTANCE = "click_sigil_space_instance"
ENTER_SIGIL_SPACE_INSTANCE = "enter_sigil_space_instance"
CLICK_SIGIL_SPACE_DESRIPTION = "click_sigil_space_desription"

CLICK_SELECT_HERO_DESCRIPTION = "click_select_hero_description"
ENTER_LOST_LAND_BASE = "enter_lost_land_base"
CLICK_LOST_LAND_NEWBEE = "click_lost_land_newbee"
ENTER_LOST_LAND_DETAIL = "enter_lost_land_detail"
CLICK_LOST_LAND_DETAIL = "click_lost_land_detail"
ENTER_LOST_LAND = "enter_lost_land"
CLICK_LOST_LAND_BATTLE = "click_lost_land_battle"
CLICK_SELECT_HERO_BATTLE = "click_select_hero_battle"

ENTER_VOID_CHAMPIONSHIP = "enter_void_championship"
CLICK_VOID_CHAMPIONSHIP_DESCRIPTION = "click_void_championship_description"

CLICK_EXCLUSIVE_TOGGLE = "click_exclusive_toggle"
ENTER_EXCLUSIVE_INSTANCE = "enter_exclusive_instance"
CLICK_EXCLUSIVE_INSTANCE_BATTLE = "click_exclusive_instance_battle"

ENTER_WANTED_ENTRANCE = "enter_wanted_entrance"
CLICK_FACTION_WANTED = "click_faction_wanted"
ENTER_FACTION_WANTED = "enter_faction_wanted"
CLICK_FACITON_WANTED_BATTLE = "click_faciton_wanted_battle"

CLICK_LEGEND = "click_legend"
ENTER_LEGEND = "enter_legend"
CLICK_LEGEND_GROUP = "click_legend_group"
ENTER_LEGEND_APPLY = "enter_legend_apply"
CLICK_LEGEND_APPLY = "click_legend_apply"

CHECK_LEGEND_ACHIEVEMENT = "check_legend_achievement"
CLICK_LEGEND_ACHIEVEMENT = "click_legend_achievement"
ENTER_LEGEND_ACHIEVEMENT = "enter_legend_achievement"
RECEIVE_LEGEND_ACHIEVEMENT = "receive_legend_achievement"
LEGEND_ACHIEVEMENT_CLOSE = "legend_achievement_close"
CLICK_LEGEND_SHOP = "click_legend_shop"

---------------------------异界迷宫-------------------------------
UPDATE_MAZE_PART_DATA = "update_maze_part_data"
UPDATE_MAZE_PATH = "update_maze_path"
RESET_MAZE_MAP = "reset_maze_map"
UPDATE_MAZE_MAP = "update_maze_map"
LOAD_MAZE_SCENE = "load_maze_scene"
MAZE_NEXT_LEVEL = "maze_next_level"
MAZE_RECEIVE_PASS_REWARD = "maze_receive_pass_reward"
MAZE_SPRING_EVENT = "maze_spring_event"
MAZE_RELIVE_EVENT = "maze_relive_event"
MAZE_TEAR_EVENT = "maze_tear_event"
MAZE_SELECT_RELIC = "maze_select_relic"
MAZE_CARRIAGE_EVENT = "maze_carriage_event"
MAZE_SELECT_RELIC = "maze_select_relic"
MAZE_SHOW_SELECT_RELIC = "maze_show_select_relic"
MAZE_SHOW_SHOP_BOSS = "maze_show_shop_boss"
MAZE_UPDATE_HPMP = "maze_update_hpmp"
MAZE_DELETE_EVENT = "maze_delete_event"
MAZE_RELIC_FLY = "maze_relic_fly"
MAZE_HERO_FLY = "maze_hero_fly"
MAZE_MAP_SHOW = "maze_map_show"
MAZE_HERO_FLY_START = "maze_hero_fly_start"
MAZE_RELIC_FLY_START = "maze_relic_fly_start"
MAZE_RELIC_FLY_INIT = "maze_relic_fly_init"
MAZE_GUARD_HERO_FLY = "maze_guard_hero_fly"
--一键扫荡
MAZE_A_KEY_SWEEP_DATA = "maze_a_key_sweep_data"--一键扫荡更新数据
MAZE_A_KEY_SWEEP_REFRESH_UI = "maze_a_key_sweep_refresh_ui"--一键扫荡更新ui

------------------------------------------------------------------

-- 游戏埋点
GAME_EVENT_REPORT = "game_event_report" -- 自定义游戏事件

UPDATE_GATEWAY_NETSPEED_TEST = "update_gateway_netspeed_test"

-- 英雄评论
ENTER_HERO_COMMENT = "enter_hero_comment" -- 进入英雄评论

-- 广播
APPEND_BROADCAST_WITH_ID = "append_broadcast_with_id" -- 添加广播（配置）
APPEND_BROADCAST = "append_broadcast" --添加广播
RECEIVE_BROADCAST = "receive_broadcast" -- 接收服务器广播

UPDATE_TARGET_TASK_REDDOT = "update_target_task_reddot" --目标任务红点
UPDATE_TARGET_TASK_DATA = "update_target_task_data" --目标任务数据
UPDATE_TARGET_SINGLE_TASK_DATA = "update_target_single_task_data" --目标单一任务数据
UPDATE_TARGET_TASK_PAGE_INDEX = "update_target_task_page_index"
--更新目标任务页面
UPDATE_TARGET_TASK_CHAPTER_DATA = "update_target_task_chapter_data"
--更新章节数据
UPDATE_TARGET_TASK_REDDOT_DATA = "update_target_task_reddot_data"
--更新红点数据
UPDATE_TARGET_TASK_BUBBLE_DATA = "update_target_task_bubble_data"
--更新气泡数据

CLOSE_ALL_SCENE = "close_all_scene" -- 关闭所有的场景
CLOSE_ALL_CACHE_UI = "close_all_cache_ui"
--关闭所有缓存界面
CLOSE_LITTLE_GAME_SCENE_UI = "CLOSE_LITTLE_GAME_SCENE_UI" --关闭小游戏界面

UPDATE_ONLINE_REFRESH_TIME = "update_online_refresh_time" --更新在线奖励刷新时间
UPDATE_ONLINE_REWARD_DATA = "update_online_reward_data" --刷新在线奖励数据
UPDATE_ONLINE_REWARD_DATA2 = "update_online_reward_data2" --刷新在线登录十连抽奖励数据
UPDATE_GET_REWARD = "update_get_reward" --得到在线奖励回复
UPDATE_FIRST_RECHARGE_DATA = "update_first_rechatge_data" --刷新首充礼包数据
ENTER_UI_ONLINE_REWARD2 = "enter_ui_online_reward2"
CLICK_UI_ONLINE_REWARD2 = "click_ui_online_reward2"
-- 快速作战相关
UPDATE_QUICK_FIGHT_DATA = "update_quick_fight_data" -- 快速作战是否开启

ENTER_UI_QUICK_FIGHT = "enter_ui_quick_fight"
CLICK_UI_QUICK_FIGHT = "click_ui_quick_fight"

UNFORCED_GUIDE_POINTER_SHOW = "unforced_guide_pointer_show"
UPDATE_SCENELOCK_ITEM = "update_scenelock_item" -- 大厅场景是否解锁更新

UI_SELECT_HERO_SHOW = "ui_select_hero_show" -- 出战界面显示
UI_SELECT_HERO_CLOSE = "ui_select_hero_close" -- 出战界面关闭
BATTLE_TEAM_SHOW_ARTIFACT = "battle_team_show_artifact" --神器按钮功能

UI_LOBBY_SHOW = "ui_lobby_show" --大厅界面显示
UI_LOBBY_HIDE = "ui_lobby_hide"
UI_LOBBY_SUB_UI_SHOW = "ui_lobby_sub_show" --大厅子界面显示（拼成大厅界面的子界面）
BATTLE_START = "battle_start"
BATTLE_CAUSE_DAMAGE = "battle_cause_damage"
BATTLE_NOTIFY_DEAD = "battle_notify_dead"
UPDATE_SUMMONED_PROP = "update_summoned_prop" --更新召唤物属性
SHOW_HERO_SUMMON_EFFECT = "SHOW_HERO_SUMMON_EFFECT" --显示特效
BATTLE_NOTIFY_SUMMONED_BORN = "battle_notify_summoned_born" --通知召唤物出生
BATTLE_NOTIFY_SUMMONED_REBORN = "battle_notify_summoned_reborn" --通知召唤物复活
BATTLE_NOTIFY_SUMMONED_ATTACK = "battle_notify_summoned_attack" --通知召唤物攻击
UPDATE_SHOP_ITEM_LIST = "UPDATE_SHOP_ITEM_LIST" -- 更新商店列表
PING_RECOED_BEGAIN = "ping_record_begain"
--开始记录ping值
PING_RECOED_END = "ping_record_begain"
--开始记录ping值

HERO_RANK_LIST_UPDATE = "hero_rank_list_update"
--英雄排行榜更新
TEAM_RANK_LIST_UPDATE = "team_rank_list_update"
--队伍排行榜更新

UPDATE_SPECIAL_GIFT_DATA = "update_special_gift_data" --特惠礼包数据更新
UPDATE_MONTHCARD_INFO = "update_monthcard_info"
--月卡刷新消息

RESET_ARROW_ORDER = "reset_arrow_order"
--重置英雄界面左右箭头层级
UPDATE_lINEUP = "update_lineup"
UPDATE_lINEUP_INFO = "update_lineup_info"
SET_LINEUP_INFO_FAIL = "set_lineup_info_FAIL"

BATTLE_BUFF_ACTIVE = "battle_buff_active"
BATTLE_BUFF_REMOVE = "battle_buff_remove"

--=====================排行榜====================
RANK_DETAIL_NTF = "rank_detail_ntf" --排行榜详情回复
RANK_STAGE_NTF = "rank_stage_ntf" --第一名回复
RANK_GAIN_RSP = "rank_gain_rsp" --领取奖励回复
RANK_TOP_FIVE_NTF = "rank_top_five_ntf" --前五回复
RANK_FRIST_PLACE_DATA = "rank_firt_place_data" --第一名刷新
RANK_DETAIL_DATA = "rank_detail_data"
FACE_FRAME_CHANGE = "face_frame_change" --头像框发生改变UPDATE_RANK_ACHI_DATA = "update_rank_achi_data" -- 排行榜数成就据更新
LOAD_WORLDMAP_SCENE = "load_worldmap_scene"
CHANGE_IDLE_MAP = "change_idle_map"
RANK_ACHIVE_DATA_UPDATE = "rank_achive_data_update" --排行榜成就数据更新

RESET_WAIT_SUB_UI = "reset_wait_sub_ui" --更新等待子界面标志

CHECK_DOWNLOAD_STATE = "check_download_state" -- 检测后台下载网络事件

-----------------------通行证----------------------
PASSPORT_RECEIVE_REWARD = "passport_receive_reward"
UPDATE_PASSPORT_PART_DATA = "update_passport_part_data"
UPDATE_PASSPORT_UI = "update_passport_ui"
--------------------------------------------------
--游戏数据加载
GAME_DATA_LUA_INTERFACE = "game_data_lua_interface" --导出lua数据接口完成
GAME_LOGIN_PRELOAD_RES = "game_login_preload_res" --登录预加载资源
GAME_LOGIN_PRELOAD_RES_Progress = "game_login_preload_res_progress" --登录预加载资源进度
GAME_LOGIN_PRELOAD_OPEN_LOBBY = "game_login_preload_open_lobby" --登录预加载打开大厅和场景，进游戏直接显示，避免黑屏问题
FIRST_LOGIN_CREATE_DATA_FINISH = "first_login_create_data_finish" --首次登录创建数据完成
FIRST_LOGIN_CREATE_ROGUE_ENTITY_FINISH = "first_login_create_rogue_entity_finish" --首次登录创建道具实体完成
--首次登录时优化Require的步骤（重登也会触发） 0/1/2/3/4/5  预留0/进入了Lobby1/开始创建Player2/城建数据下发了3/城建资源加载好（默认只判断加载场景）4/首次登录创建数据完成5
FIRST_LOGIN_OPTIMIZE_REQUIRE_STEP = "FIRST_LOGIN_OPTIMIZE_REQUIRE_STEP"
RECORD_LOGIN_COST_TIME_POINT = "RECORD_LOGIN_COST_TIME_POINT" --记录登录耗时点
--用户协议
USER_TERMS_ACCEPT = "user_terms_accept"

UPDATE_HERO_CALL_BTN_ENALE_STATE = "update_hero_call_btn_state" --更新召唤法阵界面英雄按钮状态
HERO_CALL_END = "hero_call_end" --召唤结束
UPDATE_BATTLE_PLAYBACK_RSP = "update_battle_playback_rsp"
--更新战斗录像回放回复
TARGET_TASK_GET_SUCCESS = "target_task_get_success"
TARGET_TASK_GET_FINAL_SUCCESS = "target_task_get_final_success"
--成功获取最终神器

-----------------------好友系统----------------------
UPDATE_FRIEND_LIST = "update_friend_list"
UPDATE_UI_FRIEND = "update_ui_friend"
UPDATE_FRIEND_TIPS = "update_friend_tips"
UPDATE_FRIEND_RECOMMEND_DATA = "update_friend_recommend_data"
REBUILD_FRIEND_RECOMMEND_LIST = "rebuild_friend_recommend_list"
UPDATE_FRIEND_SEARCH_DATA = "update_friend_search_data"
UPDATE_FRIEND_SEARCH_UI = "update_friend_search_ui"
CLEAR_FRIEND_SEARCH_DATA = "clear_friend_search_data"
UPDATE_FRIEND_OWN_APPLY = "update_friend_own_apply"
UPDATE_FRIEND_RECEIVE_TIPS = "update_friend_receive_tips"
UPDATE_FRIEND_OTHER_APPLY = "update_friend_other_apply"
UPDATE_FRIEND_BLOCK = "update_friend_block"
UPDATE_FRIEND_APPLY_DATA = "update_friend_apply_data"
CLEAR_FRIEND_SEARCH_LIST = "clear_friend_search_list"
UPDATE_FRIEND_ALL_APPLY_RESULT = "update_friend_all_apply_result"
UPDATE_FRIEND_LIST_DATA = "update_friend_list_data"
-----------------------苦力系统----------------------
UPDATE_UI_COOLIE = "update_ui_coolie"
UPDATE_UI_COOLIE_POWER = "update_ui_coolie_power"
UPDATE_UI_LINEUP = "update_ui_lineup"
UPDATE_COOLIE_TIPS = "update_coolie_tips"
UPDATE_COOLIE_LIST = "update_coolie_list"
UPDATE_FRIEND_COOLIE_LIST = "update_friend_coolie_list"
UPDATE_BATTLE_RECORD_LIST = "update_battle_record_list"
UPDATE_COOLIE_POWER = "update_coolie_power"
UPDATE_COOLIE_RECEIVE_TIPS = "update_coolie_receive_tips"
RECEIVE_COOLIE_AWARD = "receive_coolie_award"
RECEIVE_COOLIE_BUY_POWER = "receive_coolie_buy_power"
RECEIVE_COOLIE_IDLE_REWARD = "receive_idle_reward"   --苦力挂机奖励
RECEIVE_COOLIE_RELEASE = "receive_coolie_release"   --苦力释放
UPDATE_COOLIE_LINEUP = "update_coolie_lineup"
UPDATE_ARREST_COOLIE_LIST = "update_arrest_coolie_list"
UPDATE_SLAVE_MAIL_LIST = "update_slave_mail_list"
CLICK_SCREEN_COOLIE = "click_screen_coolie"
SHOW_FRIEND_GUIDE = "show_friend_guide"
HIDE_FRIEND_GUIDE = "hide_friend_guide"
UPDATE_SLAVE_DETAIL = "update_slave_detail"
RECEIVE_COOLIE_ONE_REWARD = "receive_coolie_one_reward"
SHOW_COOLIE_ONE_REWARD = "show_coolie_one_reward"
UPDATE_RECOMMEND_COOLIE_LIST = "update_recommend_coolie_list"
UPDATE_RECOMMEND_COOLIE_VIEW = "update_recommend_coolie_view"

-----------------------灵魂链接----------------------
UPDATE_SOUL_LINK_DATA = "update_soul_link_data"
UNLOCK_SOUL_LINK_SLOT = "unlock_soul_link_slot"
INCREASE_SOUL_LINK_SLOT_LIMIT = "increase_soul_link_slot_limit"
PUT_IN_SOUL_LINK_SLOT = "put_in_soul_link_slot"
PUT_OUT_SOUL_LINK_SLOT = "put_out_soul_link_slot"
SOUL_LINK_SLOT_RESET_CD = "soul_link_slot_reset_cd"
SOUL_LINK_SLOT_UPDATE = "soul_link_slot_update"
SOUL_LINK_SLOT_UPDATE_UI_NOTIFY = "soul_link_slot_update_ui_notify"
UNLOCK_SOUL_LINK = "unlock_soul_link"
ADD_SOUL_LINK = "add_soul_link"
UPGRADE_SOUL_LINK_LV = "upgrade_soul_link_lv"

---------------------节日活动----------------------
FESTIVAL_GAME_EVENT_REPORT = "festival_game_event_report"
UPDATE_FESTIVAL_DATA = "update_festival_data"
UPDATE_FESTIVAL_SIGN_DATA = "update_festival_sign_data"

FESTIVAL_USE_ITEM = "festival_use_item"

FESTIVAL_SIGN_RESPONSE = "festival_sign_response"
FESTIVAL_EXCHANGE_RESPONSE = "festival_exchange_response"
FESTIVAL_EXCHANGE_RESPONSE_FAILED = "festival_exchange_response_failed"

UPDATE_FESTIVAL_EXTRA_TASK_DATA = "update_festival_extra_task_data"
RESPOND_ACTIVITY_EXTRA_TASK = "respond_activity_extra_task"

FESTIVAL_DECORATE_TREE_RESPONSE = "festival_decorate_tree_response"
FESTIVAL_CHRISTMAS_CARD_RESPONSE = "festival_christmas_card_response"
FESTIVAL_CENTURION_CARD_RESPONSE = "festival_centurion_card_response"
FESTIVAL_CHANGE_TAG = "festival_change_tag"
FESTIVAL_PROSSES_RESPONSE = "festival_prosses_response"
FESTIVAL_PROSSES_RESPONSE_NEW = "festival_prosses_response_new"
RESPONSE_FESTIVAL_WEEKLY_CARD = "response_festival_weekly_card"
FESTIVAL_ACTIVITY_BASE_CLOSE = "festival_activity_base_close"

UPDATE_GIFT_REDDOT = "update_gift_reddot"

UPDATE_CHRISTMAS_CARD_INFO = "update_christmas_card_info"
UPDATE_CHRISTMAS_FESTIVAL_RED = "update_christmas_festival_red"
ACTIVITY_CUSTOM_UPDATE_GIFT = "activity_custom_update_gift" --定制礼包定制刷新
ACTIVITY_CUSTOM_UPDATA_PURSE = "activity_custom_update_purse" --订制礼包购买刷新
ACTIVITY_EVERYDAY_GET = "activity_everyday_get" --每日充值领取日礼包
ACTIVITY_EVERYDAY_GET_BIGREWARD = "activity_everyday_get_bigreward" --每日充值领取大奖(累计礼包)
ACTIVITY_BACKGROUND_DATA_UPDATE = "ACTIVITY_BACKGROUND_DATA_UPDATE"  --后台数据更新
--------------------------------------------------
UPDATE_LIFE_LONG_DATA_EVENT = "UPDATE_LIFE_LONG_DATA_EVENT" --刷新终身卡
-------------------------虚空主宰-----------------------------
SPACE_DOMINATOR_GET_RANK_INFO = "space_dominator_get_rank_info" --获取分组消息
SPACE_DOMINATOR_RANK_NTF = "space_dominator_rank_ntf" --排名通知
SPACE_DOMINATOR_BATTLE_VIDEO = "space_dominator_battle_video" --排名信息通知
--------------------------------------------------------------

-------------------------弹窗事件--------------------------
SHOW_POPUP_MODULE = "show_popup_module"
--打开弹窗
CHECK_DATA_FINISH = "check_data_finish"
--检查所有配置加载情况
ADD_AFTER_POPUP_MODULE = "add_after_popup_module"
--加载弹窗后弹出的窗口
CHECK_CHONGMU_POPUP = "check_chongmu_popup"--觉醒后检查虫母弹窗
-------------------------------------------------------------
-------------------------------------------------------------
SPACE_DOMINATOR_CHALLENGE_COUNT_NTF = "space_dominator_challenge_count_ntf" --剩余次数通知
SPACE_DOMINATOR_SELF_RANK_NTF = "space_dominator_self_rank_ntf" --个人排名通知
SPACE_DOMINATOR_USER_SETTLE_NTF = "space_dominator_user_settle_ntf" --结算通知
SPACE_DOMINATOR_GET_REWARD_RSP = "space_dominator_get_reward_rsp"
--奖励回复
--------------------------------------------------------------

-------------------------------------------------------------
UPDATE_ACHI_HERO_CUP = "update_achi_hero_cup" --英雄杯打点
--------------------------------------------------------------

UPDATE_STAR_CHESTID = "update_star_chestid"
--刷新星星宝箱
UPDATE_HOOK_CHESTID = "update_hook_chestid"
--刷新关卡宝箱
UPDATE_HOOK_STAR = "update_hook_star"
--刷新关卡星星
SET_STAR_FLY_EFFECT = "set_star_fly_effect"
--星星飞行动画
CLOSE_HERO_REWARD_SELECT = "close_hero_reward_select"
--检测关卡奖励英雄选择界面已关闭
CLOSE_ROLE_LEVEL_UP = "close_role_level_up"
--检测角色升级弹框已关闭
CLOUD_EFFECT_CLOSED = "cloud_effect_closed"
--章节切换特效完成
UPDATE_STAR_CHEST_POPUP = "update_star_chest_popup"
--更新星星宝箱弹窗界面
GOT_HOOK_REWARD = "got_hook_reward" --收取挂机奖励

---------------------联盟互助---------------------------------
PLEAGUEHELP_INFO_CHANGE = "pleaguehelp_info_change"
PLEAGUEGELP_REWARDINFO_CHANGE = "pleaguehelp_rewardinfo_change"
PLEAGUEGELP_HELP_ROLE_ID = "pleaguehelp_help_role_id"
PLEAGUEGELP_HELP_REWARD_DATA_CHANGE = "PLEAGUEGELP_HELP_REWARD_DATA_CHANGE" --互助奖励发生变化

--------------------4.2更新引导--------------------------------
UPDATE_GUIDE = "update_guide"

---------------------装备修改----------------------------
UPDATE_EQUIP_LOCK = "update_equip_lock"
EQUIP_ENHANCE_START = "equip_enhance_start"
EQUIPMENT_QUICK_ENHANCE_END = "equipment_quick_enhance_end"
EQUIPMENT_QUICK_ENHANCE_ERROR = "equipment_quick_enhance_error"
UPDATE_EQUIP_RECAST = "update_equip_recast"
UPDATE_EQUIP_RECAST_SAVE = "update_equip_recast_save"
EQUIP_RECAST_POPUP = "equip_recast_popup"
SHARE_EQUIP_RECAST = "share_equip_recast"

---------------------联盟战域二期-----------------------------
SHOW_SOCIATY_FIGHT_ENTER_ERROR_TIPS = "SHOW_SOCIATY_FIGHT_ENTER_ERROR_TIPS" --显示无法进入战域提示
ENTER_SOCIATY_FIGHT = "ENTER_SOCIATY_FIGHT" --进入战域
SYNC_SOCIATY_FIGHT_MAP_INFO = "SYNC_SOCIATY_FIGHT_MAP_INFO"
--同步战域地块信息

---------------------家园-----------------------------
HOMELAND_SLOT_MACHINE_DATA_UPDATE = "homeland_slot_machine_data_update"
HOMELAND_UPDATE_ADDENERGYRTIME = "homeland_update_addenergyrtime"
HOMELAND_UPDATE_EVENT_PLAYER_DETAIL = "homeland_update_event_player_detail"
HOMELAND_UNLOCK_BUILD = "homeland_unlock_build"
HOMELAND_UPDATE_MY_BUILD_BY_ID = "homeland_update_my_build_by_id"
HOMELAND_BUILD_LEVEL_UP = "homeland_build_level_up"
HOMELAND_UPDATE_DIG = "homeland_update_dig"
HOMELAND_UPDATE_ATTACK = "homeland_update_attack"
HOMELAND_UPDATE_SHIELD = "homeland_update_shield"
HOMELAND_UPDATE_ENERGY = "homeland_update_energy"
HOMELAND_UPDATE_AIM_ROLE = "homeland_update_aim_role"
HOMELAND_ATTACK_BUILD = "homeland_attack_build"
HOMELAND_REPAIR_BUILD = "homeland_repair_build"
HOMELAND_DIG = "homeland_dig"
HOMELAND_UPDATE_EVENT_RANK = "homeland_update_event_rank"
HOMELAND_UPDATE_PREVIEW_WEAPON = "homeland_update_preview_weapon"
HOMELAND_UPDATE_SCORE_REWARD = "homeland_update_score_reward"
HOMELAND_UPDATE_BUILD_RED = "homeland_update_build_red"
LOAD_HOMELAND_SCENE = "load_homeland_scene"
HOMELAND_FINISH_SELECT = "homeland_finish_select"
HOMELAND_INIT_PURCHASE_ENERGY = "homeland_init_purchase_energy"
HOMELAND_UPDATE_PURCHASE_ENERGY = "homeland_update_purchase_energy"
HOMELAND_UPDATE_BOX_IDX = "homeland_update_box_idx"
HOMELAND_CREATE_CHIP = "homeland_create_chip"
HOMELAND_UNENOUGH_ENERGY = "homeland_unenough_energy"
--老虎机跳过动画
SLOT_ATTACK_EVENT_SKIP = "slot_attack_event_skip"--老虎机跳攻击回复事件（跳过动画）

---------------------三星锦标赛-----------------------------
RANK_DETAIL_CHAMPIONSHIPS = "rank_detail_championships" --三星锦标赛详情回复
RANK_DETAIL_DATA_CHAMPIONSHIPS = "rank_detail_data_championships" --数据刷新
RANK_CHAMPIONSHIPSRANK_REWARD = "rank_championshipsrank_reward" --三星锦标奖励回复
UPDATA_CHAMPIONSHIPSRANK_OPENNOVICE = "updata_championships_opennovice" --玩家参加锦标赛开启，刷新界面
CHAMPIONSHIPS_RANK_REWARD = "championships_rank_reward" --三星锦标奖励回复
CHAMPIONSHIPS_RANK_HIDE_BUBBLE = "championships_rank_hide_bubble" --三星锦标赛隐藏气泡

---------------------副本--------------------------
UPDATE_INSTANCE_INTERFACE = "update_instance_interface" --副本界面刷新
INSTANCE_RECEIVE_PRIZE = "instance_receive_prize" --副本大奖刷新
INSTANCE_SWEEP = "instance_sweep" --副本扫荡回复

--------------------娱乐城------------------------------------
ENTERTAINMENT_UPDATE_DATA = "entertainment_update_data" -- 娱乐城数据刷新
ENTERTAINMENT_SELECT_SPECIAL = "entertainment_select_special" -- 娱乐城更替特等奖
ENTERTAINMENT_DIVINATE_RES = "entertainment_divinate_res" -- 占卜结果
ENTERTAINMENT_UPDATE_MONTHLYCARD_INFO = "entertainment_update_monthlycard_info" -- 娱乐城占卜结果响应

------------------------活跃度boss------------------------
UPDATE_SOCIATY_ACTIVE_BOSS_RANK = "UPDATE_SOCIATY_ACTIVE_BOSS_RANK" --刷新排行榜
UPDAT_SOCIATY_ACTIVE_BOSS_STATE = "UPDAT_SOCIATY_ACTIVE_BOSS_STATE" --刷新活动状态
UPDATE_SOCIATY_ACTIVE_BOSS_CHALLEGE_COUNT = "UPDATE_SOCIATY_ACTIVE_BOSS_CHALLEGE_COUNT" --刷新挑战次数

-------------------------盟战事件--------------------------
UPDATE_SOCIATY_WAR_BASE_INFO = "update_sociaty_war_base_info" --刷新盟战基础信息
UPDATE_SOCIATY_WAR_RANK_LIST = "update_sociaty_war_rank_list" --刷新盟战排行榜
UPDATE_SOCIATY_WAR_SQUAD_LIST = "update_sociaty_war_squad_list" --刷新战场阵容列表信息
UPDATE_SOCIATY_WAR_MEMBER_LIST = "update_sociaty_war_member_list" --刷新参战盟友列表信息
UPDATE_SOCIATY_WAR_SCENE_ATTACK_INFOS = "update_sociaty_war_scene_attack_infos" --刷新战场主界面的攻击信息
UPDATE_SOCIATY_WAR_SCORE_RANK = "update_sociaty_war_score_rank" --刷新盟战积分榜
UPDATE_SOCIATY_WAR_ATTACK_INFO = "update_sociaty_war_attack_info" --刷新盟战攻击信息
AUTO_FIND_ENEMY = "auto_find_enemy" --自动寻找玩家
UPDATE_SOCIATY_WAR_STATE = "update_sociaty_war_state" --刷新盟战入口倒计时状态
UPDATE_SOCIATY_WAR_TARGET_SQUAD = "update_sociaty_war_target_squad" --刷新目标阵容信息
UPDATE_SOCIATY_HOT_REDTIP = "update_sociaty_hot_redtip" --刷新联盟页签hottips红点
FIRST_ENTER_SOCIATY_WAR = "first_enter_sociaty_war" --首次进入盟战引导
CLICK_SIGN_BTN = "click_sign_btn"
CHOOSE_DEFEND_HERO = "choose_defend_hero"
SELECT_SOCIATY_WAR_DEFEND_HERO = "select_sociaty_war_defend_hero"
FIRST_ENTER_SOCIATY_SCENE = "first_enter_sociaty_scene"
ENTER_SOCIATY_WAR_SCENE = "enter_sociaty_war_scene"
FIRST_ATTEND_SOCIATY_WAR = "first_attend_sociaty_war"
ENTER_SOCIATY_WAR = "enter_sociaty_war"
CHOOSE_COMBAT_HERO = "choose_combat_hero"
SELECT_SOCIATY_WAR_COMBAT_HERO = "select_sociaty_war_combat_hero"
FIRST_CHOOSE_COMBAT_TARGET = "first_choose_combat_target"
CHOOSE_COMBAT_TARGET = "choose_combat_target"
UPDATE_SOCIATY_WAR_SCORE_RANK_LIST = "update_sociaty_war_score_rank_list" --刷新积分对比排行榜
UPDATE_SOCIATY_WAR_USEDPALS = "UPDATE_SOCIATY_WAR_USEDPALS" --刷新盟战玩家历史阵容信息
----------------------------------------------------------

-----------------------公测活动----------------------
UPDATE_BIND_MAIL_INTERFACE = "update_bind_mail_interface" --刷新邮箱绑定界面
UPDATE_SHARE_REWARD_INTERFACE = "update_share_reward_interface" --刷新分享有礼界面
UPDATE_BIND_ACCOUNT_POPUP_INTERFACE = "update_bind_account_popup_interface" --刷新绑定弹窗界面
UPDATE_CHANGE_PACKAGE_POPUP_INTERFACE = "update_change_package_popup_interface" --刷新换包弹窗界面
UPDATE_GLOBAL_EVENT_INTERFACE = "update_global_event_interface" --刷新全服大事件界面
-----------------------新英雄周活动----------------------
UPDATE_HERO_BOOK_LV = "update_hero_book_lv"
--英雄宝典当前等级
UPDATE_HERO_BOOK_COMMOM_LV = "update_hero_book_common_lv"
--英雄宝典免费奖励已领取最大等级
UPDATE_HERO_BOOK_ADVANCED_LV = "update_hero_book_advanced_lv"
--英雄宝典进阶奖励已领取最大等级
UPDATE_HERO_BOOK_LOCK_FLAG = "update_hero_book_flag"
--英雄宝典进阶奖励解锁状态
UPDATE_NEW_HERO_WEEK_TASK = "update_new_hero_week_task"
--英雄宝典日常/挑战任务数据
UPDATE_HERO_BOOK_LV_REWARD = "update_hero_book_lv_reward"
--领取英雄宝典等级奖励
UPDATE_HERO_CALL_ENDTIME = "update_hero_call_endtime"
--限时召唤结束时间戳
UPDATE_HERO_BOOK_ENDTIME = "update_hero_book_endtime"
--英雄宝典结束时间戳
UPDATE_LIMITED_GIFT_ENDTIME = "update_limited_gift_endtime"
--限时礼包结束时间戳
UPDATE_HERO_SHARE_SUCCESS = "update_hero_share_success"
--分享许愿成功
UPDATE_HERO_CALL_TOTAL_COUNT = "update_hero_call_total_count"
--更新新英雄召唤总次数
UPDATE_NEW_HERO_BASE_DATA = "update_new_hero_base_data"
--更新新英雄产出活动
UPDATE_LIMITED_GIFT_BAG_BUY_COUNT = "update_limited_gift_bag_buy_count"
--刷新新英雄限时特惠活动购买次数
UPDATE_SKIN_GIFT_BUY_COUNT = "update_skin_gif_buy_count"
--刷新新英雄活动皮肤礼包购买次数
HERO_BOOK_PAY_SUCCESS = "hero_book_pay_success"
--宝典等级付费奖励成功付费
UPDATE_HERO_CALL_VERSION = "update_hero_call_version"
--召唤版本号刷新
UPDATE_HEROBOOK_LV_UP_BUY_COUNT = "update_herobook_lv_up_buy_count"
--刷新新英雄宝典等级提升购买次数
UPDATE_PLOT_DATA = "update_plot_data" -- 更新剧情副本数据
--活动打开界面
ACTIVITY_SHOW_WINDOW = "activity_show_window"
----------------------------------------------------------

-----------------------合服活动----------------------
MERGE_SERVER_DATA_NTF = "meger_server_data_ntf"
--合服数据通知
MERGE_SERVER_REWARD_RSP = "merge_server_reward_rsp"
--合服奖励领取
MERGE_SERVER_HAND_POINT = "merge_server_hand_point"
--手指光效引导
----------------------------------------------------------

-----------------------回归活动----------------------
RETURN_PLAYERDATA_NTF = "return_player_data_ntf"
--回归数据通知
RETURN_PLAYERREWARD_RSP = "return_player_reward_rsp"
--回归奖励领取
RETURN_PLAYERHAND_POINT = "return_player_hand_point"
--回归光效引导

--回归宝典进阶奖励解锁状态
UPDATE_RETURN_BOOK_TASK = "update_return_book_task"

--回归宝典奖励更新
UPDATE_RETURN_BOOK_AWARD = "UPDATE_RETURN_BOOK_AWARD"

--回归宝典免费奖励已领取最大等级
UPDATE_RETURN_BOOK_COMMOM_LV = "update_return_book_commom_lv"
--回顾宝典进阶奖励已领取最大等级
UPDATE_RETURN_BOOK_ADVANCED_LV = "update_return_book_advanced_lv"

--刷新回归宝典等级提升购买次数
UPDATE_RETURN_BOOK_LV_UP_BUY_COUNT = "update_return_book_lv_up_buy_count"

--宝典奖品领取更新
UPDATE_RETURN_BOOK_LV_REWARD = "update_return_book_lv_reward"

--回归好礼打点上报
RETURN_GAME_EVENT_REPORT = "return_game_event_report"

--宝典购买成功
RETURN_BOOK_PAY_SUCCESS = "return_book_pay_success"

UPDATE_RETURN_BOOK_LV = "UPDATE_RETURN_BOOK_LV"

ENTER_RETURN_SEVEN = "enter_return_seven"
CLICK_ENTER_RETURN_SEVEN = "click_enter_return_seven"
--回归宝典数据更新
UPDATE_RETURN_PASSPORT_UI = "update_return_passport_ui"--可能不需要了
----------------------------------------------------------

-----------------------------联盟boss战-----------------------------
UPDATE_SOCIATY_BOSS_WAR_BASE_INFO = "UPDATE_SOCIATY_BOSS_WAR_BASE_INFO" --联盟boss信息刷新
UPDATE_SOCIATY_BOSS_WAR_RANK_INFOS = "UPDATE_SOCIATY_BOSS_WAR_RANK_INFOS" --联盟排行榜刷新
UPDATE_SOCIATY_BOSS_WAR_MEMBER_DMG_INFOS = "UPDATE_SOCIATY_BOSS_WAR_MEMBER_DMG_INFOS" --联盟个人伤害榜刷新
UPDATE_SOCIATY_BOSS_WAR_SHOP_REDTIP = "UPDATE_SOCIATY_BOSS_WAR_SHOP_REDTIP" --刷新联盟商店红点信息

----------------------------------------------------------
UPDATE_ROOKIE_DATA = "update_rookie_data"
--新手高抽活动数据通知
ROOKIE_GETREWARD_RESPONSE = "rookie_getreward_response"
ROOKIE_MULTIPLE_GETREWARD_RESPONSE = "rookie_multiple_getreward_response"
ROOKIE_GET_REWARDS = "rookie_get_reward"
ROOKIE_TARGET_GET_REWARDS = "rookie_target_get_rewards"
--新手目标活动
ROOKIE_TARGET_UPDATE = "rookie_target_update"
--新手目标活动

ROOKIE_CONTINUE_CHARGE_REWARD_REQ = "rookie_continue_charge_reward_req"
ROOKIE_DAILY_GIFT_REWARD_RES = "rookie_daily_gift_reward_res"

----------------------------虚空擂台---------------------------

VOID_RING_DATA_UPDATE = "VOID_RING_DATA_UPDATE" -- 虚空擂台数据更新
UPDATE_VOID_RING_HOT_SIGN = "UPDATE_VOID_RING_HOT_SIGN" -- 虚空擂台hot标签数据更新

--------------------------------------------------------------

----------------------------月度基金---------------------------

UPDATE_MONTHFUND_REWARD_STATE = "update_monthfund_reward_state" -- 更新奖励状态
UPDATE_MONTHFUND_REDDOT = "update_monthfund_reddot" -- 更新月度基金红点
UPDATE_MONTHFUND_TIMER = "update_monthfund_timer" -- 更新倒计时
UPDATE_MONTHFUND_BUY = "update_monthfund_buy" -- 更新购买

--------------------------------------------------------------

----------------------------大富翁翻牌---------------------------
NET_TURNABLE_MOVE_RSP = "turnable_move_rsp" --投骰子RSB
NET_TURNABLE_STAR_PRIZE_RSP = "turnable_star_prize_rsp" --心心奖励
NET_TURNABLE_RANDOM_EFFECT_RSP = "turnable_random_effect_rsp" --效果牌
NET_TURNABLE_TIME_NTF = "turnable_time_ntf" --时间通知
NET_TURNABLE_SET_SELECT_HERO = "net_turnable_set_select_hero"
--选择英雄

TASK_TURNABLE_DATA = "task_turnable_data"
--任务通知
TASK_TRUNABLE_STARS_DATA = "task_turnable_stars_data"
--星星奖励通知
TASK_TRUNABLE_GRID_DATA = "task_turnable_grid_data"
--格子数据通知
--------------------------------------------------------------

----------------------------圣诞节翻牌---------------------------
ACTIVITY_FLIP_INIT_RSP = "activity_flip_init_rsp" --翻牌数据
ACTIVITY_FLIP_DEST_SET_RSP = "activity_flip_dest_set_rsp"
--设置奖励成功
ACTIVITY_FLIP_FLIP_RSP = "activity_flip_flip_rsp" -- 翻牌回复
ACTIVITY_FLIP_NEXT_LEVEL_RSP = "activity_flip_next_level_rsp"
--下一关回复
ACTIVITY_FLIP_GET_BIGREWARD = "activity_flip_get_bigreward"
--下一关回复
--------------------------------------------------------------

----------------------------联盟星战---------------------------
CLICK_STAR_WAR_SCENE_OBJECT = "CLICK_STAR_WAR_SCENE_OBJECT" --点击星战场景地表对象
-------------------------------------------------------
----------------------------巅峰赛---------------------------

TOPRACE_GETTOPLIST = "toprace_gettoplist" -- 巅峰赛或名人堂排行榜数据更新
TOPRACE_GROUPSTATE = "toprace_groupstate" -- 巅峰赛分组数据更新
TOPRACE_GUESSPUSH = "toprace_guesspush" -- 巅峰赛竞猜数据更新
TOPRACE_BATTLEVIDEO = "toprace_battlevideo" -- 巅峰赛查看战斗录像
TOPRACE_GUESS = "toprace_guess" -- 巅峰赛投票更新
TOPRACE_18PAL_LIST = "toprace_18pal_list" -- 巅峰赛查看玩家英雄列表
TOPRACE_VOTE_NDAY_AGTABLE = "toprace_vote_nday_agtable" -- 巅峰赛竞猜结果玩家对阵表
TOPRACE_DATA = "toprace_data" -- 巅峰赛基础消息回复
TOPRACE_LIKE_UPDATE = "toprace_like_update" -- 巅峰赛点赞消息回复
TOPRACE_SETDEFENSELINEUP = "toprace_setdefenselineup" -- 巅峰赛设置阵容成功消息回复
TOPRACE_RANK_REWARD = "toprace_rank_reward" -- 巅峰赛领取排行回复

--------------------------------------------------------------

----------------------------广告、抽奖---------------------------
EVENT_REWAEDEDAD_CLOSED = "event_rewardedad_closed"
EVENT_UDAPTE_LOTTERY_DATA = "event_update_lottery_data"
EVENT_ONMSG_LOTTERYWISH_RSP = "event_onmsg_lotterywish_rsp"
EVENT_REWARDEDAD_CALLBACK = "event_rewardedad_callback"
EVENT_ONMSG_LOTTERYWISH_RSP = "event_onmsg_lotterywish_rsp"
EVENT_UPDATE_LOTTERY_REDDOT = "event_update_lottery_reddot"
CLICK_NEXT_VIDEO_BTN = "click_next_video_btn"
----------------------------时空裂缝---------------------------
FIRST_UNLOCK_SPACEGAP = "first_unlock_spaceGap"
ENTER_SPACEGAP = "enter_spaceGap"
CHOOSE_SPACEGAP_LV = "choose_spaceGap_lv"
SELECT_SPACEGAP_LV = "select_spaceGap_lv"

CLICK_SPACEGAP_TIP = "click_spaceGap_tip"
SHOW_SPACEGAP_TIP = "show_spaceGap_tip"
CLOSE_SPACEGAP_TIP = "close_spaceGap_tip"
SELECT_SPACEGAP_HERO = "select_spaceGap_hero"

GUIDE_SELECT_HERO = "guide_select_hero"
END_SELECT_HERO = "end_select_hero"
INTRODUCE_TIPBTN_POSITION = "introduce_tipBtn_position"
CLICK_TIPBTN = "click_tipBtn"
INTRODUCE_TIPS = "introduce_tips"
END_INTRODUCE_TIPS = "end_introduce_tips"

FINISH_SPACEGAP_STORY = "finish_spacegap_story"
FINISH_PLOT_STORY = "finish_plot_story"
--------------------------------------------------------------
------------------------联盟徽章----------------------------------
UPDATE_LEAGUE_MEDAL_BASE = "update_league_medal_base"
--联盟徽章基础数据
UPDATE_LEAGUE_MEDAL_REWARD = "update_league_medal_reward"
--联盟徽章领奖响应
UPDATE_LEAGUE_MEDAL_WARN_AUTO = "update_league_medal_warn_auto"
--联盟徽章设置/取消自动提醒响应
UPDATE_LEAGUE_MEDAL_WARN = "update_league_medal_warn"
--联盟徽章一键提醒响应
UPDATE_LEAGUE_MEDAL_RANK = "update_league_medal_rank"
--联盟徽章排行榜更新
UPDATE_LEAGUE_MEDAL_WARNDEL = "update_league_medal_warndel"
--联盟徽章删除提醒响应
--------------------------------------------------------------
---------------------关卡锦标赛-----------------------------
UPDAT_RANK_DATA_HOOK_LEVEL_TOURNAMENT = "UPDAT_RANK_DATA_HOOK_LEVEL_TOURNAMENT" --排名数据刷新
UPDATE_HOOK_LEVEL_TOURNAMENT_STATE = "update_hook_level_tournament_state" --刷新关卡锦标赛开启状态
UPDATE_HOOK_LEVEL_TOURNAMENT_PLAYER_HEAD = "UPDATE_HOOK_LEVEL_TOURNAMENT_PLAYER_HEAD" --刷新关卡锦标赛场景头像信息
----------------------------------------------------------------------------------------资源不足引导----------------------------------
EXP_GUIDE1_START = "exp_guide1_start"
EXP_GUIDE1_END = "exp_guide1_end"
EXP_GUIDE2_START = "exp_guide2_start"
EXP_GUIDE2_END = "exp_guide2_end"

LV_GUIDE1_START = "lv_guide1_start"
LV_GUIDE1_END = "lv_guide1_end"
LV_GUIDE2_START = "lv_guide2_start"
LV_GUIDE2_END = "lv_guide2_end"

GOLD_GUIDE1_START = "gold_guide1_start"
GOLD_GUIDE1_END = "gold_guide1_end"
GOLD_GUIDE2_START = "gold_guide2_start"
GOLD_GUIDE2_END = "gold_guide2_end"
--------------------------------------------------------------------

UPDATE_WISH_HERO_EVENT = "UPDATE_WISH_HERO_EVENT" --刷新心愿英雄事件
UPDATE_WISH_LINEUP_NUM_EVENT = "UPDATE_WISH_LINEUP_NUM_EVENT" --选择阵容数量
UPDATE_WISH_HERO_DATA_EVENT = "UPDATE_WISH_HERO_DATA_EVENT" --刷新心愿英雄数据事件

UPDATE_BLESS_HERO_EVENT = "update_bless_hero_event"
--刷新召唤祝福英雄
UPDATE_BLESS_HERO_GET_EVENT = "update_bless_hero_get_event"
--获取召唤祝福英雄

--新手引导
enter_hero_summon = "enter_hero_summon"
end_enter_hero_summon = "end_enter_hero_summon"
enter_wish_hero = "enter_wish_hero"
end_enter_wish_hero = "end_enter_wish_hero"
hero_wish_select = "hero_wish_select"
end_hero_wish_select = "end_hero_wish_select"
hero_wish_look = "hero_wish_look"
end_hero_wish_look = "end_hero_wish_look"
TASK_UNFORCED_GUIDE = "task_unforced_guide"
TASK_REFRESH_UNFORCED_GUIDE = "task_refresh_unforced_guide"

-----------------------公测活动--------------------------------------
OPEN_TEST_RECEIVE_DATA = "open_test_receive_data"
--------------------------------------------------------------------

----------------------------皮肤系统-------------------------
UPDATE_HERO_SKIN_PROP = "update_hero_skin_prop"
UPDATE_HERO_SKIN = "update_hero_skin"
------------------------------------------------------------

----------------------------热气球-------------------------
HOT_BALL_HOOK_SCENE_START = "HOT_BALL_HOOK_SCENE_START" --开始播发热气球
HOT_BALL_HOOK_SCENE_TIME_OUT = "HOT_BALL_HOOK_SCENE_TIME_OUT" --热气球暂停
CANCEL_HOT_BALL_HOOK_SCENE_TIME_OUT = "CANCEL_HOT_BALL_HOOK_SCENE_TIME_OUT" --取消热气球暂停
CHECK_HOT_BALL_FLY = "CHECK_HOT_BALL_FLY" --检查是否弹出热气球
------------------------------------------------------------

MARK_OWNED_HERO = "mark_owned_hero"
--标记星英雄

----------------------------联盟研究所-------------------------
UNLOCK_INSTITUTE = "unlock_institute"
ENTER_INSTITUTE = "enter_institute"
CHOOSE_INSTITUTE_SKILL = "choose_institute_skill"
CLICK_INSTITUTE_SKILL = "click_institute_skill"
--------------------------------------------------------------

----------------------------重铸活动-------------------------
UPDATE_RECAST_ACTIVITY_DATA = "UPDATE_RECAST_ACTIVITY_DATA"

----------------------------益智小游戏-------------------------
PUZZLE_WIN = "puzzle_win"
PUZZLE_PASS_LEVEL = "puzzle_pass_level"
PUZZLE_CREATURE_DIE = "puzzle_creature_die"
PUZZLE_MOVEMENT = "puzzle_movement"
PUZZLE_GAME_OPEN = "puzzle_game_open"
GAME_CONST_CHECKALL = "game_const_checkall"
CLICK_PASS_LEVEL = "click_pass_level"
XYX_ADVRW_RSP = "xyx_advrw_rsp"
XYX_STAGE_REWARD_RSP = "xyx_stage_reward_rsp"
CHAPTER_STAGE_REWARD_UPDATE = "chapter_stage_reward_update"
PUZZLE_LEVEL_UP = "sock_package_level_up" --马甲包技能升级

MINIGAME_EXIT = "minigame_exit"
NEWYEARGAME_REWARD_BACK = "NEWYEARGAME_REWARD_BACK" --新年小游戏奖励返回
NEWYEARGAME_CHANGELEVEL = "NEWYEARGAME_CHANGELEVEL"
--新年小游戏更换关卡
---------------------------情人节活动----------------------
UPDATE_VALENTINE_INTIMACY = "UPDATE_VALENTINE_INTIMACY" --亲密度刷新界面
UPDATE_VALENTINE_SEND_GIFT = "UPDATE_VALENTINE_SEND_GIFT" --送礼刷新界面
UPDATE_VALENTINE_ARCHERY_STATE = "UPDATE_VALENTINE_ARCHERY_STATE" --射箭状态刷新
UPDATE_VALENTINE_ARCHERY_RECORD = "UPDATE_VALENTINE_ARCHERY_RECORD" --射箭结果刷新
UPDATE_VALENTINE_ARCHERY_BUY_TIMES = "UPDATE_VALENTINE_ARCHERY_BUY_TIMES" --射箭购买轮次
SHOW_VALENTINE_ARCHERY_REWARD = "SHOW_VALENTINE_ARCHERY_REWARD" --展示射箭奖励
UPDATE_FESTIVAL_BASE_UI_ORDER = "UPDATE_FESTIVAL_BASE_UI_ORDER" --刷新活动界面层级
SHOW_VALENTINE_BIG_REWARD_RESULT = "SHOW_VALENTINE_BIG_REWARD_RESULT" --展示特惠大奖结果
SHOW_VALENTINE_BUY_UPDATE = "SHOW_VALENTINE_BUY_UPDATE" --展示特惠大奖结果

-------------------------------清明节活动---------------------------
MOVE_SUCCESS_REWARD = "move_success_reward" --移动成功奖励
UPDATE_HIKING_RED = "update_hiking_red"
HIKING_QINGTUAN_RED = "hiking_qingtuan_red"

------------------------------印记系统------------------------
ENHANCE_SIGIL_RESPONSE = "enhance_sigil_response"
DEVOUR_SIGIL_RESPONSE = "devour_sigil_response"
REPLACE_SIGIL_RESPONSE = "replace_sigil_response"
UPDAGE_CURRENT_HEROSID = "update_current_herosid"
--------------------------------------------------------------
FESTIVAL_BASE_SORTING_GROUP_CONTROL = "FESTIVAL_BASE_SORTING_GROUP_CONTROL"

---------------------------Bingo复活节活动----------------------
BINGO_DATA_INIT = "bingo_data_init"
--更新数据
UPDATE_BINGO_GRID = "update_bingo_grid"
UPDATE_BINGO_LINE = "update_bingo_line"
UPDATE_BINGO_FINAL = "update_bingo_final"
UPDATE_BINGO_RED = "update_bingo_red"
RESPONSE_BINGO_REWARD = "response_bingo_reward"

---------------------------新手首充礼包--------------------------
UPDATE_FIRST_RECHAGE = "update_first_rechage"--红点

---------------------------虫母累充礼包--------------------------
UPDATE_PROGRESS_GIFT = "update_progress_gift"

---------------------------战舰科技------------------------
BATTLE_SHIP_TECHNOLOGY_UPGRADE = "battle_ship_technology_upgrade" --战舰科技升级
SPACE_EXPLORATION_BASE_DATA_UPDATE = "space_exploration_base_data_update" --星际探索基础数据更新
SPACE_EXPLORATION_GALAXY_DATA_UPDATE = "space_exploration_galaxy_data_update" --星际探索星系数据更新
SPACE_EXPLORATION_EXPLORE_HERO_SELECT = "space_exploration_explore_hero_select" --星际探索探索星球选择英雄
SPACE_EXPLORATION_GET_ALL_REWARD = "space_exploration_get_all_reward" --星际探索奖励弹窗
UPDATE_SPACE_EXPLORATION_STAR_ACTIVE_STATE = "update_space_exploration_star_active_state" --更新星际探索星球激活状态
SPACE_EXPLORATION_SELECT_GALAXY = "space_exploration_select_galaxy" --选择星系
SPACE_EXPLORATION_ACTIVE_GALAXY = "space_exploration_active_galaxy" --激活星系
SPACE_EXPLORATION_PIRATE_UPDATE = "space_exploration_pirate_update" --海盗更新
SPACE_EXPLORATION_MAP_CONTROL = "space_exploration_map_control" --地图控制
SPACE_EXPLORATION_RETURN = "space_exploration_return" --返航
SPACE_EXPLORATION_FORCE_QUIT = "space_exploration_force_quit" --周期刷新
SPACE_EXPLORATION_MAIN_ORDER_UPDATE = "space_exploration_main_order_update" --界面层级刷新
SPACE_EXPLORATION_PROP_DATA_UPDATE = "space_exploration_prop_data_update" --星际探索属性更新
UPDATE_SPACE_EXPLORATION_START_EXPLORE = "update_space_exploration_start_explore" --开始探索资源星球
SPACE_EXPLORATION_BUY_PRIVILEGE_SUCCESSFULLY = "space_exploration_buy_privilege_successfully" --购买特权成功

CLICK_SPACE_EXPLORATION = "click_space_exploration"
ENTER_SPACE_EXPLORATION_LOBBY = "enter_space_exploration_lobby"
CLICK_WAR_SHIP_TECHNOLOGY_MAIN = "click_war_ship_technology_main"
ENTER_WAR_SHIP_TECHNOLOGY_MAIN = "enter_war_ship_technology_main"
CLICK_WAR_SHIP_TECHNOLOGY = "click_war_ship_technology"
ENTER_WAR_SHIP_TECHNOLOGY = "enter_war_ship_technology"
CLICK_WAR_SHIP_TECHNOLOGY_UPGRADE = "click_war_ship_technology_upgrade"
CLICK_WAR_SHIP_TECHNOLOGY_UPGRADE_COMPLETE = "click_war_ship_technology_upgrade_complete"
CLICK_WAR_SHIP_TECHNOLOGY_DESCRIPTION = "click_war_ship_technology_description"
CLICK_WAR_SHIP_TECHNOLOGY_DESCRIPTION_COMPLETE = "click_war_ship_technology_description_complete"
CLICK_WAR_SHIP_TECHNOLOGY_BACK = "click_war_ship_technology_back"
ENTER_SPACE_EXPLORATION_GALAXY = "enter_space_exploration_galaxy"
CLICK_SPACE_EXPLORATION_GALAXY = "click_space_exploration_galaxy"
ENTER_SPACE_EXPLORATION = "enter_space_exploration"
CLICK_SPACE_EXPLORATION_DESCRIPTION = "click_space_exploration_description"
CLICK_SPACE_EXPLORATION_DESCRIPTION_COMPLETE = "click_space_exploration_description_complete"
CLICK_SPACE_EXPLORATION_STOP_UPDATE_TIMER = "click_space_exploration_stop_update_timer"
CLICK_SPACE_EXPLORATION_RESTART_UPDATE_TIMER = "click_space_exploration_restart_update_timer"

UPDATE_MENU_BOT_DATA = "update_menu_bot_data"
UPDATE_MENU_BOT_UI = "update_menu_bot_ui"

WAR_SHIP_TECHNOLOGY_RESET_SUCCESSFULLY = "war_ship_technology_reset_successfully"

---------------------------------------------传奇锦标赛------------------------------
LEGEND_CHAMPIONSHIPS_DATA_UPDATE = "legend_championships_data_update" --赛季数据刷新
LEGEND_CHAMPIONSHIPS_APPLY_SUCCESSFULLY = "legend_championships_apply_successfully" --报名成功
UPDATE_LEGEND_CHAMPIONSHIPS_RANK_LIST = "update_legend_championships_rank_list" --刷新排名列表
UPDATE_LEGEND_CHAMPIONSHIPS_CHALLENGE_LIST = "update_legend_championships_challenge_list" --刷新挑战列表
UPDATE_LEGEND_CHAMPIONSHIPS_CHALLENGE_LIST_UI = "update_legend_championships_challenge_list_ui"
UPDATE_LEGEND_CHAMPIONSHIPS_BATTLE_RECORD_LIST = "update_legend_championships_battle_record_list" --刷新战斗记录列表
UPDATE_LEGEND_CHAMPIONSHIPS_BATTLE_RECORD_LIST_UI = "update_legend_championships_battle_record_list_ui"
LEGEND_CHAMPIONSHIPS_GET_REWARD_SUCCESSFULLY = "legend_championships_get_reward_successfully" --领奖成功
LEGEND_CHAMPIONSHIPS_SEASON_DATA_UPDATE = "legend_championships_season_data_update"
UPDATE_LEGEND_CHAMPIONSHIPS_RANK_LIST_UI = "update_legend_championships_rank_list_ui"
UPDATE_LEGEND_CHAMPIONSHIPS_TEAM_SORT = "update_legend_championships_team_sort"
UPDATE_LEGEND_CHAMPIONSHIPS_DEFEND = "update_legend_championships_defend"
LEGEND_CHAMPIONSHIPS_BATTLE_RESPOND = "legend_championships_battle_respond"
UI_BATTLE_RESULT_REPLAY = "ui_battle_result_replay"
UPDATE_LEGEND_CHAMPIONSHIPS_BASE_ORDER = "update_legend_championships_base_order"
LEGEND_BATTLE_RESULT_CLOSE = "legend_battle_result_close"
UPDATE_LEGEND_SETDEFENSELINEUP = "update_legend_setdefenselineup"
FORMATION_SETTING_START_BATTLE = "formation_setting_start_battle"
LEGEND_TROOPS_RESPOND = "legend_troops_respond"
DAILY_REWARD_RESPOND = "daily_reward_respond" --每日奖励领取
GET_VICTORY_REWARD_3V3 = "get_victory_reward_3v3" --领取每日奖励恢复
UPDATE_GUIDE_STATUS = "update_guide_status"
---------------------------------------订阅礼包更新-----------------------------------------
UPDATE_SUBMITE_GIFT = "update_submite_gift"

---------------------------阵营通缉--------------------------
FACTION_LEVELUP = "faction_levelup"
FACTION_SKILL_LEVELUP = "faction_skill_levelup"
-------------------------------------------------------------

UPDATE_PLAYER_DIAMOND = "update_player_diamond"

--顶部资源数更新
UPDATE_PLAYER_RESOURCE = "update_player_resource"

---------------------------黑暗首脑boss引导---------------------
ENTER_SOCIATY = "enter_sociaty"
CLICK_SOCIATY = "click_sociaty"
ENTER_SOCIATY_BOSS = "enter_sociaty_boss"
CLICK_SOCIATY_BOSS = "click_sociaty_boss"
ENTER_SOCIATY_BOSS_BATTLE = "enter_sociaty_boss_battle"
CLICK_SOCIATY_BOSS_BATTLE = "click_sociaty_boss_battle"
---------------------------------------------------------------
UPDATE_MAIL_ROLE_NAME = "update_mail_role_name"

---------------------------符文活动----------------------
RESPONSE_FESTIVAL_SIGIL_REWARD = "response_festival_sigil_reward"
UPDATE_FESTIVAL_SIGIL_DATA = "update_festival_sigil_data"
UPDATE_FESTIVAL_SIGIL_JACKPOT = "update_festival_sigil_jackpot"
--------------------------------------------------------

---------------------------Boss试炼 & 升星之路----------------------
RESONSE_SELECT_GROUP_UP_HERO = "resonse_select_group_up_hero"
------------------------------------------------------------------------

------------------------单人秘境-------------------------------------
SECRETPLACE_SHOW_SELECT_RELIC = "secretplace_show_select_relic"
--遗物选择界面触发
UPDATE_SECRET_PLACE = "update_secret_place"
--刷新单人秘境界面
NEXT_WAVE = "next_wave"
--刷新波数
SECRET_RELIC_FLY = "secret_relic_fly"
--单人秘境遗物飞行
QUICK_SERIES_BATTLE_INFO_UPDATE = "quick_series_battle_info_update"
--快速连续战斗数据更新
UPDATE_SECRET_PLACE_REWARD_STATE = "update_secret_place_reward_state"
--单人秘境领奖状态刷新
--------------------------------------------------------------------

------------------------多人秘境-------------------------------------
UPDATE_MULTI_SECRET_PLACE = "update_multi_secret_place"
--刷新多人秘境界面
UPDATE_MULTI_SECRET_PLACE_REWARD_STATE = "update_multi_secret_place_reward_state"
--多人秘境领奖状态刷新
UPDATE_MULTI_SECRET_PLACE_RANK = "update_multi_secret_place_rank"
--多人秘境排行榜刷新
--------------------------------------------------------------------
---
------------------------指挥官之路-------------------------------------
ENTER_COMMANDER_TRIAL = "enter_commander_trial"
CLICK_COMMANDER_TRIAL = "click_commander_trial"
INTRODUCT_COMMANDER_TRIAL = "introduct_commander_trial"
END_INTRODUCT_COMMANDER_TRIAL = "end_introduct_commander_trial"
CLICK_COMMANDER_TRIAL_REWARD = "click_commander_trial_reward"
CLICK_COMMANDER_TRIAL_CHAPTER_REWARD = "click_commander_trial_chapter_reward"
GET_COMMANDER_TRIAL_CHAPTER_REWARD = "get_commander_trial_chapter_reward"
CLICK_BACK_HUNT = "click_back_hunt"
CLICK_FIGHT_BUTTON = "click_fight_button"
----------------------------------------------------------------------
------------------------运营活动-------------------------------------

OPERATE_REWARD_STATE = "operate_reward_state"
UPDATE_OPERATE_ACTIVE_CONFIG = "update_operate_active_config"
UPDATE_OPERATE_ACTIVE_DATA = "update_operate_active_data"

----------------------------------------------------------------------
------------------------光暗心愿抽-------------------------------------

RESPONSE_SHENMING_FACTIONRECOMCARD = "Response_Shenming_FactionRecomCard"

------------------------国旗-------------------------------------
UPDATE_NATIONAL_FLAG = "update_national_flag"
UPDATE_PLAYER_NATIONAL_FLAG = "update_player_nationial_flag"
UPDATE_LEAGUE_NATIONAL_FLAG = "update_league_national_flag"
------------------------国旗-------------------------------------
----------------------------------------------------------------------
-------------------小游戏激活弱引导----------------------
ENTER_HUNT = "enter_hunt"
UNFORCED_GUIDE_PRE_STEP_COMPLETE = "unforced_guide_pre_step_complete"
CLICK_MINIGAME = "click_miniGame"
CLICK_MINIGAME_CHAPTER = "click_minigame_chapter"
MINIGAME_WIN = "minigame_win"
ENTER_MINIGAME_CHAPTER = "enter_miniGame_chapter"
CLICK_MINIGAME_LEVEL = "click_miniGame_level"
CLICK_GO_HUNT_BUTTON = "click_go_hunt_button"
CLICK_CHATPER_REWARD = "click_chatper_reward"
CLOSE_CHATPER_REWARD = "close_chatper_reward"
UI_PUZZLE_GAME_DEFEAT_CLOSE = "ui_puzzle_game_defeat_close"
MINIGAME_SHOW_UNLOCK_TIPS = "minigame_show_unlock_tips"

----------------------------------------------------------------------
-------------------时空擂台-------------------------------------------
WEEKEND_ARENA_TOP_PLAYER_INFO = "weekend_arena_top_player_info"
WEEKEND_ARENA_REPORT_REDDOT = "weekend_arena_report_reddot"
RECEIVE_WEEKEND_ARENA_ENEMY_LIST = "receive_weekend_arena_enemy_list"
UPDATE_WEEKEND_ARENA_ALL_RANKINFO = "update_weekend_arena_all_rankinfo"
--防守阵容
UPDATE_WEEKEND_ARENA_DEFENSE_TEAM = "update_weekend_arena_defense_team"
--巅峰赛改版
UPDATE_WEEKEND_ARENA_RECORD = "update_weekend_arena_record"
--巅峰赛领取每日奖励
WEEKEND_GET_VICTORY_REWARD = "get_weekend_arena_victory_reward"

----------------------------------------------------------------------
-------------------手机绑定--------------------------------------------
BINDING_PHONE_SUCCESSFULLY = "binding_phone_successfully"
----------------------------------------------------------------------
-------------------舰队远征----------------------
PLAY_FLEET_EXPEDITION_ENTER_GALAXY_ANIMATION = "play_fleet_expedition_enter_galaxy_animation"
--进入星系动画
PLAY_FLEET_EXPEDITION_ENTER_NEBULA_ANIMATION = "play_fleet_expedition_enter_nebula_animation"
--进入星云动画
CHANGE_FLEET_EXPEDITION_PASS_LEVEL = "change_fleet_expedition_pass_level"
--更新通关关卡
UPDATE_FLEET_EXPEDITION_STAR = "update_fleet_expedition_star"
--更新每关获得星星数据
UPDATE_FLEET_EXPEDITION_CHAPTER_STAR = "update_fleet_expedition_chapter_star"
--更新星星领取数据
UPDATE_FLEET_EXPEDITION_SWEEP_COUNT = "update_fleet_expedition_sweep_count"
--更新扫荡次数
UPDATE_FLEET_EXPEDITION_SWEEP_SUCCESS = "update_fleet_expedition_sweep_success"
--扫荡成功
CHANGE_NEBULAID = "change_nebulaid"
CLICK_FLEETFIGHT = "click_fleetfight"
ENTER_FLEETFIGHT = "enter_fleetfight"
ENTER_FLEETFIGHT_MAIN = "enter_fleetfight_main"
CLICK_FLEETFIGHT_MAIN = "click_fleetfight_main"

EVENT_CLOSE_CHOOSE_AREA = "event_close_choose_area"

--------------密钥----------------
UPDATA_GATEWAY_STEP = "update_gateway_step"

UNION_AD_GET_REWARDED = "union_ad_get_rewarded"

--------------------------------- AIHelp  START----------------
AI_HELP_UNREAD_MSG_RED = "AI_HELP_UNREAD_MSG_RED"
--------------------------------- AIHelp  END----------------

--------------饰品工坊----------------
UPDATE_DERACOTE_SHOP_CHOU_EVENT = "update_deracote_shop_chou_event"
UPDATE_DERACOTE_SHOP_INFO_EVENT = "update_deracote_shop_info_event"
UPDATE_DERACOTE_SHOP_WISH_EVENT = "update_deracote_shop_wish_event"
UPDATE_DERACOTE_CUSTOM_SUSED_EVENT = "update_deracote_custom_sused_event"
UPDATE_DERACOTE_SHOP_DINGXHI_EVENT = "update_deracote_shop_dingxhi_event"
SHOW_DERACOTE_SHOP_REWARD = "show_deracote_shop_reward"

------------------饰品模块开始----------------------
ENHANCE_BLESS_RESPONSE = "enhance_bless_response"
ENHANCE_DECORATE_RESPONSE = "enhance_decorate_response"
REPLACE_DECORATE_RESPONSE = "replace_decorate_response"
UPDATE_DECORATE_LOCK_STATE = "update_decorate_lock_state"
ENHANCE_MULTI_BLESS_RESPONSE = "ENHANCE_MULTI_BLESS_RESPONSE"
UPDATE_DERACTE_NUM_CHOSE = "UPDATE_DERACTE_NUM_CHOSE"
------------------饰品模块结束---------------------->>>>>>> .r85641

--------------------------------- 联盟据点战  START----------------
UNION_SLG_LOGIN_PUSH_DATA = "UNION_SLG_LOGIN_PUSH_DATA"
-- 报名成功
UNION_SLG_SIGN_UP_SUCCESS = "UNION_SLG_SIGN_UP_SUCCESS"
UNION_SLG_MAP_ID_RESPONSE = "UNION_SLG_MAP_ID_RESPONSE"
UNION_SLG_LEAGUE_HISTORY_OWN_RESPONSE = "UNION_SLG_LEAGUE_HISTORY_OWN_RESPONSE"
UNION_SLG_STATION_INFO_RESPONSE = "UNION_SLG_STATION_INFO_RESPONSE"
UNION_SLG_DEPLOY_TEAM_RESPONSE = "UNION_SLG_DEPLOY_TEAM_RESPONSE"
UNION_SLG_POINTS_RANK_RESPONSE = "UNION_SLG_POINTS_RANK_RESPONSE"
UNION_SLG_BUY_HIDE_RESPONSE = "UNION_SLG_BUY_HIDE_RESPONSE"
UNION_SLG_HIDE_TEAM_RESPONSE = "UNION_SLG_HIDE_TEAM_RESPONSE"
UNION_SLG_HIDE_COUNT_RESPONSE = "UNION_SLG_HIDE_COUNT_RESPONSE"
UNION_SLG_LOACATE_TO_MAP = "UNION_SLG_LOACATE_TO_MAP"
UNION_SLG_GET_LEAGUE_LEADER_RESPONSE = "UNION_SLG_GET_LEAGUE_LEADER_RESPONSE"


-- 回复我的队伍
UNION_SLG_GET_MY_TEAM_RESPONSE = "UNION_SLG_GET_MY_TEAM_RESPONSE"
-- 保存队伍成功
UNION_SLG_UPDATE_SAVE_TEAM = "UNION_SLG_UPDATE_SAVE_TEAM"
-- 自选奖励
UNION_SLG_SELECT_REWARD = "UNION_SLG_SELECT_REWARD"
-- 托管操作成功
UNION_SLG_TEAM_TRUST_SUCCESS = "UNION_SLG_TEAM_TRUST_SUCCESS"
-- 驻军成功
UNION_SLG_TEAM_GARRISON_SUCCESS = "UNION_SLG_TEAM_GARRISON_SUCCESS"
-- 触发底部导航栏返回
UNION_SLG_BOTTOM_BASE_QUIT = "UNION_SLG_BOTTOM_BASE_QUIT"

--------------------------------- 联盟据点战  END----------------

--------------------------------- AIHelp  START----------------
AI_HELP_UNREAD_MSG_RED = "AI_HELP_UNREAD_MSG_RED"
--------------------------------- AIHelp  END----------------
-------------------------深渊之城 start-------------------
UPDATE_DUNGEON_RANKLIST = "UPDATE_DUNGEON_RANKLIST"
-------------------------深渊之城 end-------------------

-- -------------------------- 通用节日活动小游戏 START ---------------------

--- 节日活动小游戏行动网络请求回复
FESTIVAL_GAME_ACTION_NET_RESPONSE = "FESTIVAL_GAME_ACTION_NET_RESPONSE"
--- 节日活动小游戏行动网络请求超时
FESTIVAL_GAME_ACTION_NET_TIMEOUT = "FESTIVAL_GAME_ACTION_NET_TIMEOUT"
--- 节日活动小游戏行动成功
FESTIVAL_GAME_ACTION_SUCCESS = "FESTIVAL_GAME_ACTION_SUCCESS"
--- 节日活动小游戏行动失败
FESTIVAL_GAME_ACTION_FAILED = "FESTIVAL_GAME_ACTION_FAILED"

-- -------------------------- 通用节日活动小游戏 END ---------------------

DOWNLOAD_REWARRD_SUCCESSFULLY = "download_reward_successfully"--下载资源后奖励领取成功
DOWNLOAD_LANGRES_COMPLETE = "DOWNLOAD_LANGRES_COMPLETE" --下载语言资源完成
DOWNLOAD_LANGRES_FAIL = "DOWNLOAD_LANGRES_FAIL"--下载语言资源失败 

--- 窗口排序
WINDOW_MGR_SORT_MODULE = "WINDOW_MGR_SORT_MODULE"

---------------------------------facebook社群引导-------------------------------------------
COMMUNITY_FB_REDDOT = "community_fb_reddot" --facebook社群引导红点
COMMUNITY_FB_REWARD = "community_fb_reward" ---facebook社群引导获取奖励
---------------------------------facebook社群引导End----------------------------------------

---------------------------------公告-------------------------------------------
UPDATE_ANNOUNCEMENT_DATA = "update_announcement_data" --首页头像处公告数据刷新
UPDATE_ANNOUNCEMENT_REDDOT = "update_announcement_reddot" --首页头像处公告红点刷新
---------------------------------公告End-----------


-- -------------------------- 春节活动红包 START   ---------------------
--春节发红包信息返回
CHINESE_NEWYEAR_GIVE_REDPACKET_RSP = "CHINESE_NEWYEAR_GIVE_REDPACKET_RSP"
--春节抢红包奖励获得
CHINESE_NEWYEAR_RED_ENVELOPE_GET_REWARD = "CHINESE_NEWYEAR_RED_ENVELOPE_GET_REWARD"
--春节红包详情信息返回
CHINESE_NEWYEAR_REDPACKET_DETAIL_RSP = "CHINESE_NEWYEAR_REDPACKET_DETAIL_RSP"
--刷新抢红包的红点
CHINESE_NEWYEAR_REDPACKET_REFRESH_RED = "CHINESE_NEWYEAR_REDPACKET_REFRESH_RED"
--红包排行版返回
CHINESE_NEWYEAR_GIVE_REDPACKET_RANK_RSP = "CHINESE_NEWYEAR_GIVE_REDPACKET_RANK_RSP"
--更新福卡信息
CHINESE_NEWYEAR_UPDATE_FU_CARD_INFO = "CHINESE_NEWYEAR_UPDATE_FU_CARD_INFO"
-- -------------------------- 春节活动红包 END   ---------------------
-- ---------------------------劳动节集市活动 Start -----------------------
UPDATE_LABORDAY_ACCOUNT_BOOK_DATA = "UPDATE_LABORDAY_ACCOUNT_BOOK_DATA"--劳动节账本数据更新
UPDATE_LABORDAY_RANK_DATA = "UPDATE_LABORDAY_RANK_DATA"--劳动节排行榜数据更新
UPDATE_SELF_SELL_COUNT = "UPDATE_SELF_SELL_COUNT"--更新分享弹窗的售卖次数
UPDATA_MARKET_TOPIC_DATA = "UPDATA_MARKET_TOPIC_DATA"--更新劳动节基本数据（topic协议下发）
UPDATA_OTHER_MARKET_DATA = "UPDATA_OTHER_MARKET_DATA"--更新别人集市的数据
UPDATA_OTHER_MARKET_STATE_CHANGE = "UPDATA_OTHER_MARKET_STATE_CHANGE"--更新别人集市的状态
UPDATA_OTHER_MARKET_DATA_SELLTIMES = "UPDATA_OTHER_MARKET_DATA_SELLTIMES"--更新别人集市的数据之可售卖次数

UPDATE_LABORDAY_TEAMINFO = "UPDATE_LABORDAY_TEAMINFO"--小队数据更新
UPDATE_LABORDAY_TEAMMEMBER = "UPDATE_LABORDAY_TEAMMEMBER"--小队成员数据更新
LABORDAY_JOINTEAM = "LABORDAY_JOINTEAM"--加入小队
LABORDAY_QUITTEAM = "LABORDAY_QUITTEAM"--退出小队
LABORDAY_KICKTEAM = "LABORDAY_KICKTEAM"--踢人
PLAYER_SHARE_INFO_UPDATE = "PLAYER_SHARE_INFO_UPDATE"--分享信息更新
UPDATE_LABORDAY_FRIENDS = "UPDATE_LABORDAY_FRIENDS"

DAY_RABBIT_OPEN_SELL_WINDOW = "day_rabbit_open_sell_window" --劳动节弱引导
DAY_RABBIT_OPEN_CHAT_WINDOW = "day_rabbit_open_chat_window" --劳动节弱引导
DAY_RABBIT_SELL = "day_rabbit_sell"

-- ----------------------------劳动节集市活动 End-------------------------
-- -------------------------- 轮回秘宝 START ---------------------
UPDATE_TREASURE_RARE = "update_treasure_rare"
UPDATE_TREASURE_RARE_DATA = "update_treasure_rare_data"
UPDATE_TREASURE_RARE_MAKER_DATA = "update_treasure_rare_maker_data"
TREASURERARE_HAMMER_RSP = "treasure_hammer_rsp"
TREASURERARE_REWARD_RSP = "treasure_reward_rsp"
TREASURERARE_ENHANCE_RSP = "treasure_enhance_rsp"
TREASURERARE_ADVANCE_RSP = "treasure_advance_rsp"
TREASURERARE_COMMON_GIFT_DATA = "treasure_gift_data"
TREASURERARE_RED_REFRESH = "treasure_red_refresh"

tTreasureRareEnterHunt = "tTreasureRareEnterHunt"
cTreasureRareEnterHunt = "cTreasureRareEnterHunt"
tEventEnterTreasureRareEntrance = "tEventEnterTreasureRareEntrance"
cEventClickTreasureRareEntrance = "cEventClickTreasureRareEntrance"
tEventEnterTreasureRareForge = "tEventEnterTreasureRareForge"
cEventClickTreasureRareForge = "cEventClickTreasureRareForge"
tEventEnterTreasureRareForgeConfirm = "tEventEnterTreasureRareForgeConfirm"
cEventClickTreasureRareForgeConfirm = "cEventClickTreasureRareForgeConfirm"
tEventEnterTreasureRareGet = "tEventEnterTreasureRareGet"
cEventClickTreasureRareGet = "cEventClickTreasureRareGet"
-- -------------------------- 轮回秘宝 END   ---------------------
-- 主线推关 调整阵容顺序
ENTER_HERO_SWITCH_BATTLE_ARRAY = "enter_hero_switch_battle_array"
CLICK_HERO_ADJUST_BTN = "click_hero_adjust_btn"
--------------------------------- 英雄解构  START----------------
ENTER_HERO_DECONSTRUCTION = "enter_hero_deconstruction"
ENTER_SHOP_TIP = "enter_shop_tip"
ENTER_SHOP_DECONSTRUCTION = "enter_shop_deconstruction"
CLICK_HERO_DECONSTRUCTION = "click_hero_deconstruction"
CLICK_SHOP_TIP = "click_shop_tip"

--------------------------------- 英雄解构  END----------------


----------------------------做菜小游戏start---------------------------------------------
UPDATE_COOLTIME_OF_CREATEORDER = "update_cooltime_of_createorder" --倒计时冷却
UPDATE_MAKE_FOODS_GAME_ORDER_LIST = "update_make_foods_game_order_list" --更新订单列表
UPDATE_MAEK_FOODS_GAME_ORDER_CREATE_TIEMS = "update_make_foods_game_order_create_times" --更新交易建单剩余次数
UPDATE_MAKE_FOODS_GAME_ACCOUNT_BOOK_DATA = "update_make_foods_game_account_book_data"--更新账本数据

UPDATA_MAKE_FOOD_SHOW = "updata_make_food_show" --做菜菜品展示
UPDATA_MAKE_FOOD_MAKE_OPT = "updata_make_food_make_opt"  --做菜操作完成
UPDATA_COMM_ACTIVITY_RANK = "updata_comm_activity_rank"  --做菜游戏排行榜

MAKE_FOOD_OPEN_WINDOW = "make_food_open_window" --劳动节弱引导
MAKE_FOOD_OPEN_WINDOW_END = "make_food_open_window_end"
MAKE_FOOD_MAKEFUN = "make_food_makefun"
MAKE_FOOD_MAKEFUN_END = "make_food_makefun_end"
MAKE_FOOD_EXCHANGE = "make_food_exchange"
MAKE_FOOD_EXCHANGE_END = "make_food_exchange_end"

----------------------------做菜小游戏end-----------------------------------------

--------------------------------- ABTest AB包配置  START----------------
UPDATE_AB_TYPE = "update_ab_type" --玩家的ab类型改变
--------------------------------- ABTest AB包配置 END----------------

-- -------------------------- 星河神殿 START   ---------------------
STAR_TEMPLE_CLEAR_DATA = "STAR_TEMPLE_CLEAR_DATA" --清理数据缓存
STAR_TEMPLE_CLEAR_ENEMY_DATA = "STAR_TEMPLE_CLEAR_ENEMY_DATA" --清理怪物数据缓存
STAR_TEMPLE_REFRESH_INFO = "STAR_TEMPLE_REFRESH_INFO" --刷新信息
STAR_TEMPLE_OPTIONAL_HERO = "STAR_TEMPLE_OPTIONAL_HERO" --可选的英雄
STAR_TEMPLE_SELECT_HERO = "STAR_TEMPLE_SELECT_HERO" --选择英雄
STAR_TEMPLE_SELECT_ONE_HERO = "STAR_TEMPLE_SELECT_ONE_HERO" --选择一个英雄
STAR_TEMPLE_ENEMY = "STAR_TEMPLE_ENEMY" -- 获取敌人信息
STAR_TEMPLE_STORE_REFRESH = "STAR_TEMPLE_STORE_REFRESH"  --刷新商店
STAR_TEMPLE_GOD_EQUIP_OPERATION = "STAR_TEMPLE_GOD_EQUIP_OPERATION" --神器操作
STAR_TEMPLE_STORE_BUY = "STAR_TEMPLE_STORE_BUY" --商店中购买
STAR_TEMPLE_STORE_SALE = "STAR_TEMPLE_STORE_SALE" --商店中出售
enter_star_temple_guide = "enter_star_temple_guide" --进入星河神殿引导
click_star_temple_guide_here = "click_star_temple_guide_here" --这里是星河神殿点击
star_temple_check_mes_tip_start = "star_temple_check_mes_tip_start" --查看挑战信息提示开始
star_temple_check_mes_tip_end = "star_temple_check_mes_tip_end" --查看挑战信息提示结束
star_temple_free_count_tip_start = "star_temple_free_count_tip_start" --提示免费次数开始
star_temple_free_count_tip_end = "star_temple_free_count_tip_end" --提示免费次数开始
star_temple_go_select_start = "star_temple_go_select_start" --去选择英雄开始
star_temple_go_select_end = "star_temple_go_select_end" --去选择英雄结束
star_temple_build_tip_start = "star_temple_build_tip_start" --组建阵容提示开始
star_temple_build_tip_end = "star_temple_build_tip_end" --组建阵容提示开始
star_temple_select_tip_start = "star_temple_select_tip_start" --首次选择英雄提示开始
star_temple_select_tip_end = "star_temple_select_tip_end" --首次选择英雄提示结束
star_temple_store_guide_tip_start = "star_temple_store_guide_tip_start" --首次进入商店提示开始
star_temple_store_guide_sale_end = "star_temple_store_guide_sale_end" --首次商店销售物品提示

STAR_TEMPLE_REFRESH_MAIN_LAYER = "STAR_TEMPLE_REFRESH_MAIN_LAYER" --更新主界面的Layer
STAR_TEMPLE_REFRESH_FIGHT_COUNT = "STAR_TEMPLE_REFRESH_FIGHT_COUNT" -- 刷新挑战次数
STAR_TEMPLE_PLAY_BATTLE = "STAR_TEMPLE_PLAY_BATTLE"
STAR_TEMPLE_REFRESH_RED_DOT = "STAR_TEMPLE_REFRESH_RED_DOT" --红点刷新
STAR_TEMPLE_OPEN = "STAR_TEMPLE_OPEN"                       --活动开启
STAR_TEMPLE_IS_COMING_TO_END = "STAR_TEMPLE_IS_COMING_TO_END" --活动即将结束                      
STAR_TEMPLE_RECORDS = "STAR_TEMPLE_RECORDS"                  --出战记录
STAR_TEMPLE_TASK_REFRESH = "STAR_TEMPLE_TASK_REFRESH"        --任务刷新
STAR_TEMPLE_TASK_REWARD = "STAR_TEMPLE_TASK_REWARD"          --任务奖励效果
STAR_TEMPLE_TASK_SCORE = "STAR_TEMPLE_TASK_SCORE"           --任务积分
STAR_TEMPLE_INTEREST_GET = "STAR_TEMPLE_INTEREST_GET" --获取利息
STAR_TEMPLE_UPDATE_NEW_IMG = "STAR_TEMPLE_UPDATE_NEW_IMG" --new img
STAR_TEMPLE_TIME_UPDATE = "STAR_TEMPLE_TIME_UPDATE" --过了11点刷新主界面按钮状态
STAR_TEMPLE_NEXT_DAY = "STAR_TEMPLE_NEXT_DAY" --跨天
STAR_TEMPLE_UPDATE_SWITCH = "STAR_TEMPLE_UPDATE_SWITCH" --更新开关
STAR_TEMPLE_ACTIVITY_OVER = "STAR_TEMPLE_ACTIVITY_OVER" --活动结束
STAR_TEMPLE_GALAXYSTART = "STAR_TEMPLE_GALAXYSTART" --开始一轮竞技
STAR_TEMPLE_GALAXYFINISH = "STAR_TEMPLE_GALAXYFINISH" --结束一轮竞技
STAR_TEMPLE_GALAXYROUNDOPEN = "STAR_TEMPLE_GALAXYROUNDOPEN" --回合开始
STAR_TEMPLE_GALAXYROUNDCLOSE = "STAR_TEMPLE_GALAXYROUNDCLOSE" --回合结束
STAR_TEMPLE_GALAXYMONSTERCHALLENGE = "STAR_TEMPLE_GALAXYMONSTERCHALLENGE" --关卡挑战
STAR_TEMPLE_GALAXYREWARD = "STAR_TEMPLE_GALAXYREWARD" --获得奖励
STAR_TEMPLE_GALAXYTASKINTEGRAL = "STAR_TEMPLE_GALAXYTASKINTEGRAL" --任务积分变化
STAT_TEMPLE_LEVEL_RECORDS = "STAT_TEMPLE_LEVEL_RECORDS" --通关时的英雄信息
STAT_TEMPLE_RANK_RECORDS = "STAT_TEMPLE_RANK_RECORDS" --排行榜挑战记录
STAT_TEMPLE_AREAN_LIST = "STAT_TEMPLE_AREAN_LIST" --竞技场列表
STAT_TEMPLE_ROLE_INFO = "STAT_TEMPLE_ROLE_INFO" --角色信息
STAT_TEMPLE_ARENA_TRY_CHALLENGE = "STAT_TEMPLE_ARENA_TRY_CHALLENGE" --排行榜尝试挑战回调
STAT_TEMPLE_ARENA_RANK = "STAT_TEMPLE_ARENA_RANK" --赛区排行榜
STAT_TEMPLE_ARENA_RANK_REWARD = "STAT_TEMPLE_ARENA_RANK_REWARD" --排行榜奖励
STAT_TEMPLE_DAY_REWARD = "STAT_TEMPLE_DAY_REWARD" --每日奖励
STAT_TEMPLE_PARTS_NTF_UPDATE = "STAT_TEMPLE_PARTS_UPDATE" --部件数据ntf更新
STAT_TEMPLE_RANKINGS_NTF_UPDATE = "STAT_TEMPLE_RANKINGS_NTF_UPDATE" --离线玩家排名ntf更新
STAT_TEMPLE_RENA_OPEN_UPDATE = "STAT_TEMPLE_RENA_OPEN_UPDATE" --竞技场打开消息更新
STAT_TEMPLE_RANKINGS_CHALLENGE_UPDATE = "STAT_TEMPLE_RANKINGS_CHALLENGE_UPDATE" --点击竞技场挑战
STAT_TEMPLE_BTN_MASK_SHOW = "STAT_TEMPLE_BTN_MASK_SHOW" --特殊按钮加遮罩，防止连点
STAT_TEMPLE_ACROSS_DAY = "STAT_TEMPLE_ACROSS_DAY" --跨天
-- -------------------------- 星河神殿 End   ---------------------

------------------------------天命锦鲤 Start -----------------------
UPDATE_FATE_KOI_DATA = "UPDATE_FATE_KOI_DATA" --天命锦鲤数据更新
UPDATE_FATE_KOI_SETBIGPRIZE = "UPDATE_FATE_KOI_SETBIGPRIZE" --设置大奖
UPDATE_FATE_KOI_NEXTROUND = "UPDATE_FATE_KOI_NEXTROUND" --到下一轮大奖
UPDATE_FATE_KOI_GETREWARD = "UPDATE_FATE_KOI_GETREWARD" --抽到奖励
UPDATE_FATE_KOI_GETBLESSREWARD = "UPDATE_FATE_KOI_GETBLESSREWARD" --获取到祝福奖励
KOI_OPENBIGREWARDLIST_START = "koi_openbigrewardlist_start"
KOI_CHOOSEBIGREWARD_END = "koi_choosebigreward_end"
KOI_CHOOSEBIGREWARD_START = "koi_choosebigreward_start"
KOI_OPENBIGREWARDLIST_END = "koi_openbigrewardlist_end"
KOI_LOADEFFECT = "KOI_LOADEFFECT"--加载指定特效
KOI_UNLOADEFFECT = "KOI_UNLOADEFFECT"--卸载指定特效
-- ----------------------------天命锦鲤 End-------------------------
-- ----------------------------------端午节抽奖 Start-------------------------
UPDATE_DRAGONBOATGIFT_REWARD = "UPDATE_DRAGONBOATGIFT_REWARD" --抽奖返回
UPDATE_DRAGONBOATGIFT_RECHARGEAMOUNT = "UPDATE_DRAGONBOATGIFT_RECHARGEAMOUNT" --端午节累充
-- ----------------------------------端午节抽奖 end-------------------------
-----------------------------------巅峰赛---------------------------------------
UPDATE_PEAKGAME_CHAMPION_TIP = "update_peakgame_champion_tip"
RESPONSE_PEAKGAME_MISSION_BONUS = "response_peakgame_mission_bonus"
PEEKGAME_TOTAL = "peekgame_total"
PEEKGAME_SET_LINEUP = "peekgame_set_lineup"
PEEKGAME_SET_ORDER = "peekgame_set_order"
PEEKGAME_UPDATE_NICE_COUNT = "peekgame_update_nice_count"
PEEKGAME_UPDATE_ROOMDATA = "peekgame_update_roomdata"
PEEKGAME_UPDATE_GUESS = "peekgame_update_guess"
PEEKGAME_UPDATE_HERO = "peekgame_update_hero"
PEEKGAME_UPDATE_LINEUP = "peekgame_update_lineup"
PEEKGAME_CHANGE_ROOMINDEX = "peekgame_change_roomindex"
UPDATE_MATCH_PLACE_PEAKGAME_CHAMPION_TIP = "update_match_place_peakgame_champion_tip"
RECEIVE_PEAKGAME_RANK_REWARD = "receive_peakgame_rank_reward"
RECEIVE_PEAKGAME_RANK_NTF = "receive_peakgame_rank_ntf"
PEAKGAME_CHILD_MODULE = "peakgame_child_moduel"
---------------------------- 巅峰赛 END   --------------------------------------- ----------------------------------端午节抽奖 end-------------------------

-- -------------------------- 通用节日活动 START ---------------------
--- 节日活动皮肤礼包
FESTIVAL_SKIN_GIFT = "FESTIVAL_SKIN_GIFT"
-- -------------------------- 通用节日活动 END ---------------------

-- -------------------------- 原力武器 Start ----------------------------

UI_FORCE_BASE_CLOSE = "UI_FORCE_BASE_CLOSE" --基础界面关闭事件

WEAPON_LV_UPDATE = "WEAPON_LV_UPDATE" --武器等级更新
WEAPON_GEM_OPERATION_UPDATE = "WEAPON_GEM_OPERATION_UPDATE" --武器宝石操作更新
WEAPON_SKILL_UPDATE = "WEAPON_SKILL_UPDATE" --武器技能更新
WEAPON_CHANGE_BASE_SELECTED = "WEAPON_CHANGE_BASE_SELECTED" --改变英雄列表选中事件
WEAPON_CHANGE_BASE_SELECTED_TO_HEROBASE = "WEAPON_CHANGE_BASE_SELECTED_TO_HEROBASE" --改变英雄列表选中事件
WEAPON_RED_POINT_CHANGED = "WEAPON_RED_POINT_CHANGED" --红点数据刷新

START_FORCE_WEAPON_ENTER = "start_force_weapon_enter" --武器进入引导开始
CLICK_FORCE_WEAPON_ENTER = "click_force_weapon_enter" --武器界面进入点击

ENTER_FORCE_WEAPON_MAIN = "enter_force_weapon_main" --原力武器主界面引导
FORCE_WEAPON_MAIN_CLICK = "force_weapon_main_click" --原力武器主界面引导结束

FORCE_WEAPON_SLOT_OPEN = "force_weapon_slot_open" --原力武器插槽开启
FORCE_WEAPON_SLOT_CLICK = "force_weapon_slot_click" --原力武器插槽点击

FORCE_BASE_OPEN_TARGET = "FORCE_BASE_OPEN_TARGET" --原力基础窗开启指定窗口

FORCE_WEAPON_PACKDATA_UPDATE = "FORCE_WEAPON_PACKDATA_UPDATE" --原力武器用，背包数据更新事件

FORCE_BASE_UPGRADE_RED_UPDATE = "FORCE_BASE_UPGRADE_RED_UPDATE" --切页升级按钮红点更新事件
FORCE_BASE_EQUIP_RED_UPDATE = "FORCE_BASE_EQUIP_RED_UPDATE" --切页镶嵌按钮红点更新事件
FORCE_BASE_GEM_RED_UPDATE = "FORCE_BASE_GEM_RED_UPDATE" --切页宝石按钮红点更新事件

FORCE_WEAPON_MAIN_CLOSE_GEMLIST = "FORCE_WEAPON_MAIN_CLOSE_GEMLIST" --主界面关闭宝石列表
-- -------------------------- 原力武器 End ----------------------------

Evt_CollectSourceSuccess = "Evt_CollectSourceSuccess" --收集资源成功
-- -------------------------- 原力宝石 Start ----------------------------
UPDATE_FORCE_GEM_SYNTHESIS_MAIN_INFO = "UPDATE_FORCE_GEM_SYNTHESIS_MAIN_INFO"--更新合成宝石主界面信息
FORCE_GEM_COMPOSITE_REP = "FORCE_GEM_COMPOSITE_REP"--合成宝石后返回
FORCE_GEM_TAKE_OFF_REP = "FORCE_GEM_TAKE_OFF_REP"--脱下宝石返回
FORCE_GEM_EXCHANGE_REP = "FORCE_GEM_EXCHANGE_REP"--兑换宝石碎片返回
FORCE_GEM_SYNTHESIS_OPERATION = "FORCE_GEM_SYNTHESIS_OPERATION"--点击了合成按钮
FORCE_GEM_SYNTHESIS_ERROR_CODE = "force_gem_synthesis_error_code" --合成有错误码
FORCE_GEM_TOPIC_DATA_UPDATED = "FORCE_GEM_TOPIC_DATA_UPDATED"--原力宝石Topic数据更新
FORCE_GEM_LOCK_STATUS_RSP = "FORCE_GEM_LOCK_STATUS_RSP"--原力宝石更新锁定状态回复
FORCE_GEM_BATCH_COMPOSITE_REP = "FORCE_GEM_BATCH_COMPOSITE_REP"--原力宝石批量合成回复
-- -------------------------- 原力宝石 End ----------------------------


-- -------------------------- 次元之战 Start ----------------------------
DIMENSION_WAR_FINISH_PLAY_BACK = "DIMENSION_WAR_FINISH_PLAY_BACK"--次元之战战斗回放结束
DIMENSION_WAR_START_PLAY_BACK = "DIMENSION_WAR_START_PLAY_BACK"--次元之战战斗回放开始
DIMENSION_WAR_BOSS_FINISH_PLAY_BACK = "DIMENSION_WAR_BOSS_FINISH_PLAY_BACK"--次元之战Boss战斗回放结束
DIMENSION_WAR_BOSS_START_PLAY_BACK = "DIMENSION_WAR_BOSS_START_PLAY_BACK"--次元之战Boss战斗回放开始
DIMENSION_WAR_MiSSION_DATA_UPDATE = "dimension_war_mission_data_update"--任务数据刷新
DIMENSION_WAR_BOSS_UPGRADE_GRIDDATA = "DIMENSION_WAR_BOSS_UPGRADE_GRIDDATA"--刷新据点数据
DIMENSION_WAR_LOACATE_TO_MAP = "DIMENSION_WAR_LOACATE_TO_MAP" --定位到对应据点
DIMENSION_WAR_UPDATE_STATION_INFO = "DIMENSION_WAR_UPDATE_STATION_INFO" -- 获取要塞数据
DIMENSION_WAR_POINTS_RANK_RESPONSE = "DIMENSION_WAR_POINTS_RANK_RESPONSE" --奖励排行信息
DIMENSION_WAR_PUSH_DATA_SUCCESS = "DIMENSION_WAR_PUSH_DATA_SUCCESS" --拉取数据
DIMENSION_WAR_SIGN_UP_SUCCESS = "DIMENSION_WAR_SIGN_UP_SUCCESS" --报名成功
DIMENSION_WAR_ATTACK_TEAM = "DIMENSION_WAR_ATTACK_TEAM" --攻击要塞
DIMENSION_WAR_GARRISON_TEAM = "DIMENSION_WAR_GARRISON_TEAM" --迁移要塞
DIMENSION_WAR_SAVE_TEAM = "DIMENSION_WAR_SAVE_TEAM" --保存阵容
DIMENSION_WAR_SELF_TROOP = "DIMENSION_WAR_SELF_TROOP" --我的队伍
DIMENSION_WAR_BOSS_INFO = "DIMENSION_WAR_BOSS_INFO"--次元之战Boss信息
DIMENSION_WAR_PERIOD_DAY_CHANGED = "DIMENSION_WAR_PERIOD_DAY_CHANGED" --日期变更事件
DIMENSION_WAR_CLOSE_SCENE = "DIMENSION_WAR_CLOSE_SCENE"--关闭场景
DIMENSION_WAR_UPDATE_REDDOT = "dimension_war_update_reddot"--刷新红点数据
DIMENSION_WAR_REPORT_ATTACK = "DIMENSION_WAR_REPORT_ATTACK"--消耗行动力上报
DIMENSION_WAR_UPDATE_ALL_UNION_DATA = "DIMENSION_WAR_UPDATE_ALL_UNION_DATA"--更新所有联盟信息
DIMENSION_WAR_ALLIANCE_NUM = "DIMENSION_WAR_ALLIANCE_NUM"--更新联盟增益数量
DIMENSION_WAR_REWARD_TIP = "DIMENSION_WAR_REWARD_TIP"--奖励提示框
DIMENSION_WAR_PLAY_ALPHA_ANI = "DIMENSION_WAR_PLAY_ALPHA_ANI"--播放地块透明度动画
DIMENSION_WAR_GET_GATHER_RESPONSE = "DIMENSION_WAR_GET_GATHER_RESPONSE"--请求集结回复
DIMENSION_WAR_ALL_GATHER_RESPONSE = "DIMENSION_WAR_ALL_GATHER_RESPONSE"--请求所有集结点回复

-- -------------------------- 周年庆 Start ----------------------------
ANNIVERSARY_GRAB_DATA_RSP = "ANNIVERSARY_GRAB_DATA_RSP"--抢红包次数应答
ANNIVERSARY_GRABWORLDREDENV_DATA_RSP = "ANNIVERSARY_GRABWORLDREDENV_DATA_RSP" --全服抢红包回复
ANNIVERSARY_ADDGAMETIMES_DATA_RSP = "ANNIVERSARY_ADDGAMETIMES_DATA_RSP" --购买小游戏次数
ANNIVERSARY_GETREDENVGAME_DATA_RSP = "ANNIVERSARY_GETREDENVGAME_DATA_RSP" --获取红包小游戏奖励回复
-- -------------------------- 周年庆 End ----------------------------



DIMENSION_WAR_START = "dimension_war_start" --引导开始
DIMENSION_WAR_END = "dimension_war_end" --引导结束
DIMENSION_WAR_TEAM_START = "dimension_war_team_start" --引导开始
DIMENSION_WAR_TEAM_END = "dimension_war_team_end" --引导结束

-- -------------------------- 次元之战 End ----------------------------

--------------------------------- 轮盘抽奖  START----------------
UPDATE_ACTIVITI_RECHARGE_NUM = "update_activity_recharge_num" --更新面板充值金额
GET_TURNTABLE_REWARD = "get_turntable_reward" --更新面板充值金额
--------------------------------- 轮盘抽奖 END----------------

---------------邮箱订阅 start ---------------
UPDATE_EMAIL_SUBSCRIBE_REWARD_VIEW = "UPDATE_EMAIL_SUBSCRIBE_REWARD_VIEW" --更新领奖
---------------邮箱订阅 end ---------------
---
---------------小游戏合集 start ---------------
MINIGAME_SELECT_SWITCH = "MINIGAME_SELECT_SWITCH" --小游戏切换

---------------小游戏合集 end-----------------

-- -------------------------- 双旦活动 Start ----------------------------
--拼团信息更新
UPDATE_CHRISTMAS_GROUP_INFO = "UPDATE_CHRISTMAS_GROUP_INFO"
OPEN_CHRISTMAS_SHARE = "OPEN_CHRISTMAS_SHARE"
OPEN_CHRISTMAS_INVITE = "OPEN_CHRISTMAS_INVITE"
UPDATE_CHRISTMAS_GROUP_REWARD = "UPDATE_CHRISTMAS_GROUP_REWARD" --拼团奖励领取
--拼团引导事件注册
START_TEAM_ENTER = "start_team_enter"
CLICK_MASK_1 = "click_mask_1"
START_TEAM_STEP_2 = "start_team_step_2"
CLICK_MASK_2 = "click_mask_2"
-- 救援活动
PICK_THE_ROUTE_REWARD_RSP = "PICK_THE_ROUTE_REWARD_RSP" --当前关卡事件奖励领取
PICK_THE_ROUTE_FIRST_SHARE_RSP = "PICK_THE_ROUTE_FIRST_SHARE_RSP" --首次分享奖励
PICK_THE_ROUTE_ARCHIVED_SAVE_RSP = "PICK_THE_ROUTE_ARCHIVED_SAVE_RSP" --保存救援路径成功
-- -------------------------- 双旦活动 End ----------------------------
--=================================活动日历===================================
CALENDAR_JUMP_TO_ACTIVITY = "CALENDAR_JUMP_TO_ACTIVITY"

----------------------------做菜小游戏start---------------------------------------------
UPDATE_COOLTIME_OF_CREATEORDER = "update_cooltime_of_createorder" --倒计时冷却
UPDATE_MAKE_FOODS_GAME_ORDER_LIST = "update_make_foods_game_order_list" --更新订单列表
UPDATE_MAEK_FOODS_GAME_ORDER_CREATE_TIEMS = "update_make_foods_game_order_create_times" --更新交易建单剩余次数
UPDATE_MAKE_FOODS_GAME_ACCOUNT_BOOK_DATA = "update_make_foods_game_account_book_data"--更新账本数据

UPDATA_MAKE_FOOD_SHOW = "updata_make_food_show" --做菜菜品展示
UPDATA_MAKE_FOOD_MAKE_OPT = "updata_make_food_make_opt"  --做菜操作完成
UPDATA_COMM_ACTIVITY_RANK = "updata_comm_activity_rank"  --做菜游戏排行榜
UPDATA_MAKE_FOOD_MAKE_AUTO_OPT = "UPDATA_MAKE_FOOD_MAKE_AUTO_OPT"  --自动做菜操作完成

MAKE_FOOD_OPEN_WINDOW = "make_food_open_window" --劳动节弱引导
MAKE_FOOD_OPEN_WINDOW_END = "make_food_open_window_end"
MAKE_FOOD_MAKEFUN = "make_food_makefun"
MAKE_FOOD_MAKEFUN_END = "make_food_makefun_end"
MAKE_FOOD_EXCHANGE = "make_food_exchange"
MAKE_FOOD_EXCHANGE_END = "make_food_exchange_end"

----------------------------做菜小游戏end-----------------------------------------

----------------------------万圣装扮玩法start---------------------------------------------
UPDATE_HALLOWEEN_DAY_DRESS = "update_halloween_day_dress"--装扮界面更新
HALLOWEEN_DAY_DRESS_INFO_RSP = "halloween_day_dress_info_rsp" -- MSG_ALLSAINTSDAY_BASEINFO_RSP调用
UPDATE_QUICK_USE_NEW = "update_quick_use_new"--装扮界面更新
UPDATE_OTHER_PUMKIN_POP = "update_other_pumkin_pop"--进入分享界面刷新
UPDATE_HALLOWEEN_DRESS_PROGRESS = "update_halloween_dress_progress"--进入分享界面刷新
UPDATE_DRESS_GAME_ORDER_LIST = "update_dress_game_order_list"
----------------------------万圣装扮玩法end-----------------------------------------

----------------------------新春祝福start---------------------------------------
UPDATE_NEW_YEAR_BLESSING = "update_new_year_blessing"
----------------------------新春祝福end-----------------------------------------

---------------------------- 春节活动 Start ----------------------------
---画糖人
START_GUIDE_DRAW_SUGARMAN_ENTER = "start_guide_draw_sugarman_enter"     --进入画糖人开始按钮引导开始
CLICK_GUIDE_DRAW_SUGARMAN_ENTER = "click_guide_draw_sugarman_enter"     --进入画糖人开始按钮引导结束
START_GUIDE_DRAW_SUGARMAN_3_STAR = "start_guide_draw_sugarman_3_star"     --进入画糖人3星引导开始
CLICK_GUIDE_DRAW_SUGARMAN_3_STAR = "click_guide_draw_sugarman_3_star"     --进入画糖人3星引导结束
DRAW_THE_SUGAR_MAN_GET_REWARDS_RESP = "DRAW_THE_SUGAR_MAN_GET_REWARDS_RESP" -- 返回领取画糖人奖励
DRAW_THE_SUGAR_MAN_GET_REWARDS_RESP_ERROR = "DRAW_THE_SUGAR_MAN_GET_REWARDS_RESP_ERROR" -- 返回领取画糖人奖励失败
DRAW_THE_SUGAR_MAN_DATA_UPDATE = "DRAW_THE_SUGAR_MAN_DATA_UPDATE"       -- 画糖人数据变化通知
---活动入口
SHOW_MODULE_CHINESE_YEAR_ACTIVITY = "show_module_chinese_year_activity" -- 置顶春节活动入口界面
---迎春好礼
CHINESE_YEAR_ACTIVITY_GET_REWARD = "CHINESE_YEAR_ACTIVITY_GET_REWARD" -- 置顶春节活动入口界面
---------------------------- 春节活动 End ----------------------------

----------------------------新春存钱罐start---------------------------------------
PIGGYBANK_GET_EVENT = "piggybank_get_event"
PIGGYBANK_SAVE_EVENT = "piggybank_save_event"
----------------------------新春存钱罐end-----------------------------------------
------------------------------------刷新活动顶部切页物品--------------------------------------------
REFRESH_ACTIVITY_TOP_ITEMS = "REFRESH_ACTIVITY_TOP_ITEMS"--刷新活动顶部切页物品
---------------------------------刷新活动顶部切页物品--------------------------------------------
-- -------------------------- 轮回空间诅咒梦境 START ---------------------
REBIRTH_TEAM_RECOMMEND = "REBIRTH_TEAM_RECOMMEND"
UPDATE_REBIRTH_SPACE = "update_rebirth_space"--更新基础数据
UPDATE_REBIRTH_SPACE_RANK = "update_rebirth_space_rank"--更新排行榜
UPDATE_REBIRTH_SPACE_RANK_TOTAL_COUNT = "update_rebirth_space_rank_total_count"--更新排行榜数据长度

UPDATE_REBIRTH_SPACE_PALACE = "update_rebirth_space_palace"--更新轮回殿堂
UPDATE_REBIRTH_SPACE_BATTLE_RECORD = "update_rebirth_space_battle_record"
REBIRTH_TEAM_SORT = "REBIRTH_TEAM_SORT"
REBIRTH_PLAY_BATTLE = "REBIRTH_PLAY_BATTLE"
REBIRTH_RECOMMEND_TEAM = "REBIRTH_RECOMMEND_TEAM" -- 推荐阵容
REBIRTH_CHANGE_SHOW_ARTIFACT = "REBIRTH_CHANGE_SHOW_ARTIFACT" -- 显示神器
REBIRTH_REFRESH_ACHI_REDDOT = "REBIRTH_REFRESH_ACHI_REDDOT" --刷新成就红点
REBIRTH_SHOW_CHAMPION_CHANGE_TIP = "REBIRTH_SHOW_CHAMPION_CHANGE_TIP" -- 冠军弹窗

-- 引导事件
enter_adventure = "enter_adventure"
click_void_building = "click_void_building"
enter_adventure_mask = "enter_adventure_mask"
click_void_building_mask = "click_void_building_mask"
enter_void_championship2 = "enter_void_championship2"
click_rebirth_btn = "click_rebirth_btn"
enter_rebirth_main = "enter_rebirth_main"
click_first_tip = "click_first_tip"

-- -------------------------- 轮回空间诅咒梦境 END   ---------------------
-- -------------------------- 联盟宝箱 START   ---------------------
--联盟宝箱下一步
LEAGUE_REWARD_BOX_ACTION = "LEAGUE_REWARD_BOX_ACTION"
--回复某条宝藏历史记录
LEAGUE_REWARD_BOX_REFRESH_HISTORY = "LEAGUE_REWARD_BOX_REFRESH_HISTORY"
-- -------------------------- 联盟宝箱 END   ------------------------------------------------------刷新活动顶部切页物品--------------------------------------------
USE_ITEMS_SUCCESS = "USE_ITEMS_SUCCESS"  --使用物品返回 
----------------------------幸运翻牌start---------------------------------------
LUCK_TURN_OVER_CARD_START = "LUCK_TURN_OVER_CARD_START"   --开始翻牌游戏
LUCK_TURN_OVER_CARD_GET_CARD = "LUCK_TURN_OVER_CARD_GET_CARD"   --翻牌
LUCK_TURN_OVER_BASE_INFO = "LUCK_TURN_OVER_BASE_INFO"   --翻牌基本信息更新
LUCK_TURN_OVER_UPDATE_DATA = "LUCK_TURN_OVER_UPDATE_DATA"   --翻牌游戏数据刷新
LUCK_TURN_OVER_CLOSE_GET_REWARD = "LUCK_TURN_OVER_CLOSE_GET_REWARD"   --奖励界面关闭通知

LUCK_TURN_OVER_GUIDE1_START = "LUCK_TURN_OVER_GUIDE1_START"   --翻牌引导1开始
LUCK_TURN_OVER_GUIDE1_END = "LUCK_TURN_OVER_GUIDE1_END"   --翻牌引导1结束
LUCK_TURN_OVER_GUIDE2_START = "LUCK_TURN_OVER_GUIDE2_START"   --翻牌引导2开始
LUCK_TURN_OVER_GUIDE2_END = "LUCK_TURN_OVER_GUIDE2_END"   --翻牌引导2结束
----------------------------幸运翻牌end-----------------------------------------
USE_ITEMS_SUCCESS = "USE_ITEMS_SUCCESS"  --使用物品返回 
-----------------------------------巅峰赛---------------------------------------
UPDATE_PEAKGAME_CHAMPION_TIP = "update_peakgame_champion_tip"
RESPONSE_PEAKGAME_MISSION_BONUS = "response_peakgame_mission_bonus"
PEEKGAME_TOTAL = "peekgame_total"
PEEKGAME_SET_LINEUP = "peekgame_set_lineup"
PEEKGAME_SET_ORDER = "peekgame_set_order"
PEEKGAME_UPDATE_NICE_COUNT = "peekgame_update_nice_count"
PEEKGAME_UPDATE_ROOMDATA = "peekgame_update_roomdata"
PEEKGAME_UPDATE_GUESS = "peekgame_update_guess"
PEEKGAME_UPDATE_HERO = "peekgame_update_hero"
PEEKGAME_UPDATE_LINEUP = "peekgame_update_lineup"
PEEKGAME_CHANGE_ROOMINDEX = "peekgame_change_roomindex"
UPDATE_MATCH_PLACE_PEAKGAME_CHAMPION_TIP = "update_match_place_peakgame_champion_tip"
RECEIVE_PEAKGAME_RANK_REWARD = "receive_peakgame_rank_reward"
RECEIVE_PEAKGAME_RANK_NTF = "receive_peakgame_rank_ntf"
PEAKGAME_CHILD_MODULE = "peakgame_child_moduel"
PEAKGAME_UPDATE_VSDATA = "peakgame_update_vsdata"
PEAKGAME_MAKE_UP_NTF_DATA = "peakgame_make_up_ntf_data"
OPEN_PEAKGAME_SELECT_HERO_UI = "open_peakgame_select_hero_ui"
---------------------------- 巅峰赛 END   -------------------------------------

---------------------------------科研系统-----------------------------------------------
UPDATE_QUEUE01_TIME = "UPDATE_QUEUE01_TIME" --刷新组1的时间
UPDATE_QUEUE02_TIME = "UPDATE_QUEUE02_TIME" --刷新组2的时间

QUEUE01_FINISH = "QUEUE01_FINISH" --组1结束
QUEUE02_FINISH = "QUEUE02_FINISH" --组2结束

QUEUE_TIME_LEFT_FINISH = "QUEUE_TIME_LEFT_FINISH" --倒计时结束

UPDATE_QUEUE_STATE = "UPDATE_QUEUE_STATE" --刷新队列信息，带参。
UPDATE_QUEUE = "UPDATE_QUEUE" --刷新队列信息
UPDATE_TECHNOLOGY = "UPDATE_TECHNOLOGY" --刷新科研信息

GET_TECHNOLOGY = "GET_TECHNOLOGY"--主动领取一个科研。

SHOW_MINI_TECHNOLOGY_BUBBLE = "SHOW_MINI_TECHNOLOGY_BUBBLE" --展示科研小气泡
HIDE_MINI_TECHNOLOGY_BUBBLE = "HIDE_MINI_TECHNOLOGY_BUBBLE" --挂起科研小气泡

------------------------------加速相关--------------------------------------------------
SPEED_UP_SUCCESS_BY_USE_ITEM = "SPEED_UP_SUCCESS_BY_USE_ITEM" --使用道具加速成功
SPEED_UP_SUCCESS_BY_USE_DIAMOND = "SPEED_UP_SUCCESS_BY_USE_DIAMOND" --使用钻石加速成功

FINISH_TECHNOLOGY = "FINISH_TECHNOLOGY" --完成一个科研加速。

FINISH_BUILDING = "FINISH_BUILDING" --完成一个建筑加速。
FINISH_BUILDING_RIGHT_NOW = "FINISH_BUILDING_RIGHT_NOW" --立即完成一个建筑加速
UPDATE_ALL_BUILDING_QUEUE = "UPDATE_ALL_BUILDING_QUEUE" --刷新所有的建筑队列

FINISH_SOLDIER_TRAINING = "FINISH_SOLDIER_TRAINING" --完成一个兵营训练/晋升加速

FINISH_SOLDIER_TREATMENT = "FINISH_SOLDIER_TREATMENT" --完成一个士兵治疗加速
FINISH_SOLDIER_TREATMENT_RIGHT_NOW = "FINISH_SOLDIER_TREATMENT_RIGHT_NOW" --立即完成一个士兵治疗加速
--------------------------------编队相关---------------------------------------------
SAVE_SQUAD = "SAVE_SQUAD" --保存一个队列
UPDATE_SQUAD = "UPDATE_SQUAD" --刷新一个队列
UPDATE_SQUAD_ORDER = "UPDATE_SQUAD_ORDER" --刷新队列的order

UPDATE_TROOP_STATE = "UPDATE_TROOP_STATE" --队伍的空闲状态变更时

REFRESH_SQUAD = "REFRESH_SQUAD" --队列刷新成功后的回调

UPDATE_PLAYER_PROP_INFO = "UPDATE_PLAYER_PROP_INFO" --刷新玩家道具信息
--------------------------------无人机相关-------------------------------------------
UPDATE_MAGIC_WEAPON = "UPDATE_MAGIC_WEAPON" --立即刷新无人机信息
UPDATE_MAGIC_WEAPON_ITEM = "UPDATE_MAGIC_WEAPON_ITEM" --刷新无人机的道具状态
UPDATE_MAGIC_WEAPON_EQUIPMENT_SUCCESS = "UPDATE_MAGIC_WEAPON_EQUIPMENT_SUCCESS" --饰品升级成功
MAGIC_WEAPON_EQUIPMENT_EXCHANGE_SUCCESS = "MAGIC_WEAPON_EQUIPMENT_EXCHANGE_SUCCESS" --饰品转换成功
REFRESH_SHOW_MODEL = "REFRESH_SHOW_MODEL" -- 刷新无人机模型
--------------------------------士兵相关---------------------------------------------
START_MILITARY_SOLDIER = "START_MILITARY_SOLDIER" --开始训练士兵
FINISH_MILITARY_SOLDIER = "FINISH_MILITARY_SOLDIER" --本地士兵倒计时训练结束
GET_MILITARY_SOLDIER = "GET_MILITARY_SOLDIER" --成功领取训练的士兵

START_TREATMENT_SOLDIER = "START_TREATMENT_SOLDIER" --开始治疗士兵
FINISH_TREATMENT_SOLDIER = "FINISH_TREATMENT_SOLDIER" --本地士兵治疗倒计时结束
GET_TREATMENT_SOLDIER = "GET_TREATMENT_SOLDIER" --成功领取治疗的士兵

TRAIN_CENTER_UPDATE = "TRAIN_CENTER_UPDATE" --刷新校场
--------------------------------军备竞赛、积分及物品Slider弹窗----------------------------------
LOAD_RANK_LIST_FINISH = "LOAD_RANK_LIST_FINISH"
ON_RACE_CHANGE = "ON_RACE_CHANGE"
UPDATE_ARM_COMPETITION_PANEL = "UPDATE_ARM_COMPETITION_PANEL"
SHOW_POINT_SLIDER = "SHOW_POINT_SLIDER"
HIDE_POINT_SLIDER = "HIDE_POINT_SLIDER"
SHOW_ITEM_SLIDER = "SHOW_ITEM_SLIDER"
HIDE_ITEM_SLIDER = "HIDE_ITEM_SLIDER"
ON_POINT_UPDATE = "ON_POINT_UPDATE"
SHOW_POWER_UP_TIPS = "SHOW_POWER_UP_TIPS"
--------------------------------城建相关---------------------------------------------
FIREFIGHTING_CALLBACK = "FIREFIGHTING_CALLBACK" --灭火回调
START_BURNING = "START_BURNING" --起火了
START_RECOVER = "START_RECOVER" --灭火了
WALL_FIRE_END_TIME_CHANGE = "WALL_FIRE_END_TIME_CHANGE" --起火结束时间发生改变
WALL_HP_UPDATE = "WALL_HP_UPDATE" --城墙的HP刷新
GW_HOME_UPDATE_VISTING_SURVIVOR = "GW_HOME_UPDATE_VISTING_SURVIVOR" --刷新幸存者来访状态
GW_OPEN_VISTING_SURVIVOR_PANEL = "GW_OPEN_VISTING_SURVIVOR_PANEL" --打开幸存者界面
GW_HOME_CLOSE_VISTING_SURVIVOR_PANEL = "GW_HOME_CLOSE_VISTING_SURVIVOR_PANEL" --招募失败时关闭界面
GW_HOME_UPDATE_VISTING_VISITOR = "GW_HOME_UPDATE_VISTING_VISITOR" --刷新消息推送访客队列
--------------------------------用户账号---------------------------------------------
USER_DATA_RESET = "USER_DATA_RESET" --用户数据重置

NEW_BATTLE_START = "NEW_BATTLE_START" --新战斗开始(防止用老的出现问题)
NEW_BATTLE_BACK = "NEW_BATTLE_BACK" --新战斗返回(防止用老的出现问题)
New_UPDATE_PLAYER_PROP_INFO = "New_UPDATE_PLAYER_PROP_INFO" --刷新玩家属性信息(新 防止用老的出现问题)

LOGIN_ENTER_LOBBY = "LOGIN_ENTER_LOBBY" --登录成功进入大厅

--------------------------------小游戏---------------------------------------------
NEW_XYX_BUILD_SUCCESS = "NEW_XYX_BUILD_SUCCESS"---小游戏初始化成功
NEW_XYX_GET_REWARD = "NEW_XYX_GET_REWARD"---领取奖励
NEW_XYX_PASS_LEVEL = "NEW_XYX_PASS_LEVEL"---通关关卡
NEW_XYX_START_LEVEL = "NEW_XYX_START_LEVEL"---进入关卡
NEW_XYX_END_LEVEL = "NEW_XYX_END_LEVEL"---结束关卡
--------------------------------新任务------------------------------------------------
NEW_TASK_REFRESH = "NEW_TASK_REFRESH" --刷新任务

--------------------------------将军的试炼------------------------------------------------
GENERALTRIAL_REFRSHUI = "GENERALTRIAL_REFRSHUI"
TMSG_GENERALTRIAL_PERSONALDATA_NTF = "TMSG_GENERALTRIAL_PERSONALDATA_NTF"

--------------------------------位面2新VIP-----------------------------------------------
--领取每日礼包
GW_GET_DAILY_GIFT = "GW_GET_DAILY_GIFT"
--VIP专属礼包数据变化
GW_VIP_SPECIAL_GIFT_CHANGE = "GW_VIP_SPECIAL_GIFT_CHANGE"
--vip经验值变化
GW_VIP_EXP_CHANGE = "GW_VIP_EXP_CHANGE"
--vip等级提升
GW_VIP_LEVEL_UP = "GW_VIP_LEVEL_UP"
--vip时间变化
GW_VIP_TIME_CHANGE = "GW_VIP_TIME_CHANGE"
--领取VIP经验
GW_GET_VIP_EXP = "GW_GET_VIP_EXP"
--vip经验购买数变化
GW_VIP_EXP_NUM_CHANGE = "GW_VIP_EXP_NUM_CHANGE"

---------------充值物品发货通知（位面2新增）---------------------
--充值物品发货通知
GW_GET_RECHARGE_GOODS = "GW_GET_RECHARGE_GOODS"
--充值礼包数据刷新通知
GW_REFRESH_RECHARGE_GOODS = "GW_REFRESH_RECHARGE_GOODS"
--免费礼包领取回调
GW_GET_FREE_GIFT = "GW_GET_FREE_GIFT"
--持续奖励礼包领取回调
GW_GET_FOLLOWUP_GIFT = "GW_GET_FOLLOWUP_GIFT"

--解救人质 登录弹窗
BOMBERMAN_ENTER_FRESH = "BOMBERMAN_ENTER_FRESH"

--第一次登录进入游戏 （首充解救人质等弹窗时机用到）
FIRST_ENTER_GAME = "FIRST_ENTER_GAME"

--第一次进入主界面,开始登录弹窗
FIRST_ENTER_MAIN = "FIRST_ENTER_MAIN"

--第一次进入沙盘（阵营试炼时机）
FIRST_ENTER_SAND = "FIRST_ENTER_SAND"

-----------全屏特效-----------------------
FULL_SCREEN_EFFECT_PAUSE = "FULL_SCREEN_EFFECT_PAUSE"
FULL_SCREEN_EFFECT_RESUME = "FULL_SCREEN_EFFECT_RESUME"

------荣誉墙--------------------------

HONOR_WALL_UPGRADE_SUCCESS = "HONOR_WALL_UPGRADE_SUCCESS"
HONOR_WALL_DATA_UPDATE = "HONOR_WALL_DATA_UPDATE"

-----装饰物-------------------------
DECORATE_BUILDING_DATA_UPDATE = "DECORATE_BUILDING_DATA_UPDATE"
-----------跨服---------------------
ON_CROSS_SERVER_TIPS = "ON_CROSS_SERVER_TIPS"

-----登录弹窗结束后触发-------------------------
LOGIN_POPUP_END = "LOGIN_POPUP_END"
-------------------------

-----每日特惠通知刷新UI-------------------------
DALITY_GIFT_REFRESHUI = "DALITY_GIFT_REFRESHUI"
-------------------------

-----资源飘动动画事件-------------------------
TASK_TRACKING_ITEM_ANIM = "TASK_TRACKING_ITEM_ANIM"--主界面任务追踪资源动画
-------------------------
-- 打开主界面事件，使用ShowModule,View很多东西没有初始化完
OPEN_MAIN_SLG_VIEW = "OPEN_MAIN_SLG_VIEW"

-----橡果酒馆任务-------------------------
-- 获取酒馆信息回复
MSG_ACORNPUB_GET_INFO_RSP = "MSG_ACORNPUB_GET_INFO_RSP"
-- 掠夺任务回复
--MSG_ACORNPUB_GET_INFO_RSP = "MSG_ACORNPUB_GET_INFO_RSP" 
-- 刷新协助UI
REFRESH_TAVERN_STATEUI = "REFRESH_TAVERN_STATEUI"
-- 刷新任务回复
MSG_ACORNPUB_REFRESHTASK_RSP = "MSG_ACORNPUB_REFRESHTASK_RSP"
-- 超级刷新任务回复
MSG_ACORNPUB_SUPERREFRESHTASK_RSP = "MSG_ACORNPUB_SUPERREFRESHTASK_RSP"
-- 批量领取任务奖励回复
MSG_ACORNPUB_BATCHGETREWARD_RSP = "MSG_ACORNPUB_BATCHGETREWARD_RSP"
-- 刷新沙盘HUD - 协助 & 掠夺
MSG_REFRESH_SANDHUD = "MSG_REFRESH_SANDHUD"
-----橡果酒馆任务-------------------------
---------------------------限时礼包------------------------------------
UPDATE_TOPIC_EVENT = "update_topic_event"
UPDATE_SHOW_INDEX = "update_show_index"
BATTLE_CLOSE_SEND = "BATTLE_CLOSE_SEND"
------------------------------限时礼包---------------------------------

---------------------------闪金集市------------------------------------
UPDATE_SHINING_RED_DOT = "UPDATE_SHINING_RED_DOT"
------------------------------闪金集市---------------------------------

-----------------------新版本资源预下载资源start------------------------------------------
NEWVERRES_PRELOAD_DOWN_START = "NEWVERRES_PRELOAD_DOWN_START"   --新版本资源预下载开始
-----------------------新版本资源预下载资源end--------------------------------------------

-----------------------新版本场景状态机事件start------------------------------------------
GW_SCENE_CHANGE_SUCCESS = "GW_SCENE_CHANGE_SUCCESS"  ----场景切换成功事件,para1 目标场景
-----------------------新版本资源预下载资源end--------------------------------------------
---
------小游戏 ---------------------------------------------
SELECTED_HERO = "SELECTED_HERO"
SOLDIER_FIGHT = "SOLDIER_FIGHT"
MINI_SOLIDER_LEVEL = "MINI_SOLDIER_LEVEL"
SLOT_REMOVE_HERO = "SLOT_REMOVE_HERO"
EARLY_WARNING_CREATE = "EARLY_WARNING_CREATE"
EARLY_WARNING_REMOVE = "EARLY_WARNING_REMOVE"
EARLY_WARNING_UPDATE = "EARLY_WARNING_UPDATE"
CLOSE_REWARD_RESULT = "CLOSE_REWARD_RESULT"
---
----kingshot--------------------------------
WARNING_CREATE = "WARNING_CREATE"
WARNING_REMOVE = "WARNING_REMOVE"
WARNING_UPDATE = "WARNING_UPDATE"
--KINGSHOT剧情模式
KINGSHOT_HOME_MODE_START = "KINGSHOT_HOME_MODE_START"
KINGSHOT_HOME_MODE_END = "KINGSHOT_HOME_MODE_END"
------士兵突击游戏 ---------------------------------------------
MINIGAME_CLOSE = "MINIGAME_CLOSE"

BATTLE_RESULT_CLOSE = "battle_result_close"

MINIGAME_SCENE_LOAD_SUCCESS = "MINIGAME_SCENE_LOAD_SUCCESS"

MINIGAME_GET_REWARD_COIN = "MINIGAME_GET_REWARD_COIN"

MINIGAME_REWARD_FINISH_STAGE = "MINIGAME_REWARD_FINISH_STAGE" --士兵突击奖励关卡阶段奖励完成
----主线关卡过关事件 para1 win/lose   para2  类型 0/1/2  士兵突击游戏/卡片战斗/区域解锁
GW_LEVEL_PASS_EVENT = "GW_LEVEL_PASS_EVENT"
--主界面
--打开沙盘主界面
GW_OPEN_SAND_MAIN = "GW_OPEN_SAND_MAIN"

--关闭主界面不关闭顶部
GW_CLOSE_SAND_MAIN_NO_TOP = "GW_CLOSE_SAND_MAIN_NO_TOP"

--关闭主界面
GW_CLOSE_SAND_MAIN = "GW_CLOSE_SAND_MAIN"

EVENT_COMMON_POPUP_TRIGGER = "EVENT_COMMON_POPUP_TRIGGER"

---@field 功能开启初始化完成 string 在这前注册事件会导致每次登录都会触发一次
FUNCTION_OPEN_INIT_FINISH = "FUNCTION_OPEN_INIT_FINISH"

--退出战斗，关闭界面等触发 检测等待执行的方法
EVENT_COMMON_TRIGGER_WAIT_FUNC = "EVENT_COMMON_TRIGGER_WAIT_FUNC"

--UI的全屏状态发生变化 para== 全屏状态 bool值
UI_FULL_SCREEN_STATE_CHANGE = "UI_FULL_SCREEN_STATE_CHANGE" 

POPUPS_CHECK_STATE = "POPUPS_CHECK_STATE"

--登录弹窗关闭结束时机
LOGIN_UI_POPUP_END = "LOGIN_UI_POPUP_END" 
--沙盘entity数据改变(因为gw_comp_entity是异步创建的，这个时机是在entity)
EVENT_SAND_ENTITY_UPDATE = "EVENT_SAND_ENTITY_UPDATE"

--一次性下发大量数据时，会分帧处理comp，这边delay的时机
EVENT_SAND_COMP_ENTITY_UPDATE_DELAY = "EVENT_SAND_COMP_ENTITY_UPDATE_DELAY"
--一次性下发大量数据时，会分帧处理march，这边delay的时机
EVENT_SAND_MARCH_ENTITY_UPDATE_DELAY = "EVENT_SAND_MARCH_ENTITY_UPDATE_DELAY"

--当主界面的UI发生变化时广播
MAIN_SLG_UPDATE_UI = "MAIN_SLG_UPDATE_UI"

--主界面试炼按钮红点New重置
TRIAL_BUTTON_RED_CHANGE = "TRIAL_BUTTON_RED_CHANGE"
--刷新主界面竞技场气泡显示
REFRESH_ARENA_BUBBLE_SHOW = "REFRESH_ARENA_BUBBLE_SHOW"
--刷新竞技场挑战次数
REFRESH_ARENA_CHALLENGE_NUM = "REFRESH_ARENA_CHALLENGE_NUM"

-- 点赞记录信息回复
MSG_LIKE_RECORD = "MSG_LIKE_RECORD"
-- 发送感谢请求
MSG_GRATITUDE_OTHER = "MSG_GRATITUDE_OTHER"
--打开酒馆任务协助页面
MSG_OPEN_ACORNPUB_HELP = "MSG_OPEN_ACORNPUB_HELP"
--刷新可协助气泡
MSG_REFRESH_ACORNPUB_BUBBLE = "MSG_REFRESH_ACORNPUB_BUBBLE"
MSG_SETBUBBLESHOWSWITCH = "MSG_SETBUBBLESHOWSWITCH" 

------------------------------- 丧尸宝藏二期 start------------------------------
REFRESH_BUBBLE_MAIN_LINE = "REFRESH_BUBBLE_MAIN_LINE" 
REFRESH_BUBBLE_MAIN_LINE_RED = "REFRESH_BUBBLE_MAIN_LINE_RED"
BATTLEFINAL = "BATTLEFINAL"
REFRESH_HOOK_ENTRANCE = "REFRESH_HOOK_ENTRANCE"
SHOW_HOOK_STAR_ANIMATE = "SHOW_HOOK_STAR_ANIMATE"
ENABLE_CHECK_RED_FUNC = "ENABLE_CHECK_RED_FUNC"
------------------------------- 丧尸宝藏二期 end------------------------------
----专属客服（VIP入会）
EVENT_ECS_REFRESHECS = "EVENT_ECS_REFRESHECS"


--处于主城主界面
EVENT_ENTER_MAIN_CITY = "EVENT_ENTER_MAIN_CITY"

--通用奖励获得界面关闭通知
REWARD_RESULT_NEW_CLOSE = "REWARD_RESULT_NEW_CLOSE"

--流浪狗狗屋信息更新
STRAY_DOG_INFO_UPDATE = "STRAY_DOG_INFO_UPDATE"
--流浪狗礼包激活（购买第一个礼包）
STRAY_DOG_PACKAGE_ACTIVE = "STRAY_DOG_PACKAGE_ACTIVE"