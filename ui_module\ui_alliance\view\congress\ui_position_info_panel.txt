local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local type = type
local tonumber = tonumber

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_position_info_panel_binding"
local Common_Util = CS.Common_Util.UIUtil
local GWConst = require "gw_const"
local game_scheme = require "game_scheme"
local congress_data = require "congress_data"
local face_item = require "face_item_new"
local sprite_asset = require "card_sprite_asset"
local os = os
local time_util = require "time_util"
local player_mgr        = require "player_mgr"
local event = require "event"
local PlayerPrefs = CS.UnityEngine.PlayerPrefs

--region View Life
module("ui_position_info_panel")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)
    self.faceSpriteAsset = sprite_asset.CreateHeroAsset()
    self:InitScrollRectTable()
    self.VData = {}
    self.timeTicker = nil
    self.cdTimer = nil
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base:OnHide()
end

function UIView:Close()   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil
    if self.faceSpriteAsset then
        self.faceSpriteAsset:Dispose()
    end
    if self.timeTicker then
        util.RemoveDelayCall(self.timeTicker)
        self.timeTicker = nil
    end
    if self.cdTimer then
        util.RemoveDelayCall(self.cdTimer)
        self.cdTimer = nil
    end
    self.__base.Close(self)
end
--endregion

--region View Logic

function UIView:OnShowPanel(data,isOtherWorld,worldId)
    self.positionId = data
    self.isOtherWorld = isOtherWorld == true
    if self.isOtherWorld then
        self.worldId = worldId
    end
    self:OnUpdatePanel()
end

function UIView:InitScrollRectTable()
    self.srt_TakeOfficeListContent.onItemRender = OnItemRender;
    self.srt_TakeOfficeListContent.onItemDispose = function(scroll_rect_item,index)
        if scroll_rect_item then
            local ArrowUp = scroll_rect_item:Get("ArrowUp");
            local Delete = scroll_rect_item:Get("Delete");
            ArrowUp.onClick:RemoveAllListeners();
            Delete.onClick:RemoveAllListeners();
            if scroll_rect_item.data and scroll_rect_item.data[3] then
                scroll_rect_item.data[3]:Dispose()
            end
        end
    end
    self.srt_applyContent.onItemRender = OnItemRender2;
    self.srt_applyContent.onItemDispose = function(scroll_rect_item,index)
        if scroll_rect_item then
            local Agree = scroll_rect_item:Get("Agree");
            local Disagree = scroll_rect_item:Get("Disagree");
            Agree.onClick:RemoveAllListeners();
            Disagree.onClick:RemoveAllListeners();
            if scroll_rect_item.data and scroll_rect_item.data[3] then
                scroll_rect_item.data[3]:Dispose()
            end
        end
    end
    self.srt_JobBuffContent.onItemRender = OnItemRender3;
    self.srt_RecordContent.onItemRender = OnItemRender4;
    self.srt_RecordContent.onItemDispose = function(scroll_rect_item,index)
        if scroll_rect_item and scroll_rect_item.data then
            if scroll_rect_item.data[3] then
                scroll_rect_item.data[3]:Dispose()
                scroll_rect_item.data[3] = nil
            end
        end
    end
end

function OnItemRender4(scroll_rect_item,index,dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index;
    scroll_rect_item.data[2] = dataItem;
    Common_Util.SetActive(scroll_rect_item.gameObject,true)
    local name = scroll_rect_item:Get("name");
    local time = scroll_rect_item:Get("time");
    local headTran = scroll_rect_item:Get("headTran");
    local selectBg = scroll_rect_item:Get("selectBg");
    local Tips = scroll_rect_item:Get("Tips");

    name.text = dataItem.playerInfo.name;
    time.text = dataItem.time;
    Tips.text = dataItem.tipsStr;
    if not scroll_rect_item.data[3] then
        scroll_rect_item.data[3] = face_item:CFaceItem():Init(headTran, function(p)
            if not p then
                return
            end
        end, 1)
    end
    local faceStr = dataItem.playerInfo.faceID
    if dataItem.playerInfo.faceStr and not string.IsNullOrEmpty(dataItem.playerInfo.faceStr) then 
        faceStr = dataItem.playerInfo.faceStr
    end
    scroll_rect_item.data[3]:SetFaceInfo(faceStr,function()
        --self.showPlayerInfo()
    end)
    scroll_rect_item.data[3]:SetFrameID(dataItem.playerInfo.frameID, true)
end

function OnItemRender3(scroll_rect_item,index,dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index;
    scroll_rect_item.data[2] = dataItem;
    Common_Util.SetActive(scroll_rect_item.gameObject,true)
    local buffName = scroll_rect_item:Get("name");
    local value = scroll_rect_item:Get("value");
    local bg = scroll_rect_item:Get("bg");
    Common_Util.SetActive(bg,index % 2 == 0)
    buffName.text = lang.Get(dataItem.name)
    value.text = dataItem.value
end

function OnItemRender2(scroll_rect_item,index,dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index;
    scroll_rect_item.data[2] = dataItem;
    Common_Util.SetActive(scroll_rect_item.gameObject,true)
    local roleName = scroll_rect_item:Get("name");
    local timeText = scroll_rect_item:Get("time");
    local headTran = scroll_rect_item:Get("headTran");
    local Agree = scroll_rect_item:Get("Agree");
    local Disagree = scroll_rect_item:Get("Disagree");
    local SelfSelectBg = scroll_rect_item:Get("SelfSelectBg");
    local Tips = scroll_rect_item:Get("Tips")
    
    local selfAuthority = congress_data.OnGetSelfAuthority()
    local isPre = #selfAuthority > 0 and selfAuthority[2] == true--congress_data.OnGetSelfPositionID() == GWConst.enCongress_PositionType.enCongress_PositionType_Pres
    
    Common_Util.SetActive(Agree,isPre)
    Common_Util.SetActive(Disagree,isPre or dataItem.isSelf)
    
    if isPre then
        Agree.onClick:RemoveAllListeners();
        Agree.onClick:AddListener(
                function()
                    congress_data.OnApplyforMgr(dataItem.positionId,dataItem.data.playerInfo.roleID,true)
                end
        );
        Disagree.onClick:RemoveAllListeners();
        Disagree.onClick:AddListener(
                function()
                    congress_data.OnApplyforMgr(dataItem.positionId,dataItem.data.playerInfo.roleID,false)
                end
        );
        Common_Util.SetActive(SelfSelectBg,false)
    else
        Common_Util.SetActive(SelfSelectBg,dataItem.isSelf)
        if dataItem.isSelf then
            Disagree.onClick:RemoveAllListeners();
            Disagree.onClick:AddListener(
                function()
                    congress_data.OnSelfApplyfor(dataItem.positionId,false)
                end
            );
        end
    end
    if not scroll_rect_item.data[3] then
        scroll_rect_item.data[3] = face_item:CFaceItem():Init(headTran, function(p)
            if not p then
                return
            end
        end, 1)
    end
    local faceStr = dataItem.data.playerInfo.faceID
    if dataItem.data.playerInfo.faceStr and not string.IsNullOrEmpty(dataItem.data.playerInfo.faceStr) then 
        faceStr = dataItem.data.playerInfo.faceStr
    end
    scroll_rect_item.data[3]:SetFaceInfo(faceStr,function()

    end)
    scroll_rect_item.data[3]:SetFrameID(dataItem.data.playerInfo.frameID, true)
    roleName.text = dataItem.data.playerInfo.name;
    local value = PlayerPrefs.GetInt("CongressShowTimeType")
    local date1 = {};
    if value == 1 then
        date1 = os.date("*t", dataItem.data.time_Apply)--这个接口已经自动转成本地时间了
        Tips.text = lang.Get(602123)
    else
        local serverDate = time_util.GetLocalDate(dataItem.data.time_Apply)
        date1 = serverDate
        Tips.text = lang.Get(602124)
    end
    local startDay = string.format('%d-%02d-%02d %02d:%02d:%02d', date1.year, date1.month, date1.day,date1.hour, date1.min, date1.sec)
    timeText.text = startDay
    --buffEffect.text = dataItem.value;
end

function OnItemRender(scroll_rect_item,index,dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index;
    scroll_rect_item.data[2] = dataItem;
    Common_Util.SetActive(scroll_rect_item.gameObject,true)
    local roleName = scroll_rect_item:Get("name");
    local timeText = scroll_rect_item:Get("time");
    local headTran = scroll_rect_item:Get("headTran");
    local PresidentObj = scroll_rect_item:Get("PresidentObj");
    local ArrowUp = scroll_rect_item:Get("ArrowUp");
    local ArrowUpGray = scroll_rect_item:Get("ArrowUpGray");
    local Delete = scroll_rect_item:Get("Delete");
    local State = scroll_rect_item:Get("State");
    local Tips = scroll_rect_item:Get("Tips");

    local selfAuthority = congress_data.OnGetSelfAuthority()
    local isPre = #selfAuthority > 0 and selfAuthority[1] == true
    Common_Util.SetActive(PresidentObj,isPre)
    if isPre then
        Common_Util.SetActive(Delete,true)
        Common_Util.SetActive(ArrowUp,not dataItem.isInCd)
        Common_Util.SetActive(ArrowUpGray,dataItem.isInCd)
        ArrowUp.onClick:RemoveAllListeners();
        ArrowUp.onClick:AddListener(
            function()
                congress_data.OnApplyPosition(dataItem.positionId,dataItem.data.playerInfo.roleID)
            end
        );
        Delete.onClick:RemoveAllListeners();
        Delete.onClick:AddListener(
                function()
                    congress_data.OnCancelQueue(dataItem.positionId,dataItem.data.playerInfo.roleID,dataItem.data.playerInfo.leagueid)
                end
        );
        Common_Util.SetActive(State,dataItem.data.playerInfo.logoutTime == 0)
    else
        local isSelf = dataItem.data.playerInfo.roleID == player_mgr.GetPlayerRoleID()
        Common_Util.SetActive(Delete,isSelf)
        if isSelf then
            Delete.onClick:RemoveAllListeners();
            Delete.onClick:AddListener(
                    function()
                        congress_data.OnCancelQueue(dataItem.positionId,dataItem.data.playerInfo.roleID,dataItem.data.playerInfo.leagueid)
                    end
            );
        end
    end
    if not scroll_rect_item.data[3] then
        scroll_rect_item.data[3] = face_item:CFaceItem():Init(headTran, function(p)
            if not p then
                return
            end
        end, 1)
    end
    local faceStr = dataItem.data.playerInfo.faceID
    if dataItem.data.playerInfo.faceStr and not string.IsNullOrEmpty(dataItem.data.playerInfo.faceStr) then 
        faceStr = dataItem.data.playerInfo.faceStr
    end
    scroll_rect_item.data[3]:SetFaceInfo(faceStr,function()
        
    end)
    scroll_rect_item.data[3]:SetFrameID(dataItem.data.playerInfo.frameID, true)
    roleName.text = dataItem.data.playerInfo.name;
    local value = PlayerPrefs.GetInt("CongressShowTimeType")
    local date1 = {}
    if value == 1 then
        date1 = os.date("*t", dataItem.readyTime)
        Tips.text = lang.Get(671030)
    else
        date1 = time_util.GetLocalDate(dataItem.readyTime)
        Tips.text = lang.Get(602124)
    end
    
    local startDay = string.format('%d-%02d-%02d %02d:%02d:%02d', date1.year, date1.month, date1.day,date1.hour, date1.min, date1.sec)
    timeText.text = startDay
    --buffEffect.text = dataItem.value;
end

function UIView:OnSetRecord(list)
    local data = {}
    local len = 0
    for i,v in ipairs(list) do
        local value = PlayerPrefs.GetInt("CongressShowTimeType")
        local date1 = {}
        local date2 = {}
        local tipsStr = ""
        if value == 1 then
            date1 = os.date("*t", v.time_begin)
            date2 = os.date("*t", v.time_end)
            tipsStr = lang.Get(671030)
        else
            date1 = time_util.GetLocalDate(v.time_begin)
            date2 = time_util.GetLocalDate(v.time_end)
            tipsStr = lang.Get(602124)
        end
        
        local sendDay = string.format('%d-%02d-%02d %02d:%02d:%02d', date1.year, date1.month, date1.day,date1.hour, date1.min, date1.sec)
        
        local sendDay2 = string.format('%d-%02d-%02d %02d:%02d:%02d', date2.year, date2.month, date2.day,date2.hour, date2.min, date2.sec)
        local strings = {sendDay," - ",sendDay2}
        local playerInfo = v:HasField("playerInfo") and v.playerInfo or nil
        local temp =
        {
            time = table.concat(strings),
            playerInfo = playerInfo,
            tips = tipsStr,
        }
        table.insert(data,temp)
        len = len + 1
    end
    if len > 0 then
        self.srt_RecordContent.data = data
        self.srt_RecordContent:Refresh(0,-1)
        Common_Util.SetActive(self.obj_RecordEmpty,false)
        Common_Util.SetActive(self.obj_RecordContent,true)
    else
        Common_Util.SetActive(self.obj_RecordEmpty,true)
        Common_Util.SetActive(self.obj_RecordContent,false)
    end

end

function UIView:OnUpdatePanel()
    local positionCfg = game_scheme:CongressOfficialPosition_0(self.positionId)
    self.txt_jobName.text = lang.Get(positionCfg.PositionName)
    local positionData = self.isOtherWorld and congress_data.OnGetOtherOfficialList()[self.positionId] or congress_data.OnGetOfficialList()[self.positionId]
    local isEmpty = true;
    local getQueueListLen = self.isOtherWorld and (congress_data.OnGetOtherQueueListData()[self.positionId] and #congress_data.OnGetOtherQueueListData()[self.positionId] or 0) or (congress_data.OnGetQueueListData()[self.positionId] and #congress_data.OnGetQueueListData()[self.positionId] or 0)
    local queueSetting = congress_data.OnGetQueueSettingTime()
    if positionData and positionData:HasField("playerInfo") then
        isEmpty = false
        Common_Util.SetActive(self.obj_PlayerInfo,true)
        Common_Util.SetActive(self.obj_EmptyTips,false)
        
        local faceStr = positionData.playerInfo.faceID
        if positionData.playerInfo.faceStr and not string.IsNullOrEmpty(positionData.playerInfo.faceStr) then 
            faceStr = positionData.playerInfo.faceStr
        end
        self:SetStringFace(self.img_headIcon,faceStr)
        local strings = {}
        local ui_util = require "ui_util"
        local worldId = ui_util.GetWorldIDToShowWorldID(positionData.playerInfo.worldId,nil,ui_util.WorldIDRangeType.Normal)
        if positionData.playerInfo.leagueShortName and positionData.playerInfo.leagueShortName ~= "" then
            strings = {"#",worldId,"[",positionData.playerInfo.leagueShortName,"]",positionData.playerInfo.name}
        else
            strings = {"#",worldId,positionData.playerInfo.name}
        end
        self.txt_PlayerNameText.text = table.concat(strings);
        local timeLeft = os.server_time() - positionData.time_begin
        if self.timeTicker then
            util.RemoveDelayCall(self.timeTicker)
            self.timeTicker = nil
        end
        if timeLeft >= queueSetting and (getQueueListLen > 0) then --满上任时间
            self.timeTicker = util.DelayCallOnce(1,function() --延时一秒刷新
                congress_data.OnGetOfficialData(self.worldId)
                self.timeTicker = nil
            end)
        else
            self.timeTicker = util.IntervalCall(1,function()
                timeLeft = timeLeft + 1
                self.txt_timeLeft.text = string.format2(lang.Get(671107),time_util.FormatTime5(timeLeft)) ;
                if timeLeft >= queueSetting and (getQueueListLen > 0) then
                    congress_data.OnGetOfficialData(self.worldId)
                    if self.timeTicker then
                        util.RemoveDelayCall(self.timeTicker)
                        self.timeTicker = nil
                    end
                end
            end)
        end
    else
        Common_Util.SetActive(self.obj_PlayerInfo,false)
        Common_Util.SetActive(self.obj_EmptyTips,true)
        self:SetStringFace(self.img_headIcon,700)
        if getQueueListLen > 0 then
            --无人在任，却有人排队，则延迟1s后发送请求
            if self.timeTicker then
                util.RemoveDelayCall(self.timeTicker)
                self.timeTicker = nil
            end
            self.timeTicker = util.DelayCallOnce(1,function()
                congress_data.OnGetOfficialData(self.worldId)
                self.timeTicker = nil
            end)
        end
    end
    
    local buff = {}
    local cfg = game_scheme:CongressOfficialPosition_0(self.positionId)
    local log = require "log"
    if cfg then
        for i = 0,cfg.OfficialBonus.count - 1 do
            local buffCfg = game_scheme:GWMapBuff_0(cfg.OfficialBonus.data[i])
            if buffCfg then
                for k = 0,buffCfg.effect.count - 1 do
                    local buffEffect = game_scheme:GWMapEffect_0(buffCfg.effect.data[k])
                    if buffEffect then
                        local proToLang = game_scheme:ProToLang_0(buffEffect.nGroupID)
                        local strings = {"+",buffEffect.strParam[0]}

                        if proToLang then
                            if proToLang.isPercentage == 1 then
                                strings = {"+",buffEffect.strParam[0]/100,"%"}
                                --temp.value = "+"..buffEffect.strParam[0] / 10000 .. "%"
                            end
                            
                        end
                        local temp =
                        {
                            name = buffEffect.name,
                            value = table.concat(strings),
                        }
                        table.insert(buff,temp)
                    end

                end

            end
        end
    end
    self.srt_JobBuffContent.data = buff
    self.srt_JobBuffContent:Refresh(0,-1)

    if self.isOtherWorld == true then
        Common_Util.SetActive(self.obj_positionInfo,false)
    else
        Common_Util.SetActive(self.obj_positionInfo,true)
        local selfAuthority = congress_data.OnGetSelfAuthority()
        local isPre = #selfAuthority > 0 and selfAuthority[1] == true
        if isPre then
            Common_Util.SetActive(self.btn_ApplyJob,false)
            Common_Util.SetActive(self.txt_applyTimeTips,false)
            Common_Util.SetActive(self.btn_Self_Dissmiss,false)
            Common_Util.SetActive(self.btn_Dissmiss,not isEmpty)
            Common_Util.SetActive(self.obj_Dissmiss_Gray,isEmpty)
            if positionData then
                local timeLeft = positionData.time_appoint - os.server_time()
                if self.cdTimer then
                    util.RemoveDelayCall(self.cdTimer)
                    self.cdTimer = nil
                end
                if timeLeft > 0 then
                    Common_Util.SetActive(self.obj_Appoint_Gray,true)
                    Common_Util.SetActive(self.btn_Appoint,false)
                    self.txt_AppointTimer.text = time_util.FormatTime5(timeLeft);
                    self.cdTimer = util.IntervalCall(1,function()
                        timeLeft = timeLeft - 1
                        self.txt_AppointTimer.text = time_util.FormatTime5(timeLeft);
                        if timeLeft <= 0 then
                            if self.cdTimer then
                                util.RemoveDelayCall(self.cdTimer)
                                self.cdTimer = nil
                            end
                            Common_Util.SetActive(self.obj_Appoint_Gray,false)
                            Common_Util.SetActive(self.btn_Appoint,true)
                        end
                    end)
                else
                    Common_Util.SetActive(self.obj_Appoint_Gray,false)
                    Common_Util.SetActive(self.btn_Appoint,true)
                end
            else
                Common_Util.SetActive(self.obj_Appoint_Gray,false)
                Common_Util.SetActive(self.btn_Appoint,true)
            end

        else
            Common_Util.SetActive(self.btn_Dissmiss,false)
            Common_Util.SetActive(self.obj_Dissmiss_Gray,false)
            Common_Util.SetActive(self.obj_Appoint_Gray,false)
            Common_Util.SetActive(self.btn_Appoint,false)
            local selfApplyPos = congress_data.OnGetSelfApplyforId()
            --local isOnApply = congress_data.SelfInOnApply(self.positionId)
            if selfApplyPos == self.positionId then
                Common_Util.SetActive(self.btn_ApplyJob,false)
                Common_Util.SetActive(self.txt_applyTimeTips,true)
                self.txt_applyTimeTips.text = lang.Get(671067)
            elseif selfApplyPos ~= 0 then
                Common_Util.SetActive(self.btn_ApplyJob,false)
                Common_Util.SetActive(self.txt_applyTimeTips,true)
                local posCfg = game_scheme:CongressOfficialPosition_0(selfApplyPos)
                self.txt_applyTimeTips.text = string.format2(lang.Get(671095),lang.Get(posCfg.PositionName))
            else
                local selfPosition = congress_data.OnGetSelfPositionID()
                if selfPosition ~= 0 then
                    Common_Util.SetActive(self.txt_applyTimeTips,true)
                    Common_Util.SetActive(self.btn_ApplyJob,false)
                    if selfPosition == self.positionId then
                        self.txt_applyTimeTips.text = lang.Get(671108)
                    else
                        local posCfg = game_scheme:CongressOfficialPosition_0(selfPosition)
                        if posCfg then
                            self.txt_applyTimeTips.text = string.format2(lang.Get(671109),lang.Get(posCfg.PositionName))
                        else
                            log.Error("不存在ID为"..selfPosition.."的官职！请确认CongressOfficialPosition表的数据！")
                        end
                    end
                else
                    --local queue = congress_data.OnGetQueueListData()[self.positionId] --判定自己是否正在排队？
                    local queue,index = congress_data.OnGetSelfQueueList();
                    if queue ~= 0 then
                        if queue == self.positionId then
                            local time_appoint = congress_data.OnGetOfficialList()[self.positionId]
                            if time_appoint then
                                local readyTime = time_appoint.time_begin + queueSetting * index
                                local date1 = {}

                                local value = PlayerPrefs.GetInt("CongressShowTimeType")
                                if value == 1 then
                                    date1 = os.date("*t", readyTime)--这个接口已经自动转成本地时间了
                                else
                                    local serverDate = time_util.GetLocalDate(readyTime)
                                    date1 = serverDate
                                end

                                --date1 = os.date("*t", readyTime)
                                local startDay = string.format('%d-%02d-%02d', date1.year, date1.month, date1.day)
                                local startTime = string.format('%02d:%02d:%02d',date1.hour, date1.min, date1.sec)
                                self.txt_applyTimeTips.text = string.format2(lang.Get(671090),startDay,startTime)
                                local timeLeft = readyTime - os.server_time()
                                if self.cdTimer then
                                    util.RemoveDelayCall(self.cdTimer)
                                    self.cdTimer = nil
                                end
                                if timeLeft > 0 then
                                    Common_Util.SetActive(self.btn_ApplyJob,false)
                                    Common_Util.SetActive(self.txt_applyTimeTips,true)
                                    self.cdTimer = util.IntervalCall(1,function()
                                        timeLeft = timeLeft - 1
                                        --time_util.FormatTime5(timeLeft));
                                        if timeLeft <= 0 then
                                            if self.cdTimer then
                                                util.RemoveDelayCall(self.cdTimer)
                                                self.cdTimer = nil
                                            end
                                            Common_Util.SetActive(self.btn_ApplyJob,true)
                                            Common_Util.SetActive(self.txt_applyTimeTips,false)
                                        end
                                    end)
                                else
                                    Common_Util.SetActive(self.btn_ApplyJob,true)
                                    Common_Util.SetActive(self.txt_applyTimeTips,false)
                                end
                            else
                                Common_Util.SetActive(self.btn_ApplyJob,false)
                                Common_Util.SetActive(self.txt_applyTimeTips,true)
                                local posCfg = game_scheme:CongressOfficialPosition_0(queue)
                                self.txt_applyTimeTips.text = string.format2(lang.Get(671095),lang.Get(posCfg.PositionName))
                                log.Error("排队信息有误，id为"..queue.."。该日志为辅助检查用")
                            end
                        else
                            Common_Util.SetActive(self.btn_ApplyJob,false)
                            Common_Util.SetActive(self.txt_applyTimeTips,true)
                            local posCfg = game_scheme:CongressOfficialPosition_0(queue)
                            self.txt_applyTimeTips.text = string.format2(lang.Get(671095),lang.Get(posCfg.PositionName))
                        end
                    else
                        local selfData = congress_data.OnGetSelfApplyforData()[self.positionId]
                        if selfData then
                            local timeLeft = selfData.time_ready - os.server_time()
                            if self.cdTimer then
                                util.RemoveDelayCall(self.cdTimer)
                                self.cdTimer = nil
                            end
                            if timeLeft > 0 then
                                Common_Util.SetActive(self.btn_ApplyJob,false)
                                Common_Util.SetActive(self.txt_applyTimeTips,true)
                                self.cdTimer = util.IntervalCall(1,function()
                                    timeLeft = timeLeft - 1
                                    self.txt_applyTimeTips.text = string.format2(lang.Get(671096),time_util.FormatTime5(timeLeft));
                                    --self.txt_applyTimeTips.text = string.format2(lang.Get(671090),time_util.FormatTime5(timeLeft));
                                    if timeLeft <= 0 then
                                        if self.cdTimer then
                                            util.RemoveDelayCall(self.cdTimer)
                                            self.cdTimer = nil
                                        end
                                        Common_Util.SetActive(self.btn_ApplyJob,true)
                                        Common_Util.SetActive(self.txt_applyTimeTips,false)
                                    end
                                end)
                            else
                                Common_Util.SetActive(self.btn_ApplyJob,true)
                                Common_Util.SetActive(self.txt_applyTimeTips,false)
                            end
                        else
                            Common_Util.SetActive(self.btn_ApplyJob,true)
                            Common_Util.SetActive(self.txt_applyTimeTips,false)
                            Common_Util.SetActive(self.btn_Self_Dissmiss,false)
                        end
                    end
                end

            end
        end
    end

    --self:UpdateQueue()
end

--申请记录
function UIView:UpdateApplyList()
    if self.isOtherWorld then
        return
    end
    local list = congress_data.OnGetApplyforData()[self.positionId]
    local roleID = player_mgr.GetPlayerRoleID()
    if list then
        local len = 0
        local temp = {}
        for i,v in ipairs(list.list) do
            len = len + 1;
            local tempData =
            {
                data = v,
                positionId = self.positionId,
                isSelf = roleID == v.playerInfo.roleID,
            }
            table.insert(temp,tempData)
        end
        if len <= 0 then
            Common_Util.SetActive(self.obj_ApplyListStateScrollList,false)
            Common_Util.SetActive(self.obj_ApplyListEmpty,true)
            Common_Util.SetActive(self.obj_RedDot,false)
        else
            Common_Util.SetActive(self.obj_ApplyListStateScrollList,true)
            Common_Util.SetActive(self.obj_ApplyListEmpty,false)
            self.srt_applyContent.data = temp;
            self.srt_applyContent:Refresh(0,-1)
            local selfAuthority = congress_data.OnGetSelfAuthority()
            local isPre = #selfAuthority > 0 and selfAuthority[2] == true
            Common_Util.SetActive(self.obj_RedDot,isPre)
        end
        self.txt_ApplyListTips.text = string.format("%s(%s/50)",lang.Get(108),len)
        
    else
        self.txt_ApplyListTips.text = string.format("%s(%s/50)",lang.Get(108),0)
        Common_Util.SetActive(self.obj_RedDot,false)
    end
end

--排队上阵记录
function UIView:UpdateQueue()
    if self.isOtherWorld then
        return
    end
    local time_appoint = congress_data.OnGetOfficialList()[self.positionId]
    local isInCd = false
    if time_appoint then
        isInCd = os.server_time() - time_appoint.time_appoint < 0
    end
    local timeBegin = time_appoint and time_appoint.time_begin or 0 --再包一层避免报空
    local queueSetting = congress_data.OnGetQueueSettingTime() --每一任要等多久
    --local timeLeft = queueSetting - (os.server_time() - time_appoint.time_begin) --这里意思是还有多久就要下任。
    local queue = congress_data.OnGetQueueListData()[self.positionId]
    
    if queue then
        local len = 0
        local temp = {}
        for i,v in ipairs(queue.list) do
            len = len + 1;
            local tempData =
            {
                data=v,
                isInCd = isInCd,
                positionId = self.positionId,
                readyTime = timeBegin + queueSetting * i
            }
            if not time_appoint or not time_appoint:HasField("playerInfo") then --当前没有上任者，取当前服务器时间作为上任时间
                tempData.readyTime = os.server_time() + queueSetting * i
            end
            table.insert(temp,tempData)
        end
        if len <= 0 then
            Common_Util.SetActive(self.obj_TakeOfficeListContent,false)
            Common_Util.SetActive(self.obj_TakeOfficeListEmpty,true)
        else
            Common_Util.SetActive(self.obj_TakeOfficeListContent,true)
            Common_Util.SetActive(self.obj_TakeOfficeListEmpty,false)
            self.srt_TakeOfficeListContent.data = temp;
            self.srt_TakeOfficeListContent:Refresh(0,-1)
        end
        self.txt_Auto_TakeOfficeTips.text = string.format("%s(%s/50)",lang.Get(671075),len)
    else
        Common_Util.SetActive(self.obj_TakeOfficeListContent,false)
        Common_Util.SetActive(self.obj_TakeOfficeListEmpty,true)
        self.txt_Auto_TakeOfficeTips.text = string.format("%s(%s/50)",lang.Get(671075),0)
    end
end

function UIView:SetFace(faceID)
    local faceImage = 700
    if faceID > 0 then
        local cfg_face = game_scheme:RoleFace_0(faceID)
        if not cfg_face then
            ----				 --print("<color=#FF0000>未找到对应头像配置，faceID = "..self.faceID..",临时用headID = 9201的头像代替</color>")
            cfg_face = game_scheme:RoleFace_0(9209)
        end
        faceImage = cfg_face.rivalType
    elseif faceID == -1 then
        faceImage = 700
    else
        local cfg_face = game_scheme:RoleFace_0(9209)
        faceImage = cfg_face.rivalType
    end

    self.faceSpriteAsset:GetSprite(faceImage, function(sprite)
        self.img_headIcon.sprite = sprite
    end)
end

--更改通用设置头像图标
function UIView:SetStringFace(transform,faceStr) 
    local faceNumber = tonumber(faceStr)
    if faceNumber then
        --self:SetFaceIcon(transform,faceStr)
        self:SetFace(faceNumber)
        return
    end
    local custom_avatar_mgr = require "custom_avatar_mgr"
    custom_avatar_mgr.SetAvatarIcon(transform,faceStr)
end

function UIView:OnShowRecord()
    event.Trigger(event.GAME_EVENT_REPORT, "Kongress_Job_record",{}) --打点上报
    Common_Util.SetActive(self.btn_TipsMask,true)
    Common_Util.SetActive(self.obj_RecordList,true)
end

function UIView:OnApplyRecord()
    event.Trigger(event.GAME_EVENT_REPORT, "Kongress_Application_List",{}) --打点上报
    Common_Util.SetActive(self.btn_TipsMask,true)
    Common_Util.SetActive(self.obj_ApplyList,true)
end

function UIView:OnHideRecord()
    Common_Util.SetActive(self.btn_TipsMask,false)
    Common_Util.SetActive(self.obj_RecordList,false)
    Common_Util.SetActive(self.obj_ApplyList,false)
end

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil)
        else
			window:LoadUIResource(ui_path, nil, nil, nil)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
