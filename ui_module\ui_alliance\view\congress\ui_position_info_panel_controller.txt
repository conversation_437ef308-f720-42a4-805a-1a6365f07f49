local require = require
local pairs = pairs     
local string = string
local table = table
local newClass = newclass
local type = type

local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"
local e_handler_mgr = require "e_handler_mgr"
local event = require "event"
local event_Congress_define = require("event_Congress_define")
local congress_data = require "congress_data"
local log = require "log"
local game_scheme = require "game_scheme"
local gw_common_util = require "gw_common_util"

--region Controller Life
module("ui_position_info_panel_controller")
local controller = nil
local UIController = newClass("ui_position_info_panel_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    self.CData = {}
    self.CData.positionID = data.positionID;
    self.CData.worldId = data.worldId or gw_common_util.GetSandZoneSandBoxSid();
    self.CData.isOtherWorld = data.worldId ~= gw_common_util.GetSandZoneSandBoxSid();

    self.minPositionID = 2;
    self.maxPositionID = 7;
    self.targetPlayer = nil;
    e_handler_mgr.TriggerHandler(self.view_name, "OnShowPanel",self.CData.positionID,self.CData.isOtherWorld,self.CData.worldId)
    if self.CData.isOtherWorld ~= true then
        e_handler_mgr.TriggerHandler("ui_position_info_panel", "UpdateApplyList")
        congress_data.OnGetApplyforList(self.CData.positionID)
        congress_data.OnGetOfficeRecord(self.CData.positionID)
    end
    congress_data.OnGetQueueList(self.CData.positionID)

end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close()   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.CData = nil

    self.__base.Close(self)
end

function UIController:AutoSubscribeEvents()
    event.Register(event_Congress_define.ON_CONGRESS_QUEUEUP_UPDATE,self.OnUpdateQueue)
    self.updateApplyQueue = function()
        self:OnUpdateApplyQueue()
    end
    event.Register(event_Congress_define.ON_CONGRESS_APPLYFOR_LIST_UPDATE,self.updateApplyQueue)
    self.updateAllPanel = function(_,msg)
        self:OnUpdateAllPanel()
    end
    event.Register(event_Congress_define.ON_CONGRESS_OFFICIAL_UPDATE,self.updateAllPanel)
    
    self.updatePanel = function()
        --self:OnUpdatePanel()
    end
    --event.Register(event_Congress_define.ON_CONGRESS_APPLYFOR_SELF_UPDATE,self.updatePanel)
    self.setRecord = function(_,msg)
        self:OnUpdateRecord(msg)
    end
    event.Register(event_Congress_define.TMSG_CONGRESS_OFFICE_RECORD_RSP,self.setRecord)
end

function UIController:AutoUnsubscribeEvents()
    event.Unregister(event_Congress_define.ON_CONGRESS_QUEUEUP_UPDATE,self.OnUpdateQueue)
    event.Unregister(event_Congress_define.ON_CONGRESS_APPLYFOR_LIST_UPDATE,self.updateApplyQueue)
    event.Unregister(event_Congress_define.ON_CONGRESS_OFFICIAL_UPDATE,self.updateAllPanel)
    event.Unregister(event_Congress_define.TMSG_CONGRESS_OFFICE_RECORD_RSP,self.setRecord)
    --event.Unregister(event_Congress_define.ON_CONGRESS_APPLYFOR_SELF_UPDATE,self.updatePanel)
end
--endregion

--region Controller Logic

function UIController:OnUpdateRecord(msg)
    e_handler_mgr.TriggerHandler("ui_position_info_panel", "OnSetRecord",msg.list)
end

function UIController:OnUpdatePanel()
    e_handler_mgr.TriggerHandler("ui_position_info_panel", "OnShowPanel",self.CData.positionID,self.CData.isOtherWorld,self.CData.worldId)
end

function UIController:OnUpdateAllPanel()
    --log.Error("刷新！！！！！")
    e_handler_mgr.TriggerHandler("ui_position_info_panel", "OnShowPanel",self.CData.positionID,self.CData.isOtherWorld,self.CData.worldId)
    congress_data.OnGetQueueList(self.CData.positionID)
    congress_data.OnGetApplyforList(self.CData.positionID)
    congress_data.OnGetOfficeRecord(self.CData.positionID)
end

function UIController:OnUpdateApplyQueue()
    e_handler_mgr.TriggerHandler("ui_position_info_panel", "UpdateApplyList")
    e_handler_mgr.TriggerHandler("ui_position_info_panel", "OnShowPanel",self.CData.positionID,self.CData.isOtherWorld,self.CData.worldId)
end

function UIController:OnUpdateQueue()
    e_handler_mgr.TriggerHandler("ui_position_info_panel", "UpdateQueue")
end

function  UIController:OnBtnCloseBtnClickedProxy()
    congress_data.OnGetOfficialData(self.CData.worldId) --关闭时请求一次刷新
	ui_window_mgr:UnloadModule(self.view_name)
end

function  UIController:OnBtnLeftArrowClickedProxy()
    self.CData.positionID = self.CData.positionID -1;
    if self.CData.positionID < self.minPositionID then
        self.CData.positionID = self.maxPositionID;
    end
    e_handler_mgr.TriggerHandler(self.view_name, "OnShowPanel",self.CData.positionID,self.CData.isOtherWorld,self.CData.worldId)
    congress_data.OnGetQueueList(self.CData.positionID)
    congress_data.OnGetApplyforList(self.CData.positionID)
    congress_data.OnGetOfficeRecord(self.CData.positionID)
end

function  UIController:OnBtnRightArrowClickedProxy()
    self.CData.positionID = self.CData.positionID + 1;
    if self.CData.positionID > self.maxPositionID then
        self.CData.positionID = self.minPositionID;
    end
    e_handler_mgr.TriggerHandler(self.view_name, "OnShowPanel",self.CData.positionID,self.CData.isOtherWorld,self.CData.worldId)
    congress_data.OnGetQueueList(self.CData.positionID)
    congress_data.OnGetApplyforList(self.CData.positionID)
    congress_data.OnGetOfficeRecord(self.CData.positionID)
end

function  UIController:OnBtnApplyJobClickedProxy()
    congress_data.OnSelfApplyfor(self.CData.positionID,true)
end

function  UIController:OnBtnRecordClickedProxy()
    e_handler_mgr.TriggerHandler(self.view_name, "OnShowRecord")
end

function  UIController:OnBtnShowOfficeListClickedProxy()
    e_handler_mgr.TriggerHandler(self.view_name, "OnApplyRecord")
end

function  UIController:OnBtnAppointClickedProxy()
    local queue = game_scheme:CongressOfficialPosition_0(self.CData.positionID)
    if queue then
        ui_window_mgr:ShowModule("ui_official_appointment_panel",nil,nil,queue)
    end
end

function  UIController:OnBtnDissmissClickedProxy()
    local player_mgr = require "player_mgr"
    local value = player_mgr.GetDismissCongressTips()
    if value then
        local message_box = require "message_box"
        message_box.SetUISkin("ui/prefabs/uimessagebox.prefab")
        local positionCfg = game_scheme:CongressOfficialPosition_0(self.CData.positionID)
        local positionPlayer = congress_data.OnGetOfficialList()[self.CData.positionID]
        if positionCfg and positionPlayer and positionPlayer.playerInfo then
            local title = lang.Get(671092)
            local desc = string.format2(lang.Get(671093),positionPlayer.playerInfo.name,lang.Get(positionCfg.PositionName))
            message_box.Open(desc, message_box.STYLE_YESNO,function(d,r)
                if r == message_box.RESULT_YES then
                    congress_data.OnDissmissPosition(self.CData.positionID)
                end
            end,nil,lang.Get(30708), lang.Get(30707), title,nil, true, nil, nil, nil, nil, nil, nil,lang.Get(182147),
                    function(isOn)
                        player_mgr.SetDismissCongressTips(not isOn)
                    end)
        else
            if not positionCfg then
                log.Error("不存在ID为"..self.CData.positionID.."的官职！")
            elseif not positionPlayer or not positionPlayer.playerInfo then
                log.Error("ID为"..self.CData.positionID.."的职位上没有玩家数据！")
            end
        end
    else
        congress_data.OnDissmissPosition(self.CData.positionID)
    end

end

function  UIController:OnBtnTipsMaskClickedProxy()
    e_handler_mgr.TriggerHandler(self.view_name, "OnHideRecord")
end

function  UIController:OnBtnSelf_DissmissClickedProxy()
    
end
--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
