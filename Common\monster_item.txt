--简易头像 只有头像、品质框
local require = require
local typeof = typeof

local game_scheme = require "game_scheme"
local sprite_asset = require "card_sprite_asset"
local log = require "log"
local util = require "util"
local typeof = typeof
local string = string
local lang = require "lang"
local Vector3 = CS.UnityEngine.Vector3
local UIUtil = CS.Common_Util.UIUtil

module("monster_item")

local entityResPath = "ui/prefabs/gw/gw_common/monster_item.prefab"

local M = {}

M.widget_table = 
{
	-- 通用UI
	newiconImg = {path = "icon", type = "Image"},                        -- 头像图标
	frame = {path = "Frame", type = "Image"},
    battleFlag = {path = "battleFlag", type = "Image"},
    nameText = {path = "name", type = "Text"},
    button = {path = "", type = "Button"}
}

function M:ctor(selfType)
    
    faceSpriteAsset = faceSpriteAsset or sprite_asset.CreateGWSandMonsterIcon()
    self.monsterID = nil             -- 怪物配置id
    self.monsterCfg = nil            -- 怪物配置
    self.callMonsterBack = nil
    self.isShowName = true
    self.isShowBattleFlag = false
    --self.actorLv = 0              -- 玩家等级
    --self.isShowLv = false		  -- 是否显示等级
end

function M:Init(parentTrans, callback, scale)
    self:LoadResource(entityResPath, "",
    function()
        if callback then
            callback()
        end
        self.scale = scale or 1
        if scale then
            self.transform.localScale = { x = scale, y = scale, z = scale }
        else
            self.transform.localScale = Vector3.one
        end

        self.transform.anchoredPosition3D = {x=0, y= 0, z= 0}
		self.transform.anchorMin = {x=0.5, y= 0.5}
		self.transform.anchorMax = {x=0.5, y= 0.5}

        if self.monsterID then
            self:DisplayInfo()
        end
        self:RegistEvents()
    end,
    true,
    parentTrans
    )
    return self
end

--[[设置头像数据
@param monsterID 头像配置ID
@param callback 点击回调函数
]]
function M:SetMonsterInfo(monsterID,clickCallback)
    self.monsterID = monsterID
    self.monsterCfg = game_scheme:SandMapMonster_0(monsterID)
    if not self.monsterCfg then
        log.Error("monsterCfg is nil, monsterID = ",monsterID)
        return
    end
    if self.UIRoot then
        self:DisplayInfo()
        self.callMonsterBack = clickCallback
    end
end

-- [[头像数据显示]]
function M:DisplayInfo()
    if not self.monsterID then
        return
    end
    self:SetMonsterIcon(self.monsterID)	
    self:ShowMonsterName(self.isShowName)
    self:ShowBattleIcon(self.isShowBattleFlag)
end

--[[更改头像图标]]
function M:SetMonsterIcon(_monsterID)
    self.monsterID = _monsterID
    if not self.UIRoot then
        return
    end
    faceSpriteAsset:GetSprite(self.monsterCfg.icon,
            function(sprite)
                if self and self.newiconImg and not self.newiconImg:IsNull() then
                    self.newiconImg.sprite = sprite
                end
            end)
end

function M:ShowMonsterName(isShow)
    self.isShowName = isShow
    if not self.UIRoot then
        return
    end
    UIUtil.SetActive(self.nameText,isShow == true)
    if self.nameText then
        self.nameText.text = string.format("Lv.%s %s",self.monsterCfg.level,lang.Get(self.monsterCfg.name))
    end
end

function M:ShowBattleIcon(isShow)
    self.isShowBattleFlag = isShow
    if not self.UIRoot then
        return
    end
    if self.battleFlag then
        UIUtil.SetActive(self.battleFlag,self.isShowBattleFlag)
    end
end

function M:RegistEvents()
    self.onClickMonster = function(...)
        if self.callMonsterBack then
            self.callMonsterBack(self)
        end
    end
    self.button.onClick:AddListener(self.onClickMonster)

    --event.Register(event.SCREEN_SIZE_CHANGED, self.OnScreenSizeChanged)
end

function M:Dispose()
    self.isDisposed = true
    self.monsterID = nil             -- 头像配置id
    self.callMonsterBack = nil
    self.monsterCfg = nil   
    self.isShowName = true
    self.isShowBattleFlag = false
    --self.actorLv = 0              -- 玩家等级
    if self.onClickMonster and self.button and not util.IsObjNull(self.button) then
        self.button.onClick:RemoveListener(self.onClickMonster)
        self.onClickMonster = nil
    end
    self.__base:Dispose()
end

local class = require "class"
local base_game_object = require "base_game_object"
CMonsterItem = class(base_game_object, nil, M)