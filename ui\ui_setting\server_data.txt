local require = require
local pairs = pairs
local ipairs = ipairs
local string = string
local print = print
local tonumber = tonumber
local tostring = tostring
local table = table
local math = math
local os = os
local type = type

local event = require "event"
local game_config = require "game_config"
local json               = require "dkjson"
local log = require "log"
local util = require "util"
local http_inst = require "http_inst"
local preload_resources = require "preload_resources"
local ReviewingUtil = require "ReviewingUtil"
local IsReviewing = ReviewingUtil.IsReviewing()
local files_version_mgr = require "files_version_mgr"

local Application = CS.UnityEngine.Application
local RuntimePlatform = CS.UnityEngine.RuntimePlatform
local AssetBundleManager = CS.War.Base.AssetBundleManager
local serverUpdateJson = AssetBundleManager.serverUpdateJson
local DynamicServerInfo = CS.War.Script.ServerInfo
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local Time = CS.UnityEngine.Time
local AlgorithmUtils = CS.War.Common.AlgorithmUtils
local Config = CS.War.Base.Config
local BaseLoader = CS.War.Base.BaseLoader
local AssetsUpdator = CS.AssetsUpdator
local ABPathKey = CS.War.Common.ABPathKey
local time_util = require "time_util"
local val = require "val"
module("server_data")

local M = {}
local tcplist = {}
local lslJson = "local_server_list.json"

--登陆链接的枚举
CONNECT_TYPE_UDP = 0
CONNECT_TYPE_TCP = 1
CONNECT_TYPE_MAX = 2
CONNECT_TYPE_WEB = 3

local enable_user_terms = true
local reqtime = 3--服务器列表请求时长
local localReqTime = 10
-- {
--     "server_list" : [
-- 		{
-- 			"tcp_ip_port":["gateway3069.szgla.com:23069"],
-- 			"udp_ip_port":["gateway3069.szgla.com:23069"],
-- 			"real_ip":["************:23069"],
-- 			"server_id":3069,
-- 			"server_name":"Avengers 12",
-- 			"region_id":2,
-- 			"is_repaired":false,
-- 			"is_new":true
--         },
        
--     ],
-- 	"connect_test" : [
-- 		{
-- 			"ip":["http://source-nginx.q1.com/status.json"],
-- 			"domain":["http://test-nginx.q1.com/status.json"]
-- 		}
-- 	]
-- }
-- tcp_ip_port 是目前客户端tcp连接网关的配置,目前用的这个配置
-- udp_ip_port 是客户端udp连接网关的配置，早期cbb用过一段时间，目前位面是关闭了这种方式
-- real_ip 是tcp_ip_port dns解析后的网关ip，主要做ip测试连通性的配置用
local req_serverData = true
local stt = util.RegisterConsole("req_serverData",1,function ( st )
	req_serverData = st == 1
end)

local use_webUrlLogin = true
util.RegisterConsole("use_webUrlLogin",1,function ( st )
	use_webUrlLogin = st == 1
end)

function GetConsoleWebLogin()
    return use_webUrlLogin
end


function StartReqData_Test()
    req_serverData = true
    ReqData()
end

function Init()
    if M.bInit then
        return
    end

    local jd = DynamicServerInfo.Instance.mConfigJson
    if jd then
        M.data = json.decode(jd)
        ParseSource()
        -- log.Warning("zzd______DynamicServerInfo.Instance.mConfigJson：",jd)
    end
    local it_sec = 1200
    M.ticker = util.DelayCallOnce(it_sec,function ()
        ReqData(true)
        return it_sec
    end)


    if IsReviewing then
        return
    end
    if serverUpdateJson == nil then
        return
    end
    -- log.Warning("zzd______updateinfo：",serverUpdateJson)
    local updateinfo = json.decode(serverUpdateJson)
    if not updateinfo then 
        log.Warning("server_data no server updatejson")
        return
    end

    UpdateUserTermsInfo(updateinfo)
end

local reqTimes = 0 --reqData Times
local reqTimesMax = 0

local sw_req_serverData_by_Gate

local function CanReqServerDataByGate()
    --如果是审核服，直接返回false
    if IsReviewing then
        return false,1
    end
    if Application.isEditor then
        return false,2
    end
    if val.IsTrue("sw_req_serverData_by_Gate") then
        return true,3
    end
    local worldId = PlayerPrefs.GetInt("worldid", 0)
    --不是新包
    if worldId and  worldId > 0 then
        return false,4
    end
    local url_operation_mgr = require "url_operation_mgr"
    return  url_operation_mgr.GetConfig("enable_server_list_by_gate") == true,5
end
function ReqData(dontcallback)
    local retType
    sw_req_serverData_by_Gate,retType =  CanReqServerDataByGate()
    log.WarningWithLayer(-1,"Mark1GetSDByGate:",sw_req_serverData_by_Gate,retType)
    local files_version_mgr = require "files_version_mgr"
    if not files_version_mgr.GetWaitServerList() and not req_serverData then
        log.Error("not req server data")
        preload_resources.CheckFinishAll(3)
        return
    end

    log.Warning("zzd_____ReqData()___ reqTimes = ",reqTimes)
    -- 这里清除数据会有隐患，如果数据回来的晚，导致服务器数据没有了
    --tcplist = {}
    local server_info_url = nil
    if serverUpdateJson == nil then
        if Application.isEditor then
            -- 编辑器模拟模式下不需要下载 update json 文件
            log.Warning("Unity 模拟模式下不下载 update json")
            preload_resources.CheckFinishAll(3)
            return
        end
        -- log.LoginWarning("update json 为空,将影响登录流程")

        local files_version_mgr = require "files_version_mgr"
        server_info_url = files_version_mgr.GetServerInfoUrl()
        if IsReviewing then
            server_info_url = files_version_mgr.GetReviewingServerInfoUrl()
        end
        if server_info_url == nil then
            preload_resources.CheckFinishAll(3)
            log.Error("update json 为空，且无法获取更新链接，影响登录流程")
            return
        else
            server_info_url = tostring(server_info_url)
        end
    else
        local updateinfo = json.decode(serverUpdateJson)
        if not updateinfo then 
            log.Warning("server_data no server updatejson")
            preload_resources.CheckFinishAll(3)
            log.Error("update json 解析失败,将影响登录流程")
            return
        end
        server_info_url = updateinfo.server_info_url
        if not server_info_url then
            log.Warning("zzd____server_data no server info url")
            event.RecodeTrigger("local_server_list", {ww_state = "no_server_info_url"})
            CheckCurWorldIDInTcplist(dontcallback)
            return
        end
        if IsReviewing then
            server_info_url = updateinfo.reviewing_server_info_url
        end

        UpdateUserTermsInfo(updateinfo)
    end

    -- update.json和server_info.json的加密选项
    local encryptUpdateAndServerInfo = Config.IsTrue and Config.IsTrue("p_encryptUpdateAndServerInfo") and AlgorithmUtils.XorEncrypt
    if encryptUpdateAndServerInfo then
        server_info_url = string.gsub(server_info_url, ".json$", ".data")
        log.Warning("==== +++++++ server info:", server_info_url)
    end

    local second = math.floor(os.time()/ 180) 
    local tmpUrl = server_info_url .. "?r="..second
    http_inst.Req_Timeout(tmpUrl,reqtime,function ( str, hasError, bytes )
        log.Warning("server info data:", str)
        if hasError then
            log.Warning("server_data Req_Timeout", tmpUrl, hasError, encryptUpdateAndServerInfo and "" or str)
            util.DelayCallOnce(1, function (  )
                log.Warning("zzd_____ReqData() hasError util.DelayCallOnce___ reqTimes = ",reqTimes)
                event.RecodeTrigger("local_server_list", {ww_state = "ReqData_hasError"})

                if sw_req_serverData_by_Gate then
                    if reqTimes >= reqTimesMax then
                        SetGateGetServerListSW(true)
                        log.WarningWithLayer(-1,"Mark1GetSDByGate: 获取CDN ServerList Fail 准备通过网关获取服务器list ")
                        event.RecodeTrigger("GetSDByGate", {gate_state = "ReqCDNUrlTimeout"})
                        LoadLocalServerData(dontcallback)
                        return
                    end
                elseif files_version_mgr.GetEnableReadLocalServerList() then
                    if reqTimes >= reqTimesMax then						
                        LoadLocalServerData(dontcallback)
                        return
                    end
                end
                ReqData(dontcallback)
                reqTimes = reqTimes + 1
            end)
        else
            if encryptUpdateAndServerInfo then
                local lt = Time.realtimeSinceStartup
                str = AlgorithmUtils.XorEncrypt(bytes) -- 字节数组在lua能直接当string用。
                log.Warning("==+++++++ time 1:" .. tostring(Time.realtimeSinceStartup - lt))
            end

            M.data = json.decode(str)
            local bSuccess = ParseSource()
            if bSuccess then
                -- if not dontcallback then
                --     preload_resources.CheckFinishAll(3)
                -- end

                -- event.Trigger(event.UPDATE_SERVER_LIST_INFO)

                PlayerPrefs.SetString("ServerData",str)
                log.Warning("server_info数据请求成功", tmpUrl, hasError, str)
                log.Warning("zzd____Successfully parsed the data in server_info_url and saved the data."..tmpUrl)
                log.Warning("zzd____PlayerPrefs.SetString:"..str)
                event.RecodeTrigger("local_server_list", {ww_state = "ReqData_ParseSuccess"})
                reqTimes = 0
                ReqData_1(dontcallback)
            else
                log.Warning("server_data parse error", tmpUrl, hasError, encryptUpdateAndServerInfo and "" or str)
                util.DelayCallOnce(1, function ()
                    log.Warning("zzd_____ReqData parse error util.DelayCallOnce___ reqTimes = ",reqTimes)
                    event.RecodeTrigger("local_server_list", {ww_state = "ReqData_ParseError"})
                    if files_version_mgr.GetEnableReadLocalServerList() then
                        if reqTimes >= reqTimesMax then
                            LoadLocalServerData(dontcallback)
                            return
                        end
                    end
                    ReqData(dontcallback)
                    reqTimes = reqTimes + 1
                end)
            end
        end
    end)

end

function ReqData_1(dontcallback)
    -- print("-----------ReqData_SA------server_data---------------------------")
    log.Warning("zzd_____ReqData_1()___ reqTimes = ",reqTimes)
    log.Warning("zzd_____ReqData_1()___ serverUpdateJson = ",serverUpdateJson)
    local server_info_url = nil
    if serverUpdateJson == nil then
        if Application.isEditor then
            -- 编辑器模拟模式下不需要下载 update json 文件
            log.Warning("Unity 模拟模式下不下载 update json")
            preload_resources.CheckFinishAll(3)
            return
        end
        -- log.LoginWarning("update json 为空,将影响登录流程")

        local files_version_mgr = require "files_version_mgr"
        server_info_url = files_version_mgr.GetServerInfoUrl1()
        
        -- if IsReviewing then
        --     server_info_url = files_version_mgr.GetReviewingServerInfoUrl()
        -- end
        if server_info_url == nil or IsReviewing then
            preload_resources.CheckFinishAll(3)
            log.Error("update json 为空，且无法获取更新链接，影响登录流程")
            return
        else
            server_info_url = tostring(server_info_url)
        end
    else
        -- print("------serverUpdateJson-------", serverUpdateJson)
        local updateinfo = json.decode(serverUpdateJson)
        if not updateinfo then 
            log.Warning("server_data no server updatejson")
            preload_resources.CheckFinishAll(3)
            log.Error("update json 解析失败,将影响登录流程")
            return
        end
        -- log.Warning("zzd_____ReqData_1()___ updateinfo = ",updateinfo)

        server_info_url = updateinfo.server_info_url1
        -- print("美区--------server_info_url1 = ",server_info_url)
        if not server_info_url then
            CheckCurWorldIDInTcplist(dontcallback)

            -- preload_resources.CheckFinishAll(3)
            -- log.Warning("server_data no server server_info_url1")
            -- event.Trigger(event.UPDATE_SERVER_LIST_INFO)
            return
        end

        -- server_info_url = updateinfo.server_info_url
        if IsReviewing then
            -- server_info_url = updateinfo.reviewing_server_info_url
            preload_resources.CheckFinishAll(3)
            --log.Error("审核服在server_info_url1存在时不需要再次读取reviewing_server_info_url")
            return
        end

        UpdateUserTermsInfo(updateinfo)
    end

    -- update.json和server_info.json的加密选项
    local encryptUpdateAndServerInfo = Config.IsTrue and Config.IsTrue("p_encryptUpdateAndServerInfo") and AlgorithmUtils.XorEncrypt
    if encryptUpdateAndServerInfo then
        server_info_url = string.gsub(server_info_url, ".json$", ".data")
        log.Warning("==== +++++++ server info 2:", server_info_url)
    end

    local second = math.floor(os.time()/ 180) 
    local tmpUrl = server_info_url .. "?r="..second
    http_inst.Req_Timeout(tmpUrl,reqtime,function ( str, hasError, bytes )
        log.Warning("server info data:", encryptUpdateAndServerInfo and "" or str)
        if hasError then
            log.Warning("server_data Req_Timeout", tmpUrl, hasError, str)
            util.DelayCallOnce(1, function (  )
                log.Warning("zzd_____ReqData_1() hasError util.DelayCallOnce___ reqTimes = ",reqTimes)
                if files_version_mgr.GetEnableReadLocalServerList() then
                    if reqTimes >= 1 then
                        LoadLocalServerData(dontcallback)
                        return
                    end
                end
                ReqData_1(dontcallback)
                reqTimes = reqTimes + 1
            end)
        else
            if encryptUpdateAndServerInfo then
                local lt = Time.realtimeSinceStartup
                str = AlgorithmUtils.XorEncrypt(bytes) -- 字节数组在lua能直接当string用。
                log.Warning("==+++++++ time 2:" .. tostring(Time.realtimeSinceStartup - lt))
            end

            M.data1 = json.decode(str)
            local bSuccess = ParseSource()
            if bSuccess then

                PlayerPrefs.SetString("ServerData1",str)
                -- log.Warning("zzd_______ ServerData1:", str)
                event.RecodeTrigger("local_server_list", {ww_state = "ReqData_ParseSuccess"})
                CheckCurWorldIDInTcplist(dontcallback)
                
                -- if not dontcallback then
                --     preload_resources.CheckFinishAll(3)
                -- end
                -- event.Trigger(event.UPDATE_SERVER_LIST_INFO)
            else
                log.Warning("server_data parse error", tmpUrl, hasError, encryptUpdateAndServerInfo and "" or str)
                util.DelayCallOnce(1, function ()
                    log.Warning("zzd_____ReqData_1() parse error util.DelayCallOnce___ reqTimes = ",reqTimes)
                    event.RecodeTrigger("local_server_list", {ww_state = "ReqData_ParseError"})
                    if files_version_mgr.GetEnableReadLocalServerList() then
                        if reqTimes >= 1 then
                            LoadLocalServerData(dontcallback)
                            return
                        end
                    end
                    ReqData_1(dontcallback)
                    reqTimes = reqTimes + 1
                end)
            end
        end
    end)

end

function LoadLocalServerData (dontcallback)
    log.Warning("zzd_____LoadLocalServerData")
    event.RecodeTrigger("local_server_list", {ww_state = "LoadLocalServerData_Enter"})
    local updateinfo = json.decode(serverUpdateJson)
    local has_server_info_url,has_server_info_url1 = updateinfo.server_info_url,updateinfo.server_info_url1
    local isPlayerPrefsData,isPlayerPrefsData1 = has_server_info_url and ParsePlayerPrefsData("ServerData", false),has_server_info_url1 and ParsePlayerPrefsData("ServerData1", true)

    local hasPlayerPrefsData = false
    if has_server_info_url and not has_server_info_url1 then
        hasPlayerPrefsData = isPlayerPrefsData
    elseif not has_server_info_url and has_server_info_url1 then
        hasPlayerPrefsData = isPlayerPrefsData1
    elseif has_server_info_url and has_server_info_url1 then
        hasPlayerPrefsData = isPlayerPrefsData and isPlayerPrefsData1
    end

    if hasPlayerPrefsData then
        log.Warning("zzd_____LoadLocalServerData ParsePlayerPrefsData_Success")
        DoCheckFinish(dontcallback)
        if #M.tcplist > 0 then
            log.Warning("zzd______Parse PlayerPrefs ServerData Success")
            event.Trigger(event.UPDATE_SERVER_LIST_INFO)
            event.RecodeTrigger("local_server_list", {ww_state = "ParsePrefs_tcplistright"})
        else
            log.Error("zzd____Parse PlayerPrefs ServerData fail")
            event.RecodeTrigger("local_server_list", {ww_state = "ParsePrefs_tcplistfail"})
        end
    else
        --这里尝试读取本地服务器列表文件并请求
        log.Warning("zzd_____LoadLocalServerData ParsePlayerPrefsData_Error")
        event.RecodeTrigger("local_server_list", {ww_state = "ParsePlayerPrefsData_Error"})
        local garble,path = LocalServerListPath()

        http_inst.Req_Timeout(path,localReqTime,function ( str, hasError, bytes )
            if HttpReqSuccess(hasError) then
                M.data = json.decode(GetDecryptorStr(str,bytes,garble))
                local isSuccess = "HttpJsonSuccess_"..tostring(ParseSourceSuccess())
                event.RecodeTrigger("local_server_list", {ww_state = isSuccess})                
            end
            DoCheckFinish(dontcallback)
        end)
    end
end

function GetDecryptorStr(str,bytes,garble)
    local fileStr
    if garble then
        if AssetsUpdator.Decryptor then
            local name = ABPathKey.Instance:Get(lslJson)
            if not name then
                log.Error("zzd_____ABPathKey.Instance:Get not")
            end
            fileStr = AssetsUpdator.Decryptor(bytes, name)
        end
        -- if fileStr then
        --     fileStr = str
        -- end
    else
        fileStr = str
    end
    return fileStr
end
function DoCheckFinish(dontcallback)    --dontcallback通常都为false，原来的逻辑用来elseif中判断是否执行preload_resources.CheckFinishAll(3)
    if not dontcallback then
        preload_resources.CheckFinishAll(3)
    end
end
function ParsePlayerPrefsData(serverData, isServerUrl1)  
    local ret = false  
    local serverDataJson = PlayerPrefs.GetString(serverData)
    log.Warning("zzd____ParsePlayerPrefsData:"..tostring(serverDataJson))   
    if #serverDataJson > 0 then
        log.Warning("zzd_____ParsePlayerPrefsData ParsePlayerPrefsData_Success")
        ret = true		
        if isServerUrl1 then
            M.data1 = json.decode(serverDataJson)
        else
            M.data = json.decode(serverDataJson)
        end
        local bParseDataSuccess = ParseSource()
        log.Warning("zzd____parse PlayerPrefs ServerData ",serverData,":",bParseDataSuccess)		
    else
        log.Warning("zzd_____ParsePlayerPrefsData ParseSource_Error")
    end
    local parseDataSuccess = tostring(serverData).." ParsePlayerPrefsData_"..tostring(ret)
    event.RecodeTrigger("local_server_list", {ww_state = parseDataSuccess})
    return ret
end
function LocalServerListPath()
    local jsonName = nil
    if AssetsUpdator.IsGarble then
        jsonName = AssetsUpdator.IsGarble(lslJson)
    end
    local garble = false
    if jsonName and jsonName ~= lslJson  then
        garble = true
    end

    local persistentRelativeUrl = BaseLoader.GetStreamAssetRelativePath()
    local serverListFilePath = persistentRelativeUrl .. "/" .. jsonName

    local path = serverListFilePath
    if Application.platform == RuntimePlatform.IPhonePlayer then
        path = "file://" .. path
    end
    log.Warning("zzd_____LocalServerListPath garble"..tostring(garble)..",path:"..tostring(path))
    return garble,path
end
function HttpReqSuccess(hasError)
    log.Warning("zzd_____HttpReqSuccess hasError:"..tostring(hasError))
    if hasError then
        event.RecodeTrigger("local_server_list", {ww_state = "http_inst_Req_Error"})
        return false
    end
    return true
end
function ParseSourceSuccess()
    if ParseSource() then
        log.Warning("zzd_______prase local serverList Success")
        event.Trigger(event.UPDATE_SERVER_LIST_INFO)
        return true
    else
        log.Error("zzd_______prase local serverList file read fail")
        return false
    end
end

function CheckCurWorldIDInTcplist(dontcallback)
    local cur_worldid = PlayerPrefs.GetInt("worldid")
    log.Warning("zzd_______cur_worldid = ",cur_worldid)
	event.RecodeTrigger("local_server_list", {ww_state = "cur_worldid_"..tostring(cur_worldid)})
    local tcplistCount = #M.tcplist
    local bExist = false
    if tcplistCount > 0 then
        for i = 1, #M.tcplist do
            if cur_worldid == M.tcplist[i].serverid then
                bExist = true
                log.Warning("zzd_________worldid is in Tcplist")
                event.RecodeTrigger("local_server_list", {ww_state = "worldidInTcplist"})
                DoCheckFinish(dontcallback)
                event.Trigger(event.UPDATE_SERVER_LIST_INFO)
                return
            end
        end
    end
    if not bExist then
        log.Warning("zzd_________Server not found by worldid, check to see if the local server file switch is open.")
        if files_version_mgr.GetEnableReadLocalServerList() then

            local garble,path = LocalServerListPath()

            http_inst.Req_Timeout(path,localReqTime,function ( str, hasError, bytes )
                if HttpReqSuccess(hasError) then
                    local data = json.decode(GetDecryptorStr(str,bytes,garble))
                    local bFoundInLocalServerList = false
                    for i,v in ipairs(data.server_list) do
                        if v.server_id == cur_worldid then
                            log.Warning("zzd______The current worldid was found in the local server file ")
                            bFoundInLocalServerList = true
                            local temptcplist = tcplist
                            tcplist = {}
                            table.insert(M.data.server_list,v)
                            local isSuccess = "ParseHttpLocalFile_"..tostring(ParseSourceSuccess())
                            event.RecodeTrigger("local_server_list", {ww_state = isSuccess})
                            if not isSuccess then
                                tcplist = temptcplist
                            end
                            DoCheckFinish(dontcallback)
                            return
                        end
                    end
                    if not bFoundInLocalServerList then
                        log.Warning("zzd________Current worldid in local server file Not found")
                        event.RecodeTrigger("local_server_list", {ww_state = "parse_local_server_list_notFound"})
                    end
                end
                DoCheckFinish(dontcallback)
            end)
        else
            log.Warning("zzd____Local server file function not turned on")
            DoCheckFinish(dontcallback)
            event.RecodeTrigger("local_server_list", {ww_state = "load_local_server_list_false"})
        end
    end
end

function ParseIpPort(ipport,iplist,portlist)
    -- 判断字符串是否以 "wss://" 开头
    local is_ws = false
    if string.sub(ipport, 1, 6) == "wss://" then
        -- 获取 "wss://" 后的内容
        if not string.find(string.sub(ipport, 7), ":") then
            ipport=ipport..":-1"
        end
        is_ws = true
    end
    if string.sub(ipport, 1, 5) == "ws://" then
        -- 获取 "wss://" 后的内容
        if not string.find(string.sub(ipport, 6), ":") then
            ipport=ipport..":-1"
        end
        is_ws = true
    end
    local sp = string.split(ipport,":")
    if #sp >1 then
        if is_ws and #sp>2 then
            table.insert(iplist,sp[1]..":"..sp[2])
            table.insert(portlist,tonumber(sp[3]))
        elseif tonumber(sp[2]) ~=nil then
            table.insert(iplist,sp[1])
            table.insert(portlist,tonumber(sp[2]))
        else
            log.Error("ipport:",ipport,"格式错误，检查端口是否正确!!!")
        end
    else
        log.Error("ParseIpPort error len",ipport)
    end
end



function ParseSource()
    
    if not M.data or not M.data.server_list then
        log.Warning("ParseSource:data==null")
        return
    end
    
    tcplist = {}
    local udplist = {}

    local repairedlist = {}

    if M.data.repaired and M.data.repaired[1] then
        if M.data.repaired[1].interval then
            for i, v in ipairs(M.data.repaired[1].interval) do
                if v[1] and v[2] then
                    for j = v[1], v[2] do
                        repairedlist[j] = true
                    end
                end
            end
        end

        if M.data.repaired[1].single then
            for i, v in ipairs(M.data.repaired[1].single) do
                repairedlist[v] = true
            end
        end
    end

    local taskTcpList = {}
    local maxServer = 0

    local allServerList = {M.data.server_list}
    if M.data1 and M.data1.server_list then
        allServerList = {M.data.server_list, M.data1.server_list}
    end
    
    for index, server_list in ipairs(allServerList) do
        for i,v in ipairs(server_list) do
            local ipinfo = {}
            ipinfo.ipList = {}
            ipinfo.portList = {}
            ipinfo.realIpList = {}
            ipinfo.realPortList = {}
            for ii,vv in ipairs(v.tcp_ip_port) do
                ParseIpPort(vv,ipinfo.ipList,ipinfo.portList)
            end
            
            for ii,vv in ipairs(v.real_ip) do
                ParseIpPort(vv,ipinfo.realIpList,ipinfo.realPortList)
            end
            ipinfo.serverid = v.server_id
            ipinfo.servername = v.server_name
            ipinfo.isnew = v.is_new
            ipinfo.isws = not not v.is_ws
            ipinfo.regionId = v.region_id
            ipinfo.isrepaired = v.is_repaired or repairedlist[v.server_id]
            ipinfo.index = i
            ipinfo.areaID = v.area_id
    
            local k,serverInfoFieldV
            for k,serverInfoFieldV in pairs(v) do
                ipinfo[k] = serverInfoFieldV
            end
            ParseNetServerOpenStatus(ipinfo, v)
            ParseNetServerWeight(ipinfo, v)
    
            table.insert(tcplist, ipinfo)
        end
    end
    

    M.tcplist = tcplist

    return true
end

--获取domain推荐服务器
function GetDomainRecommendServer(originWorldId, originServerInfo)
    local isReviewing = require("ReviewingUtil").IsReviewing()
    local url_operation_mgr = require"url_operation_mgr"
    local login_log = require("login_log")
    local game_switch = require("game_switch")
    local domainServerId = url_operation_mgr.GetConfig("recommendServerId")
    local disable_recommendserverid = game_switch.TryGetValue("disable_recommendserverid", false)
    local isDisable = disable_recommendserverid == true or disable_recommendserverid == "true"
    if not isDisable and not isReviewing and domainServerId and type(domainServerId) == "number" and domainServerId > originWorldId then
        login_log.Warning("use domainServerId: ", domainServerId, "originWorldId: ", originWorldId, "disable_recommendserverid:", disable_recommendserverid, originServerInfo == nil)
        local domainServerInfo
        if originServerInfo then
            domainServerInfo = DeepCopyTable(originServerInfo)
            domainServerInfo.id = domainServerId
            domainServerInfo.name = GetServerNameByServerId(domainServerId)
        end

        SaveDomainRecommendServer(domainServerId, originWorldId, originServerInfo)
        SaveDomainRecommendServerLocally(domainServerId, originWorldId, originServerInfo)
        event.Trigger(event.GAME_EVENT_REPORT, "DomainRecommendServerId", {
            domainRecommendServerId = domainServerId,
            originServerId =  originWorldId,
            isUseDomainServerId = 1,
        })
        return domainServerId, domainServerInfo
    end

    event.Trigger(event.GAME_EVENT_REPORT, "DomainRecommendServerId", {
        domainRecommendServerId = domainServerId,
        originServerId =  originWorldId,
        isUseDomainServerId = 0,
    })
    login_log.Warning("not use domainServerId: ", domainServerId, "originWorldId: ", originWorldId, "disable_recommendserverid:", disable_recommendserverid)
    return originWorldId, originServerInfo
end


--保存domain推荐服务器到缓存列表
function SaveDomainRecommendServer(domainServerId, originServerId, originData)
    local login_log = require("login_log")
    if not M.tcplist or #M.tcplist < 1 then
        login_log.Warning("M.tcplist is nil", domainServerId, originServerId)
        return
    end

    table.sort(M.tcplist, function(a, b)
        if a.serverid and b.serverid then
            return a.serverid > b.serverid
        end
        return false
    end)
    
    local maxServerId = M.tcplist[1].serverid
    local isContinuous = true
    local isExist, originServerInfo
    for i, v in ipairs(M.tcplist) do
        if maxServerId - (i - 1) ~= v.serverid then
            isContinuous = false
        end
        if not originServerInfo and v.serverid == originServerId then
            --针对内网可能存在的重复serverId
            if not originServerInfo or (v.isnew == originData.isnew and v.regionId == originData.regionId) then
                originServerInfo = v
            end
        elseif v.serverid == domainServerId then
            isExist = true
            break
        end
    end

    if isExist then
        login_log.Warning("domainServerId is exist in M.tcplist", domainServerId, originServerId)
        return
    end

    originServerInfo = originServerInfo or M.tcplist[1]
    if not isContinuous or maxServerId > domainServerId  then
        login_log.Warning("server_info config error, server_id is not continuous in M.tcplist", maxServerId, domainServerId, originServerId)
        local domainServerInfo = GetDomainServerInfo(originServerInfo, domainServerId)
        table.insert(M.tcplist, 1 , domainServerInfo)
    else
        local index = 1
        for i = domainServerId, maxServerId + 1, -1 do
            local domainServerInfo = GetDomainServerInfo(originServerInfo, i)
            table.insert(M.tcplist, index, domainServerInfo)
            index = index + 1
        end
    end
    login_log.Warning("SaveDomainRecommendServer success", domainServerId, originServerId, "originServerInfo:", json.encode(originServerInfo))
end

function GetDomainServerInfo(originServerInfo, serverId)
    local domainServerInfo = DeepCopyTable(originServerInfo)
    domainServerInfo.serverid = serverId
    domainServerInfo.servername = GetServerNameByServerId(serverId)
    domainServerInfo.server_id = serverId
    domainServerInfo.server_name = domainServerInfo.servername
    return domainServerInfo
end

--在本地保存domain推荐服务器
function SaveDomainRecommendServerLocally(domainServerId, originServerId, originData)
    local login_log = require("login_log")
    if not M.data or not M.data.server_list or #M.data.server_list < 1 then
        login_log.Warning("M.data or M.data.server_list is nil", domainServerId, originServerId)
        return
    end

    table.sort(M.data.server_list, function(a, b)
        if a.server_id and b.server_id then
            return a.server_id > b.server_id
        end
        return false
    end)

    local maxServerId = M.data.server_list[1].server_id
    local isContinuous = true
    local isExist, originServerInfo
    for i, v in ipairs(M.data.server_list) do
        if maxServerId - (i - 1) ~= v.server_id then
            isContinuous = false
        end
        if v.server_id == originServerId then
            --针对内网可能存在的重复serverId
            if not originServerInfo or (v.is_new == originData.isnew and v.region_id == originData.regionId) then 
                originServerInfo = v 
            end
        elseif v.server_id == domainServerId then
            isExist = true
            break
        end
    end
    
    if isExist then
        login_log.Warning("domainServerId is exist in M.data.server_list", domainServerId, originServerId)
        return
    end

    originServerInfo = originServerInfo or M.data.server_list[1]
    if not isContinuous or maxServerId > domainServerId then
        login_log.Warning("server_info config error, server_id is not continuous in M.data.server_list", maxServerId, domainServerId, originServerId)
        local domainServerInfo = GetDomainServerInfoForLocal(originServerInfo, domainServerId)
        table.insert(M.data.server_list, 1 , domainServerInfo)
    else
        local index = 1
        for i = domainServerId, maxServerId + 1, -1 do
            local domainServerInfo = GetDomainServerInfoForLocal(originServerInfo, i)
            table.insert(M.data.server_list, index, domainServerInfo)
            index = index + 1
        end
    end
    
    local jsonStr = json.encode(M.data)
    PlayerPrefs.SetString("ServerData", jsonStr)
    login_log.Warning("SaveDomainRecommendServerLocally success", domainServerId, originServerId, "ServerData: ", jsonStr)
end

function GetDomainServerInfoForLocal(originServerInfo, serverId)
    local domainServerInfo = DeepCopyTable(originServerInfo)
    domainServerInfo.server_id = serverId
    domainServerInfo.server_name = GetServerNameByServerId(serverId)
    return domainServerInfo
end

function GetServerNameByServerId(serverId)
    if serverId == 0 then return 0 end
    local digits = math.floor(math.log(serverId, 10)) 
    local divisor = 10^digits
    
    return string.format("S%s", math.floor(serverId % divisor) )
end

function DeepCopyTable(oldTable)
    if not oldTable then
        return 
    end
    local newTable = {}
    for key, val in pairs(oldTable) do
        if type(key) == "table" then
            key = DeepCopyTable(key)
        end
        if type(val) == "table" then
            newTable[key] = DeepCopyTable(val)
        else
            newTable[key] = val
        end
    end
    return newTable
end
function GetGateGetServerListSW()
    return M.get_server_list_sw
end

function SetGateGetServerListSW(active)
    M.get_server_list_sw = active
end
--@public 通过网关获取的服务器列表后推荐的新的worldID
function SetGateGetServerListCommendWorldId(worldId)
    M.get_server_list_commend_id = worldId
end

--@public 获取--通过网关获取的服务器列表后推荐的新的worldID
function GetGateGetServerListCommendWorldId()
    return M.get_server_list_commend_id 
end

--@public 设置--后台记录的登录worldID
function SetRecordWorldID(worldId)
    M.record_world_id = worldId
end

--@public 获取--后台记录的登录worldID
function GetRecordWorldID()
    return M.record_world_id
end

---@public 通过网关获取的服务器列表来设置登录的worldID
function SetGateServerList(js_data) 
    if not js_data or string.IsNullOrEmpty(js_data) then
        return false
    end
    local parse_data = json.decode(js_data)
    --解析失败或者未配置
    if not parse_data or not parse_data.server_list or #parse_data.server_list == 0 then
        return false
    end
    local needChange = true
    --这里修改成直接覆盖 --以最新为准，1，因为可能老服关闭了 2，同样的区服，但对应其中的字段属性可能变了
    M.data =  parse_data    
    --if not  M.data then
    --    M.data =  parse_data
    --    needChange = true
    --else
    --    --这时候合并处理，并不去覆盖      
    --    if not M.data.server_list then
    --        M.data.server_list ={}
    --    end
    --    for k,v in pairs(parse_data.server_list) do
    --        local isExist = false
    --        for i1, v1 in ipairs(M.data.server_list) do
    --            if v1.server_id == v.server_id then
    --                isExist = true
    --                break
    --            end
    --        end
    --        if not isExist then
    --            table.insert(M.data.server_list,v)
    --            needChange = true
    --        end
    --    end
    --end
    if not needChange then
        log.WarningWithLayer(-1,"Mark1GetSDByGate SetGateServerListing not neeed Change")
        event.RecodeTrigger("GetSDByGate", {gate_state = "SetGateServerList",ChangeType = 0})
        return true
    end
    local  bSuccess = ParseSource()
    if not bSuccess then
        return false
    end
    --设置worldID    
    local setting_server_data = require "setting_server_data"
    local lastWorldId = setting_server_data.GetLoginWorldID() or 0    
    setting_server_data.ClearNewestWorldInfo()
    local selectWorldID =  setting_server_data.GetNewestWorldInfo()
    local changeType = 0
    --如果这个设备以前登录过，哪怕卸载了，但仍然会走后台记录的，优先级最高，并不会使用新的推荐WorldId
    local record_world_id = GetRecordWorldID()
    local hasRecord = record_world_id and type(record_world_id) == "number" and record_world_id > 0
    --条件： 1，没有设备登录账号记录，2新推荐的和旧的不一样，且推荐的是有效的时候才设置
    if (not hasRecord) and selectWorldID and  selectWorldID > 0 and lastWorldId ~= selectWorldID then
        SetGateGetServerListCommendWorldId(selectWorldID)
        setting_server_data.SetLoginWorldID(selectWorldID)        
        --同时设置fromWorldID
        local net_login_module = require "net_login_module"
        net_login_module.SetLoginWorldID(selectWorldID)
		changeType = 1
    end   
    log.WarningWithLayer(-1,"Mark1GetSDByGate SetGateServerListing lastWorldId=",lastWorldId, "selectWorldID=",selectWorldID,"hasRecord",hasRecord)
    event.RecodeTrigger("GetSDByGate", {gate_state = "SetGateServerList",gate_lastWorldId = lastWorldId,gate_selectWorldId = selectWorldID,ChangeType = changeType,hasRecord = hasRecord})
    return true
end

function GetServerListForTest()
    if not M.data or not M.data.server_list then
        return {}
    end
    return M.data.server_list
end

function ParseNetServerWeight( parsedServerInfo, netServerInfo )
    if netServerInfo.weight == nil then
        parsedServerInfo.weight = 0
    else
        parsedServerInfo.weight = netServerInfo.weight
    end
end

function ParseNetServerOpenStatus( parsedServerInfo, netServerInfo )
    if netServerInfo.open_time == nil then
        parsedServerInfo.openTime = 0
        parsedServerInfo.newServerTime = 0
        parsedServerInfo.isOpenServer = true
        return
    end
    -- 局域网测试，误差 < 1 s
    -- log.Error("服务器时间与本地时间误差:",  time_util.UTCTime2Seconds(DateTime.UtcNow) - os.server_time(), " 秒")
    -- 服务器开启时间
    parsedServerInfo.openTime =  time_util.Str2Stamp(netServerInfo.open_time)
    -- 服务器标识为新服结束的时间
    parsedServerInfo.newServerTime =  time_util.Str2Stamp(netServerInfo.new_server_time)
    -- 新版逻辑中使用 openTime 和 newServerTime 来判断，并重写旧 isnew 标识，不再采用服务器列表中的 is_new 来标识
    local isOpenServer = IsServerOpen(parsedServerInfo)
    local isNewServer = (parsedServerInfo.newServerTime - os.server_time() > 0)
    -- if not isOpenServer then
    --     log.Error(parsedServerInfo.servername, "距离服务器开放:", parsedServerInfo.openTime - os.server_time(), '秒')
    -- end
    -- 重写新服务器标识
    if isOpenServer and isNewServer then
        parsedServerInfo.isnew = true
        -- log.Error(parsedServerInfo.servername.." is new server")
    else
        parsedServerInfo.isnew = false
    end
    parsedServerInfo.isOpenServer = isOpenServer
end

function IsServerOpen( serverInfo )
    return (serverInfo.openTime - os.server_time() <= 0)
end

function ModifyServerOpenStatus(serverList)
    local serverCount = #serverList
    local i,serverInfo
    for i=1,serverCount do
        serverInfo = serverList[i]
        if serverInfo.openTime > 0 then
            serverInfo.isOpenServer = IsServerOpen(serverInfo)
            local isNewServer = (serverInfo.newServerTime - os.server_time() > 0)
            if serverInfo.isOpenServer and isNewServer then
                serverInfo.isnew = true
            else
                serverInfo.isnew = false
            end
        end
    end
end

function UpdateServerTime()
    ModifyServerOpenStatus(GetTcpServerInfo())
end


function GetTcpServerInfo()
    -- if not M.data then
    --     return DynamicServerInfo.Instance:GetTcpServerInfo()
    -- end
    return M.tcplist or {}
end

function GetUdpServerInfo()
    -- if not M.data then
    --     return DynamicServerInfo.Instance:GetUdpServerInfo()
    -- end
    return M.udplist or {}
end

-- M.data内部数据，外部不要随便调用
function GetData()
    return M.data
end

function UpdateUserTermsInfo( updateinfo )
    -- log.Warning("zzd______UpdateUserTermsInfo：",updateinfo)
    
    if updateinfo.user_terms ~= nil then
        enable_user_terms = (not not updateinfo.user_terms)
    end
end

function IsEnableUserTerms()
    --国服包屏蔽协议
    if game_config.Q1SDK_DOMESTIC then
        return false
    end
    return enable_user_terms
end

function ReqServerList()
    ReqData(true)
end
event.Register(event.REQ_SERVER_LIST, ReqServerList)
Init()
