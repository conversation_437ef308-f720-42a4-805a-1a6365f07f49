-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
local error_code_pb=require("error_code_pb")
local tbs_pb=require("tbs_pb")
local common_new_pb=require("common_new_pb")
local common_pb=require("common_pb")
local role_pb=require("role_pb")
module('sandbox_pb')


V1M=V(4,"enSandboxType_World",0,0)
V2M=V(4,"enSandboxType_Desert",1,1)
V3M=V(4,"enSandboxType_Max",2,2)
E1M=E(3,"enSandboxType",".CSMsg.enSandboxType")
V4M=V(4,"enSXCondType_MonFirstKill",0,1)
V5M=V(4,"enSXCondType_BuildLevel",1,2)
E2M=E(3,"enSandboxCondType",".CSMsg.enSandboxCondType")
V6M=V(4,"enSXKastenbox_RadarTreasure",0,1)
V7M=V(4,"enSXKastenbox_SiegeTreasure",1,2)
V8M=V(4,"enSXKastenbox_ZombieApocalypse",2,3)
E3M=E(3,"enSandKastenboxType",".CSMsg.enSandKastenboxType")
V9M=V(4,"SANDBOX_MASS_MEMBER_MAX",0,5)
V10M=V(4,"SANDBOX_MONTYPE_TK",1,1)
V11M=V(4,"SANDBOX_MONTYPE_LS",2,2)
V12M=V(4,"SANDBOX_MONTYPE_TB",3,3)
V13M=V(4,"SANDBOX_MONTYPE_JY",4,4)
V14M=V(4,"SANDBOX_MONTYPE_YD",5,5)
V15M=V(4,"SANDBOX_MONTYPE_EMCB",6,6)
V16M=V(4,"SANDBOX_MONTYPE_LDSS",7,7)
V17M=V(4,"SANDBOX_MONTYPE_LDMNK",8,11)
V18M=V(4,"SANDBOX_MONTYPE_GATHERING",9,13)
V19M=V(4,"SANDBOX_MONTYPE_ACORNPUBTASK",10,16)
V20M=V(4,"SANDBOX_MARCH_SPEED_ERROR",11,0)
V21M=V(4,"SANDBOX_MARCH_SPEED_1",12,1)
V22M=V(4,"SANDBOX_MARCH_SPEED_2",13,2)
V23M=V(4,"SANDBOX_MARCH_SPEED_3",14,3)
E4M=E(3,"enSandboxConstant",".CSMsg.enSandboxConstant")
V24M=V(4,"enSandboxSearchType_Invalid",0,0)
V25M=V(4,"enSandboxSearchType_Monster_Iron",1,1)
V26M=V(4,"enSandboxSearchType_Monster_Food",2,2)
V27M=V(4,"enSandboxSearchType_Monster_Gold",3,3)
V28M=V(4,"enSandboxSearchType_Monster_JY",4,4)
V29M=V(4,"enSandboxSearchType_Monster_MassHuodong",5,5)
V30M=V(4,"enSandboxSearchType_Monster_Zombie_Gold",6,6)
V31M=V(4,"enSandboxSearchType_Resource_Iron",7,101)
V32M=V(4,"enSandboxSearchType_Resource_Food",8,102)
V33M=V(4,"enSandboxSearchType_Resource_Gold",9,103)
E5M=E(3,"enSandboxSearchType",".CSMsg.enSandboxSearchType")
V34M=V(4,"enSandboxCrossMoveType_Invalid",0,0)
V35M=V(4,"enSandboxCrossMoveType_SystemKictOut",1,1)
V36M=V(4,"enSandboxCrossMoveType_TestPosMove",2,2)
V37M=V(4,"enSandboxCrossMoveType_AllianceDuel_Battle",3,4)
V38M=V(4,"enSandboxCrossMoveType_ZoneDuel_Battle",4,5)
V39M=V(4,"enSandboxCrossMoveType_FreeCrossAllianceMove",5,6)
V40M=V(4,"enSandboxCrossMoveType_CrossAllianceMove",6,7)
E6M=E(3,"enSandboxCrossMoveType",".CSMsg.enSandboxCrossMoveType")
V41M=V(4,"enSandboxEntitySort_Invalid",0,0)
V42M=V(4,"enSandboxEntitySort_RoleCity",1,1)
V43M=V(4,"enSandboxEntitySort_NeutralCity",2,2)
V44M=V(4,"enSandboxEntitySort_Monster",3,3)
V45M=V(4,"enSandboxEntitySort_Resource",4,5)
V46M=V(4,"enSandboxEntitySort_DiggingTreasures",5,6)
V47M=V(4,"enSandboxEntitySort_SandkastenBox",6,7)
V48M=V(4,"enSandboxEntitySort_DesertStromEntity",7,8)
E7M=E(3,"enSandboxEntitySort",".CSMsg.enSandboxEntitySort")
V49M=V(4,"enSandboxEntity_Invalid",0,0)
V50M=V(4,"enSandboxEntity_Base",1,1)
V51M=V(4,"enSandboxEntity_NeutralCity",2,2)
V52M=V(4,"enSandboxEntity_FixedMonster",3,3)
V53M=V(4,"enSandboxEntity_WonderMonster",4,4)
V54M=V(4,"enSandboxEntity_Resource",5,5)
V55M=V(4,"enSandboxEntity_March",6,6)
V56M=V(4,"enSandboxEntity_RadarDemonCastle",7,7)
V57M=V(4,"enSandboxEntity_RadarBeastInvasion",8,8)
V58M=V(4,"enSandboxEntity_RadarEnvironmentExplorate",9,9)
V59M=V(4,"enSandboxEntity_RadarCollection",10,10)
V60M=V(4,"enSandboxEntity_RadarChallenge",11,11)
V61M=V(4,"enSandboxEntity_RadarTreasure",12,12)
V62M=V(4,"enSandboxEntity_WorldBoss",13,13)
V63M=V(4,"enSandboxEntity_KastenBox",14,14)
V64M=V(4,"enSandboxEntity_Carriage",15,15)
V65M=V(4,"enSandboxEntity_CommonMonster",16,16)
V66M=V(4,"enSandboxEntity_AllianceTrain",17,17)
V67M=V(4,"enSandboxEntity_DesertStromEntity",18,18)
V68M=V(4,"enSandboxEntity_GeneralEntity",19,19)
V69M=V(4,"enSandboxEntity_ZombieApocalypse",20,20)
V70M=V(4,"enSandboxMaxEntity_Type",21,21)
V71M=V(4,"enSandboxEntity_Relative",22,100)
E8M=E(3,"enSandboxEntityType",".CSMsg.enSandboxEntityType")
V72M=V(4,"enSandboxAreaAttr_Safe",0,1)
V73M=V(4,"enSandboxAreaAttr_NotMove",1,2)
V74M=V(4,"enSandboxAreaAttr_Stain",2,3)
E9M=E(3,"enSandboxAreaAttr",".CSMsg.enSandboxAreaAttr")
V75M=V(4,"enStaminaOperType_FreeGet",0,1)
V76M=V(4,"enStaminaOperType_BuyRecovery",1,2)
V77M=V(4,"enStaminaOperType_UsrItem",2,3)
E10M=E(3,"enStaminaOperType",".CSMsg.enStaminaOperType")
V78M=V(4,"enSandboxProp_DesertStrom_BattleTime",0,1001)
V79M=V(4,"enSandboxProp_DesertStrom_ResultTime",1,1002)
V80M=V(4,"enSandboxProp_DesertStrom_MatchId",2,1003)
E11M=E(3,"enSandboxProp",".CSMsg.enSandboxProp")
V81M=V(4,"enSandboxBaseBoolBit_ZombieApocalyse",0,1)
E12M=E(3,"enSandboxBaseBoolBit",".CSMsg.enSandboxBaseBoolBit")
V82M=V(4,"enSXEntityProp_Base_SkinID",0,1)
V83M=V(4,"enSXEntityProp_AllianceID",1,2)
V84M=V(4,"enSXEntityProp_ServerID",2,3)
V85M=V(4,"enSXEntityProp_Cfg_Id",3,4)
V86M=V(4,"enSXEntityProp_March_TargetId",4,5)
V87M=V(4,"enSXEntityProp_RoleID",5,6)
V88M=V(4,"enSXEntityProp_LineEntityId",6,7)
V89M=V(4,"enSXEntityProp_Base_EffectID",7,8)
V90M=V(4,"enSXEntityProp_General_Association_CfgID",8,9)
V91M=V(4,"enSXEntityProp_Base_BoolBit",9,10)
V92M=V(4,"enSXEntityProp_PlateID",10,11)
V93M=V(4,"enSXEntityProp_Appearance_Max",11,20)
V94M=V(4,"enSXEntityProp_Resource_Num",12,21)
V95M=V(4,"enSXEntityProp_Resource_BeginTime",13,22)
V96M=V(4,"enSXEntityProp_Resource_EndTime",14,23)
V97M=V(4,"enSXEntityProp_Resource_EstimatedTime",15,24)
V98M=V(4,"enSXEntityProp_Resource_EstimatedNum",16,25)
V99M=V(4,"enSXEntityProp_Resource_Speed",17,26)
V100M=V(4,"enSXEntityProp_Resource_Load",18,27)
V101M=V(4,"enSXEntityProp_FaceID",19,31)
V102M=V(4,"enSXEntityProp_FrameID",20,32)
V103M=V(4,"enSXEntityProp_PlayerLevel",21,33)
V104M=V(4,"enSXEntityProp_CreateTime",22,34)
V105M=V(4,"enSXEntityProp_ViewID",23,37)
V106M=V(4,"enSXEntityProp_BeCover",24,38)
V107M=V(4,"enSXEntityProp_BeMassCount",25,39)
V108M=V(4,"enSXEntityProp_Wonder_HP",26,51)
V109M=V(4,"enSXEntityProp_Wonder_Retaliate",27,57)
V110M=V(4,"enSXEntityProp_Wonder_LineID",28,58)
V111M=V(4,"enSXEntityProp_Wonder_TriggerID",29,59)
V112M=V(4,"enSXEntityProp_Detect_Time1",30,52)
V113M=V(4,"enSXEntityProp_Detect_Time2",31,53)
V114M=V(4,"enSXEntityProp_Detect_Gold",32,54)
V115M=V(4,"enSXEntityProp_Detect_Iron",33,55)
V116M=V(4,"enSXEntityProp_Detect_Food",34,56)
V117M=V(4,"enSXEntityProp_March_WonderID",35,61)
V118M=V(4,"enSXEntityProp_March_TargetName",36,62)
V119M=V(4,"enSXEntityProp_March_FollowLineSid",37,63)
V120M=V(4,"enSXEntityProp_March_BattleState",38,64)
V121M=V(4,"enSXEntityProp_March_TargetDBId",39,65)
V122M=V(4,"enSXEntityProp_March_CarriageID",40,66)
V123M=V(4,"enSXEntityProp_March_AllianceTrainID",41,67)
V124M=V(4,"enSXEntityProp_March_LastTargetId",42,68)
V125M=V(4,"enSXEntityProp_Base_WALL_FIRE_ENDTIME",43,70)
V126M=V(4,"enSXEntityProp_Base_Safety",44,71)
V127M=V(4,"enSXEntityProp_Is_Only_Visible_To_Self",45,72)
V128M=V(4,"enSXEntityProp_Base_CITY_WALL_LEVEL",46,73)
V129M=V(4,"enSXEntityProp_CreatorRoleID",47,74)
V130M=V(4,"enSXEntityProp_IsRadarCreate",48,75)
V131M=V(4,"enSXEntityProp_RadarMissionId",49,76)
V132M=V(4,"enSXEntityProp_RewardNumber",50,77)
V133M=V(4,"enSXEntityProp_Carriage_OwnerType",51,78)
V134M=V(4,"enSXEntityProp_Carriage_Quality",52,79)
V135M=V(4,"enSXEntityProp_Carriage_TradeID",53,80)
V136M=V(4,"enSXEntityProp_Carriage_TradeTm",54,81)
V137M=V(4,"enSXEntityProp_Carriage_Interitry",55,82)
V138M=V(4,"enSXEntityProp_Carriage_NxtSiteTm",56,83)
V139M=V(4,"enSXEntityProp_Carriage_LootTimes",57,84)
V140M=V(4,"enSXEntityProp_Carriage_Visiable",58,85)
V141M=V(4,"enSXEntityProp_Carriage_LineID",59,86)
V142M=V(4,"enSXEntityProp_Carriage_TriggerID",60,87)
V143M=V(4,"enSXEntityProp_WORLDBOSS_ACHIEVE",61,88)
V144M=V(4,"enSXEntityProp_WORLDBOSS_CHALLENGES",62,89)
V145M=V(4,"enSXEntityProp_WORLDBOSS_BORNTM",63,90)
V146M=V(4,"enSXEntityProp_WORLDBOSS_DEATHTM",64,91)
V147M=V(4,"enSXEntityProp_GeneralTrial_BelongId",65,92)
V148M=V(4,"enSXEntityProp_MultiplierRewardNumber",66,93)
V149M=V(4,"enSXEntityProp_AllianceBoss_StateTime",67,94)
V150M=V(4,"enSXEntityProp_AllianceBoss_TotalDamage",68,95)
V151M=V(4,"enSXEntityProp_AcornPub_TaskID",69,96)
V152M=V(4,"enSXEntityProp_AcornPub_DoneTime",70,97)
V153M=V(4,"enSXEntityProp_AcornPub_HelpDbid",71,98)
V154M=V(4,"enSXEntityProp_AcornPub_TaskSID",72,99)
V155M=V(4,"enSXEntityProp_Treasure_Progress_Sum",73,101)
V156M=V(4,"enSXEntityProp_Treasure_Progress_Now",74,102)
V157M=V(4,"enSXEntityProp_Treasure_Digger_RoleNums",75,103)
V158M=V(4,"enSXEntityProp_RadarMission_TaskId",76,104)
V159M=V(4,"enSXEntityProp_Treasure_Digger_CostTime",77,105)
V160M=V(4,"enSXEntityProp_Neutral_SoldierNum",78,107)
V161M=V(4,"enSXEntityProp_Neutral_Durability",79,108)
V162M=V(4,"enSXEntityProp_Neutral_IsFirst",80,109)
V163M=V(4,"enSXEntityProp_Neutral_AllianceIdx",81,110)
V164M=V(4,"enSXEntityProp_NC_DW_EndTime",82,111)
V165M=V(4,"enSXEntityProp_NC_ProtectCD",83,112)
V166M=V(4,"enSXEntityProp_NC_AllianceIconID",84,115)
V167M=V(4,"enSXEntityProp_NC_OfflineAttackCD",85,116)
V168M=V(4,"enSXEntityProp_NC_AbandonCD",86,117)
V169M=V(4,"enSXEntityProp_NC_OfflineAttackTime",87,118)
V170M=V(4,"enSXEntityProp_ZombieOwner",88,120)
V171M=V(4,"enSXEntityProp_ZombieType",89,121)
V172M=V(4,"enSXEntityProp_CrossServerType",90,125)
V173M=V(4,"enSXEntityProp_Base_State",91,126)
V174M=V(4,"enSXEntityProp_Base_Lv",92,127)
V175M=V(4,"enSXEntityProp_Base_KillNum",93,128)
V176M=V(4,"enSXEntityProp_Base_Power",94,129)
V177M=V(4,"enSXEntityProp_Base_Misc_Flag",95,131)
V178M=V(4,"enSXEntityProp_Base_WorldId",96,132)
V179M=V(4,"enSXEntityProp_Base_CUR_DEFEND",97,133)
V180M=V(4,"enSXEntityProp_Base_WonderTime",98,134)
V181M=V(4,"enSXEntityProp_Base_WALL_BEGINTIME",99,135)
V182M=V(4,"enSXEntityProp_Base_CongressOfficial",100,136)
V183M=V(4,"enSXEntityProp_Fixed_Countdown",101,137)
V184M=V(4,"enSXEntityProp_Fixed_MaxKIllLv",102,138)
V185M=V(4,"enSXEntityProp_Fixed_DayMRewardCnt",103,139)
V186M=V(4,"enSXEntityProp_Fixed_DaySRewardCnt",104,140)
V187M=V(4,"enSXEntityProp_AllianceTrain_TrainType",105,141)
V188M=V(4,"enSXEntityProp_AllianceTrain_TrainID",106,142)
V189M=V(4,"enSXEntityProp_AllianceTrain_LootCount",107,143)
V190M=V(4,"enSXEntityProp_AllianceTrain_ArrivalTime",108,144)
V191M=V(4,"enSXEntityProp_AllianceTrain_LineID",109,145)
V192M=V(4,"enSXEntityProp_AllianceTrain_AllianceFlag",110,146)
V193M=V(4,"enSXEntityProp_AllianceTrain_Visiable",111,147)
V194M=V(4,"enSXEntityProp_DesertStorm_UnlockTime",112,148)
V195M=V(4,"enSXEntityProp_DesertStorm_BelongAllianceId",113,149)
V196M=V(4,"enSXEntityProp_DesertStorm_BelongRoleId",114,150)
V197M=V(4,"enSXEntityProp_DesertStorm_BelongRoleFrameId",115,151)
V198M=V(4,"enSXEntityProp_DesertStorm_IsFirstOccupy",116,152)
V199M=V(4,"enSXEntityProp_DesertStorm_FirstOccupierHp",117,153)
V200M=V(4,"enSXEntityProp_DesertStorm_ScoreBoxScoreNum",118,154)
V201M=V(4,"enSXEntityProp_DesertStorm_OilWellChangeTime",119,155)
V202M=V(4,"enSXEntityProp_DesertStorm_OilWellChangeNum",120,156)
V203M=V(4,"enSXEntityProp_DesertStorm_AllianceFlag",121,157)
V204M=V(4,"enSXEntityProp_DesertStorm_MoveCityCD",122,158)
V205M=V(4,"enSXEntityProp_DesertStorm_RoleCityHP",123,159)
V206M=V(4,"enSXEntityProp_DesertStorm_Camp",124,160)
V207M=V(4,"enSXEntityProp_DesertStorm_CurBuildingTeamIndex",125,161)
V208M=V(4,"enSXEntityProp_DesertStorm_AllPlunderScore",126,162)
V209M=V(4,"enSXEntityProp_ZoneBattleDuel_Giant_Occupy_World",127,200)
V210M=V(4,"enSXEntityProp_ZoneBattleDuel_Occupy_World_Score",128,201)
V211M=V(4,"enSXEntityProp_ZoneBattleDuel_FaceID",129,202)
V212M=V(4,"enSXEntityProp_ZoneBattleDuel_FrameID",130,203)
V213M=V(4,"enSXEntityProp_ZoneBattleDuel_AtkWorld_Score",131,204)
V214M=V(4,"enSXEntityProp_ZoneBattleDuel_DefWorld_Score",132,205)
V215M=V(4,"enSXEntityProp_ZoneBattleDuel_Occupy_World_OverTime",133,206)
V216M=V(4,"enSXEntityProp_SiegeCamp_Reinforcements_Count",134,210)
V217M=V(4,"enSXEntityProp_SiegeCamp_Durability",135,211)
V218M=V(4,"enSXEntityProp_ZombieApocalypse_DifficultyLevel",136,212)
V219M=V(4,"enSXEntityProp_ZombieApocalypse_State",137,213)
V220M=V(4,"enSXEntityProp_ZombieApocalypse_Wave",138,214)
V221M=V(4,"enSXEntityProp_ZombieApocalypse_Time",139,215)
V222M=V(4,"enSXEntityProp_ZombieApocalypse_DefensiveWave",140,216)
V223M=V(4,"enSXEntityProp_ZombieApocalypse_LineType",141,217)
V224M=V(4,"enSXEntityProp_ZombieApocalypse_BelongRoleId",142,218)
V225M=V(4,"enSXEntityProp_ZombieApocalypse_LineID",143,219)
V226M=V(4,"enSXEntityProp_ZombieApocalypse_TriggerID",144,220)
E13M=E(3,"enSXEntityProp",".CSMsg.enSXEntityProp")
V227M=V(4,"enSXEntityPropStr_RoleName",0,1)
V228M=V(4,"enSXEntityPropStr_AllianceName",1,2)
V229M=V(4,"enSXEntityPropStr_AllianceShortName",2,3)
V230M=V(4,"enSXEntityPropStr_Carriage_Sites",3,4)
V231M=V(4,"enSXEntityPropStr_Carriage_Lineup",4,5)
V232M=V(4,"enSXEntityPropStr_Carriage_TakeItems",5,6)
V233M=V(4,"enSXEntityPropStr_AcornPub_RobPlayer",6,7)
V234M=V(4,"enSXEntityPropStr_MonsterTroop",7,10)
V235M=V(4,"enSXEntityProp_NC_AllianceList",8,11)
V236M=V(4,"enSXEntityProp_NC_CongressInfo",9,12)
V237M=V(4,"enSXEntityPropStr_FaceStr",10,13)
V238M=V(4,"enSXEntityPropStr_ZoneBattleDuel_FaceStr",11,14)
V239M=V(4,"enSXEntityProp_SiegeCamp_CityPos",12,15)
V240M=V(4,"enSXEntityProp_ZoneBattleDuel_Occupy_AllianceShort",13,16)
E14M=E(3,"enSXEntityPropStr",".CSMsg.enSXEntityPropStr")
V241M=V(4,"enSandboxLineType_Invalid",0,0)
V242M=V(4,"enSandboxLineType_Collect",1,1)
V243M=V(4,"enSandboxLineType_Spy",2,2)
V244M=V(4,"enSandboxLineType_Reinforce",3,3)
V245M=V(4,"enSandboxLineType_Troop",4,4)
V246M=V(4,"enSandboxLineType_AtkMinMon",5,5)
V247M=V(4,"enSandboxLineType_AtkBoss",6,6)
V248M=V(4,"enSandboxLineType_AtkCity",7,7)
V249M=V(4,"enSandboxLineType_Congress",8,8)
V250M=V(4,"enSandboxLineType_DesertAloneAtkPlay",9,10)
V251M=V(4,"enSandboxLineType_DesertAloneAtkBuild",10,11)
V252M=V(4,"enSandboxLineType_DesertMassAtkBuild",11,12)
V253M=V(4,"enSandboxLineType_DesertMassAtkPlay",12,13)
V254M=V(4,"enSandboxLineType_DesertCollect",13,14)
V255M=V(4,"enSandboxLineType_MassPlay",14,21)
V256M=V(4,"enSandboxLineType_MassBoss",15,22)
V257M=V(4,"enSandboxLineType_MassWonderMon",16,23)
V258M=V(4,"enSandboxLineType_MassLMBoss",17,24)
V259M=V(4,"enSandboxLineType_MassCity",18,25)
V260M=V(4,"enSandboxLineType_MassCongress",19,26)
V261M=V(4,"enSandboxLineType_ZoneBattleDuel_Congress",20,27)
V262M=V(4,"enSandboxLineType_ZoneBattleDuel_MassCongress",21,28)
V263M=V(4,"enSandboxLineType_ZoneBattleDuel_BigGun",22,29)
V264M=V(4,"enSandboxLineType_ZoneBattleDuel_MassBigGun",23,30)
V265M=V(4,"enSandboxLineType_ZombieGold",24,31)
V266M=V(4,"enSandboxLineType_ZombieMonsterLeader",25,32)
V267M=V(4,"enSandboxLineType_SiegeCamp",26,33)
V268M=V(4,"enSandboxLineType_WonderMonster",27,100)
V269M=V(4,"enSandboxLineType_NoTeamMarch",28,101)
V270M=V(4,"enSandboxLineType_RadarExplorate",29,103)
V271M=V(4,"enSandboxLineType_RadarHelpAlliance",30,104)
V272M=V(4,"enSandboxLineType_AcornPub",31,105)
V273M=V(4,"enSandboxLineType_CarriageMarch",32,201)
V274M=V(4,"enSandboxLineType_AllianceTrain",33,202)
V275M=V(4,"enSandboxLineType_ZombieApocalypse",34,301)
V276M=V(4,"enSandboxLineType_ZombieApocalypsePoison",35,302)
V277M=V(4,"enSandboxLineType_ZombieApocalypseTruck",36,303)
V278M=V(4,"enSandboxLineType_ZombieApocalypseMutantTruck",37,304)
V279M=V(4,"enSandboxLineType_Max",38,305)
E15M=E(3,"enSandboxLineType",".CSMsg.enSandboxLineType")
V280M=V(4,"enSandboxMarchSpeedUp_Invalid",0,0)
V281M=V(4,"enSandboxMarchSpeedUp_Monster",1,1)
V282M=V(4,"enSandboxMarchSpeedUp_Collect",2,2)
V283M=V(4,"enSandboxMarchSpeedUp_AtkRole",3,3)
V284M=V(4,"enSandboxMarchSpeedUp_AtkCity",4,4)
V285M=V(4,"enSandboxMarchSpeedUp_Reinforce",5,5)
E16M=E(3,"enSandboxMarchSpeedUp",".CSMsg.enSandboxMarchSpeedUp")
V286M=V(4,"enSandboxEntityCover_None",0,0)
V287M=V(4,"enSandboxEntityCover_Allow",1,1)
V288M=V(4,"enSandboxEntityCover_NotAllow",2,2)
E17M=E(3,"enSandboxEntityCover",".CSMsg.enSandboxEntityCover")
V289M=V(4,"enSandboxMarchBattleState_NoBattle",0,0)
V290M=V(4,"enSandboxMarchBattleState_WaitBattle",1,1)
V291M=V(4,"enSandboxMarchBattleState_Battleing",2,2)
V292M=V(4,"enSandboxMarchBattleState_Win",3,3)
V293M=V(4,"enSandboxMarchBattleState_Lose",4,4)
E18M=E(3,"enSandboxMarchBattleState",".CSMsg.enSandboxMarchBattleState")
V294M=V(4,"enSandboxReinforceType_Invalid",0,0)
V295M=V(4,"enSandboxReinforceType_ZoneBattleDuel_Congress",1,1)
V296M=V(4,"enSandboxReinforceType_ZoneBattleDuel_BigGun",2,2)
V297M=V(4,"enSandboxReinforceType_SiegeCamp",3,3)
E19M=E(3,"enSandboxReinforceType",".CSMsg.enSandboxReinforceType")
V298M=V(4,"enSXMassOperType_Invalid",0,0)
V299M=V(4,"enSXMassOperType_Create",1,1)
V300M=V(4,"enSXMassOperType_Del",2,2)
V301M=V(4,"enSXMassOperType_Join",3,3)
V302M=V(4,"enSXMassOperType_Exit",4,4)
V303M=V(4,"enSXMassOperType_Kict",5,5)
V304M=V(4,"enSXMassOperType_StateChange",6,6)
V305M=V(4,"enSXMassOperType_StateMassTimeOut",7,7)
E20M=E(3,"enSandboxMassOperType",".CSMsg.enSandboxMassOperType")
V306M=V(4,"enSXMassStateType_Invalid",0,0)
V307M=V(4,"enSXMassStateType_Team",1,1)
V308M=V(4,"enSXMassStateType_Wait",2,2)
V309M=V(4,"enSXMassStateType_Go",3,3)
E21M=E(3,"enSandboxMassStateType",".CSMsg.enSandboxMassStateType")
V310M=V(4,"enSandbox_AlertType_None",0,0)
V311M=V(4,"enSandbox_AlertType_BeAtkCity",1,1)
V312M=V(4,"enSandbox_AlertType_BeMass_Wait",2,2)
V313M=V(4,"enSandbox_AlertType_BeMass_March",3,3)
V314M=V(4,"enSandbox_AlertType_BeAtkResource",4,4)
V315M=V(4,"enSandbox_AlertType_Wonder",5,5)
V316M=V(4,"enSandbox_AlertType_Detect",6,6)
V317M=V(4,"enSandbox_AlertType_BeAtkResource_Detect",7,7)
V318M=V(4,"enSandbox_AlertType_ZombieApocalypse",8,8)
E22M=E(3,"enSandbox_AlertType",".CSMsg.enSandbox_AlertType")
V319M=V(4,"enSandbox_AlertListType_Normal",0,0)
V320M=V(4,"enSandbox_AlertListType_Activity",1,1)
E23M=E(3,"enSandbox_AlertListType",".CSMsg.enSandbox_AlertListType")
V321M=V(4,"enKastenBoxState_UnReceived",0,0)
V322M=V(4,"enKastenBoxState_Received",1,1)
E24M=E(3,"enKastenBoxState",".CSMsg.enKastenBoxState")
V323M=V(4,"enSXNCPOType_MoveCity",0,1)
V324M=V(4,"enSXNCPOType_Horn",1,2)
V325M=V(4,"enSXNCPOType_Occupy",2,3)
V326M=V(4,"enSXNCPOType_Reward",3,4)
V327M=V(4,"enSXNCPOType_LoseOccupy",4,5)
V328M=V(4,"enSXNCPOType_CancelHorn",5,6)
E25M=E(3,"enSandboxNCPopupsType",".CSMsg.enSandboxNCPopupsType")
V329M=V(4,"RealityMoveCityType_None",0,0)
V330M=V(4,"RealityMoveCityType_FreeMoveUseItem",1,1)
E26M=E(3,"RealityMoveCityType",".CSMsg.RealityMoveCityType")
V331M=V(4,"SxSpecialEffect_AllianceOutFire",0,1)
E27M=E(3,"SxSpecialEffectType",".CSMsg.SxSpecialEffectType")
F1D=F(2,"x",".CSMsg.TSandboxPos.x",1,0,2,false,0,5,1)
F2D=F(2,"y",".CSMsg.TSandboxPos.y",2,1,2,false,0,5,1)
F3D=F(2,"sandboxSid",".CSMsg.TSandboxPos.sandboxSid",3,2,1,false,0,13,3)
M1G=D(1,"TSandboxPos",".CSMsg.TSandboxPos",false,{},{},nil,{})
M2G=D(1,"TMSG_SANDBOX_BASE_INFO_REQ",".CSMsg.TMSG_SANDBOX_BASE_INFO_REQ",false,{},{},{},{})
F4D=F(2,"zoneSandBoxSid",".CSMsg.TMSG_SANDBOX_BASE_INFO_RSP.zoneSandBoxSid",1,0,1,false,0,13,3)
F5D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_BASE_INFO_RSP.errCode",2,1,2,false,nil,14,8)
F6D=F(2,"roleSandBoxSid",".CSMsg.TMSG_SANDBOX_BASE_INFO_RSP.roleSandBoxSid",3,2,1,false,0,13,3)
F7D=F(2,"rolePos",".CSMsg.TMSG_SANDBOX_BASE_INFO_RSP.rolePos",4,3,1,false,nil,11,10)
F8D=F(2,"roleCitySid",".CSMsg.TMSG_SANDBOX_BASE_INFO_RSP.roleCitySid",5,4,1,false,0,4,4)
M3G=D(1,"TMSG_SANDBOX_BASE_INFO_RSP",".CSMsg.TMSG_SANDBOX_BASE_INFO_RSP",false,{},{},nil,{})
F9D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_ROLECITY_NTF.sandboxSid",1,0,1,false,0,13,3)
F10D=F(2,"exist",".CSMsg.TMSG_SANDBOX_ROLECITY_NTF.exist",2,1,1,false,false,8,7)
F11D=F(2,"citySid",".CSMsg.TMSG_SANDBOX_ROLECITY_NTF.citySid",3,2,1,false,0,4,4)
F12D=F(2,"x",".CSMsg.TMSG_SANDBOX_ROLECITY_NTF.x",4,3,1,false,0,5,1)
F13D=F(2,"y",".CSMsg.TMSG_SANDBOX_ROLECITY_NTF.y",5,4,1,false,0,5,1)
M5G=D(1,"TMSG_SANDBOX_ROLECITY_NTF",".CSMsg.TMSG_SANDBOX_ROLECITY_NTF",false,{},{},nil,{})
F14D=F(2,"sandBoxSid",".CSMsg.TMSG_SANDBOX_ENTER_REQ.sandBoxSid",1,0,2,false,0,13,3)
F15D=F(2,"pos",".CSMsg.TMSG_SANDBOX_ENTER_REQ.pos",2,1,1,false,nil,11,10)
M6G=D(1,"TMSG_SANDBOX_ENTER_REQ",".CSMsg.TMSG_SANDBOX_ENTER_REQ",false,{},{},nil,{})
F16D=F(2,"pos",".CSMsg.TMSG_SANDBOX_MOVE_VIEW_REQ.pos",1,0,2,false,nil,11,10)
F17D=F(2,"viewLevel",".CSMsg.TMSG_SANDBOX_MOVE_VIEW_REQ.viewLevel",2,1,1,false,0,5,1)
F18D=F(2,"sandBoxSid",".CSMsg.TMSG_SANDBOX_MOVE_VIEW_REQ.sandBoxSid",3,2,2,false,0,5,1)
F19D=F(2,"reqID",".CSMsg.TMSG_SANDBOX_MOVE_VIEW_REQ.reqID",4,3,1,false,0,5,1)
M7G=D(1,"TMSG_SANDBOX_MOVE_VIEW_REQ",".CSMsg.TMSG_SANDBOX_MOVE_VIEW_REQ",false,{},{},nil,{})
F20D=F(2,"pos",".CSMsg.TMSG_SANDBOX_ENTER_RSP.pos",1,0,1,false,nil,11,10)
F21D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_ENTER_RSP.errCode",2,1,2,false,nil,14,8)
F22D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_ENTER_RSP.sandboxSid",3,2,1,false,0,5,1)
M8G=D(1,"TMSG_SANDBOX_ENTER_RSP",".CSMsg.TMSG_SANDBOX_ENTER_RSP",false,{},{},nil,{})
F23D=F(2,"enterSandboxSid",".CSMsg.TMSG_SANDBOX_VISUAL_ENTER_REQ.enterSandboxSid",1,0,2,false,0,13,3)
F24D=F(2,"pos",".CSMsg.TMSG_SANDBOX_VISUAL_ENTER_REQ.pos",2,1,2,false,nil,11,10)
F25D=F(2,"enterType",".CSMsg.TMSG_SANDBOX_VISUAL_ENTER_REQ.enterType",3,2,1,false,0,13,3)
M9G=D(1,"TMSG_SANDBOX_VISUAL_ENTER_REQ",".CSMsg.TMSG_SANDBOX_VISUAL_ENTER_REQ",false,{},{},nil,{})
F26D=F(2,"enterSandboxSid",".CSMsg.TMSG_SANDBOX_VISUAL_ENTER_RSP.enterSandboxSid",1,0,1,false,0,13,3)
F27D=F(2,"errorCode",".CSMsg.TMSG_SANDBOX_VISUAL_ENTER_RSP.errorCode",2,1,2,false,nil,14,8)
F28D=F(2,"pos",".CSMsg.TMSG_SANDBOX_VISUAL_ENTER_RSP.pos",3,2,1,false,nil,11,10)
F29D=F(2,"enterType",".CSMsg.TMSG_SANDBOX_VISUAL_ENTER_RSP.enterType",4,3,1,false,0,13,3)
M10G=D(1,"TMSG_SANDBOX_VISUAL_ENTER_RSP",".CSMsg.TMSG_SANDBOX_VISUAL_ENTER_RSP",false,{},{},nil,{})
F30D=F(2,"exitSandboxSid",".CSMsg.TMSG_SANDBOX_EXIT_REQ.exitSandboxSid",1,0,2,false,0,13,3)
M11G=D(1,"TMSG_SANDBOX_EXIT_REQ",".CSMsg.TMSG_SANDBOX_EXIT_REQ",false,{},{},nil,{})
F31D=F(2,"exitSandboxSid",".CSMsg.TMSG_SANDBOX_EXIT_RSP.exitSandboxSid",1,0,2,false,0,13,3)
F32D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_EXIT_RSP.errCode",2,1,2,false,nil,14,8)
M12G=D(1,"TMSG_SANDBOX_EXIT_RSP",".CSMsg.TMSG_SANDBOX_EXIT_RSP",false,{},{},nil,{})
F33D=F(2,"propID",".CSMsg.TSandBoxProp.propID",1,0,2,false,0,5,1)
F34D=F(2,"value",".CSMsg.TSandBoxProp.value",2,1,1,false,0,3,2)
M13G=D(1,"TSandBoxProp",".CSMsg.TSandBoxProp",false,{},{},nil,{})
F35D=F(2,"propID",".CSMsg.TSandBoxPropStr.propID",1,0,2,false,0,5,1)
F36D=F(2,"strValue",".CSMsg.TSandBoxPropStr.strValue",2,1,1,false,"",9,9)
M14G=D(1,"TSandBoxPropStr",".CSMsg.TSandBoxPropStr",false,{},{},nil,{})
F37D=F(2,"pos",".CSMsg.TSandboxLineDot.pos",1,0,2,false,nil,11,10)
F38D=F(2,"timeStamp",".CSMsg.TSandboxLineDot.timeStamp",2,1,2,false,0,13,3)
F39D=F(2,"doublePos",".CSMsg.TSandboxLineDot.doublePos",4,2,1,false,nil,11,10)
M15G=D(1,"TSandboxLineDot",".CSMsg.TSandboxLineDot",false,{},{},nil,{})
F40D=F(2,"FaceID",".CSMsg.TSandboxMarchMember.FaceID",1,0,1,false,0,5,1)
F41D=F(2,"FrameID",".CSMsg.TSandboxMarchMember.FrameID",2,1,1,false,0,5,1)
F42D=F(2,"team",".CSMsg.TSandboxMarchMember.team",3,2,1,false,nil,11,10)
F43D=F(2,"FaceStr",".CSMsg.TSandboxMarchMember.FaceStr",4,3,1,false,"",9,9)
F44D=F(2,"memberDBid",".CSMsg.TSandboxMarchMember.memberDBid",5,4,1,false,0,5,1)
M16G=D(1,"TSandboxMarchMember",".CSMsg.TSandboxMarchMember",false,{},{},nil,{})
F45D=F(2,"dotList",".CSMsg.TSandboxLine.dotList",1,0,3,false,{},11,10)
F46D=F(2,"type",".CSMsg.TSandboxLine.type",2,1,2,false,nil,14,8)
F47D=F(2,"team",".CSMsg.TSandboxLine.team",3,2,1,false,nil,11,10)
F48D=F(2,"state",".CSMsg.TSandboxLine.state",4,3,1,false,nil,14,8)
F49D=F(2,"startTime",".CSMsg.TSandboxLine.startTime",5,4,1,false,0,13,3)
F50D=F(2,"endTime",".CSMsg.TSandboxLine.endTime",6,5,1,false,0,13,3)
F51D=F(2,"targetSid",".CSMsg.TSandboxLine.targetSid",7,6,1,false,0,4,4)
F52D=F(2,"lineSid",".CSMsg.TSandboxLine.lineSid",8,7,1,false,0,13,3)
F53D=F(2,"targetdbid",".CSMsg.TSandboxLine.targetdbid",9,8,1,false,0,5,1)
F54D=F(2,"targetName",".CSMsg.TSandboxLine.targetName",10,9,1,false,"",9,9)
F55D=F(2,"changePos",".CSMsg.TSandboxLine.changePos",11,10,1,false,nil,11,10)
M18G=D(1,"TSandboxLine",".CSMsg.TSandboxLine",false,{},{},nil,{})
F56D=F(2,"type",".CSMsg.TSandboxEntity.type",1,0,2,false,nil,14,8)
F57D=F(2,"sid",".CSMsg.TSandboxEntity.sid",2,1,2,false,0,4,4)
F58D=F(2,"pos",".CSMsg.TSandboxEntity.pos",3,2,2,false,nil,11,10)
F59D=F(2,"line",".CSMsg.TSandboxEntity.line",6,3,1,false,nil,11,10)
F60D=F(2,"propType",".CSMsg.TSandboxEntity.propType",11,4,3,false,{},5,1)
F61D=F(2,"propValue",".CSMsg.TSandboxEntity.propValue",12,5,3,false,{},3,2)
F62D=F(2,"strPropsType",".CSMsg.TSandboxEntity.strPropsType",13,6,3,false,{},5,1)
F63D=F(2,"strPropsValue",".CSMsg.TSandboxEntity.strPropsValue",14,7,3,false,{},9,9)
M21G=D(1,"TSandboxEntity",".CSMsg.TSandboxEntity",false,{},{},nil,{})
F64D=F(2,"SandboxEntity",".CSMsg.TMSG_SANDBOX_DATA_NTF.SandboxEntity",1,0,3,false,{},11,10)
F65D=F(2,"removeSid",".CSMsg.TMSG_SANDBOX_DATA_NTF.removeSid",2,1,3,false,{},4,4)
F66D=F(2,"neutralCityID",".CSMsg.TMSG_SANDBOX_DATA_NTF.neutralCityID",3,2,1,false,0,5,1)
F67D=F(2,"neutralAllianceName",".CSMsg.TMSG_SANDBOX_DATA_NTF.neutralAllianceName",4,3,1,false,"",9,9)
F68D=F(2,"viewLevel",".CSMsg.TMSG_SANDBOX_DATA_NTF.viewLevel",5,4,1,false,0,5,1)
F69D=F(2,"neutralAllianceShortName",".CSMsg.TMSG_SANDBOX_DATA_NTF.neutralAllianceShortName",6,5,1,false,"",9,9)
F70D=F(2,"dataSandBoxSid",".CSMsg.TMSG_SANDBOX_DATA_NTF.dataSandBoxSid",7,6,1,false,0,13,3)
F71D=F(2,"viewPos",".CSMsg.TMSG_SANDBOX_DATA_NTF.viewPos",8,7,1,false,nil,11,10)
F72D=F(2,"reqID",".CSMsg.TMSG_SANDBOX_DATA_NTF.reqID",9,8,1,false,0,5,1)
F73D=F(2,"neutralRegionID",".CSMsg.TMSG_SANDBOX_DATA_NTF.neutralRegionID",10,9,1,false,0,5,1)
M23G=D(1,"TMSG_SANDBOX_DATA_NTF",".CSMsg.TMSG_SANDBOX_DATA_NTF",false,{},{},nil,{})
F74D=F(2,"lineSid",".CSMsg.TSandboxMarch.lineSid",1,0,2,false,0,4,4)
F75D=F(2,"type",".CSMsg.TSandboxMarch.type",2,1,2,false,nil,14,8)
F76D=F(2,"state",".CSMsg.TSandboxMarch.state",3,2,2,false,nil,14,8)
F77D=F(2,"dotList",".CSMsg.TSandboxMarch.dotList",4,3,3,false,{},11,10)
F78D=F(2,"changePos",".CSMsg.TSandboxMarch.changePos",5,4,1,false,nil,11,10)
F79D=F(2,"team",".CSMsg.TSandboxMarch.team",6,5,1,false,nil,11,10)
F80D=F(2,"startTime",".CSMsg.TSandboxMarch.startTime",7,6,1,false,0,13,3)
F81D=F(2,"endTime",".CSMsg.TSandboxMarch.endTime",8,7,1,false,0,13,3)
F82D=F(2,"rootCityPos",".CSMsg.TSandboxMarch.rootCityPos",11,8,1,false,nil,11,10)
F83D=F(2,"member",".CSMsg.TSandboxMarch.member",12,9,3,false,{},11,10)
F84D=F(2,"propType",".CSMsg.TSandboxMarch.propType",16,10,3,false,{},5,1)
F85D=F(2,"propValue",".CSMsg.TSandboxMarch.propValue",17,11,3,false,{},3,2)
F86D=F(2,"strPropsType",".CSMsg.TSandboxMarch.strPropsType",18,12,3,false,{},5,1)
F87D=F(2,"strPropsValue",".CSMsg.TSandboxMarch.strPropsValue",19,13,3,false,{},9,9)
F88D=F(2,"dotList_txy",".CSMsg.TSandboxMarch.dotList_txy",20,14,3,false,{},13,3)
F89D=F(2,"dotList_change_idx",".CSMsg.TSandboxMarch.dotList_change_idx",21,15,1,false,0,13,3)
M24G=D(1,"TSandboxMarch",".CSMsg.TSandboxMarch",false,{},{},nil,{})
F90D=F(2,"marchList",".CSMsg.TMSG_SANDBOX_MARCH_NTF.marchList",1,0,3,false,{},11,10)
F91D=F(2,"removeSid",".CSMsg.TMSG_SANDBOX_MARCH_NTF.removeSid",2,1,3,false,{},4,4)
F92D=F(2,"viewLevel",".CSMsg.TMSG_SANDBOX_MARCH_NTF.viewLevel",3,2,1,false,0,5,1)
F93D=F(2,"dataSandBoxSid",".CSMsg.TMSG_SANDBOX_MARCH_NTF.dataSandBoxSid",4,3,1,false,0,13,3)
M25G=D(1,"TMSG_SANDBOX_MARCH_NTF",".CSMsg.TMSG_SANDBOX_MARCH_NTF",false,{},{},nil,{})
F94D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_ALONE_MARCH_DATA_REQ.sandboxSid",1,0,2,false,0,13,3)
F95D=F(2,"marchSid",".CSMsg.TMSG_SANDBOX_ALONE_MARCH_DATA_REQ.marchSid",2,1,2,false,0,4,4)
F96D=F(2,"callId",".CSMsg.TMSG_SANDBOX_ALONE_MARCH_DATA_REQ.callId",3,2,1,false,0,5,1)
M26G=D(1,"TMSG_SANDBOX_ALONE_MARCH_DATA_REQ",".CSMsg.TMSG_SANDBOX_ALONE_MARCH_DATA_REQ",false,{},{},nil,{})
F97D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_ALONE_MARCH_DATA_RSP.sandboxSid",1,0,2,false,0,13,3)
F98D=F(2,"marchSid",".CSMsg.TMSG_SANDBOX_ALONE_MARCH_DATA_RSP.marchSid",2,1,2,false,0,4,4)
F99D=F(2,"errorCode",".CSMsg.TMSG_SANDBOX_ALONE_MARCH_DATA_RSP.errorCode",3,2,1,false,nil,14,8)
F100D=F(2,"marchData",".CSMsg.TMSG_SANDBOX_ALONE_MARCH_DATA_RSP.marchData",4,3,1,false,nil,11,10)
F101D=F(2,"callId",".CSMsg.TMSG_SANDBOX_ALONE_MARCH_DATA_RSP.callId",5,4,1,false,0,5,1)
M27G=D(1,"TMSG_SANDBOX_ALONE_MARCH_DATA_RSP",".CSMsg.TMSG_SANDBOX_ALONE_MARCH_DATA_RSP",false,{},{},nil,{})
F102D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_ALONE_ENTITY_DATA_REQ.sandboxSid",1,0,2,false,0,13,3)
F103D=F(2,"entitySid",".CSMsg.TMSG_SANDBOX_ALONE_ENTITY_DATA_REQ.entitySid",2,1,2,false,0,4,4)
F104D=F(2,"nViewLev",".CSMsg.TMSG_SANDBOX_ALONE_ENTITY_DATA_REQ.nViewLev",3,2,2,false,0,5,1)
F105D=F(2,"callId",".CSMsg.TMSG_SANDBOX_ALONE_ENTITY_DATA_REQ.callId",4,3,1,false,0,5,1)
M28G=D(1,"TMSG_SANDBOX_ALONE_ENTITY_DATA_REQ",".CSMsg.TMSG_SANDBOX_ALONE_ENTITY_DATA_REQ",false,{},{},nil,{})
F106D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_ALONE_ENTITY_DATA_RSP.sandboxSid",1,0,2,false,0,13,3)
F107D=F(2,"entitySid",".CSMsg.TMSG_SANDBOX_ALONE_ENTITY_DATA_RSP.entitySid",2,1,2,false,0,4,4)
F108D=F(2,"errorCode",".CSMsg.TMSG_SANDBOX_ALONE_ENTITY_DATA_RSP.errorCode",3,2,1,false,nil,14,8)
F109D=F(2,"entityData",".CSMsg.TMSG_SANDBOX_ALONE_ENTITY_DATA_RSP.entityData",4,3,1,false,nil,11,10)
F110D=F(2,"callId",".CSMsg.TMSG_SANDBOX_ALONE_ENTITY_DATA_RSP.callId",5,4,1,false,0,5,1)
M29G=D(1,"TMSG_SANDBOX_ALONE_ENTITY_DATA_RSP",".CSMsg.TMSG_SANDBOX_ALONE_ENTITY_DATA_RSP",false,{},{},nil,{})
F111D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_ALLIANCE_SIEGECAMP_DATA_REQ.sandboxSid",1,0,2,false,0,13,3)
M30G=D(1,"TMSG_SANDBOX_ALLIANCE_SIEGECAMP_DATA_REQ",".CSMsg.TMSG_SANDBOX_ALLIANCE_SIEGECAMP_DATA_REQ",false,{},{},nil,{})
F112D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_ALLIANCE_SIEGECAMP_DATA_RSP.sandboxSid",1,0,2,false,0,13,3)
F113D=F(2,"sid",".CSMsg.TMSG_SANDBOX_ALLIANCE_SIEGECAMP_DATA_RSP.sid",2,1,2,false,0,4,4)
F114D=F(2,"pos",".CSMsg.TMSG_SANDBOX_ALLIANCE_SIEGECAMP_DATA_RSP.pos",3,2,2,false,nil,11,10)
F115D=F(2,"errorCode",".CSMsg.TMSG_SANDBOX_ALLIANCE_SIEGECAMP_DATA_RSP.errorCode",4,3,1,false,nil,14,8)
M31G=D(1,"TMSG_SANDBOX_ALLIANCE_SIEGECAMP_DATA_RSP",".CSMsg.TMSG_SANDBOX_ALLIANCE_SIEGECAMP_DATA_RSP",false,{},{},nil,{})
F116D=F(2,"FaceID",".CSMsg.TSandboxReinforceInfo.FaceID",1,0,2,false,0,5,1)
F117D=F(2,"RoleName",".CSMsg.TSandboxReinforceInfo.RoleName",2,1,2,false,"",9,9)
F118D=F(2,"simpleTroopStr",".CSMsg.TSandboxReinforceInfo.simpleTroopStr",3,2,2,false,"",9,9)
F119D=F(2,"FrameID",".CSMsg.TSandboxReinforceInfo.FrameID",4,3,2,false,0,5,1)
F120D=F(2,"arriveTime",".CSMsg.TSandboxReinforceInfo.arriveTime",5,4,1,false,0,13,3)
F121D=F(2,"startTime",".CSMsg.TSandboxReinforceInfo.startTime",6,5,1,false,0,13,3)
F122D=F(2,"FaceStr",".CSMsg.TSandboxReinforceInfo.FaceStr",7,6,1,false,"",9,9)
M32G=D(1,"TSandboxReinforceInfo",".CSMsg.TSandboxReinforceInfo",false,{},{},nil,{})
F123D=F(2,"sid",".CSMsg.TMSG_SANDBOX_SIEGECAMP_REINFORCELIST_REQ.sid",1,0,2,false,0,4,4)
F124D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_SIEGECAMP_REINFORCELIST_REQ.sandboxSid",2,1,1,false,0,4,4)
M33G=D(1,"TMSG_SANDBOX_SIEGECAMP_REINFORCELIST_REQ",".CSMsg.TMSG_SANDBOX_SIEGECAMP_REINFORCELIST_REQ",false,{},{},nil,{})
F125D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_SIEGECAMP_REINFORCELIST_RSP.errCode",1,0,2,false,nil,14,8)
F126D=F(2,"List",".CSMsg.TMSG_SANDBOX_SIEGECAMP_REINFORCELIST_RSP.List",2,1,3,false,{},11,10)
F127D=F(2,"sid",".CSMsg.TMSG_SANDBOX_SIEGECAMP_REINFORCELIST_RSP.sid",3,2,2,false,0,4,4)
F128D=F(2,"dbid",".CSMsg.TMSG_SANDBOX_SIEGECAMP_REINFORCELIST_RSP.dbid",4,3,1,false,0,4,4)
F129D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_SIEGECAMP_REINFORCELIST_RSP.sandboxSid",5,4,1,false,0,4,4)
M34G=D(1,"TMSG_SANDBOX_SIEGECAMP_REINFORCELIST_RSP",".CSMsg.TMSG_SANDBOX_SIEGECAMP_REINFORCELIST_RSP",false,{},{},nil,{})
F130D=F(2,"teamInfo",".CSMsg.TMSG_SANDBOX_TEAMINFO_NTF.teamInfo",1,0,3,false,{},11,10)
M35G=D(1,"TMSG_SANDBOX_TEAMINFO_NTF",".CSMsg.TMSG_SANDBOX_TEAMINFO_NTF",false,{},{},nil,{})
F131D=F(2,"sid",".CSMsg.TMSG_SANDBOX_GET_DETAIL_REQ.sid",1,0,2,false,0,4,4)
F132D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_GET_DETAIL_REQ.sandboxSid",2,1,2,false,0,13,3)
F133D=F(2,"lineSid",".CSMsg.TMSG_SANDBOX_GET_DETAIL_REQ.lineSid",3,2,1,false,0,4,4)
F134D=F(2,"nViewLev",".CSMsg.TMSG_SANDBOX_GET_DETAIL_REQ.nViewLev",4,3,1,false,0,5,1)
M36G=D(1,"TMSG_SANDBOX_GET_DETAIL_REQ",".CSMsg.TMSG_SANDBOX_GET_DETAIL_REQ",false,{},{},nil,{})
F135D=F(2,"nAllianceID",".CSMsg.TNatureCityRankInfo.nAllianceID",1,0,2,false,0,5,1)
F136D=F(2,"sAllianceShortName",".CSMsg.TNatureCityRankInfo.sAllianceShortName",2,1,2,false,"",9,9)
F137D=F(2,"nScore",".CSMsg.TNatureCityRankInfo.nScore",3,2,2,false,0,13,3)
F138D=F(2,"ScoreTime",".CSMsg.TNatureCityRankInfo.ScoreTime",4,3,2,false,0,13,3)
M37G=D(1,"TNatureCityRankInfo",".CSMsg.TNatureCityRankInfo",false,{},{},nil,{})
F139D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_GET_DETAIL_RSP.errCode",1,0,2,false,nil,14,8)
F140D=F(2,"sid",".CSMsg.TMSG_SANDBOX_GET_DETAIL_RSP.sid",2,1,2,false,0,4,4)
F141D=F(2,"props",".CSMsg.TMSG_SANDBOX_GET_DETAIL_RSP.props",3,2,3,false,{},11,10)
F142D=F(2,"strProps",".CSMsg.TMSG_SANDBOX_GET_DETAIL_RSP.strProps",4,3,3,false,{},11,10)
F143D=F(2,"type",".CSMsg.TMSG_SANDBOX_GET_DETAIL_RSP.type",5,4,1,false,nil,14,8)
F144D=F(2,"collect_speed",".CSMsg.TMSG_SANDBOX_GET_DETAIL_RSP.collect_speed",6,5,3,false,{},13,3)
F145D=F(2,"collect_addLoad",".CSMsg.TMSG_SANDBOX_GET_DETAIL_RSP.collect_addLoad",7,6,3,false,{},13,3)
F146D=F(2,"roleInfoList",".CSMsg.TMSG_SANDBOX_GET_DETAIL_RSP.roleInfoList",8,7,3,false,{},11,10)
F147D=F(2,"rankInfoList",".CSMsg.TMSG_SANDBOX_GET_DETAIL_RSP.rankInfoList",9,8,3,false,{},11,10)
F148D=F(2,"carriageItems",".CSMsg.TMSG_SANDBOX_GET_DETAIL_RSP.carriageItems",10,9,3,false,{},11,10)
F149D=F(2,"lineSid",".CSMsg.TMSG_SANDBOX_GET_DETAIL_RSP.lineSid",11,10,1,false,0,4,4)
F150D=F(2,"presName",".CSMsg.TMSG_SANDBOX_GET_DETAIL_RSP.presName",12,11,1,false,"",9,9)
F151D=F(2,"trainCarriageDataList",".CSMsg.TMSG_SANDBOX_GET_DETAIL_RSP.trainCarriageDataList",13,12,3,false,{},11,10)
F152D=F(2,"entityData",".CSMsg.TMSG_SANDBOX_GET_DETAIL_RSP.entityData",14,13,1,false,nil,11,10)
F153D=F(2,"marchData",".CSMsg.TMSG_SANDBOX_GET_DETAIL_RSP.marchData",15,14,1,false,nil,11,10)
M38G=D(1,"TMSG_SANDBOX_GET_DETAIL_RSP",".CSMsg.TMSG_SANDBOX_GET_DETAIL_RSP",false,{},{},nil,{})
F154D=F(2,"callback",".CSMsg.TMSG_SANDBOX_CALLBACK_DETAIL_REQ.callback",1,0,2,false,0,5,1)
F155D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_CALLBACK_DETAIL_REQ.sandboxSid",2,1,2,false,0,13,3)
F156D=F(2,"sid",".CSMsg.TMSG_SANDBOX_CALLBACK_DETAIL_REQ.sid",3,2,2,false,0,4,4)
F157D=F(2,"prop_int",".CSMsg.TMSG_SANDBOX_CALLBACK_DETAIL_REQ.prop_int",4,3,3,false,{},14,8)
F158D=F(2,"prop_str",".CSMsg.TMSG_SANDBOX_CALLBACK_DETAIL_REQ.prop_str",5,4,3,false,{},14,8)
F159D=F(2,"pos",".CSMsg.TMSG_SANDBOX_CALLBACK_DETAIL_REQ.pos",6,5,1,false,nil,11,10)
M42G=D(1,"TMSG_SANDBOX_CALLBACK_DETAIL_REQ",".CSMsg.TMSG_SANDBOX_CALLBACK_DETAIL_REQ",false,{},{},nil,{})
F160D=F(2,"err",".CSMsg.TMSG_SANDBOX_CALLBACK_DETAIL_RSP.err",1,0,2,false,nil,14,8)
F161D=F(2,"callback",".CSMsg.TMSG_SANDBOX_CALLBACK_DETAIL_RSP.callback",2,1,2,false,0,5,1)
F162D=F(2,"exist",".CSMsg.TMSG_SANDBOX_CALLBACK_DETAIL_RSP.exist",3,2,2,false,false,8,7)
F163D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_CALLBACK_DETAIL_RSP.sandboxSid",4,3,2,false,0,13,3)
F164D=F(2,"sid",".CSMsg.TMSG_SANDBOX_CALLBACK_DETAIL_RSP.sid",5,4,2,false,0,4,4)
F165D=F(2,"enType",".CSMsg.TMSG_SANDBOX_CALLBACK_DETAIL_RSP.enType",6,5,1,false,nil,14,8)
F166D=F(2,"prop_int",".CSMsg.TMSG_SANDBOX_CALLBACK_DETAIL_RSP.prop_int",7,6,3,false,{},11,10)
F167D=F(2,"prop_str",".CSMsg.TMSG_SANDBOX_CALLBACK_DETAIL_RSP.prop_str",8,7,3,false,{},11,10)
F168D=F(2,"pos",".CSMsg.TMSG_SANDBOX_CALLBACK_DETAIL_RSP.pos",9,8,1,false,nil,11,10)
M45G=D(1,"TMSG_SANDBOX_CALLBACK_DETAIL_RSP",".CSMsg.TMSG_SANDBOX_CALLBACK_DETAIL_RSP",false,{},{},nil,{})
F169D=F(2,"sid",".CSMsg.TMSG_SANDBOX_ATTACK_REQ.sid",1,0,2,false,0,4,4)
F170D=F(2,"pos",".CSMsg.TMSG_SANDBOX_ATTACK_REQ.pos",2,1,2,false,nil,11,10)
F171D=F(2,"teamIndex",".CSMsg.TMSG_SANDBOX_ATTACK_REQ.teamIndex",3,2,2,false,0,5,1)
F172D=F(2,"soldierNum",".CSMsg.TMSG_SANDBOX_ATTACK_REQ.soldierNum",4,3,1,false,nil,11,10)
F173D=F(2,"lineType",".CSMsg.TMSG_SANDBOX_ATTACK_REQ.lineType",5,4,1,false,0,5,1)
M46G=D(1,"TMSG_SANDBOX_ATTACK_REQ",".CSMsg.TMSG_SANDBOX_ATTACK_REQ",false,{},{},nil,{})
F174D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_ATTACK_RSP.errCode",1,0,2,false,nil,14,8)
M48G=D(1,"TMSG_SANDBOX_ATTACK_RSP",".CSMsg.TMSG_SANDBOX_ATTACK_RSP",false,{},{},nil,{})
F175D=F(2,"lineUp",".CSMsg.TMSG_SANDBOX_SAVE_TEAM_REQ.lineUp",1,0,2,false,nil,11,10)
F176D=F(2,"teamIndex",".CSMsg.TMSG_SANDBOX_SAVE_TEAM_REQ.teamIndex",2,1,2,false,0,5,1)
M49G=D(1,"TMSG_SANDBOX_SAVE_TEAM_REQ",".CSMsg.TMSG_SANDBOX_SAVE_TEAM_REQ",false,{},{},nil,{})
F177D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_SAVE_TEAM_RSP.errCode",1,0,2,false,nil,14,8)
M51G=D(1,"TMSG_SANDBOX_SAVE_TEAM_RSP",".CSMsg.TMSG_SANDBOX_SAVE_TEAM_RSP",false,{},{},nil,{})
F178D=F(2,"sid",".CSMsg.TMSG_SANDBOX_EXPEDITION_DETAIL_REQ.sid",1,0,2,false,0,4,4)
F179D=F(2,"attackType",".CSMsg.TMSG_SANDBOX_EXPEDITION_DETAIL_REQ.attackType",2,1,2,false,0,5,1)
F180D=F(2,"attackState",".CSMsg.TMSG_SANDBOX_EXPEDITION_DETAIL_REQ.attackState",3,2,2,false,0,5,1)
F181D=F(2,"teamIndex",".CSMsg.TMSG_SANDBOX_EXPEDITION_DETAIL_REQ.teamIndex",4,3,2,false,0,5,1)
M52G=D(1,"TMSG_SANDBOX_EXPEDITION_DETAIL_REQ",".CSMsg.TMSG_SANDBOX_EXPEDITION_DETAIL_REQ",false,{},{},nil,{})
F182D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_EXPEDITION_DETAIL_RSP.errCode",1,0,2,false,nil,14,8)
F183D=F(2,"sid",".CSMsg.TMSG_SANDBOX_EXPEDITION_DETAIL_RSP.sid",2,1,2,false,0,4,4)
F184D=F(2,"duration",".CSMsg.TMSG_SANDBOX_EXPEDITION_DETAIL_RSP.duration",3,2,1,false,0,5,1)
F185D=F(2,"attackType",".CSMsg.TMSG_SANDBOX_EXPEDITION_DETAIL_RSP.attackType",4,3,1,false,0,5,1)
F186D=F(2,"resourceAsTarget",".CSMsg.TMSG_SANDBOX_EXPEDITION_DETAIL_RSP.resourceAsTarget",5,4,1,false,0,5,1)
F187D=F(2,"notSDayReward",".CSMsg.TMSG_SANDBOX_EXPEDITION_DETAIL_RSP.notSDayReward",6,5,1,false,false,8,7)
F188D=F(2,"teamIndex",".CSMsg.TMSG_SANDBOX_EXPEDITION_DETAIL_RSP.teamIndex",7,6,1,false,0,5,1)
M53G=D(1,"TMSG_SANDBOX_EXPEDITION_DETAIL_RSP",".CSMsg.TMSG_SANDBOX_EXPEDITION_DETAIL_RSP",false,{},{},nil,{})
F189D=F(2,"sandBoxSid",".CSMsg.TMSG_MOVE_CITY_REQ.sandBoxSid",1,0,2,false,0,13,3)
F190D=F(2,"pos",".CSMsg.TMSG_MOVE_CITY_REQ.pos",2,1,2,false,nil,11,10)
F191D=F(2,"crossMoveType",".CSMsg.TMSG_MOVE_CITY_REQ.crossMoveType",3,2,1,false,nil,14,8)
M54G=D(1,"TMSG_MOVE_CITY_REQ",".CSMsg.TMSG_MOVE_CITY_REQ",false,{},{},nil,{})
F192D=F(2,"errCode",".CSMsg.TMSG_MOVE_CITY_RSP.errCode",1,0,2,false,nil,14,8)
F193D=F(2,"pos",".CSMsg.TMSG_MOVE_CITY_RSP.pos",2,1,1,false,nil,11,10)
F194D=F(2,"sandBoxSid",".CSMsg.TMSG_MOVE_CITY_RSP.sandBoxSid",3,2,2,false,0,13,3)
F195D=F(2,"crossMoveType",".CSMsg.TMSG_MOVE_CITY_RSP.crossMoveType",4,3,1,false,nil,14,8)
M56G=D(1,"TMSG_MOVE_CITY_RSP",".CSMsg.TMSG_MOVE_CITY_RSP",false,{},{},nil,{})
F196D=F(2,"allianceId",".CSMsg.TMSG_NEWPALYER_FREEMOVE_REQ.allianceId",1,0,2,false,0,5,1)
F197D=F(2,"type",".CSMsg.TMSG_NEWPALYER_FREEMOVE_REQ.type",2,1,1,false,0,5,1)
M57G=D(1,"TMSG_NEWPALYER_FREEMOVE_REQ",".CSMsg.TMSG_NEWPALYER_FREEMOVE_REQ",false,{},{},nil,{})
F198D=F(2,"errorCode",".CSMsg.TMSG_NEWPALYER_FREEMOVE_RSP.errorCode",1,0,2,false,nil,14,8)
F199D=F(2,"pos",".CSMsg.TMSG_NEWPALYER_FREEMOVE_RSP.pos",2,1,1,false,nil,11,10)
F200D=F(2,"type",".CSMsg.TMSG_NEWPALYER_FREEMOVE_RSP.type",3,2,1,false,0,5,1)
F201D=F(2,"allianceId",".CSMsg.TMSG_NEWPALYER_FREEMOVE_RSP.allianceId",4,3,1,false,0,5,1)
M58G=D(1,"TMSG_NEWPALYER_FREEMOVE_RSP",".CSMsg.TMSG_NEWPALYER_FREEMOVE_RSP",false,{},{},nil,{})
F202D=F(2,"teamIndex",".CSMsg.TMSG_SANDBOX_BACK_REQ.teamIndex",1,0,2,false,0,5,1)
M59G=D(1,"TMSG_SANDBOX_BACK_REQ",".CSMsg.TMSG_SANDBOX_BACK_REQ",false,{},{},nil,{})
F203D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_BACK_RSP.errCode",1,0,2,false,nil,14,8)
F204D=F(2,"teamIndex",".CSMsg.TMSG_SANDBOX_BACK_RSP.teamIndex",2,1,1,false,0,5,1)
M60G=D(1,"TMSG_SANDBOX_BACK_RSP",".CSMsg.TMSG_SANDBOX_BACK_RSP",false,{},{},nil,{})
F205D=F(2,"targetType",".CSMsg.TSandBoxLoot.targetType",1,0,2,false,nil,14,8)
F206D=F(2,"targetId",".CSMsg.TSandBoxLoot.targetId",2,1,2,false,0,5,1)
F207D=F(2,"time",".CSMsg.TSandBoxLoot.time",3,2,2,false,0,5,1)
F208D=F(2,"isTeamLeader",".CSMsg.TSandBoxLoot.isTeamLeader",4,3,2,false,false,8,7)
F209D=F(2,"isAlreadyGet",".CSMsg.TSandBoxLoot.isAlreadyGet",5,4,2,false,false,8,7)
F210D=F(2,"isFirstKill",".CSMsg.TSandBoxLoot.isFirstKill",6,5,2,false,false,8,7)
F211D=F(2,"rewards",".CSMsg.TSandBoxLoot.rewards",7,6,3,false,{},11,10)
F212D=F(2,"resourcRW",".CSMsg.TSandBoxLoot.resourcRW",8,7,3,false,{},11,10)
F213D=F(2,"targetName",".CSMsg.TSandBoxLoot.targetName",9,8,1,false,"",9,9)
F214D=F(2,"nDayPlunder",".CSMsg.TSandBoxLoot.nDayPlunder",10,9,1,false,0,4,4)
F215D=F(2,"nPlunderPercent",".CSMsg.TSandBoxLoot.nPlunderPercent",11,10,1,false,0,5,1)
F216D=F(2,"FaceID",".CSMsg.TSandBoxLoot.FaceID",12,11,1,false,0,5,1)
F217D=F(2,"FrameID",".CSMsg.TSandBoxLoot.FrameID",13,12,1,false,0,5,1)
F218D=F(2,"isMass",".CSMsg.TSandBoxLoot.isMass",14,13,1,false,false,8,7)
F219D=F(2,"HeroStar",".CSMsg.TSandBoxLoot.HeroStar",15,14,1,false,0,5,1)
F220D=F(2,"HeroSkillLevel",".CSMsg.TSandBoxLoot.HeroSkillLevel",16,15,1,false,0,5,1)
F221D=F(2,"FaceStr",".CSMsg.TSandBoxLoot.FaceStr",17,16,1,false,"",9,9)
M61G=D(1,"TSandBoxLoot",".CSMsg.TSandBoxLoot",false,{},{},nil,{})
F222D=F(2,"maxIndex",".CSMsg.TMSG_SANDBOX_LOOT_DATA_NTF.maxIndex",4,0,2,false,0,5,1)
M64G=D(1,"TMSG_SANDBOX_LOOT_DATA_NTF",".CSMsg.TMSG_SANDBOX_LOOT_DATA_NTF",false,{},{},nil,{})
F223D=F(2,"fromIndex",".CSMsg.TMSG_SANDBOX_LOOT_INFO_REQ.fromIndex",1,0,2,false,0,5,1)
F224D=F(2,"toIndex",".CSMsg.TMSG_SANDBOX_LOOT_INFO_REQ.toIndex",2,1,2,false,0,5,1)
M65G=D(1,"TMSG_SANDBOX_LOOT_INFO_REQ",".CSMsg.TMSG_SANDBOX_LOOT_INFO_REQ",false,{},{},nil,{})
F225D=F(2,"fromIndex",".CSMsg.TMSG_SANDBOX_LOOT_INFO_RSP.fromIndex",1,0,2,false,0,5,1)
F226D=F(2,"toIndex",".CSMsg.TMSG_SANDBOX_LOOT_INFO_RSP.toIndex",2,1,2,false,0,5,1)
F227D=F(2,"lootList",".CSMsg.TMSG_SANDBOX_LOOT_INFO_RSP.lootList",3,2,3,false,{},11,10)
F228D=F(2,"maxIndex",".CSMsg.TMSG_SANDBOX_LOOT_INFO_RSP.maxIndex",4,3,2,false,0,5,1)
M66G=D(1,"TMSG_SANDBOX_LOOT_INFO_RSP",".CSMsg.TMSG_SANDBOX_LOOT_INFO_RSP",false,{},{},nil,{})
F229D=F(2,"getCount",".CSMsg.TMSG_SANDBOX_GET_LOOT_REQ.getCount",1,0,2,false,0,5,1)
M67G=D(1,"TMSG_SANDBOX_GET_LOOT_REQ",".CSMsg.TMSG_SANDBOX_GET_LOOT_REQ",false,{},{},nil,{})
F230D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_GET_LOOT_RSP.errCode",1,0,2,false,nil,14,8)
F231D=F(2,"getCount",".CSMsg.TMSG_SANDBOX_GET_LOOT_RSP.getCount",2,1,2,false,0,5,1)
F232D=F(2,"maxIndex",".CSMsg.TMSG_SANDBOX_GET_LOOT_RSP.maxIndex",3,2,2,false,0,5,1)
M68G=D(1,"TMSG_SANDBOX_GET_LOOT_RSP",".CSMsg.TMSG_SANDBOX_GET_LOOT_RSP",false,{},{},nil,{})
F233D=F(2,"teamIndex",".CSMsg.TMSG_SANDBOX_ACCELERATE_REQ.teamIndex",1,0,2,false,0,5,1)
F234D=F(2,"accelerateIdx",".CSMsg.TMSG_SANDBOX_ACCELERATE_REQ.accelerateIdx",2,1,2,false,0,5,1)
M69G=D(1,"TMSG_SANDBOX_ACCELERATE_REQ",".CSMsg.TMSG_SANDBOX_ACCELERATE_REQ",false,{},{},nil,{})
F235D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_ACCELERATE_RSP.errCode",1,0,2,false,nil,14,8)
F236D=F(2,"teamIndex",".CSMsg.TMSG_SANDBOX_ACCELERATE_RSP.teamIndex",2,1,2,false,0,5,1)
M70G=D(1,"TMSG_SANDBOX_ACCELERATE_RSP",".CSMsg.TMSG_SANDBOX_ACCELERATE_RSP",false,{},{},nil,{})
M71G=D(1,"TMSG_SANDBOX_CROSS_BACK_POS_REQ",".CSMsg.TMSG_SANDBOX_CROSS_BACK_POS_REQ",false,{},{},{},{})
F237D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_CROSS_BACK_POS_RSP.errCode",1,0,2,false,nil,14,8)
F238D=F(2,"x",".CSMsg.TMSG_SANDBOX_CROSS_BACK_POS_RSP.x",2,1,2,false,0,5,1)
F239D=F(2,"y",".CSMsg.TMSG_SANDBOX_CROSS_BACK_POS_RSP.y",3,2,2,false,0,5,1)
M72G=D(1,"TMSG_SANDBOX_CROSS_BACK_POS_RSP",".CSMsg.TMSG_SANDBOX_CROSS_BACK_POS_RSP",false,{},{},nil,{})
F240D=F(2,"type",".CSMsg.TMSG_STAMINA_OPER_REQ.type",1,0,2,false,nil,14,8)
F241D=F(2,"operParam",".CSMsg.TMSG_STAMINA_OPER_REQ.operParam",2,1,2,false,0,5,1)
M73G=D(1,"TMSG_STAMINA_OPER_REQ",".CSMsg.TMSG_STAMINA_OPER_REQ",false,{},{},nil,{})
F242D=F(2,"errCode",".CSMsg.TMSG_STAMINA_OPER_RSP.errCode",1,0,2,false,nil,14,8)
F243D=F(2,"type",".CSMsg.TMSG_STAMINA_OPER_RSP.type",2,1,2,false,nil,14,8)
F244D=F(2,"curStamina",".CSMsg.TMSG_STAMINA_OPER_RSP.curStamina",3,2,2,false,0,5,1)
F245D=F(2,"recoveryBTime",".CSMsg.TMSG_STAMINA_OPER_RSP.recoveryBTime",4,3,2,false,0,5,1)
M75G=D(1,"TMSG_STAMINA_OPER_RSP",".CSMsg.TMSG_STAMINA_OPER_RSP",false,{},{},nil,{})
F246D=F(2,"entityType",".CSMsg.TMSG_SANDBOX_SEARCH_REQ.entityType",1,0,2,false,0,5,1)
F247D=F(2,"targetType",".CSMsg.TMSG_SANDBOX_SEARCH_REQ.targetType",2,1,2,false,0,5,1)
F248D=F(2,"targetLevel",".CSMsg.TMSG_SANDBOX_SEARCH_REQ.targetLevel",3,2,2,false,0,5,1)
F249D=F(2,"searchID",".CSMsg.TMSG_SANDBOX_SEARCH_REQ.searchID",4,3,1,false,0,5,1)
F250D=F(2,"targetID",".CSMsg.TMSG_SANDBOX_SEARCH_REQ.targetID",5,4,1,false,0,5,1)
M76G=D(1,"TMSG_SANDBOX_SEARCH_REQ",".CSMsg.TMSG_SANDBOX_SEARCH_REQ",false,{},{},nil,{})
F251D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_SEARCH_RSP.errCode",1,0,2,false,nil,14,8)
F252D=F(2,"sid",".CSMsg.TMSG_SANDBOX_SEARCH_RSP.sid",2,1,1,false,0,4,4)
F253D=F(2,"pos",".CSMsg.TMSG_SANDBOX_SEARCH_RSP.pos",3,2,1,false,nil,11,10)
M77G=D(1,"TMSG_SANDBOX_SEARCH_RSP",".CSMsg.TMSG_SANDBOX_SEARCH_RSP",false,{},{},nil,{})
F254D=F(2,"pos",".CSMsg.TOneMarkInfo.pos",1,0,2,false,nil,11,10)
F255D=F(2,"nType",".CSMsg.TOneMarkInfo.nType",2,1,2,false,0,13,3)
F256D=F(2,"sRemark",".CSMsg.TOneMarkInfo.sRemark",3,2,2,false,"",9,9)
F257D=F(2,"bServerID",".CSMsg.TOneMarkInfo.bServerID",4,3,2,false,0,13,3)
F258D=F(2,"nIndex",".CSMsg.TOneMarkInfo.nIndex",5,4,2,false,0,13,3)
F259D=F(2,"nSymbol",".CSMsg.TOneMarkInfo.nSymbol",6,5,1,false,0,13,3)
F260D=F(2,"sid",".CSMsg.TOneMarkInfo.sid",7,6,1,false,0,4,4)
F261D=F(2,"name",".CSMsg.TOneMarkInfo.name",8,7,1,false,"",9,9)
F262D=F(2,"entitylevel",".CSMsg.TOneMarkInfo.entitylevel",9,8,1,false,0,4,4)
F263D=F(2,"entityName",".CSMsg.TOneMarkInfo.entityName",10,9,1,false,0,4,4)
F264D=F(2,"quality",".CSMsg.TOneMarkInfo.quality",11,10,1,false,0,4,4)
M78G=D(1,"TOneMarkInfo",".CSMsg.TOneMarkInfo",false,{},{},nil,{})
F265D=F(2,"operate",".CSMsg.TMSG_SANDBOX_MARK_REQ.operate",1,0,2,false,0,13,3)
F266D=F(2,"info",".CSMsg.TMSG_SANDBOX_MARK_REQ.info",2,1,2,false,nil,11,10)
M79G=D(1,"TMSG_SANDBOX_MARK_REQ",".CSMsg.TMSG_SANDBOX_MARK_REQ",false,{},{},nil,{})
F267D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_MARK_RSP.errCode",1,0,2,false,nil,14,8)
F268D=F(2,"operate",".CSMsg.TMSG_SANDBOX_MARK_RSP.operate",2,1,1,false,0,13,3)
F269D=F(2,"info",".CSMsg.TMSG_SANDBOX_MARK_RSP.info",3,2,1,false,nil,11,10)
M80G=D(1,"TMSG_SANDBOX_MARK_RSP",".CSMsg.TMSG_SANDBOX_MARK_RSP",false,{},{},nil,{})
F270D=F(2,"infoList",".CSMsg.TMSG_SANDBOX_MARKLIST_NTF.infoList",1,0,3,false,{},11,10)
F271D=F(2,"nType",".CSMsg.TMSG_SANDBOX_MARKLIST_NTF.nType",2,1,2,false,0,13,3)
M81G=D(1,"TMSG_SANDBOX_MARKLIST_NTF",".CSMsg.TMSG_SANDBOX_MARKLIST_NTF",false,{},{},nil,{})
F272D=F(2,"sid",".CSMsg.TMSG_SANDBOX_REINFORCELIST_REQ.sid",1,0,2,false,0,4,4)
F273D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_REINFORCELIST_REQ.sandboxSid",2,1,1,false,0,4,4)
M82G=D(1,"TMSG_SANDBOX_REINFORCELIST_REQ",".CSMsg.TMSG_SANDBOX_REINFORCELIST_REQ",false,{},{},nil,{})
F274D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_REINFORCELIST_RSP.errCode",1,0,2,false,nil,14,8)
F275D=F(2,"List",".CSMsg.TMSG_SANDBOX_REINFORCELIST_RSP.List",2,1,3,false,{},11,10)
F276D=F(2,"sid",".CSMsg.TMSG_SANDBOX_REINFORCELIST_RSP.sid",3,2,2,false,0,4,4)
F277D=F(2,"dbid",".CSMsg.TMSG_SANDBOX_REINFORCELIST_RSP.dbid",4,3,1,false,0,4,4)
F278D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_REINFORCELIST_RSP.sandboxSid",5,4,1,false,0,4,4)
M83G=D(1,"TMSG_SANDBOX_REINFORCELIST_RSP",".CSMsg.TMSG_SANDBOX_REINFORCELIST_RSP",false,{},{},nil,{})
F279D=F(2,"sid",".CSMsg.TMSG_SANDBOX_REINFORCE_REQ.sid",1,0,2,false,0,4,4)
F280D=F(2,"teamIndex",".CSMsg.TMSG_SANDBOX_REINFORCE_REQ.teamIndex",2,1,2,false,0,5,1)
F281D=F(2,"soldierNum",".CSMsg.TMSG_SANDBOX_REINFORCE_REQ.soldierNum",3,2,1,false,nil,11,10)
F282D=F(2,"nReinforceType",".CSMsg.TMSG_SANDBOX_REINFORCE_REQ.nReinforceType",4,3,1,false,nil,14,8)
M84G=D(1,"TMSG_SANDBOX_REINFORCE_REQ",".CSMsg.TMSG_SANDBOX_REINFORCE_REQ",false,{},{},nil,{})
F283D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_REINFORCE_RSP.errCode",1,0,2,false,nil,14,8)
F284D=F(2,"pos",".CSMsg.TMSG_SANDBOX_REINFORCE_RSP.pos",2,1,1,false,nil,11,10)
M86G=D(1,"TMSG_SANDBOX_REINFORCE_RSP",".CSMsg.TMSG_SANDBOX_REINFORCE_RSP",false,{},{},nil,{})
F285D=F(2,"sid",".CSMsg.TMSG_SANDBOX_REINFORCEBACK_REQ.sid",1,0,2,false,0,4,4)
F286D=F(2,"teamDbid",".CSMsg.TMSG_SANDBOX_REINFORCEBACK_REQ.teamDbid",2,1,2,false,0,5,1)
F287D=F(2,"teamIndex",".CSMsg.TMSG_SANDBOX_REINFORCEBACK_REQ.teamIndex",3,2,2,false,0,5,1)
M87G=D(1,"TMSG_SANDBOX_REINFORCEBACK_REQ",".CSMsg.TMSG_SANDBOX_REINFORCEBACK_REQ",false,{},{},nil,{})
F288D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_REINFORCEBACK_RSP.errCode",1,0,2,false,nil,14,8)
F289D=F(2,"sid",".CSMsg.TMSG_SANDBOX_REINFORCEBACK_RSP.sid",2,1,2,false,0,4,4)
F290D=F(2,"teamDbid",".CSMsg.TMSG_SANDBOX_REINFORCEBACK_RSP.teamDbid",3,2,2,false,0,5,1)
F291D=F(2,"teamIndex",".CSMsg.TMSG_SANDBOX_REINFORCEBACK_RSP.teamIndex",4,3,2,false,0,5,1)
M88G=D(1,"TMSG_SANDBOX_REINFORCEBACK_RSP",".CSMsg.TMSG_SANDBOX_REINFORCEBACK_RSP",false,{},{},nil,{})
F292D=F(2,"roleId",".CSMsg.TSandboxMassMember.roleId",1,0,2,false,0,13,3)
F293D=F(2,"strName",".CSMsg.TSandboxMassMember.strName",2,1,2,false,"",9,9)
F294D=F(2,"arrivalTime",".CSMsg.TSandboxMassMember.arrivalTime",3,2,2,false,0,13,3)
F295D=F(2,"teamIndex",".CSMsg.TSandboxMassMember.teamIndex",4,3,2,false,0,13,3)
F296D=F(2,"team",".CSMsg.TSandboxMassMember.team",5,4,2,false,nil,11,10)
F297D=F(2,"faceStr",".CSMsg.TSandboxMassMember.faceStr",6,5,1,false,"",9,9)
F298D=F(2,"frameID",".CSMsg.TSandboxMassMember.frameID",7,6,1,false,0,5,1)
F299D=F(2,"power",".CSMsg.TSandboxMassMember.power",8,7,1,false,0,3,2)
F300D=F(2,"sex",".CSMsg.TSandboxMassMember.sex",9,8,1,false,0,13,3)
M89G=D(1,"TSandboxMassMember",".CSMsg.TSandboxMassMember",false,{},{},nil,{})
F301D=F(2,"massTeamId",".CSMsg.TSandboxMassTeam.massTeamId",1,0,2,false,0,4,4)
F302D=F(2,"massLeaderId",".CSMsg.TSandboxMassTeam.massLeaderId",2,1,2,false,0,5,1)
F303D=F(2,"targetType",".CSMsg.TSandboxMassTeam.targetType",3,2,2,false,0,5,1)
F304D=F(2,"targetId",".CSMsg.TSandboxMassTeam.targetId",4,3,2,false,0,5,1)
F305D=F(2,"massState",".CSMsg.TSandboxMassTeam.massState",5,4,2,false,0,5,1)
F306D=F(2,"massFinishTime",".CSMsg.TSandboxMassTeam.massFinishTime",6,5,2,false,0,5,1)
F307D=F(2,"targetPos",".CSMsg.TSandboxMassTeam.targetPos",7,6,2,false,nil,11,10)
F308D=F(2,"massMember",".CSMsg.TSandboxMassTeam.massMember",8,7,3,false,{},11,10)
F309D=F(2,"targetExteriorId",".CSMsg.TSandboxMassTeam.targetExteriorId",9,8,1,false,0,5,1)
F310D=F(2,"targetName",".CSMsg.TSandboxMassTeam.targetName",10,9,1,false,"",9,9)
F311D=F(2,"targetSId",".CSMsg.TSandboxMassTeam.targetSId",11,10,2,false,0,4,4)
F312D=F(2,"leaderPos",".CSMsg.TSandboxMassTeam.leaderPos",12,11,2,false,nil,11,10)
F313D=F(2,"targetLevel",".CSMsg.TSandboxMassTeam.targetLevel",13,12,1,false,0,5,1)
F314D=F(2,"sandboxSid",".CSMsg.TSandboxMassTeam.sandboxSid",14,13,1,false,0,13,3)
F315D=F(2,"targetLineSId",".CSMsg.TSandboxMassTeam.targetLineSId",15,14,1,false,0,4,4)
F316D=F(2,"massType",".CSMsg.TSandboxMassTeam.massType",16,15,1,false,0,5,1)
F317D=F(2,"allianceId",".CSMsg.TSandboxMassTeam.allianceId",17,16,1,false,0,5,1)
F318D=F(2,"allianceShortName",".CSMsg.TSandboxMassTeam.allianceShortName",18,17,1,false,"",9,9)
F319D=F(2,"gotoTargetMarchType",".CSMsg.TSandboxMassTeam.gotoTargetMarchType",19,18,1,false,0,5,1)
F320D=F(2,"targetEffectId",".CSMsg.TSandboxMassTeam.targetEffectId",20,19,1,false,0,5,1)
M90G=D(1,"TSandboxMassTeam",".CSMsg.TSandboxMassTeam",false,{},{},nil,{})
F321D=F(2,"massCount",".CSMsg.TMSG_SANDBOX_MASS_MAIN_ICON_NTF.massCount",1,0,2,false,0,5,1)
F322D=F(2,"teamIdelCount",".CSMsg.TMSG_SANDBOX_MASS_MAIN_ICON_NTF.teamIdelCount",2,1,2,false,0,5,1)
F323D=F(2,"minMassFinishTime",".CSMsg.TMSG_SANDBOX_MASS_MAIN_ICON_NTF.minMassFinishTime",3,2,1,false,0,13,3)
F324D=F(2,"isWaiting",".CSMsg.TMSG_SANDBOX_MASS_MAIN_ICON_NTF.isWaiting",4,3,1,false,false,8,7)
M91G=D(1,"TMSG_SANDBOX_MASS_MAIN_ICON_NTF",".CSMsg.TMSG_SANDBOX_MASS_MAIN_ICON_NTF",false,{},{},nil,{})
F325D=F(2,"massTeamId",".CSMsg.TMSG_SANDBOX_MASS_CHANGE_NTF.massTeamId",1,0,2,false,0,4,4)
F326D=F(2,"changeType",".CSMsg.TMSG_SANDBOX_MASS_CHANGE_NTF.changeType",2,1,2,false,nil,14,8)
F327D=F(2,"massMember",".CSMsg.TMSG_SANDBOX_MASS_CHANGE_NTF.massMember",3,2,1,false,nil,11,10)
M92G=D(1,"TMSG_SANDBOX_MASS_CHANGE_NTF",".CSMsg.TMSG_SANDBOX_MASS_CHANGE_NTF",false,{},{},nil,{})
F328D=F(2,"operType",".CSMsg.TMSG_SANDBOX__MASS_OPER_REQ.operType",1,0,2,false,nil,14,8)
F329D=F(2,"operParam1",".CSMsg.TMSG_SANDBOX__MASS_OPER_REQ.operParam1",2,1,2,false,0,5,1)
F330D=F(2,"operParam2",".CSMsg.TMSG_SANDBOX__MASS_OPER_REQ.operParam2",3,2,2,false,0,5,1)
F331D=F(2,"operParam3",".CSMsg.TMSG_SANDBOX__MASS_OPER_REQ.operParam3",4,3,2,false,0,4,4)
F332D=F(2,"soldierNum",".CSMsg.TMSG_SANDBOX__MASS_OPER_REQ.soldierNum",5,4,1,false,nil,11,10)
F333D=F(2,"callback",".CSMsg.TMSG_SANDBOX__MASS_OPER_REQ.callback",6,5,1,false,0,5,1)
M94G=D(1,"TMSG_SANDBOX__MASS_OPER_REQ",".CSMsg.TMSG_SANDBOX__MASS_OPER_REQ",false,{},{},nil,{})
F334D=F(2,"errCode",".CSMsg.TMSG_SANDBOX__MASS_OPER_RSP.errCode",1,0,2,false,nil,14,8)
F335D=F(2,"operType",".CSMsg.TMSG_SANDBOX__MASS_OPER_RSP.operType",2,1,2,false,nil,14,8)
F336D=F(2,"callback",".CSMsg.TMSG_SANDBOX__MASS_OPER_RSP.callback",3,2,1,false,0,5,1)
F337D=F(2,"teamInfo",".CSMsg.TMSG_SANDBOX__MASS_OPER_RSP.teamInfo",4,3,1,false,nil,11,10)
M95G=D(1,"TMSG_SANDBOX__MASS_OPER_RSP",".CSMsg.TMSG_SANDBOX__MASS_OPER_RSP",false,{},{},nil,{})
M96G=D(1,"TMSG_SANDBOX_MASS_TEAM_REQ",".CSMsg.TMSG_SANDBOX_MASS_TEAM_REQ",false,{},{},{},{})
F338D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_MASS_TEAM_RSP.errCode",1,0,2,false,nil,14,8)
F339D=F(2,"teamInfo",".CSMsg.TMSG_SANDBOX_MASS_TEAM_RSP.teamInfo",2,1,3,false,{},11,10)
M97G=D(1,"TMSG_SANDBOX_MASS_TEAM_RSP",".CSMsg.TMSG_SANDBOX_MASS_TEAM_RSP",false,{},{},nil,{})
F340D=F(2,"massTeamId",".CSMsg.TMSG_SANDBOX_MASS_TEAM_MEMBER_REQ.massTeamId",1,0,2,false,0,4,4)
M98G=D(1,"TMSG_SANDBOX_MASS_TEAM_MEMBER_REQ",".CSMsg.TMSG_SANDBOX_MASS_TEAM_MEMBER_REQ",false,{},{},nil,{})
F341D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_MASS_TEAM_MEMBER_RSP.errCode",1,0,2,false,nil,14,8)
F342D=F(2,"massTeamId",".CSMsg.TMSG_SANDBOX_MASS_TEAM_MEMBER_RSP.massTeamId",2,1,2,false,0,4,4)
F343D=F(2,"memberInfo",".CSMsg.TMSG_SANDBOX_MASS_TEAM_MEMBER_RSP.memberInfo",3,2,3,false,{},11,10)
M99G=D(1,"TMSG_SANDBOX_MASS_TEAM_MEMBER_RSP",".CSMsg.TMSG_SANDBOX_MASS_TEAM_MEMBER_RSP",false,{},{},nil,{})
F344D=F(2,"massTeamId",".CSMsg.TMSG_SANDBOX_MASS_LEADER_DEL_NTF.massTeamId",1,0,1,false,0,4,4)
F345D=F(2,"leaderName",".CSMsg.TMSG_SANDBOX_MASS_LEADER_DEL_NTF.leaderName",2,1,1,false,"",9,9)
M100G=D(1,"TMSG_SANDBOX_MASS_LEADER_DEL_NTF",".CSMsg.TMSG_SANDBOX_MASS_LEADER_DEL_NTF",false,{},{},nil,{})
F346D=F(2,"isAlert",".CSMsg.TMSG_SANDBOX_ALERT_NTF.isAlert",1,0,2,false,false,8,7)
M101G=D(1,"TMSG_SANDBOX_ALERT_NTF",".CSMsg.TMSG_SANDBOX_ALERT_NTF",false,{},{},nil,{})
F347D=F(2,"info",".CSMsg.TSandbox_AlertData_BeAtkCity.info",1,0,1,false,nil,11,10)
F348D=F(2,"marchSid",".CSMsg.TSandbox_AlertData_BeAtkCity.marchSid",2,1,1,false,0,4,4)
F349D=F(2,"timeBeg",".CSMsg.TSandbox_AlertData_BeAtkCity.timeBeg",3,2,1,false,0,13,3)
F350D=F(2,"timeEnd",".CSMsg.TSandbox_AlertData_BeAtkCity.timeEnd",4,3,1,false,0,13,3)
F351D=F(2,"posBeg",".CSMsg.TSandbox_AlertData_BeAtkCity.posBeg",5,4,1,false,nil,11,10)
F352D=F(2,"posNow",".CSMsg.TSandbox_AlertData_BeAtkCity.posNow",6,5,1,false,nil,11,10)
M102G=D(1,"TSandbox_AlertData_BeAtkCity",".CSMsg.TSandbox_AlertData_BeAtkCity",false,{},{},nil,{})
F353D=F(2,"info",".CSMsg.TSandbox_AlertData_BeMass_Wait.info",1,0,1,false,nil,11,10)
F354D=F(2,"timeBeg",".CSMsg.TSandbox_AlertData_BeMass_Wait.timeBeg",3,1,1,false,0,13,3)
F355D=F(2,"timeEnd",".CSMsg.TSandbox_AlertData_BeMass_Wait.timeEnd",4,2,1,false,0,13,3)
F356D=F(2,"posNow",".CSMsg.TSandbox_AlertData_BeMass_Wait.posNow",6,3,1,false,nil,11,10)
M104G=D(1,"TSandbox_AlertData_BeMass_Wait",".CSMsg.TSandbox_AlertData_BeMass_Wait",false,{},{},nil,{})
F357D=F(2,"info",".CSMsg.TSandbox_AlertData_BeMass_March.info",1,0,1,false,nil,11,10)
F358D=F(2,"marchSid",".CSMsg.TSandbox_AlertData_BeMass_March.marchSid",2,1,1,false,0,4,4)
F359D=F(2,"timeBeg",".CSMsg.TSandbox_AlertData_BeMass_March.timeBeg",3,2,1,false,0,13,3)
F360D=F(2,"timeEnd",".CSMsg.TSandbox_AlertData_BeMass_March.timeEnd",4,3,1,false,0,13,3)
F361D=F(2,"posBeg",".CSMsg.TSandbox_AlertData_BeMass_March.posBeg",5,4,1,false,nil,11,10)
F362D=F(2,"posNow",".CSMsg.TSandbox_AlertData_BeMass_March.posNow",6,5,1,false,nil,11,10)
M105G=D(1,"TSandbox_AlertData_BeMass_March",".CSMsg.TSandbox_AlertData_BeMass_March",false,{},{},nil,{})
F363D=F(2,"info",".CSMsg.TSandbox_AlertData_BeAtkResource.info",1,0,1,false,nil,11,10)
F364D=F(2,"marchSid",".CSMsg.TSandbox_AlertData_BeAtkResource.marchSid",2,1,1,false,0,4,4)
F365D=F(2,"timeBeg",".CSMsg.TSandbox_AlertData_BeAtkResource.timeBeg",3,2,1,false,0,13,3)
F366D=F(2,"timeEnd",".CSMsg.TSandbox_AlertData_BeAtkResource.timeEnd",4,3,1,false,0,13,3)
F367D=F(2,"posBeg",".CSMsg.TSandbox_AlertData_BeAtkResource.posBeg",5,4,1,false,nil,11,10)
F368D=F(2,"posNow",".CSMsg.TSandbox_AlertData_BeAtkResource.posNow",6,5,1,false,nil,11,10)
M106G=D(1,"TSandbox_AlertData_BeAtkResource",".CSMsg.TSandbox_AlertData_BeAtkResource",false,{},{},nil,{})
F369D=F(2,"marchSid",".CSMsg.TSandbox_AlertData_Wonder.marchSid",2,0,1,false,0,4,4)
F370D=F(2,"timeBeg",".CSMsg.TSandbox_AlertData_Wonder.timeBeg",3,1,1,false,0,13,3)
F371D=F(2,"timeEnd",".CSMsg.TSandbox_AlertData_Wonder.timeEnd",4,2,1,false,0,13,3)
F372D=F(2,"posBeg",".CSMsg.TSandbox_AlertData_Wonder.posBeg",5,3,1,false,nil,11,10)
F373D=F(2,"posNow",".CSMsg.TSandbox_AlertData_Wonder.posNow",6,4,1,false,nil,11,10)
F374D=F(2,"cfgId",".CSMsg.TSandbox_AlertData_Wonder.cfgId",7,5,1,false,0,13,3)
F375D=F(2,"worldId",".CSMsg.TSandbox_AlertData_Wonder.worldId",8,6,1,false,0,13,3)
M107G=D(1,"TSandbox_AlertData_Wonder",".CSMsg.TSandbox_AlertData_Wonder",false,{},{},nil,{})
F376D=F(2,"info",".CSMsg.TSandbox_AlertData_Detect.info",1,0,1,false,nil,11,10)
F377D=F(2,"marchSid",".CSMsg.TSandbox_AlertData_Detect.marchSid",2,1,1,false,0,4,4)
F378D=F(2,"timeBeg",".CSMsg.TSandbox_AlertData_Detect.timeBeg",3,2,1,false,0,13,3)
F379D=F(2,"timeEnd",".CSMsg.TSandbox_AlertData_Detect.timeEnd",4,3,1,false,0,13,3)
F380D=F(2,"posBeg",".CSMsg.TSandbox_AlertData_Detect.posBeg",5,4,1,false,nil,11,10)
F381D=F(2,"posNow",".CSMsg.TSandbox_AlertData_Detect.posNow",6,5,1,false,nil,11,10)
M108G=D(1,"TSandbox_AlertData_Detect",".CSMsg.TSandbox_AlertData_Detect",false,{},{},nil,{})
F382D=F(2,"info",".CSMsg.TSandbox_AlertData_BeAtkResource_Detect.info",1,0,1,false,nil,11,10)
F383D=F(2,"marchSid",".CSMsg.TSandbox_AlertData_BeAtkResource_Detect.marchSid",2,1,1,false,0,4,4)
F384D=F(2,"timeBeg",".CSMsg.TSandbox_AlertData_BeAtkResource_Detect.timeBeg",3,2,1,false,0,13,3)
F385D=F(2,"timeEnd",".CSMsg.TSandbox_AlertData_BeAtkResource_Detect.timeEnd",4,3,1,false,0,13,3)
F386D=F(2,"posBeg",".CSMsg.TSandbox_AlertData_BeAtkResource_Detect.posBeg",5,4,1,false,nil,11,10)
F387D=F(2,"posNow",".CSMsg.TSandbox_AlertData_BeAtkResource_Detect.posNow",6,5,1,false,nil,11,10)
M109G=D(1,"TSandbox_AlertData_BeAtkResource_Detect",".CSMsg.TSandbox_AlertData_BeAtkResource_Detect",false,{},{},nil,{})
F388D=F(2,"entitySid",".CSMsg.TSandbox_AlertData_ZombieApocalypse.entitySid",2,0,1,false,0,4,4)
F389D=F(2,"timeBeg",".CSMsg.TSandbox_AlertData_ZombieApocalypse.timeBeg",3,1,1,false,0,13,3)
F390D=F(2,"timeEnd",".CSMsg.TSandbox_AlertData_ZombieApocalypse.timeEnd",4,2,1,false,0,13,3)
F391D=F(2,"posBeg",".CSMsg.TSandbox_AlertData_ZombieApocalypse.posBeg",5,3,1,false,nil,11,10)
F392D=F(2,"allianceID",".CSMsg.TSandbox_AlertData_ZombieApocalypse.allianceID",7,4,1,false,0,5,1)
F393D=F(2,"allianceShortName",".CSMsg.TSandbox_AlertData_ZombieApocalypse.allianceShortName",8,5,1,false,"",9,9)
F394D=F(2,"allianceName",".CSMsg.TSandbox_AlertData_ZombieApocalypse.allianceName",9,6,1,false,"",9,9)
F395D=F(2,"cfgId",".CSMsg.TSandbox_AlertData_ZombieApocalypse.cfgId",10,7,1,false,0,5,1)
F396D=F(2,"state",".CSMsg.TSandbox_AlertData_ZombieApocalypse.state",11,8,1,false,0,5,1)
F397D=F(2,"roundNow",".CSMsg.TSandbox_AlertData_ZombieApocalypse.roundNow",12,9,1,false,0,5,1)
F398D=F(2,"roundMax",".CSMsg.TSandbox_AlertData_ZombieApocalypse.roundMax",13,10,1,false,0,5,1)
F399D=F(2,"worldID",".CSMsg.TSandbox_AlertData_ZombieApocalypse.worldID",14,11,1,false,0,5,1)
M110G=D(1,"TSandbox_AlertData_ZombieApocalypse",".CSMsg.TSandbox_AlertData_ZombieApocalypse",false,{},{},nil,{})
F400D=F(2,"alertType",".CSMsg.TSandbox_AlertData.alertType",1,0,2,false,nil,14,8)
F401D=F(2,"BeAtkCity",".CSMsg.TSandbox_AlertData.BeAtkCity",2,1,1,false,nil,11,10)
F402D=F(2,"BeMass_Wait",".CSMsg.TSandbox_AlertData.BeMass_Wait",3,2,1,false,nil,11,10)
F403D=F(2,"BeMass_March",".CSMsg.TSandbox_AlertData.BeMass_March",4,3,1,false,nil,11,10)
F404D=F(2,"BeAtkResource",".CSMsg.TSandbox_AlertData.BeAtkResource",5,4,1,false,nil,11,10)
F405D=F(2,"Wonder",".CSMsg.TSandbox_AlertData.Wonder",6,5,1,false,nil,11,10)
F406D=F(2,"Detect",".CSMsg.TSandbox_AlertData.Detect",7,6,1,false,nil,11,10)
F407D=F(2,"BeAtkResource_Detect",".CSMsg.TSandbox_AlertData.BeAtkResource_Detect",8,7,1,false,nil,11,10)
F408D=F(2,"alertListType",".CSMsg.TSandbox_AlertData.alertListType",9,8,1,false,nil,14,8)
F409D=F(2,"ZombieApocalypse",".CSMsg.TSandbox_AlertData.ZombieApocalypse",10,9,1,false,nil,11,10)
M111G=D(1,"TSandbox_AlertData",".CSMsg.TSandbox_AlertData",false,{},{},nil,{})
M114G=D(1,"TMSG_SANDBOX_ALERT_LIST_REQ",".CSMsg.TMSG_SANDBOX_ALERT_LIST_REQ",false,{},{},{},{})
F410D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_ALERT_LIST_RSP.errCode",1,0,2,false,nil,14,8)
M115G=D(1,"TMSG_SANDBOX_ALERT_LIST_RSP",".CSMsg.TMSG_SANDBOX_ALERT_LIST_RSP",false,{},{},nil,{})
F411D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_ALERT_LIST_NTF.sandboxSid",1,0,2,false,0,4,4)
F412D=F(2,"alert",".CSMsg.TMSG_SANDBOX_ALERT_LIST_NTF.alert",2,1,3,false,{},11,10)
M116G=D(1,"TMSG_SANDBOX_ALERT_LIST_NTF",".CSMsg.TMSG_SANDBOX_ALERT_LIST_NTF",false,{},{},nil,{})
F413D=F(2,"sid",".CSMsg.TMSG_SANDBOX_DETECT_REQ.sid",1,0,2,false,0,4,4)
F414D=F(2,"isPoisoned",".CSMsg.TMSG_SANDBOX_DETECT_REQ.isPoisoned",2,1,1,false,false,8,7)
M117G=D(1,"TMSG_SANDBOX_DETECT_REQ",".CSMsg.TMSG_SANDBOX_DETECT_REQ",false,{},{},nil,{})
F415D=F(2,"sid",".CSMsg.TMSG_SANDBOX_DETECT_RSP.sid",1,0,2,false,0,4,4)
F416D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_DETECT_RSP.errCode",2,1,2,false,nil,14,8)
F417D=F(2,"timeStamp",".CSMsg.TMSG_SANDBOX_DETECT_RSP.timeStamp",3,2,1,false,0,13,3)
M118G=D(1,"TMSG_SANDBOX_DETECT_RSP",".CSMsg.TMSG_SANDBOX_DETECT_RSP",false,{},{},nil,{})
F418D=F(2,"lineSid",".CSMsg.TMSG_SANDBOX_CANCLE_DETECT_REQ.lineSid",1,0,2,false,0,13,3)
M119G=D(1,"TMSG_SANDBOX_CANCLE_DETECT_REQ",".CSMsg.TMSG_SANDBOX_CANCLE_DETECT_REQ",false,{},{},nil,{})
F419D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_CANCLE_DETECT_RSP.errCode",1,0,2,false,nil,14,8)
F420D=F(2,"lineSid",".CSMsg.TMSG_SANDBOX_CANCLE_DETECT_RSP.lineSid",2,1,2,false,0,13,3)
M120G=D(1,"TMSG_SANDBOX_CANCLE_DETECT_RSP",".CSMsg.TMSG_SANDBOX_CANCLE_DETECT_RSP",false,{},{},nil,{})
F421D=F(2,"dbID",".CSMsg.TSandBoxMemberInfo.dbID",1,0,2,false,0,5,1)
F422D=F(2,"pos",".CSMsg.TSandBoxMemberInfo.pos",2,1,2,false,nil,11,10)
M121G=D(1,"TSandBoxMemberInfo",".CSMsg.TSandBoxMemberInfo",false,{},{},nil,{})
F423D=F(2,"sandBoxSid",".CSMsg.TMSG_SANDBOX_MEMBER_REQ.sandBoxSid",1,0,2,false,0,13,3)
F424D=F(2,"dbID",".CSMsg.TMSG_SANDBOX_MEMBER_REQ.dbID",2,1,3,false,{},5,1)
M122G=D(1,"TMSG_SANDBOX_MEMBER_REQ",".CSMsg.TMSG_SANDBOX_MEMBER_REQ",false,{},{},nil,{})
F425D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_MEMBER_RSP.errCode",1,0,2,false,nil,14,8)
F426D=F(2,"info",".CSMsg.TMSG_SANDBOX_MEMBER_RSP.info",2,1,3,false,{},11,10)
M123G=D(1,"TMSG_SANDBOX_MEMBER_RSP",".CSMsg.TMSG_SANDBOX_MEMBER_RSP",false,{},{},nil,{})
F427D=F(2,"buffID",".CSMsg.TBuffInfo.buffID",1,0,2,false,0,13,3)
F428D=F(2,"buffLevel",".CSMsg.TBuffInfo.buffLevel",2,1,2,false,0,13,3)
F429D=F(2,"startTime",".CSMsg.TBuffInfo.startTime",4,2,2,false,0,13,3)
F430D=F(2,"sid",".CSMsg.TBuffInfo.sid",5,3,2,false,0,4,4)
M124G=D(1,"TBuffInfo",".CSMsg.TBuffInfo",false,{},{},nil,{})
F431D=F(2,"buffInfo",".CSMsg.TMSG_BUFFINFO_NTF.buffInfo",1,0,3,false,{},11,10)
F432D=F(2,"removeSid",".CSMsg.TMSG_BUFFINFO_NTF.removeSid",2,1,3,false,{},4,4)
M125G=D(1,"TMSG_BUFFINFO_NTF",".CSMsg.TMSG_BUFFINFO_NTF",false,{},{},nil,{})
F433D=F(2,"sid",".CSMsg.TMSG_SANDBOX_WONDERPOS_REQ.sid",1,0,2,false,0,4,4)
M126G=D(1,"TMSG_SANDBOX_WONDERPOS_REQ",".CSMsg.TMSG_SANDBOX_WONDERPOS_REQ",false,{},{},nil,{})
F434D=F(2,"sid",".CSMsg.TMSG_SANDBOX_WONDERPOS_RSP.sid",1,0,2,false,0,4,4)
F435D=F(2,"err",".CSMsg.TMSG_SANDBOX_WONDERPOS_RSP.err",2,1,2,false,nil,14,8)
F436D=F(2,"pos",".CSMsg.TMSG_SANDBOX_WONDERPOS_RSP.pos",3,2,1,false,nil,11,10)
M127G=D(1,"TMSG_SANDBOX_WONDERPOS_RSP",".CSMsg.TMSG_SANDBOX_WONDERPOS_RSP",false,{},{},nil,{})
F437D=F(2,"monsterType",".CSMsg.TMSG_SANDBOX_MONSTERFK_NTF.monsterType",1,0,2,false,0,13,3)
F438D=F(2,"monsterLevel",".CSMsg.TMSG_SANDBOX_MONSTERFK_NTF.monsterLevel",2,1,2,false,0,13,3)
M128G=D(1,"TMSG_SANDBOX_MONSTERFK_NTF",".CSMsg.TMSG_SANDBOX_MONSTERFK_NTF",false,{},{},nil,{})
F439D=F(2,"marchSid",".CSMsg.TMSG_SANDBOX_BATTLE_RESULT_NTF.marchSid",1,0,2,false,0,4,4)
F440D=F(2,"targetSid",".CSMsg.TMSG_SANDBOX_BATTLE_RESULT_NTF.targetSid",2,1,2,false,0,4,4)
F441D=F(2,"attackWin",".CSMsg.TMSG_SANDBOX_BATTLE_RESULT_NTF.attackWin",3,2,2,false,false,8,7)
M129G=D(1,"TMSG_SANDBOX_BATTLE_RESULT_NTF",".CSMsg.TMSG_SANDBOX_BATTLE_RESULT_NTF",false,{},{},nil,{})
F442D=F(2,"sid",".CSMsg.TMSG_SANDBOX_BASEVFX_NTF.sid",1,0,2,false,0,4,4)
F443D=F(2,"dbid",".CSMsg.TMSG_SANDBOX_BASEVFX_NTF.dbid",2,1,2,false,0,13,3)
F444D=F(2,"allianceID",".CSMsg.TMSG_SANDBOX_BASEVFX_NTF.allianceID",3,2,2,false,0,13,3)
F445D=F(2,"newPos",".CSMsg.TMSG_SANDBOX_BASEVFX_NTF.newPos",4,3,2,false,nil,11,10)
F446D=F(2,"oldPos",".CSMsg.TMSG_SANDBOX_BASEVFX_NTF.oldPos",5,4,1,false,nil,11,10)
F447D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_BASEVFX_NTF.sandboxSid",6,5,1,false,0,13,3)
F448D=F(2,"enType",".CSMsg.TMSG_SANDBOX_BASEVFX_NTF.enType",7,6,1,false,nil,14,8)
F449D=F(2,"worldID",".CSMsg.TMSG_SANDBOX_BASEVFX_NTF.worldID",8,7,1,false,0,5,1)
M130G=D(1,"TMSG_SANDBOX_BASEVFX_NTF",".CSMsg.TMSG_SANDBOX_BASEVFX_NTF",false,{},{},nil,{})
F450D=F(2,"sid",".CSMsg.TMSG_SANDBOX_DEFFAILD_NTF.sid",1,0,2,false,0,4,4)
F451D=F(2,"newPos",".CSMsg.TMSG_SANDBOX_DEFFAILD_NTF.newPos",2,1,2,false,nil,11,10)
F452D=F(2,"oldPos",".CSMsg.TMSG_SANDBOX_DEFFAILD_NTF.oldPos",3,2,2,false,nil,11,10)
F453D=F(2,"isOnline",".CSMsg.TMSG_SANDBOX_DEFFAILD_NTF.isOnline",4,3,2,false,false,8,7)
M131G=D(1,"TMSG_SANDBOX_DEFFAILD_NTF",".CSMsg.TMSG_SANDBOX_DEFFAILD_NTF",false,{},{},nil,{})
F454D=F(2,"dbid",".CSMsg.TMSG_SANDBOX_UPVOTE_NTF.dbid",1,0,2,false,0,13,3)
F455D=F(2,"faceID",".CSMsg.TMSG_SANDBOX_UPVOTE_NTF.faceID",2,1,2,false,0,13,3)
F456D=F(2,"dbidBeVoted",".CSMsg.TMSG_SANDBOX_UPVOTE_NTF.dbidBeVoted",3,2,2,false,0,13,3)
F457D=F(2,"sid",".CSMsg.TMSG_SANDBOX_UPVOTE_NTF.sid",4,3,2,false,0,4,4)
F458D=F(2,"pos",".CSMsg.TMSG_SANDBOX_UPVOTE_NTF.pos",5,4,2,false,nil,11,10)
F459D=F(2,"faceStr",".CSMsg.TMSG_SANDBOX_UPVOTE_NTF.faceStr",6,5,1,false,"",9,9)
M132G=D(1,"TMSG_SANDBOX_UPVOTE_NTF",".CSMsg.TMSG_SANDBOX_UPVOTE_NTF",false,{},{},nil,{})
F460D=F(2,"sid",".CSMsg.TKastenBoxPersonInfo.sid",1,0,2,false,0,4,4)
F461D=F(2,"pos",".CSMsg.TKastenBoxPersonInfo.pos",2,1,2,false,nil,11,10)
F462D=F(2,"state",".CSMsg.TKastenBoxPersonInfo.state",3,2,2,false,nil,14,8)
M133G=D(1,"TKastenBoxPersonInfo",".CSMsg.TKastenBoxPersonInfo",false,{},{},nil,{})
F463D=F(2,"param",".CSMsg.TMSG_SANDBOX_GET_KASTENBOX_PERSON_INFO_REQ.param",1,0,1,false,0,5,1)
F464D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_GET_KASTENBOX_PERSON_INFO_REQ.sandboxSid",2,1,1,false,0,5,1)
M135G=D(1,"TMSG_SANDBOX_GET_KASTENBOX_PERSON_INFO_REQ",".CSMsg.TMSG_SANDBOX_GET_KASTENBOX_PERSON_INFO_REQ",false,{},{},nil,{})
F465D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_GET_KASTENBOX_PERSON_INFO_RSP.errCode",1,0,2,false,nil,14,8)
F466D=F(2,"infoList",".CSMsg.TMSG_SANDBOX_GET_KASTENBOX_PERSON_INFO_RSP.infoList",2,1,3,false,{},11,10)
F467D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_GET_KASTENBOX_PERSON_INFO_RSP.sandboxSid",3,2,1,false,0,5,1)
M136G=D(1,"TMSG_SANDBOX_GET_KASTENBOX_PERSON_INFO_RSP",".CSMsg.TMSG_SANDBOX_GET_KASTENBOX_PERSON_INFO_RSP",false,{},{},nil,{})
F468D=F(2,"infoList",".CSMsg.TMSG_SANDBOX_KASTENBOX_UPDATE_PERSON_INFO_NTF.infoList",1,0,3,false,{},11,10)
F469D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_KASTENBOX_UPDATE_PERSON_INFO_NTF.sandboxSid",2,1,1,false,0,5,1)
M137G=D(1,"TMSG_SANDBOX_KASTENBOX_UPDATE_PERSON_INFO_NTF",".CSMsg.TMSG_SANDBOX_KASTENBOX_UPDATE_PERSON_INFO_NTF",false,{},{},nil,{})
F470D=F(2,"sid",".CSMsg.TMSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_REQ.sid",1,0,2,false,0,4,4)
F471D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_REQ.sandboxSid",2,1,1,false,0,5,1)
M138G=D(1,"TMSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_REQ",".CSMsg.TMSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_REQ",false,{},{},nil,{})
F472D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_RSP.errCode",1,0,2,false,nil,14,8)
F473D=F(2,"rewardId",".CSMsg.TMSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_RSP.rewardId",2,1,2,false,0,13,3)
F474D=F(2,"multiplier",".CSMsg.TMSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_RSP.multiplier",3,2,2,false,0,13,3)
F475D=F(2,"sid",".CSMsg.TMSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_RSP.sid",4,3,1,false,0,4,4)
F476D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_RSP.sandboxSid",5,4,1,false,0,5,1)
M139G=D(1,"TMSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_RSP",".CSMsg.TMSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_RSP",false,{},{},nil,{})
F477D=F(2,"sid",".CSMsg.TMSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_NTF.sid",1,0,2,false,0,4,4)
F478D=F(2,"pos",".CSMsg.TMSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_NTF.pos",2,1,2,false,nil,11,10)
F479D=F(2,"faceID",".CSMsg.TMSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_NTF.faceID",3,2,2,false,0,13,3)
F480D=F(2,"frameID",".CSMsg.TMSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_NTF.frameID",4,3,2,false,0,13,3)
F481D=F(2,"playerName",".CSMsg.TMSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_NTF.playerName",5,4,2,false,"",9,9)
F482D=F(2,"allianceShortName",".CSMsg.TMSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_NTF.allianceShortName",6,5,2,false,"",9,9)
F483D=F(2,"faceStr",".CSMsg.TMSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_NTF.faceStr",7,6,1,false,"",9,9)
F484D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_NTF.sandboxSid",8,7,1,false,0,5,1)
M140G=D(1,"TMSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_NTF",".CSMsg.TMSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_NTF",false,{},{},nil,{})
F485D=F(2,"dbid",".CSMsg.TKastenBoxOnePlayerRecord.dbid",1,0,2,false,0,13,3)
F486D=F(2,"faceID",".CSMsg.TKastenBoxOnePlayerRecord.faceID",2,1,2,false,0,13,3)
F487D=F(2,"frameID",".CSMsg.TKastenBoxOnePlayerRecord.frameID",3,2,2,false,0,13,3)
F488D=F(2,"playerName",".CSMsg.TKastenBoxOnePlayerRecord.playerName",4,3,2,false,"",9,9)
F489D=F(2,"sex",".CSMsg.TKastenBoxOnePlayerRecord.sex",5,4,2,false,0,13,3)
F490D=F(2,"rewardId",".CSMsg.TKastenBoxOnePlayerRecord.rewardId",6,5,2,false,0,13,3)
F491D=F(2,"level",".CSMsg.TKastenBoxOnePlayerRecord.level",7,6,2,false,0,13,3)
F492D=F(2,"useTime",".CSMsg.TKastenBoxOnePlayerRecord.useTime",8,7,2,false,0,13,3)
F493D=F(2,"multiplier",".CSMsg.TKastenBoxOnePlayerRecord.multiplier",9,8,2,false,0,13,3)
F494D=F(2,"faceStr",".CSMsg.TKastenBoxOnePlayerRecord.faceStr",10,9,1,false,"",9,9)
M141G=D(1,"TKastenBoxOnePlayerRecord",".CSMsg.TKastenBoxOnePlayerRecord",false,{},{},nil,{})
F495D=F(2,"sid",".CSMsg.TKastenBoxInfo.sid",1,0,1,false,0,4,4)
F496D=F(2,"pos",".CSMsg.TKastenBoxInfo.pos",2,1,1,false,nil,11,10)
F497D=F(2,"boxID",".CSMsg.TKastenBoxInfo.boxID",3,2,1,false,0,13,3)
F498D=F(2,"remainNum",".CSMsg.TKastenBoxInfo.remainNum",4,3,1,false,0,13,3)
F499D=F(2,"bornTime",".CSMsg.TKastenBoxInfo.bornTime",5,4,1,false,0,4,4)
F500D=F(2,"belongerDbid",".CSMsg.TKastenBoxInfo.belongerDbid",6,5,1,false,0,13,3)
F501D=F(2,"belongerFaceID",".CSMsg.TKastenBoxInfo.belongerFaceID",7,6,1,false,0,13,3)
F502D=F(2,"belongerFrameID",".CSMsg.TKastenBoxInfo.belongerFrameID",8,7,1,false,0,13,3)
F503D=F(2,"belongerSex",".CSMsg.TKastenBoxInfo.belongerSex",9,8,1,false,0,13,3)
F504D=F(2,"belongerName",".CSMsg.TKastenBoxInfo.belongerName",10,9,1,false,"",9,9)
F505D=F(2,"recordList",".CSMsg.TKastenBoxInfo.recordList",11,10,3,false,{},11,10)
F506D=F(2,"belongerFaceStr",".CSMsg.TKastenBoxInfo.belongerFaceStr",12,11,1,false,"",9,9)
M142G=D(1,"TKastenBoxInfo",".CSMsg.TKastenBoxInfo",false,{},{},nil,{})
F507D=F(2,"sid",".CSMsg.TMSG_SANDBOX_GET_KASTENBOX_INFO_REQ.sid",1,0,2,false,0,4,4)
F508D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_GET_KASTENBOX_INFO_REQ.sandboxSid",2,1,1,false,0,5,1)
M143G=D(1,"TMSG_SANDBOX_GET_KASTENBOX_INFO_REQ",".CSMsg.TMSG_SANDBOX_GET_KASTENBOX_INFO_REQ",false,{},{},nil,{})
F509D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_GET_KASTENBOX_INFO_RSP.errCode",1,0,2,false,nil,14,8)
F510D=F(2,"kastenBoxInfo",".CSMsg.TMSG_SANDBOX_GET_KASTENBOX_INFO_RSP.kastenBoxInfo",2,1,1,false,nil,11,10)
F511D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_GET_KASTENBOX_INFO_RSP.sandboxSid",3,2,1,false,0,5,1)
M144G=D(1,"TMSG_SANDBOX_GET_KASTENBOX_INFO_RSP",".CSMsg.TMSG_SANDBOX_GET_KASTENBOX_INFO_RSP",false,{},{},nil,{})
F512D=F(2,"sid",".CSMsg.TMSG_SANDBOX_RESOURCE_EXPLORATE_REQ.sid",1,0,2,false,0,4,4)
M145G=D(1,"TMSG_SANDBOX_RESOURCE_EXPLORATE_REQ",".CSMsg.TMSG_SANDBOX_RESOURCE_EXPLORATE_REQ",false,{},{},nil,{})
F513D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_RESOURCE_EXPLORATE_RSP.errCode",1,0,2,false,nil,14,8)
M146G=D(1,"TMSG_SANDBOX_RESOURCE_EXPLORATE_RSP",".CSMsg.TMSG_SANDBOX_RESOURCE_EXPLORATE_RSP",false,{},{},nil,{})
F514D=F(2,"dbid",".CSMsg.TMSG_SANDBOX_ALLIANCE_HELP_REQ.dbid",1,0,2,false,0,13,3)
M147G=D(1,"TMSG_SANDBOX_ALLIANCE_HELP_REQ",".CSMsg.TMSG_SANDBOX_ALLIANCE_HELP_REQ",false,{},{},nil,{})
F515D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_ALLIANCE_HELP_RSP.errCode",1,0,2,false,nil,14,8)
M148G=D(1,"TMSG_SANDBOX_ALLIANCE_HELP_RSP",".CSMsg.TMSG_SANDBOX_ALLIANCE_HELP_RSP",false,{},{},nil,{})
F516D=F(2,"stageType",".CSMsg.TMSG_SANDBOX_GAME_BATTLE_REQ.stageType",1,0,2,false,nil,14,8)
F517D=F(2,"lineUp",".CSMsg.TMSG_SANDBOX_GAME_BATTLE_REQ.lineUp",2,1,1,false,nil,11,10)
F518D=F(2,"param1",".CSMsg.TMSG_SANDBOX_GAME_BATTLE_REQ.param1",3,2,1,false,0,4,4)
F519D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_GAME_BATTLE_REQ.sandboxSid",4,3,1,false,0,13,3)
M149G=D(1,"TMSG_SANDBOX_GAME_BATTLE_REQ",".CSMsg.TMSG_SANDBOX_GAME_BATTLE_REQ",false,{},{},nil,{})
F520D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_GAME_BATTLE_RSP.errCode",1,0,2,false,nil,14,8)
M151G=D(1,"TMSG_SANDBOX_GAME_BATTLE_RSP",".CSMsg.TMSG_SANDBOX_GAME_BATTLE_RSP",false,{},{},nil,{})
F521D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_CARRIAGEPOS_REQ.sandboxSid",1,0,2,false,0,13,3)
F522D=F(2,"sid",".CSMsg.TMSG_SANDBOX_CARRIAGEPOS_REQ.sid",2,1,2,false,0,4,4)
M152G=D(1,"TMSG_SANDBOX_CARRIAGEPOS_REQ",".CSMsg.TMSG_SANDBOX_CARRIAGEPOS_REQ",false,{},{},nil,{})
F523D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_CARRIAGEPOS_RSP.sandboxSid",1,0,2,false,0,13,3)
F524D=F(2,"sid",".CSMsg.TMSG_SANDBOX_CARRIAGEPOS_RSP.sid",2,1,2,false,0,4,4)
F525D=F(2,"err",".CSMsg.TMSG_SANDBOX_CARRIAGEPOS_RSP.err",3,2,2,false,nil,14,8)
F526D=F(2,"pos",".CSMsg.TMSG_SANDBOX_CARRIAGEPOS_RSP.pos",4,3,1,false,nil,11,10)
M153G=D(1,"TMSG_SANDBOX_CARRIAGEPOS_RSP",".CSMsg.TMSG_SANDBOX_CARRIAGEPOS_RSP",false,{},{},nil,{})
F527D=F(2,"marchID",".CSMsg.TFakeMarchInfo.marchID",1,0,2,false,0,4,4)
F528D=F(2,"beginTime",".CSMsg.TFakeMarchInfo.beginTime",2,1,2,false,0,13,3)
F529D=F(2,"endTime",".CSMsg.TFakeMarchInfo.endTime",3,2,2,false,0,13,3)
F530D=F(2,"startPos",".CSMsg.TFakeMarchInfo.startPos",4,3,2,false,nil,11,10)
F531D=F(2,"endPos",".CSMsg.TFakeMarchInfo.endPos",5,4,2,false,nil,11,10)
F532D=F(2,"lineType",".CSMsg.TFakeMarchInfo.lineType",6,5,2,false,nil,14,8)
M154G=D(1,"TFakeMarchInfo",".CSMsg.TFakeMarchInfo",false,{},{},nil,{})
F533D=F(2,"marchList",".CSMsg.TMSG_SANDBOX_FAKE_MARCH_NTF.marchList",1,0,3,false,{},11,10)
F534D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_FAKE_MARCH_NTF.sandboxSid",2,1,2,false,0,13,3)
M155G=D(1,"TMSG_SANDBOX_FAKE_MARCH_NTF",".CSMsg.TMSG_SANDBOX_FAKE_MARCH_NTF",false,{},{},nil,{})
F535D=F(2,"dbid",".CSMsg.TMSG_SANDBOX_ALLIANCE_HELP_SUCCESS_NTF.dbid",1,0,2,false,0,13,3)
F536D=F(2,"sid",".CSMsg.TMSG_SANDBOX_ALLIANCE_HELP_SUCCESS_NTF.sid",2,1,2,false,0,4,4)
F537D=F(2,"pos",".CSMsg.TMSG_SANDBOX_ALLIANCE_HELP_SUCCESS_NTF.pos",3,2,2,false,nil,11,10)
F538D=F(2,"radarMissionId",".CSMsg.TMSG_SANDBOX_ALLIANCE_HELP_SUCCESS_NTF.radarMissionId",4,3,2,false,0,4,4)
F539D=F(2,"taskId",".CSMsg.TMSG_SANDBOX_ALLIANCE_HELP_SUCCESS_NTF.taskId",5,4,2,false,0,5,1)
M156G=D(1,"TMSG_SANDBOX_ALLIANCE_HELP_SUCCESS_NTF",".CSMsg.TMSG_SANDBOX_ALLIANCE_HELP_SUCCESS_NTF",false,{},{},nil,{})
F540D=F(2,"nSID",".CSMsg.TMSG_SANDBOX_NC_DECLAREWAR_REQ.nSID",1,0,2,false,0,4,4)
F541D=F(2,"nType",".CSMsg.TMSG_SANDBOX_NC_DECLAREWAR_REQ.nType",2,1,2,false,0,5,1)
F542D=F(2,"sManifesto",".CSMsg.TMSG_SANDBOX_NC_DECLAREWAR_REQ.sManifesto",3,2,1,false,"",9,9)
M157G=D(1,"TMSG_SANDBOX_NC_DECLAREWAR_REQ",".CSMsg.TMSG_SANDBOX_NC_DECLAREWAR_REQ",false,{},{},nil,{})
F543D=F(2,"nSID",".CSMsg.TMSG_SANDBOX_NC_DECLAREWAR_RSP.nSID",1,0,2,false,0,4,4)
F544D=F(2,"nType",".CSMsg.TMSG_SANDBOX_NC_DECLAREWAR_RSP.nType",2,1,2,false,0,5,1)
F545D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_NC_DECLAREWAR_RSP.errCode",3,2,2,false,nil,14,8)
F546D=F(2,"regionID",".CSMsg.TMSG_SANDBOX_NC_DECLAREWAR_RSP.regionID",4,3,1,false,0,5,1)
M158G=D(1,"TMSG_SANDBOX_NC_DECLAREWAR_RSP",".CSMsg.TMSG_SANDBOX_NC_DECLAREWAR_RSP",false,{},{},nil,{})
F547D=F(2,"nSID",".CSMsg.TMSG_SANDBOX_NC_OFFLINEATTACK_REQ.nSID",1,0,2,false,0,4,4)
M159G=D(1,"TMSG_SANDBOX_NC_OFFLINEATTACK_REQ",".CSMsg.TMSG_SANDBOX_NC_OFFLINEATTACK_REQ",false,{},{},nil,{})
F548D=F(2,"nSID",".CSMsg.TMSG_SANDBOX_NC_OFFLINEATTACK_RSP.nSID",1,0,2,false,0,4,4)
F549D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_NC_OFFLINEATTACK_RSP.errCode",2,1,2,false,nil,14,8)
M160G=D(1,"TMSG_SANDBOX_NC_OFFLINEATTACK_RSP",".CSMsg.TMSG_SANDBOX_NC_OFFLINEATTACK_RSP",false,{},{},nil,{})
F550D=F(2,"nSID",".CSMsg.TMSG_SANDBOX_NC_OFFLINEATTACK_NTF.nSID",1,0,2,false,0,4,4)
F551D=F(2,"FaceInfo",".CSMsg.TMSG_SANDBOX_NC_OFFLINEATTACK_NTF.FaceInfo",2,1,3,false,{},11,10)
F552D=F(2,"nCDTime",".CSMsg.TMSG_SANDBOX_NC_OFFLINEATTACK_NTF.nCDTime",3,2,2,false,0,4,4)
M161G=D(1,"TMSG_SANDBOX_NC_OFFLINEATTACK_NTF",".CSMsg.TMSG_SANDBOX_NC_OFFLINEATTACK_NTF",false,{},{},nil,{})
F553D=F(2,"nCityID",".CSMsg.TNatureCityAttackHeroInfo.nCityID",1,0,2,false,0,13,3)
F554D=F(2,"dbid",".CSMsg.TNatureCityAttackHeroInfo.dbid",2,1,2,false,0,4,4)
F555D=F(2,"FaceInfo",".CSMsg.TNatureCityAttackHeroInfo.FaceInfo",3,2,2,false,nil,11,10)
F556D=F(2,"RoleName",".CSMsg.TNatureCityAttackHeroInfo.RoleName",4,3,2,false,"",9,9)
F557D=F(2,"killNum",".CSMsg.TNatureCityAttackHeroInfo.killNum",5,4,2,false,0,13,3)
F558D=F(2,"praiseNum",".CSMsg.TNatureCityAttackHeroInfo.praiseNum",6,5,1,false,0,13,3)
F559D=F(2,"bAlready",".CSMsg.TNatureCityAttackHeroInfo.bAlready",7,6,1,false,false,8,7)
M163G=D(1,"TNatureCityAttackHeroInfo",".CSMsg.TNatureCityAttackHeroInfo",false,{},{},nil,{})
F560D=F(2,"nSID",".CSMsg.TMSG_SANDBOX_NC_ATTACKHERO_NTF.nSID",1,0,2,false,0,4,4)
F561D=F(2,"tInfo",".CSMsg.TMSG_SANDBOX_NC_ATTACKHERO_NTF.tInfo",2,1,2,false,nil,11,10)
M164G=D(1,"TMSG_SANDBOX_NC_ATTACKHERO_NTF",".CSMsg.TMSG_SANDBOX_NC_ATTACKHERO_NTF",false,{},{},nil,{})
F562D=F(2,"nSID",".CSMsg.TMSG_SANDBOX_NC_REWARDINFO_REQ.nSID",1,0,2,false,0,4,4)
M165G=D(1,"TMSG_SANDBOX_NC_REWARDINFO_REQ",".CSMsg.TMSG_SANDBOX_NC_REWARDINFO_REQ",false,{},{},nil,{})
F563D=F(2,"nSID",".CSMsg.TMSG_SANDBOX_NC_REWARDINFO_RSP.nSID",1,0,2,false,0,4,4)
F564D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_NC_REWARDINFO_RSP.errCode",2,1,2,false,nil,14,8)
F565D=F(2,"bHave",".CSMsg.TMSG_SANDBOX_NC_REWARDINFO_RSP.bHave",3,2,1,false,false,8,7)
F566D=F(2,"bJoin",".CSMsg.TMSG_SANDBOX_NC_REWARDINFO_RSP.bJoin",4,3,1,false,false,8,7)
F567D=F(2,"tInfo",".CSMsg.TMSG_SANDBOX_NC_REWARDINFO_RSP.tInfo",5,4,3,false,{},11,10)
F568D=F(2,"bAlready",".CSMsg.TMSG_SANDBOX_NC_REWARDINFO_RSP.bAlready",6,5,1,false,false,8,7)
M166G=D(1,"TMSG_SANDBOX_NC_REWARDINFO_RSP",".CSMsg.TMSG_SANDBOX_NC_REWARDINFO_RSP",false,{},{},nil,{})
F569D=F(2,"nSID",".CSMsg.TMSG_SANDBOX_NC_GETREWARD_REQ.nSID",1,0,2,false,0,4,4)
M167G=D(1,"TMSG_SANDBOX_NC_GETREWARD_REQ",".CSMsg.TMSG_SANDBOX_NC_GETREWARD_REQ",false,{},{},nil,{})
F570D=F(2,"nSID",".CSMsg.TMSG_SANDBOX_NC_GETREWARD_RSP.nSID",1,0,2,false,0,4,4)
F571D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_NC_GETREWARD_RSP.errCode",2,1,2,false,nil,14,8)
F572D=F(2,"rewards",".CSMsg.TMSG_SANDBOX_NC_GETREWARD_RSP.rewards",3,2,3,false,{},13,3)
M168G=D(1,"TMSG_SANDBOX_NC_GETREWARD_RSP",".CSMsg.TMSG_SANDBOX_NC_GETREWARD_RSP",false,{},{},nil,{})
F573D=F(2,"nSID",".CSMsg.TMSG_SANDBOX_NC_ABANDON_REQ.nSID",1,0,2,false,0,4,4)
F574D=F(2,"nType",".CSMsg.TMSG_SANDBOX_NC_ABANDON_REQ.nType",2,1,2,false,0,5,1)
M169G=D(1,"TMSG_SANDBOX_NC_ABANDON_REQ",".CSMsg.TMSG_SANDBOX_NC_ABANDON_REQ",false,{},{},nil,{})
F575D=F(2,"nSID",".CSMsg.TMSG_SANDBOX_NC_ABANDON_RSP.nSID",1,0,2,false,0,4,4)
F576D=F(2,"nType",".CSMsg.TMSG_SANDBOX_NC_ABANDON_RSP.nType",2,1,2,false,0,5,1)
F577D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_NC_ABANDON_RSP.errCode",3,2,2,false,nil,14,8)
F578D=F(2,"nCDTime",".CSMsg.TMSG_SANDBOX_NC_ABANDON_RSP.nCDTime",4,3,1,false,0,4,4)
M170G=D(1,"TMSG_SANDBOX_NC_ABANDON_RSP",".CSMsg.TMSG_SANDBOX_NC_ABANDON_RSP",false,{},{},nil,{})
F579D=F(2,"nSID",".CSMsg.TNCPopupsInfo.nSID",1,0,2,false,0,4,4)
F580D=F(2,"nCityID",".CSMsg.TNCPopupsInfo.nCityID",2,1,2,false,0,13,3)
F581D=F(2,"nRegionID",".CSMsg.TNCPopupsInfo.nRegionID",3,2,1,false,0,13,3)
F582D=F(2,"nStartTime",".CSMsg.TNCPopupsInfo.nStartTime",4,3,1,false,0,13,3)
F583D=F(2,"nFirst",".CSMsg.TNCPopupsInfo.nFirst",5,4,1,false,0,13,3)
F584D=F(2,"sName",".CSMsg.TNCPopupsInfo.sName",6,5,1,false,"",9,9)
F585D=F(2,"sShort",".CSMsg.TNCPopupsInfo.sShort",7,6,1,false,"",9,9)
F586D=F(2,"nEndTime",".CSMsg.TNCPopupsInfo.nEndTime",8,7,1,false,0,13,3)
M171G=D(1,"TNCPopupsInfo",".CSMsg.TNCPopupsInfo",false,{},{},nil,{})
F587D=F(2,"nType",".CSMsg.TMSG_SANDBOX_NC_POPUPS_NTF.nType",1,0,2,false,nil,14,8)
F588D=F(2,"infoList",".CSMsg.TMSG_SANDBOX_NC_POPUPS_NTF.infoList",2,1,3,false,{},11,10)
M172G=D(1,"TMSG_SANDBOX_NC_POPUPS_NTF",".CSMsg.TMSG_SANDBOX_NC_POPUPS_NTF",false,{},{},nil,{})
M174G=D(1,"TMSG_SANDBOX_NC_LIST_REQ",".CSMsg.TMSG_SANDBOX_NC_LIST_REQ",false,{},{},{},{})
F589D=F(2,"nSID",".CSMsg.TNatureCityInfo.nSID",1,0,2,false,0,4,4)
F590D=F(2,"nCityID",".CSMsg.TNatureCityInfo.nCityID",2,1,2,false,0,13,3)
F591D=F(2,"tPos",".CSMsg.TNatureCityInfo.tPos",3,2,2,false,nil,11,10)
F592D=F(2,"nAllianceID",".CSMsg.TNatureCityInfo.nAllianceID",4,3,1,false,0,13,3)
F593D=F(2,"sAllianceShortName",".CSMsg.TNatureCityInfo.sAllianceShortName",5,4,1,false,"",9,9)
F594D=F(2,"sAllianceName",".CSMsg.TNatureCityInfo.sAllianceName",6,5,1,false,"",9,9)
F595D=F(2,"nCanGet",".CSMsg.TNatureCityInfo.nCanGet",7,6,1,false,0,5,1)
M175G=D(1,"TNatureCityInfo",".CSMsg.TNatureCityInfo",false,{},{},nil,{})
F596D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_NC_LIST_RSP.errCode",1,0,2,false,nil,14,8)
F597D=F(2,"tInfoList",".CSMsg.TMSG_SANDBOX_NC_LIST_RSP.tInfoList",2,1,3,false,{},11,10)
M176G=D(1,"TMSG_SANDBOX_NC_LIST_RSP",".CSMsg.TMSG_SANDBOX_NC_LIST_RSP",false,{},{},nil,{})
M177G=D(1,"TMSG_SANDBOX_NC_ACTIVITY_REQ",".CSMsg.TMSG_SANDBOX_NC_ACTIVITY_REQ",false,{},{},{},{})
F598D=F(2,"nAllianceID",".CSMsg.TNatureCityCongressInfo.nAllianceID",1,0,2,false,0,13,3)
F599D=F(2,"sShort",".CSMsg.TNatureCityCongressInfo.sShort",2,1,2,false,"",9,9)
F600D=F(2,"sName",".CSMsg.TNatureCityCongressInfo.sName",3,2,2,false,"",9,9)
F601D=F(2,"nFlag",".CSMsg.TNatureCityCongressInfo.nFlag",4,3,2,false,0,13,3)
F602D=F(2,"nScore",".CSMsg.TNatureCityCongressInfo.nScore",5,4,2,false,0,13,3)
M178G=D(1,"TNatureCityCongressInfo",".CSMsg.TNatureCityCongressInfo",false,{},{},nil,{})
F603D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_NC_ACTIVITY_RSP.errCode",1,0,2,false,nil,14,8)
F604D=F(2,"tInfoList",".CSMsg.TMSG_SANDBOX_NC_ACTIVITY_RSP.tInfoList",2,1,3,false,{},11,10)
M179G=D(1,"TMSG_SANDBOX_NC_ACTIVITY_RSP",".CSMsg.TMSG_SANDBOX_NC_ACTIVITY_RSP",false,{},{},nil,{})
F605D=F(2,"nSID",".CSMsg.TNatureCityTeamBattleInfo.nSID",1,0,2,false,0,4,4)
F606D=F(2,"nCityID",".CSMsg.TNatureCityTeamBattleInfo.nCityID",2,1,2,false,0,13,3)
F607D=F(2,"tPos",".CSMsg.TNatureCityTeamBattleInfo.tPos",3,2,2,false,nil,11,10)
F608D=F(2,"nAllianceID",".CSMsg.TNatureCityTeamBattleInfo.nAllianceID",4,3,1,false,0,13,3)
F609D=F(2,"sAllianceShortName",".CSMsg.TNatureCityTeamBattleInfo.sAllianceShortName",5,4,1,false,"",9,9)
F610D=F(2,"sAllianceName",".CSMsg.TNatureCityTeamBattleInfo.sAllianceName",6,5,1,false,"",9,9)
F611D=F(2,"nAllianceIconID",".CSMsg.TNatureCityTeamBattleInfo.nAllianceIconID",7,6,1,false,0,5,1)
F612D=F(2,"nStartTime",".CSMsg.TNatureCityTeamBattleInfo.nStartTime",8,7,1,false,0,13,3)
F613D=F(2,"nEndTime",".CSMsg.TNatureCityTeamBattleInfo.nEndTime",9,8,1,false,0,13,3)
F614D=F(2,"nSandboxSID",".CSMsg.TNatureCityTeamBattleInfo.nSandboxSID",10,9,1,false,0,13,3)
M180G=D(1,"TNatureCityTeamBattleInfo",".CSMsg.TNatureCityTeamBattleInfo",false,{},{},nil,{})
M181G=D(1,"TMSG_SANDBOX_NC_TEAMBATTLE_REQ",".CSMsg.TMSG_SANDBOX_NC_TEAMBATTLE_REQ",false,{},{},{},{})
F615D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_NC_TEAMBATTLE_RSP.errCode",1,0,2,false,nil,14,8)
F616D=F(2,"tInfoList",".CSMsg.TMSG_SANDBOX_NC_TEAMBATTLE_RSP.tInfoList",2,1,3,false,{},11,10)
M182G=D(1,"TMSG_SANDBOX_NC_TEAMBATTLE_RSP",".CSMsg.TMSG_SANDBOX_NC_TEAMBATTLE_RSP",false,{},{},nil,{})
M183G=D(1,"TMSG_SANDBOX_NC_CONGRESSLIST_REQ",".CSMsg.TMSG_SANDBOX_NC_CONGRESSLIST_REQ",false,{},{},{},{})
F617D=F(2,"FaceID",".CSMsg.TCongressTeamInfo.FaceID",1,0,2,false,0,5,1)
F618D=F(2,"RoleName",".CSMsg.TCongressTeamInfo.RoleName",2,1,2,false,"",9,9)
F619D=F(2,"simpleTroopStr",".CSMsg.TCongressTeamInfo.simpleTroopStr",3,2,2,false,"",9,9)
F620D=F(2,"FrameID",".CSMsg.TCongressTeamInfo.FrameID",4,3,2,false,0,5,1)
F621D=F(2,"FaceStr",".CSMsg.TCongressTeamInfo.FaceStr",5,4,1,false,"",9,9)
M184G=D(1,"TCongressTeamInfo",".CSMsg.TCongressTeamInfo",false,{},{},nil,{})
F622D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_NC_CONGRESSLIST_RSP.errCode",1,0,2,false,nil,14,8)
F623D=F(2,"List",".CSMsg.TMSG_SANDBOX_NC_CONGRESSLIST_RSP.List",2,1,3,false,{},11,10)
F624D=F(2,"sid",".CSMsg.TMSG_SANDBOX_NC_CONGRESSLIST_RSP.sid",3,2,2,false,0,4,4)
M185G=D(1,"TMSG_SANDBOX_NC_CONGRESSLIST_RSP",".CSMsg.TMSG_SANDBOX_NC_CONGRESSLIST_RSP",false,{},{},nil,{})
F625D=F(2,"teamDbid",".CSMsg.TMSG_SANDBOX_NC_CONGRESSBACK_REQ.teamDbid",1,0,2,false,0,5,1)
F626D=F(2,"teamIndex",".CSMsg.TMSG_SANDBOX_NC_CONGRESSBACK_REQ.teamIndex",2,1,2,false,0,5,1)
M186G=D(1,"TMSG_SANDBOX_NC_CONGRESSBACK_REQ",".CSMsg.TMSG_SANDBOX_NC_CONGRESSBACK_REQ",false,{},{},nil,{})
F627D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_NC_CONGRESSBACK_RSP.errCode",1,0,2,false,nil,14,8)
F628D=F(2,"sid",".CSMsg.TMSG_SANDBOX_NC_CONGRESSBACK_RSP.sid",2,1,2,false,0,4,4)
F629D=F(2,"teamDbid",".CSMsg.TMSG_SANDBOX_NC_CONGRESSBACK_RSP.teamDbid",3,2,2,false,0,5,1)
F630D=F(2,"teamIndex",".CSMsg.TMSG_SANDBOX_NC_CONGRESSBACK_RSP.teamIndex",4,3,2,false,0,5,1)
M187G=D(1,"TMSG_SANDBOX_NC_CONGRESSBACK_RSP",".CSMsg.TMSG_SANDBOX_NC_CONGRESSBACK_RSP",false,{},{},nil,{})
F631D=F(2,"regionID",".CSMsg.TMSG_SANDBOX_NC_GETREGIOINPOS_REQ.regionID",1,0,2,false,0,5,1)
M188G=D(1,"TMSG_SANDBOX_NC_GETREGIOINPOS_REQ",".CSMsg.TMSG_SANDBOX_NC_GETREGIOINPOS_REQ",false,{},{},nil,{})
F632D=F(2,"errCode",".CSMsg.TMSG_SANDBOX_NC_GETREGIOINPOS_RSP.errCode",1,0,2,false,nil,14,8)
F633D=F(2,"regionID",".CSMsg.TMSG_SANDBOX_NC_GETREGIOINPOS_RSP.regionID",2,1,2,false,0,5,1)
F634D=F(2,"pos",".CSMsg.TMSG_SANDBOX_NC_GETREGIOINPOS_RSP.pos",3,2,1,false,nil,11,10)
M189G=D(1,"TMSG_SANDBOX_NC_GETREGIOINPOS_RSP",".CSMsg.TMSG_SANDBOX_NC_GETREGIOINPOS_RSP",false,{},{},nil,{})
F635D=F(2,"atkDbid",".CSMsg.TMSG_SANDBOX_BATTLE_BROCAST_NTF.atkDbid",1,0,2,false,0,13,3)
F636D=F(2,"win",".CSMsg.TMSG_SANDBOX_BATTLE_BROCAST_NTF.win",2,1,2,false,false,8,7)
F637D=F(2,"beAtkSid",".CSMsg.TMSG_SANDBOX_BATTLE_BROCAST_NTF.beAtkSid",3,2,2,false,0,4,4)
F638D=F(2,"beAtkType",".CSMsg.TMSG_SANDBOX_BATTLE_BROCAST_NTF.beAtkType",4,3,1,false,0,13,3)
F639D=F(2,"beAtkPos",".CSMsg.TMSG_SANDBOX_BATTLE_BROCAST_NTF.beAtkPos",5,4,1,false,nil,11,10)
M190G=D(1,"TMSG_SANDBOX_BATTLE_BROCAST_NTF",".CSMsg.TMSG_SANDBOX_BATTLE_BROCAST_NTF",false,{},{},nil,{})
F640D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_ALLIANCE_TRAIN_POS_REQ.sandboxSid",1,0,2,false,0,13,3)
F641D=F(2,"sid",".CSMsg.TMSG_SANDBOX_ALLIANCE_TRAIN_POS_REQ.sid",2,1,2,false,0,4,4)
M191G=D(1,"TMSG_SANDBOX_ALLIANCE_TRAIN_POS_REQ",".CSMsg.TMSG_SANDBOX_ALLIANCE_TRAIN_POS_REQ",false,{},{},nil,{})
F642D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_ALLIANCE_TRAIN_POS_RSP.sandboxSid",1,0,2,false,0,13,3)
F643D=F(2,"sid",".CSMsg.TMSG_SANDBOX_ALLIANCE_TRAIN_POS_RSP.sid",2,1,2,false,0,4,4)
F644D=F(2,"err",".CSMsg.TMSG_SANDBOX_ALLIANCE_TRAIN_POS_RSP.err",3,2,2,false,nil,14,8)
F645D=F(2,"pos",".CSMsg.TMSG_SANDBOX_ALLIANCE_TRAIN_POS_RSP.pos",4,3,1,false,nil,11,10)
M192G=D(1,"TMSG_SANDBOX_ALLIANCE_TRAIN_POS_RSP",".CSMsg.TMSG_SANDBOX_ALLIANCE_TRAIN_POS_RSP",false,{},{},nil,{})
F646D=F(2,"name",".CSMsg.TGetBox_PopInfo.name",1,0,2,false,"",9,9)
F647D=F(2,"shortAllianceName",".CSMsg.TGetBox_PopInfo.shortAllianceName",2,1,2,false,"",9,9)
F648D=F(2,"frameId",".CSMsg.TGetBox_PopInfo.frameId",3,2,2,false,0,5,1)
F649D=F(2,"faceId",".CSMsg.TGetBox_PopInfo.faceId",4,3,1,false,0,5,1)
F650D=F(2,"faceStr",".CSMsg.TGetBox_PopInfo.faceStr",5,4,1,false,"",9,9)
F651D=F(2,"getTimestamp",".CSMsg.TGetBox_PopInfo.getTimestamp",6,5,2,false,0,13,3)
M193G=D(1,"TGetBox_PopInfo",".CSMsg.TGetBox_PopInfo",false,{},{},nil,{})
F652D=F(2,"level",".CSMsg.TMSG_SANDBOX_KILLSOLDIER_NTF.soldier.level",1,0,2,false,0,5,1)
F653D=F(2,"nums",".CSMsg.TMSG_SANDBOX_KILLSOLDIER_NTF.soldier.nums",2,1,2,false,0,5,1)
M195G=D(1,"soldier",".CSMsg.TMSG_SANDBOX_KILLSOLDIER_NTF.soldier",false,{},{},nil,{})
F654D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_KILLSOLDIER_NTF.sandboxSid",1,0,1,false,0,5,1)
F655D=F(2,"sid",".CSMsg.TMSG_SANDBOX_KILLSOLDIER_NTF.sid",2,1,1,false,0,4,4)
F656D=F(2,"entityType",".CSMsg.TMSG_SANDBOX_KILLSOLDIER_NTF.entityType",3,2,1,false,0,5,1)
F657D=F(2,"pos",".CSMsg.TMSG_SANDBOX_KILLSOLDIER_NTF.pos",4,3,1,false,nil,11,10)
F658D=F(2,"kill",".CSMsg.TMSG_SANDBOX_KILLSOLDIER_NTF.kill",5,4,3,false,{},11,10)
M194G=D(1,"TMSG_SANDBOX_KILLSOLDIER_NTF",".CSMsg.TMSG_SANDBOX_KILLSOLDIER_NTF",false,nil,{},nil,{})
F659D=F(2,"pos",".CSMsg.TAPosPopInfo.pos",1,0,2,false,nil,11,10)
F660D=F(2,"popInfo",".CSMsg.TAPosPopInfo.popInfo",2,1,3,false,{},11,10)
M196G=D(1,"TAPosPopInfo",".CSMsg.TAPosPopInfo",false,{},{},nil,{})
F661D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_GETBOX_NTF.sandboxSid",1,0,2,false,0,13,3)
F662D=F(2,"popList",".CSMsg.TMSG_SANDBOX_GETBOX_NTF.popList",2,1,3,false,{},11,10)
M197G=D(1,"TMSG_SANDBOX_GETBOX_NTF",".CSMsg.TMSG_SANDBOX_GETBOX_NTF",false,{},{},nil,{})
F663D=F(2,"type",".CSMsg.TMSG_SANDBOX_SPECIAL_EFFECT_NTF.type",1,0,2,false,nil,14,8)
F664D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_SPECIAL_EFFECT_NTF.sandboxSid",2,1,2,false,0,13,3)
F665D=F(2,"pos",".CSMsg.TMSG_SANDBOX_SPECIAL_EFFECT_NTF.pos",3,2,1,false,nil,11,10)
F666D=F(2,"dbid",".CSMsg.TMSG_SANDBOX_SPECIAL_EFFECT_NTF.dbid",4,3,1,false,0,5,1)
M198G=D(1,"TMSG_SANDBOX_SPECIAL_EFFECT_NTF",".CSMsg.TMSG_SANDBOX_SPECIAL_EFFECT_NTF",false,{},{},nil,{})
F667D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_IS_NORMAL_REQ.sandboxSid",1,0,2,false,0,13,3)
M200G=D(1,"TMSG_SANDBOX_IS_NORMAL_REQ",".CSMsg.TMSG_SANDBOX_IS_NORMAL_REQ",false,{},{},nil,{})
F668D=F(2,"sandboxSid",".CSMsg.TMSG_SANDBOX_IS_NORMAL_RSP.sandboxSid",1,0,2,false,0,13,3)
F669D=F(2,"errorCode",".CSMsg.TMSG_SANDBOX_IS_NORMAL_RSP.errorCode",2,1,2,false,nil,14,8)
M201G=D(1,"TMSG_SANDBOX_IS_NORMAL_RSP",".CSMsg.TMSG_SANDBOX_IS_NORMAL_RSP",false,{},{},nil,{})

E1M.values = {V1M,V2M,V3M}
E2M.values = {V4M,V5M}
E3M.values = {V6M,V7M,V8M}
E4M.values = {V9M,V10M,V11M,V12M,V13M,V14M,V15M,V16M,V17M,V18M,V19M,V20M,V21M,V22M,V23M}
E5M.values = {V24M,V25M,V26M,V27M,V28M,V29M,V30M,V31M,V32M,V33M}
E6M.values = {V34M,V35M,V36M,V37M,V38M,V39M,V40M}
E7M.values = {V41M,V42M,V43M,V44M,V45M,V46M,V47M,V48M}
E8M.values = {V49M,V50M,V51M,V52M,V53M,V54M,V55M,V56M,V57M,V58M,V59M,V60M,V61M,V62M,V63M,V64M,V65M,V66M,V67M,V68M,V69M,V70M,V71M}
E9M.values = {V72M,V73M,V74M}
E10M.values = {V75M,V76M,V77M}
E11M.values = {V78M,V79M,V80M}
E12M.values = {V81M}
E13M.values = {V82M,V83M,V84M,V85M,V86M,V87M,V88M,V89M,V90M,V91M,V92M,V93M,V94M,V95M,V96M,V97M,V98M,V99M,V100M,V101M,V102M,V103M,V104M,V105M,V106M,V107M,V108M,V109M,V110M,V111M,V112M,V113M,V114M,V115M,V116M,V117M,V118M,V119M,V120M,V121M,V122M,V123M,V124M,V125M,V126M,V127M,V128M,V129M,V130M,V131M,V132M,V133M,V134M,V135M,V136M,V137M,V138M,V139M,V140M,V141M,V142M,V143M,V144M,V145M,V146M,V147M,V148M,V149M,V150M,V151M,V152M,V153M,V154M,V155M,V156M,V157M,V158M,V159M,V160M,V161M,V162M,V163M,V164M,V165M,V166M,V167M,V168M,V169M,V170M,V171M,V172M,V173M,V174M,V175M,V176M,V177M,V178M,V179M,V180M,V181M,V182M,V183M,V184M,V185M,V186M,V187M,V188M,V189M,V190M,V191M,V192M,V193M,V194M,V195M,V196M,V197M,V198M,V199M,V200M,V201M,V202M,V203M,V204M,V205M,V206M,V207M,V208M,V209M,V210M,V211M,V212M,V213M,V214M,V215M,V216M,V217M,V218M,V219M,V220M,V221M,V222M,V223M,V224M,V225M,V226M}
E14M.values = {V227M,V228M,V229M,V230M,V231M,V232M,V233M,V234M,V235M,V236M,V237M,V238M,V239M,V240M}
E15M.values = {V241M,V242M,V243M,V244M,V245M,V246M,V247M,V248M,V249M,V250M,V251M,V252M,V253M,V254M,V255M,V256M,V257M,V258M,V259M,V260M,V261M,V262M,V263M,V264M,V265M,V266M,V267M,V268M,V269M,V270M,V271M,V272M,V273M,V274M,V275M,V276M,V277M,V278M,V279M}
E16M.values = {V280M,V281M,V282M,V283M,V284M,V285M}
E17M.values = {V286M,V287M,V288M}
E18M.values = {V289M,V290M,V291M,V292M,V293M}
E19M.values = {V294M,V295M,V296M,V297M}
E20M.values = {V298M,V299M,V300M,V301M,V302M,V303M,V304M,V305M}
E21M.values = {V306M,V307M,V308M,V309M}
E22M.values = {V310M,V311M,V312M,V313M,V314M,V315M,V316M,V317M,V318M}
E23M.values = {V319M,V320M}
E24M.values = {V321M,V322M}
E25M.values = {V323M,V324M,V325M,V326M,V327M,V328M}
E26M.values = {V329M,V330M}
E27M.values = {V331M}
M1G.fields={F1D, F2D, F3D}
F5D.enum_type=error_code_pb.E1M
F7D.message_type=M1G
M3G.fields={F4D, F5D, F6D, F7D, F8D}
M5G.fields={F9D, F10D, F11D, F12D, F13D}
F15D.message_type=M1G
M6G.fields={F14D, F15D}
F16D.message_type=M1G
M7G.fields={F16D, F17D, F18D, F19D}
F20D.message_type=M1G
F21D.enum_type=error_code_pb.E1M
M8G.fields={F20D, F21D, F22D}
F24D.message_type=M1G
M9G.fields={F23D, F24D, F25D}
F27D.enum_type=error_code_pb.E1M
F28D.message_type=M1G
M10G.fields={F26D, F27D, F28D, F29D}
M11G.fields={F30D}
F32D.enum_type=error_code_pb.E1M
M12G.fields={F31D, F32D}
M13G.fields={F33D, F34D}
M14G.fields={F35D, F36D}
F37D.message_type=M1G
F39D.message_type=M1G
M15G.fields={F37D, F38D, F39D}
F42D.message_type=common_pb.M11G
M16G.fields={F40D, F41D, F42D, F43D, F44D}
F45D.message_type=M15G
F46D.enum_type=M19G
F47D.message_type=common_pb.M11G
F48D.enum_type=common_pb.E18M
F55D.message_type=M15G
M18G.fields={F45D, F46D, F47D, F48D, F49D, F50D, F51D, F52D, F53D, F54D, F55D}
F56D.enum_type=M22G
F58D.message_type=M1G
F59D.message_type=M18G
M21G.fields={F56D, F57D, F58D, F59D, F60D, F61D, F62D, F63D}
F64D.message_type=M21G
F71D.message_type=M1G
M23G.fields={F64D, F65D, F66D, F67D, F68D, F69D, F70D, F71D, F72D, F73D}
F75D.enum_type=M19G
F76D.enum_type=common_pb.E18M
F77D.message_type=M15G
F78D.message_type=M15G
F79D.message_type=common_pb.M11G
F82D.message_type=M1G
F83D.message_type=M16G
M24G.fields={F74D, F75D, F76D, F77D, F78D, F79D, F80D, F81D, F82D, F83D, F84D, F85D, F86D, F87D, F88D, F89D}
F90D.message_type=M24G
M25G.fields={F90D, F91D, F92D, F93D}
M26G.fields={F94D, F95D, F96D}
F99D.enum_type=error_code_pb.E1M
F100D.message_type=M24G
M27G.fields={F97D, F98D, F99D, F100D, F101D}
M28G.fields={F102D, F103D, F104D, F105D}
F108D.enum_type=error_code_pb.E1M
F109D.message_type=M21G
M29G.fields={F106D, F107D, F108D, F109D, F110D}
M30G.fields={F111D}
F114D.message_type=M1G
F115D.enum_type=error_code_pb.E1M
M31G.fields={F112D, F113D, F114D, F115D}
M32G.fields={F116D, F117D, F118D, F119D, F120D, F121D, F122D}
M33G.fields={F123D, F124D}
F125D.enum_type=error_code_pb.E1M
F126D.message_type=M32G
M34G.fields={F125D, F126D, F127D, F128D, F129D}
F130D.message_type=M24G
M35G.fields={F130D}
M36G.fields={F131D, F132D, F133D, F134D}
M37G.fields={F135D, F136D, F137D, F138D}
F139D.enum_type=error_code_pb.E1M
F141D.message_type=M13G
F142D.message_type=M14G
F143D.enum_type=M22G
F146D.message_type=role_pb.M32G
F147D.message_type=M37G
F148D.message_type=common_pb.M4G
F151D.message_type=common_pb.M33G
F152D.message_type=M21G
F153D.message_type=M24G
M38G.fields={F139D, F140D, F141D, F142D, F143D, F144D, F145D, F146D, F147D, F148D, F149D, F150D, F151D, F152D, F153D}
F157D.enum_type=M43G
F158D.enum_type=M44G
F159D.message_type=M1G
M42G.fields={F154D, F155D, F156D, F157D, F158D, F159D}
F160D.enum_type=error_code_pb.E1M
F165D.enum_type=M22G
F166D.message_type=M13G
F167D.message_type=M14G
F168D.message_type=M1G
M45G.fields={F160D, F161D, F162D, F163D, F164D, F165D, F166D, F167D, F168D}
F170D.message_type=M1G
F172D.message_type=tbs_pb.M2G
M46G.fields={F169D, F170D, F171D, F172D, F173D}
F174D.enum_type=error_code_pb.E1M
M48G.fields={F174D}
F175D.message_type=common_new_pb.M3G
M49G.fields={F175D, F176D}
F177D.enum_type=error_code_pb.E1M
M51G.fields={F177D}
M52G.fields={F178D, F179D, F180D, F181D}
F182D.enum_type=error_code_pb.E1M
M53G.fields={F182D, F183D, F184D, F185D, F186D, F187D, F188D}
F190D.message_type=M1G
F191D.enum_type=M55G
M54G.fields={F189D, F190D, F191D}
F192D.enum_type=error_code_pb.E1M
F193D.message_type=M1G
F195D.enum_type=M55G
M56G.fields={F192D, F193D, F194D, F195D}
M57G.fields={F196D, F197D}
F198D.enum_type=error_code_pb.E1M
F199D.message_type=M1G
M58G.fields={F198D, F199D, F200D, F201D}
M59G.fields={F202D}
F203D.enum_type=error_code_pb.E1M
M60G.fields={F203D, F204D}
F205D.enum_type=M22G
F211D.message_type=common_new_pb.M9G
F212D.message_type=common_new_pb.M10G
M61G.fields={F205D, F206D, F207D, F208D, F209D, F210D, F211D, F212D, F213D, F214D, F215D, F216D, F217D, F218D, F219D, F220D, F221D}
M64G.fields={F222D}
M65G.fields={F223D, F224D}
F227D.message_type=M61G
M66G.fields={F225D, F226D, F227D, F228D}
M67G.fields={F229D}
F230D.enum_type=error_code_pb.E1M
M68G.fields={F230D, F231D, F232D}
M69G.fields={F233D, F234D}
F235D.enum_type=error_code_pb.E1M
M70G.fields={F235D, F236D}
F237D.enum_type=error_code_pb.E1M
M72G.fields={F237D, F238D, F239D}
F240D.enum_type=M74G
M73G.fields={F240D, F241D}
F242D.enum_type=error_code_pb.E1M
F243D.enum_type=M74G
M75G.fields={F242D, F243D, F244D, F245D}
M76G.fields={F246D, F247D, F248D, F249D, F250D}
F251D.enum_type=error_code_pb.E1M
F253D.message_type=M1G
M77G.fields={F251D, F252D, F253D}
F254D.message_type=M1G
M78G.fields={F254D, F255D, F256D, F257D, F258D, F259D, F260D, F261D, F262D, F263D, F264D}
F266D.message_type=M78G
M79G.fields={F265D, F266D}
F267D.enum_type=error_code_pb.E1M
F269D.message_type=M78G
M80G.fields={F267D, F268D, F269D}
F270D.message_type=M78G
M81G.fields={F270D, F271D}
M82G.fields={F272D, F273D}
F274D.enum_type=error_code_pb.E1M
F275D.message_type=M32G
M83G.fields={F274D, F275D, F276D, F277D, F278D}
F281D.message_type=tbs_pb.M2G
F282D.enum_type=M85G
M84G.fields={F279D, F280D, F281D, F282D}
F283D.enum_type=error_code_pb.E1M
F284D.message_type=M1G
M86G.fields={F283D, F284D}
M87G.fields={F285D, F286D, F287D}
F288D.enum_type=error_code_pb.E1M
M88G.fields={F288D, F289D, F290D, F291D}
F296D.message_type=common_pb.M11G
M89G.fields={F292D, F293D, F294D, F295D, F296D, F297D, F298D, F299D, F300D}
F307D.message_type=M1G
F308D.message_type=M89G
F312D.message_type=M1G
M90G.fields={F301D, F302D, F303D, F304D, F305D, F306D, F307D, F308D, F309D, F310D, F311D, F312D, F313D, F314D, F315D, F316D, F317D, F318D, F319D, F320D}
M91G.fields={F321D, F322D, F323D, F324D}
F326D.enum_type=M93G
F327D.message_type=M90G
M92G.fields={F325D, F326D, F327D}
F328D.enum_type=M93G
F332D.message_type=tbs_pb.M2G
M94G.fields={F328D, F329D, F330D, F331D, F332D, F333D}
F334D.enum_type=error_code_pb.E1M
F335D.enum_type=M93G
F337D.message_type=M90G
M95G.fields={F334D, F335D, F336D, F337D}
F338D.enum_type=error_code_pb.E1M
F339D.message_type=M90G
M97G.fields={F338D, F339D}
M98G.fields={F340D}
F341D.enum_type=error_code_pb.E1M
F343D.message_type=M89G
M99G.fields={F341D, F342D, F343D}
M100G.fields={F344D, F345D}
M101G.fields={F346D}
F347D.message_type=common_new_pb.M1G
F351D.message_type=M1G
F352D.message_type=M1G
M102G.fields={F347D, F348D, F349D, F350D, F351D, F352D}
F353D.message_type=common_new_pb.M1G
F356D.message_type=M1G
M104G.fields={F353D, F354D, F355D, F356D}
F357D.message_type=common_new_pb.M1G
F361D.message_type=M1G
F362D.message_type=M1G
M105G.fields={F357D, F358D, F359D, F360D, F361D, F362D}
F363D.message_type=common_new_pb.M1G
F367D.message_type=M1G
F368D.message_type=M1G
M106G.fields={F363D, F364D, F365D, F366D, F367D, F368D}
F372D.message_type=M1G
F373D.message_type=M1G
M107G.fields={F369D, F370D, F371D, F372D, F373D, F374D, F375D}
F376D.message_type=common_new_pb.M1G
F380D.message_type=M1G
F381D.message_type=M1G
M108G.fields={F376D, F377D, F378D, F379D, F380D, F381D}
F382D.message_type=common_new_pb.M1G
F386D.message_type=M1G
F387D.message_type=M1G
M109G.fields={F382D, F383D, F384D, F385D, F386D, F387D}
F391D.message_type=M1G
M110G.fields={F388D, F389D, F390D, F391D, F392D, F393D, F394D, F395D, F396D, F397D, F398D, F399D}
F400D.enum_type=M112G
F401D.message_type=M102G
F402D.message_type=M104G
F403D.message_type=M105G
F404D.message_type=M106G
F405D.message_type=M107G
F406D.message_type=M108G
F407D.message_type=M106G
F408D.enum_type=M113G
F409D.message_type=M110G
M111G.fields={F400D, F401D, F402D, F403D, F404D, F405D, F406D, F407D, F408D, F409D}
F410D.enum_type=error_code_pb.E1M
M115G.fields={F410D}
F412D.message_type=M111G
M116G.fields={F411D, F412D}
M117G.fields={F413D, F414D}
F416D.enum_type=error_code_pb.E1M
M118G.fields={F415D, F416D, F417D}
M119G.fields={F418D}
F419D.enum_type=error_code_pb.E1M
M120G.fields={F419D, F420D}
F422D.message_type=M1G
M121G.fields={F421D, F422D}
M122G.fields={F423D, F424D}
F425D.enum_type=error_code_pb.E1M
F426D.message_type=M121G
M123G.fields={F425D, F426D}
M124G.fields={F427D, F428D, F429D, F430D}
F431D.message_type=M124G
M125G.fields={F431D, F432D}
M126G.fields={F433D}
F435D.enum_type=error_code_pb.E1M
F436D.message_type=M1G
M127G.fields={F434D, F435D, F436D}
M128G.fields={F437D, F438D}
M129G.fields={F439D, F440D, F441D}
F445D.message_type=M1G
F446D.message_type=M1G
F448D.enum_type=M22G
M130G.fields={F442D, F443D, F444D, F445D, F446D, F447D, F448D, F449D}
F451D.message_type=M1G
F452D.message_type=M1G
M131G.fields={F450D, F451D, F452D, F453D}
F458D.message_type=M1G
M132G.fields={F454D, F455D, F456D, F457D, F458D, F459D}
F461D.message_type=M1G
F462D.enum_type=M134G
M133G.fields={F460D, F461D, F462D}
M135G.fields={F463D, F464D}
F465D.enum_type=error_code_pb.E1M
F466D.message_type=M133G
M136G.fields={F465D, F466D, F467D}
F468D.message_type=M133G
M137G.fields={F468D, F469D}
M138G.fields={F470D, F471D}
F472D.enum_type=error_code_pb.E1M
M139G.fields={F472D, F473D, F474D, F475D, F476D}
F478D.message_type=M1G
M140G.fields={F477D, F478D, F479D, F480D, F481D, F482D, F483D, F484D}
M141G.fields={F485D, F486D, F487D, F488D, F489D, F490D, F491D, F492D, F493D, F494D}
F496D.message_type=M1G
F505D.message_type=M141G
M142G.fields={F495D, F496D, F497D, F498D, F499D, F500D, F501D, F502D, F503D, F504D, F505D, F506D}
M143G.fields={F507D, F508D}
F509D.enum_type=error_code_pb.E1M
F510D.message_type=M142G
M144G.fields={F509D, F510D, F511D}
M145G.fields={F512D}
F513D.enum_type=error_code_pb.E1M
M146G.fields={F513D}
M147G.fields={F514D}
F515D.enum_type=error_code_pb.E1M
M148G.fields={F515D}
F516D.enum_type=common_new_pb.E1M
F517D.message_type=common_new_pb.M3G
M149G.fields={F516D, F517D, F518D, F519D}
F520D.enum_type=error_code_pb.E1M
M151G.fields={F520D}
M152G.fields={F521D, F522D}
F525D.enum_type=error_code_pb.E1M
F526D.message_type=M1G
M153G.fields={F523D, F524D, F525D, F526D}
F530D.message_type=M1G
F531D.message_type=M1G
F532D.enum_type=M19G
M154G.fields={F527D, F528D, F529D, F530D, F531D, F532D}
F533D.message_type=M154G
M155G.fields={F533D, F534D}
F537D.message_type=M1G
M156G.fields={F535D, F536D, F537D, F538D, F539D}
M157G.fields={F540D, F541D, F542D}
F545D.enum_type=error_code_pb.E1M
M158G.fields={F543D, F544D, F545D, F546D}
M159G.fields={F547D}
F549D.enum_type=error_code_pb.E1M
M160G.fields={F548D, F549D}
F551D.message_type=common_pb.M32G
M161G.fields={F550D, F551D, F552D}
F555D.message_type=common_pb.M32G
M163G.fields={F553D, F554D, F555D, F556D, F557D, F558D, F559D}
F561D.message_type=M163G
M164G.fields={F560D, F561D}
M165G.fields={F562D}
F564D.enum_type=error_code_pb.E1M
F567D.message_type=M163G
M166G.fields={F563D, F564D, F565D, F566D, F567D, F568D}
M167G.fields={F569D}
F571D.enum_type=error_code_pb.E1M
M168G.fields={F570D, F571D, F572D}
M169G.fields={F573D, F574D}
F577D.enum_type=error_code_pb.E1M
M170G.fields={F575D, F576D, F577D, F578D}
M171G.fields={F579D, F580D, F581D, F582D, F583D, F584D, F585D, F586D}
F587D.enum_type=M173G
F588D.message_type=M171G
M172G.fields={F587D, F588D}
F591D.message_type=M1G
M175G.fields={F589D, F590D, F591D, F592D, F593D, F594D, F595D}
F596D.enum_type=error_code_pb.E1M
F597D.message_type=M175G
M176G.fields={F596D, F597D}
M178G.fields={F598D, F599D, F600D, F601D, F602D}
F603D.enum_type=error_code_pb.E1M
F604D.message_type=M178G
M179G.fields={F603D, F604D}
F607D.message_type=M1G
M180G.fields={F605D, F606D, F607D, F608D, F609D, F610D, F611D, F612D, F613D, F614D}
F615D.enum_type=error_code_pb.E1M
F616D.message_type=M180G
M182G.fields={F615D, F616D}
M184G.fields={F617D, F618D, F619D, F620D, F621D}
F622D.enum_type=error_code_pb.E1M
F623D.message_type=M184G
M185G.fields={F622D, F623D, F624D}
M186G.fields={F625D, F626D}
F627D.enum_type=error_code_pb.E1M
M187G.fields={F627D, F628D, F629D, F630D}
M188G.fields={F631D}
F632D.enum_type=error_code_pb.E1M
F634D.message_type=M1G
M189G.fields={F632D, F633D, F634D}
F639D.message_type=M1G
M190G.fields={F635D, F636D, F637D, F638D, F639D}
M191G.fields={F640D, F641D}
F644D.enum_type=error_code_pb.E1M
F645D.message_type=M1G
M192G.fields={F642D, F643D, F644D, F645D}
M193G.fields={F646D, F647D, F648D, F649D, F650D, F651D}
M195G.fields={F652D, F653D}
M195G.containing_type=M194G
F657D.message_type=M1G
F658D.message_type=M195G
M194G.nested_types={M195G}
M194G.fields={F654D, F655D, F656D, F657D, F658D}
F659D.message_type=M1G
F660D.message_type=M193G
M196G.fields={F659D, F660D}
F662D.message_type=M196G
M197G.fields={F661D, F662D}
F663D.enum_type=M199G
F665D.message_type=M1G
M198G.fields={F663D, F664D, F665D, F666D}
M200G.fields={F667D}
F669D.enum_type=error_code_pb.E1M
M201G.fields={F668D, F669D}

RealityMoveCityType_FreeMoveUseItem = 1
RealityMoveCityType_None = 0
SANDBOX_MARCH_SPEED_1 = 1
SANDBOX_MARCH_SPEED_2 = 2
SANDBOX_MARCH_SPEED_3 = 3
SANDBOX_MARCH_SPEED_ERROR = 0
SANDBOX_MASS_MEMBER_MAX = 5
SANDBOX_MONTYPE_ACORNPUBTASK = 16
SANDBOX_MONTYPE_EMCB = 6
SANDBOX_MONTYPE_GATHERING = 13
SANDBOX_MONTYPE_JY = 4
SANDBOX_MONTYPE_LDMNK = 11
SANDBOX_MONTYPE_LDSS = 7
SANDBOX_MONTYPE_LS = 2
SANDBOX_MONTYPE_TB = 3
SANDBOX_MONTYPE_TK = 1
SANDBOX_MONTYPE_YD = 5
SxSpecialEffect_AllianceOutFire = 1
TAPosPopInfo =M(M196G)
TBuffInfo =M(M124G)
TCongressTeamInfo =M(M184G)
TFakeMarchInfo =M(M154G)
TGetBox_PopInfo =M(M193G)
TKastenBoxInfo =M(M142G)
TKastenBoxOnePlayerRecord =M(M141G)
TKastenBoxPersonInfo =M(M133G)
TMSG_BUFFINFO_NTF =M(M125G)
TMSG_MOVE_CITY_REQ =M(M54G)
TMSG_MOVE_CITY_RSP =M(M56G)
TMSG_NEWPALYER_FREEMOVE_REQ =M(M57G)
TMSG_NEWPALYER_FREEMOVE_RSP =M(M58G)
TMSG_SANDBOX_ACCELERATE_REQ =M(M69G)
TMSG_SANDBOX_ACCELERATE_RSP =M(M70G)
TMSG_SANDBOX_ALERT_LIST_NTF =M(M116G)
TMSG_SANDBOX_ALERT_LIST_REQ =M(M114G)
TMSG_SANDBOX_ALERT_LIST_RSP =M(M115G)
TMSG_SANDBOX_ALERT_NTF =M(M101G)
TMSG_SANDBOX_ALLIANCE_HELP_REQ =M(M147G)
TMSG_SANDBOX_ALLIANCE_HELP_RSP =M(M148G)
TMSG_SANDBOX_ALLIANCE_HELP_SUCCESS_NTF =M(M156G)
TMSG_SANDBOX_ALLIANCE_SIEGECAMP_DATA_REQ =M(M30G)
TMSG_SANDBOX_ALLIANCE_SIEGECAMP_DATA_RSP =M(M31G)
TMSG_SANDBOX_ALLIANCE_TRAIN_POS_REQ =M(M191G)
TMSG_SANDBOX_ALLIANCE_TRAIN_POS_RSP =M(M192G)
TMSG_SANDBOX_ALONE_ENTITY_DATA_REQ =M(M28G)
TMSG_SANDBOX_ALONE_ENTITY_DATA_RSP =M(M29G)
TMSG_SANDBOX_ALONE_MARCH_DATA_REQ =M(M26G)
TMSG_SANDBOX_ALONE_MARCH_DATA_RSP =M(M27G)
TMSG_SANDBOX_ATTACK_REQ =M(M46G)
TMSG_SANDBOX_ATTACK_RSP =M(M48G)
TMSG_SANDBOX_BACK_REQ =M(M59G)
TMSG_SANDBOX_BACK_RSP =M(M60G)
TMSG_SANDBOX_BASEVFX_NTF =M(M130G)
TMSG_SANDBOX_BASE_INFO_REQ =M(M2G)
TMSG_SANDBOX_BASE_INFO_RSP =M(M3G)
TMSG_SANDBOX_BATTLE_BROCAST_NTF =M(M190G)
TMSG_SANDBOX_BATTLE_RESULT_NTF =M(M129G)
TMSG_SANDBOX_CALLBACK_DETAIL_REQ =M(M42G)
TMSG_SANDBOX_CALLBACK_DETAIL_RSP =M(M45G)
TMSG_SANDBOX_CANCLE_DETECT_REQ =M(M119G)
TMSG_SANDBOX_CANCLE_DETECT_RSP =M(M120G)
TMSG_SANDBOX_CARRIAGEPOS_REQ =M(M152G)
TMSG_SANDBOX_CARRIAGEPOS_RSP =M(M153G)
TMSG_SANDBOX_CROSS_BACK_POS_REQ =M(M71G)
TMSG_SANDBOX_CROSS_BACK_POS_RSP =M(M72G)
TMSG_SANDBOX_DATA_NTF =M(M23G)
TMSG_SANDBOX_DEFFAILD_NTF =M(M131G)
TMSG_SANDBOX_DETECT_REQ =M(M117G)
TMSG_SANDBOX_DETECT_RSP =M(M118G)
TMSG_SANDBOX_ENTER_REQ =M(M6G)
TMSG_SANDBOX_ENTER_RSP =M(M8G)
TMSG_SANDBOX_EXIT_REQ =M(M11G)
TMSG_SANDBOX_EXIT_RSP =M(M12G)
TMSG_SANDBOX_EXPEDITION_DETAIL_REQ =M(M52G)
TMSG_SANDBOX_EXPEDITION_DETAIL_RSP =M(M53G)
TMSG_SANDBOX_FAKE_MARCH_NTF =M(M155G)
TMSG_SANDBOX_GAME_BATTLE_REQ =M(M149G)
TMSG_SANDBOX_GAME_BATTLE_RSP =M(M151G)
TMSG_SANDBOX_GETBOX_NTF =M(M197G)
TMSG_SANDBOX_GET_DETAIL_REQ =M(M36G)
TMSG_SANDBOX_GET_DETAIL_RSP =M(M38G)
TMSG_SANDBOX_GET_KASTENBOX_INFO_REQ =M(M143G)
TMSG_SANDBOX_GET_KASTENBOX_INFO_RSP =M(M144G)
TMSG_SANDBOX_GET_KASTENBOX_PERSON_INFO_REQ =M(M135G)
TMSG_SANDBOX_GET_KASTENBOX_PERSON_INFO_RSP =M(M136G)
TMSG_SANDBOX_GET_LOOT_REQ =M(M67G)
TMSG_SANDBOX_GET_LOOT_RSP =M(M68G)
TMSG_SANDBOX_IS_NORMAL_REQ =M(M200G)
TMSG_SANDBOX_IS_NORMAL_RSP =M(M201G)
TMSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_NTF =M(M140G)
TMSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_REQ =M(M138G)
TMSG_SANDBOX_KASTENBOX_RECIEVE_REWARD_RSP =M(M139G)
TMSG_SANDBOX_KASTENBOX_UPDATE_PERSON_INFO_NTF =M(M137G)
TMSG_SANDBOX_KILLSOLDIER_NTF =M(M194G)
TMSG_SANDBOX_KILLSOLDIER_NTF.soldier =M(M195G)
TMSG_SANDBOX_LOOT_DATA_NTF =M(M64G)
TMSG_SANDBOX_LOOT_INFO_REQ =M(M65G)
TMSG_SANDBOX_LOOT_INFO_RSP =M(M66G)
TMSG_SANDBOX_MARCH_NTF =M(M25G)
TMSG_SANDBOX_MARKLIST_NTF =M(M81G)
TMSG_SANDBOX_MARK_REQ =M(M79G)
TMSG_SANDBOX_MARK_RSP =M(M80G)
TMSG_SANDBOX_MASS_CHANGE_NTF =M(M92G)
TMSG_SANDBOX_MASS_LEADER_DEL_NTF =M(M100G)
TMSG_SANDBOX_MASS_MAIN_ICON_NTF =M(M91G)
TMSG_SANDBOX_MASS_TEAM_MEMBER_REQ =M(M98G)
TMSG_SANDBOX_MASS_TEAM_MEMBER_RSP =M(M99G)
TMSG_SANDBOX_MASS_TEAM_REQ =M(M96G)
TMSG_SANDBOX_MASS_TEAM_RSP =M(M97G)
TMSG_SANDBOX_MEMBER_REQ =M(M122G)
TMSG_SANDBOX_MEMBER_RSP =M(M123G)
TMSG_SANDBOX_MONSTERFK_NTF =M(M128G)
TMSG_SANDBOX_MOVE_VIEW_REQ =M(M7G)
TMSG_SANDBOX_NC_ABANDON_REQ =M(M169G)
TMSG_SANDBOX_NC_ABANDON_RSP =M(M170G)
TMSG_SANDBOX_NC_ACTIVITY_REQ =M(M177G)
TMSG_SANDBOX_NC_ACTIVITY_RSP =M(M179G)
TMSG_SANDBOX_NC_ATTACKHERO_NTF =M(M164G)
TMSG_SANDBOX_NC_CONGRESSBACK_REQ =M(M186G)
TMSG_SANDBOX_NC_CONGRESSBACK_RSP =M(M187G)
TMSG_SANDBOX_NC_CONGRESSLIST_REQ =M(M183G)
TMSG_SANDBOX_NC_CONGRESSLIST_RSP =M(M185G)
TMSG_SANDBOX_NC_DECLAREWAR_REQ =M(M157G)
TMSG_SANDBOX_NC_DECLAREWAR_RSP =M(M158G)
TMSG_SANDBOX_NC_GETREGIOINPOS_REQ =M(M188G)
TMSG_SANDBOX_NC_GETREGIOINPOS_RSP =M(M189G)
TMSG_SANDBOX_NC_GETREWARD_REQ =M(M167G)
TMSG_SANDBOX_NC_GETREWARD_RSP =M(M168G)
TMSG_SANDBOX_NC_LIST_REQ =M(M174G)
TMSG_SANDBOX_NC_LIST_RSP =M(M176G)
TMSG_SANDBOX_NC_OFFLINEATTACK_NTF =M(M161G)
TMSG_SANDBOX_NC_OFFLINEATTACK_REQ =M(M159G)
TMSG_SANDBOX_NC_OFFLINEATTACK_RSP =M(M160G)
TMSG_SANDBOX_NC_POPUPS_NTF =M(M172G)
TMSG_SANDBOX_NC_REWARDINFO_REQ =M(M165G)
TMSG_SANDBOX_NC_REWARDINFO_RSP =M(M166G)
TMSG_SANDBOX_NC_TEAMBATTLE_REQ =M(M181G)
TMSG_SANDBOX_NC_TEAMBATTLE_RSP =M(M182G)
TMSG_SANDBOX_REINFORCEBACK_REQ =M(M87G)
TMSG_SANDBOX_REINFORCEBACK_RSP =M(M88G)
TMSG_SANDBOX_REINFORCELIST_REQ =M(M82G)
TMSG_SANDBOX_REINFORCELIST_RSP =M(M83G)
TMSG_SANDBOX_REINFORCE_REQ =M(M84G)
TMSG_SANDBOX_REINFORCE_RSP =M(M86G)
TMSG_SANDBOX_RESOURCE_EXPLORATE_REQ =M(M145G)
TMSG_SANDBOX_RESOURCE_EXPLORATE_RSP =M(M146G)
TMSG_SANDBOX_ROLECITY_NTF =M(M5G)
TMSG_SANDBOX_SAVE_TEAM_REQ =M(M49G)
TMSG_SANDBOX_SAVE_TEAM_RSP =M(M51G)
TMSG_SANDBOX_SEARCH_REQ =M(M76G)
TMSG_SANDBOX_SEARCH_RSP =M(M77G)
TMSG_SANDBOX_SIEGECAMP_REINFORCELIST_REQ =M(M33G)
TMSG_SANDBOX_SIEGECAMP_REINFORCELIST_RSP =M(M34G)
TMSG_SANDBOX_SPECIAL_EFFECT_NTF =M(M198G)
TMSG_SANDBOX_TEAMINFO_NTF =M(M35G)
TMSG_SANDBOX_UPVOTE_NTF =M(M132G)
TMSG_SANDBOX_VISUAL_ENTER_REQ =M(M9G)
TMSG_SANDBOX_VISUAL_ENTER_RSP =M(M10G)
TMSG_SANDBOX_WONDERPOS_REQ =M(M126G)
TMSG_SANDBOX_WONDERPOS_RSP =M(M127G)
TMSG_SANDBOX__MASS_OPER_REQ =M(M94G)
TMSG_SANDBOX__MASS_OPER_RSP =M(M95G)
TMSG_STAMINA_OPER_REQ =M(M73G)
TMSG_STAMINA_OPER_RSP =M(M75G)
TNCPopupsInfo =M(M171G)
TNatureCityAttackHeroInfo =M(M163G)
TNatureCityCongressInfo =M(M178G)
TNatureCityInfo =M(M175G)
TNatureCityRankInfo =M(M37G)
TNatureCityTeamBattleInfo =M(M180G)
TOneMarkInfo =M(M78G)
TSandBoxLoot =M(M61G)
TSandBoxMemberInfo =M(M121G)
TSandBoxProp =M(M13G)
TSandBoxPropStr =M(M14G)
TSandboxEntity =M(M21G)
TSandboxLine =M(M18G)
TSandboxLineDot =M(M15G)
TSandboxMarch =M(M24G)
TSandboxMarchMember =M(M16G)
TSandboxMassMember =M(M89G)
TSandboxMassTeam =M(M90G)
TSandboxPos =M(M1G)
TSandboxReinforceInfo =M(M32G)
TSandbox_AlertData =M(M111G)
TSandbox_AlertData_BeAtkCity =M(M102G)
TSandbox_AlertData_BeAtkResource =M(M106G)
TSandbox_AlertData_BeAtkResource_Detect =M(M109G)
TSandbox_AlertData_BeMass_March =M(M105G)
TSandbox_AlertData_BeMass_Wait =M(M104G)
TSandbox_AlertData_Detect =M(M108G)
TSandbox_AlertData_Wonder =M(M107G)
TSandbox_AlertData_ZombieApocalypse =M(M110G)
enKastenBoxState_Received = 1
enKastenBoxState_UnReceived = 0
enSXCondType_BuildLevel = 2
enSXCondType_MonFirstKill = 1
enSXEntityPropStr_AcornPub_RobPlayer = 7
enSXEntityPropStr_AllianceName = 2
enSXEntityPropStr_AllianceShortName = 3
enSXEntityPropStr_Carriage_Lineup = 5
enSXEntityPropStr_Carriage_Sites = 4
enSXEntityPropStr_Carriage_TakeItems = 6
enSXEntityPropStr_FaceStr = 13
enSXEntityPropStr_MonsterTroop = 10
enSXEntityPropStr_RoleName = 1
enSXEntityPropStr_ZoneBattleDuel_FaceStr = 14
enSXEntityProp_AcornPub_DoneTime = 97
enSXEntityProp_AcornPub_HelpDbid = 98
enSXEntityProp_AcornPub_TaskID = 96
enSXEntityProp_AcornPub_TaskSID = 99
enSXEntityProp_AllianceBoss_StateTime = 94
enSXEntityProp_AllianceBoss_TotalDamage = 95
enSXEntityProp_AllianceID = 2
enSXEntityProp_AllianceTrain_AllianceFlag = 146
enSXEntityProp_AllianceTrain_ArrivalTime = 144
enSXEntityProp_AllianceTrain_LineID = 145
enSXEntityProp_AllianceTrain_LootCount = 143
enSXEntityProp_AllianceTrain_TrainID = 142
enSXEntityProp_AllianceTrain_TrainType = 141
enSXEntityProp_AllianceTrain_Visiable = 147
enSXEntityProp_Appearance_Max = 20
enSXEntityProp_Base_BoolBit = 10
enSXEntityProp_Base_CITY_WALL_LEVEL = 73
enSXEntityProp_Base_CUR_DEFEND = 133
enSXEntityProp_Base_CongressOfficial = 136
enSXEntityProp_Base_EffectID = 8
enSXEntityProp_Base_KillNum = 128
enSXEntityProp_Base_Lv = 127
enSXEntityProp_Base_Misc_Flag = 131
enSXEntityProp_Base_Power = 129
enSXEntityProp_Base_Safety = 71
enSXEntityProp_Base_SkinID = 1
enSXEntityProp_Base_State = 126
enSXEntityProp_Base_WALL_BEGINTIME = 135
enSXEntityProp_Base_WALL_FIRE_ENDTIME = 70
enSXEntityProp_Base_WonderTime = 134
enSXEntityProp_Base_WorldId = 132
enSXEntityProp_BeCover = 38
enSXEntityProp_BeMassCount = 39
enSXEntityProp_Carriage_Interitry = 82
enSXEntityProp_Carriage_LineID = 86
enSXEntityProp_Carriage_LootTimes = 84
enSXEntityProp_Carriage_NxtSiteTm = 83
enSXEntityProp_Carriage_OwnerType = 78
enSXEntityProp_Carriage_Quality = 79
enSXEntityProp_Carriage_TradeID = 80
enSXEntityProp_Carriage_TradeTm = 81
enSXEntityProp_Carriage_TriggerID = 87
enSXEntityProp_Carriage_Visiable = 85
enSXEntityProp_Cfg_Id = 4
enSXEntityProp_CreateTime = 34
enSXEntityProp_CreatorRoleID = 74
enSXEntityProp_CrossServerType = 125
enSXEntityProp_DesertStorm_AllPlunderScore = 162
enSXEntityProp_DesertStorm_AllianceFlag = 157
enSXEntityProp_DesertStorm_BelongAllianceId = 149
enSXEntityProp_DesertStorm_BelongRoleFrameId = 151
enSXEntityProp_DesertStorm_BelongRoleId = 150
enSXEntityProp_DesertStorm_Camp = 160
enSXEntityProp_DesertStorm_CurBuildingTeamIndex = 161
enSXEntityProp_DesertStorm_FirstOccupierHp = 153
enSXEntityProp_DesertStorm_IsFirstOccupy = 152
enSXEntityProp_DesertStorm_MoveCityCD = 158
enSXEntityProp_DesertStorm_OilWellChangeNum = 156
enSXEntityProp_DesertStorm_OilWellChangeTime = 155
enSXEntityProp_DesertStorm_RoleCityHP = 159
enSXEntityProp_DesertStorm_ScoreBoxScoreNum = 154
enSXEntityProp_DesertStorm_UnlockTime = 148
enSXEntityProp_Detect_Food = 56
enSXEntityProp_Detect_Gold = 54
enSXEntityProp_Detect_Iron = 55
enSXEntityProp_Detect_Time1 = 52
enSXEntityProp_Detect_Time2 = 53
enSXEntityProp_FaceID = 31
enSXEntityProp_Fixed_Countdown = 137
enSXEntityProp_Fixed_DayMRewardCnt = 139
enSXEntityProp_Fixed_DaySRewardCnt = 140
enSXEntityProp_Fixed_MaxKIllLv = 138
enSXEntityProp_FrameID = 32
enSXEntityProp_GeneralTrial_BelongId = 92
enSXEntityProp_General_Association_CfgID = 9
enSXEntityProp_IsRadarCreate = 75
enSXEntityProp_Is_Only_Visible_To_Self = 72
enSXEntityProp_LineEntityId = 7
enSXEntityProp_March_AllianceTrainID = 67
enSXEntityProp_March_BattleState = 64
enSXEntityProp_March_CarriageID = 66
enSXEntityProp_March_FollowLineSid = 63
enSXEntityProp_March_LastTargetId = 68
enSXEntityProp_March_TargetDBId = 65
enSXEntityProp_March_TargetId = 5
enSXEntityProp_March_TargetName = 62
enSXEntityProp_March_WonderID = 61
enSXEntityProp_MultiplierRewardNumber = 93
enSXEntityProp_NC_AbandonCD = 117
enSXEntityProp_NC_AllianceIconID = 115
enSXEntityProp_NC_AllianceList = 11
enSXEntityProp_NC_CongressInfo = 12
enSXEntityProp_NC_DW_EndTime = 111
enSXEntityProp_NC_OfflineAttackCD = 116
enSXEntityProp_NC_OfflineAttackTime = 118
enSXEntityProp_NC_ProtectCD = 112
enSXEntityProp_Neutral_AllianceIdx = 110
enSXEntityProp_Neutral_Durability = 108
enSXEntityProp_Neutral_IsFirst = 109
enSXEntityProp_Neutral_SoldierNum = 107
enSXEntityProp_PlateID = 11
enSXEntityProp_PlayerLevel = 33
enSXEntityProp_RadarMissionId = 76
enSXEntityProp_RadarMission_TaskId = 104
enSXEntityProp_Resource_BeginTime = 22
enSXEntityProp_Resource_EndTime = 23
enSXEntityProp_Resource_EstimatedNum = 25
enSXEntityProp_Resource_EstimatedTime = 24
enSXEntityProp_Resource_Load = 27
enSXEntityProp_Resource_Num = 21
enSXEntityProp_Resource_Speed = 26
enSXEntityProp_RewardNumber = 77
enSXEntityProp_RoleID = 6
enSXEntityProp_ServerID = 3
enSXEntityProp_SiegeCamp_CityPos = 15
enSXEntityProp_SiegeCamp_Durability = 211
enSXEntityProp_SiegeCamp_Reinforcements_Count = 210
enSXEntityProp_Treasure_Digger_CostTime = 105
enSXEntityProp_Treasure_Digger_RoleNums = 103
enSXEntityProp_Treasure_Progress_Now = 102
enSXEntityProp_Treasure_Progress_Sum = 101
enSXEntityProp_ViewID = 37
enSXEntityProp_WORLDBOSS_ACHIEVE = 88
enSXEntityProp_WORLDBOSS_BORNTM = 90
enSXEntityProp_WORLDBOSS_CHALLENGES = 89
enSXEntityProp_WORLDBOSS_DEATHTM = 91
enSXEntityProp_Wonder_HP = 51
enSXEntityProp_Wonder_LineID = 58
enSXEntityProp_Wonder_Retaliate = 57
enSXEntityProp_Wonder_TriggerID = 59
enSXEntityProp_ZombieApocalypse_BelongRoleId = 218
enSXEntityProp_ZombieApocalypse_DefensiveWave = 216
enSXEntityProp_ZombieApocalypse_DifficultyLevel = 212
enSXEntityProp_ZombieApocalypse_LineID = 219
enSXEntityProp_ZombieApocalypse_LineType = 217
enSXEntityProp_ZombieApocalypse_State = 213
enSXEntityProp_ZombieApocalypse_Time = 215
enSXEntityProp_ZombieApocalypse_TriggerID = 220
enSXEntityProp_ZombieApocalypse_Wave = 214
enSXEntityProp_ZombieOwner = 120
enSXEntityProp_ZombieType = 121
enSXEntityProp_ZoneBattleDuel_AtkWorld_Score = 204
enSXEntityProp_ZoneBattleDuel_DefWorld_Score = 205
enSXEntityProp_ZoneBattleDuel_FaceID = 202
enSXEntityProp_ZoneBattleDuel_FrameID = 203
enSXEntityProp_ZoneBattleDuel_Giant_Occupy_World = 200
enSXEntityProp_ZoneBattleDuel_Occupy_AllianceShort = 16
enSXEntityProp_ZoneBattleDuel_Occupy_World_OverTime = 206
enSXEntityProp_ZoneBattleDuel_Occupy_World_Score = 201
enSXKastenbox_RadarTreasure = 1
enSXKastenbox_SiegeTreasure = 2
enSXKastenbox_ZombieApocalypse = 3
enSXMassOperType_Create = 1
enSXMassOperType_Del = 2
enSXMassOperType_Exit = 4
enSXMassOperType_Invalid = 0
enSXMassOperType_Join = 3
enSXMassOperType_Kict = 5
enSXMassOperType_StateChange = 6
enSXMassOperType_StateMassTimeOut = 7
enSXMassStateType_Go = 3
enSXMassStateType_Invalid = 0
enSXMassStateType_Team = 1
enSXMassStateType_Wait = 2
enSXNCPOType_CancelHorn = 6
enSXNCPOType_Horn = 2
enSXNCPOType_LoseOccupy = 5
enSXNCPOType_MoveCity = 1
enSXNCPOType_Occupy = 3
enSXNCPOType_Reward = 4
enSandboxAreaAttr_NotMove = 2
enSandboxAreaAttr_Safe = 1
enSandboxAreaAttr_Stain = 3
enSandboxBaseBoolBit_ZombieApocalyse = 1
enSandboxCrossMoveType_AllianceDuel_Battle = 4
enSandboxCrossMoveType_CrossAllianceMove = 7
enSandboxCrossMoveType_FreeCrossAllianceMove = 6
enSandboxCrossMoveType_Invalid = 0
enSandboxCrossMoveType_SystemKictOut = 1
enSandboxCrossMoveType_TestPosMove = 2
enSandboxCrossMoveType_ZoneDuel_Battle = 5
enSandboxEntityCover_Allow = 1
enSandboxEntityCover_None = 0
enSandboxEntityCover_NotAllow = 2
enSandboxEntitySort_DesertStromEntity = 8
enSandboxEntitySort_DiggingTreasures = 6
enSandboxEntitySort_Invalid = 0
enSandboxEntitySort_Monster = 3
enSandboxEntitySort_NeutralCity = 2
enSandboxEntitySort_Resource = 5
enSandboxEntitySort_RoleCity = 1
enSandboxEntitySort_SandkastenBox = 7
enSandboxEntity_AllianceTrain = 17
enSandboxEntity_Base = 1
enSandboxEntity_Carriage = 15
enSandboxEntity_CommonMonster = 16
enSandboxEntity_DesertStromEntity = 18
enSandboxEntity_FixedMonster = 3
enSandboxEntity_GeneralEntity = 19
enSandboxEntity_Invalid = 0
enSandboxEntity_KastenBox = 14
enSandboxEntity_March = 6
enSandboxEntity_NeutralCity = 2
enSandboxEntity_RadarBeastInvasion = 8
enSandboxEntity_RadarChallenge = 11
enSandboxEntity_RadarCollection = 10
enSandboxEntity_RadarDemonCastle = 7
enSandboxEntity_RadarEnvironmentExplorate = 9
enSandboxEntity_RadarTreasure = 12
enSandboxEntity_Relative = 100
enSandboxEntity_Resource = 5
enSandboxEntity_WonderMonster = 4
enSandboxEntity_WorldBoss = 13
enSandboxEntity_ZombieApocalypse = 20
enSandboxLineType_AcornPub = 105
enSandboxLineType_AllianceTrain = 202
enSandboxLineType_AtkBoss = 6
enSandboxLineType_AtkCity = 7
enSandboxLineType_AtkMinMon = 5
enSandboxLineType_CarriageMarch = 201
enSandboxLineType_Collect = 1
enSandboxLineType_Congress = 8
enSandboxLineType_DesertAloneAtkBuild = 11
enSandboxLineType_DesertAloneAtkPlay = 10
enSandboxLineType_DesertCollect = 14
enSandboxLineType_DesertMassAtkBuild = 12
enSandboxLineType_DesertMassAtkPlay = 13
enSandboxLineType_Invalid = 0
enSandboxLineType_MassBoss = 22
enSandboxLineType_MassCity = 25
enSandboxLineType_MassCongress = 26
enSandboxLineType_MassLMBoss = 24
enSandboxLineType_MassPlay = 21
enSandboxLineType_MassWonderMon = 23
enSandboxLineType_Max = 305
enSandboxLineType_NoTeamMarch = 101
enSandboxLineType_RadarExplorate = 103
enSandboxLineType_RadarHelpAlliance = 104
enSandboxLineType_Reinforce = 3
enSandboxLineType_SiegeCamp = 33
enSandboxLineType_Spy = 2
enSandboxLineType_Troop = 4
enSandboxLineType_WonderMonster = 100
enSandboxLineType_ZombieApocalypse = 301
enSandboxLineType_ZombieApocalypseMutantTruck = 304
enSandboxLineType_ZombieApocalypsePoison = 302
enSandboxLineType_ZombieApocalypseTruck = 303
enSandboxLineType_ZombieGold = 31
enSandboxLineType_ZombieMonsterLeader = 32
enSandboxLineType_ZoneBattleDuel_BigGun = 29
enSandboxLineType_ZoneBattleDuel_Congress = 27
enSandboxLineType_ZoneBattleDuel_MassBigGun = 30
enSandboxLineType_ZoneBattleDuel_MassCongress = 28
enSandboxMarchBattleState_Battleing = 2
enSandboxMarchBattleState_Lose = 4
enSandboxMarchBattleState_NoBattle = 0
enSandboxMarchBattleState_WaitBattle = 1
enSandboxMarchBattleState_Win = 3
enSandboxMarchSpeedUp_AtkCity = 4
enSandboxMarchSpeedUp_AtkRole = 3
enSandboxMarchSpeedUp_Collect = 2
enSandboxMarchSpeedUp_Invalid = 0
enSandboxMarchSpeedUp_Monster = 1
enSandboxMarchSpeedUp_Reinforce = 5
enSandboxMaxEntity_Type = 21
enSandboxProp_DesertStrom_BattleTime = 1001
enSandboxProp_DesertStrom_MatchId = 1003
enSandboxProp_DesertStrom_ResultTime = 1002
enSandboxReinforceType_Invalid = 0
enSandboxReinforceType_SiegeCamp = 3
enSandboxReinforceType_ZoneBattleDuel_BigGun = 2
enSandboxReinforceType_ZoneBattleDuel_Congress = 1
enSandboxSearchType_Invalid = 0
enSandboxSearchType_Monster_Food = 2
enSandboxSearchType_Monster_Gold = 3
enSandboxSearchType_Monster_Iron = 1
enSandboxSearchType_Monster_JY = 4
enSandboxSearchType_Monster_MassHuodong = 5
enSandboxSearchType_Monster_Zombie_Gold = 6
enSandboxSearchType_Resource_Food = 102
enSandboxSearchType_Resource_Gold = 103
enSandboxSearchType_Resource_Iron = 101
enSandboxType_Desert = 1
enSandboxType_Max = 2
enSandboxType_World = 0
enSandbox_AlertListType_Activity = 1
enSandbox_AlertListType_Normal = 0
enSandbox_AlertType_BeAtkCity = 1
enSandbox_AlertType_BeAtkResource = 4
enSandbox_AlertType_BeAtkResource_Detect = 7
enSandbox_AlertType_BeMass_March = 3
enSandbox_AlertType_BeMass_Wait = 2
enSandbox_AlertType_Detect = 6
enSandbox_AlertType_None = 0
enSandbox_AlertType_Wonder = 5
enSandbox_AlertType_ZombieApocalypse = 8
enStaminaOperType_BuyRecovery = 2
enStaminaOperType_FreeGet = 1
enStaminaOperType_UsrItem = 3

