local print = print
local require = require
local pairs = pairs
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local util = require "util"
local os = require "os"
local gw_event_activity_define = require "gw_event_activity_define"
local log = require "log"
local gw_const = require "gw_const"
local gw_hero_data = require "gw_hero_data"
local event_mini_game_define = require "event_mini_game_define"
local gw_recharge_mgr = require "gw_recharge_mgr"
local red_const = require "red_const"
local red_system = require "red_system"
local gw_firstrecharge_mgr = require "gw_firstrecharge_mgr"
local ui_window_mgr = require "ui_window_mgr"
local player_mgr = require "player_mgr"
local festival_activity_mgr = require "festival_activity_mgr"
local activity_pb = require "activity_pb"
local game_scheme = require "game_scheme"
local net = require "net"
local event = require "event"
local event_task_define = require "event_task_define"
local hero_first_charge_data = require "hero_first_charge_data"
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
module("hero_first_charge_mgr")

function Init()
    event.Register(event_task_define.REFRESH_TASK, TaskPointUpdate) --任务刷新,红点注册
    event.Register(event.FUNCTION_OPEN_INIT_FINISH, InitFinishFun)
    event.Register(event.LOGIN_UI_POPUP_END, LoginPopEnd)
    event.Register(event.USER_DATA_RESET, Clear)

    event.Register(event.SERVER_CROSS_DAY, ServerCrossDayHandle) --跨天事件
    event.Register(event.UPDATE_GOODS_NUM_CHANGE, GoodsChangeFun)   --道具补充监听
    event.Register(event_mini_game_define.UPDATE_EVENT_WAIT_TO_GET_REWARD, MiniGameWaitRewardUpdate)   --小游戏奖励更新了
    event.Register(event.NEW_XYX_PASS_LEVEL, MiniGamePassLevel)   --小游戏过关了
    
    
    red_system.RegisterRedFunc(red_const.Enum.HeroFirstChargeActivity, HeroFirstChargeActivityRedPoint)
    
    red_system.RegisterRedFunc(red_const.Enum.HeroFirstChallengePage, HeroFirstChallengePageRedPoint)
    red_system.RegisterRedFunc(red_const.Enum.HeroFirstNormalModel, HeroFirstNormalModelRedPoint)
    red_system.RegisterRedFunc(red_const.Enum.HeroFirstEndlessModel,HeroFirstEndlessModelRedPoint)
    
    red_system.RegisterRedFunc(red_const.Enum.HeroFirstNormalStartBtn, HeroFirstNormalStartBtnRedPoint)
    red_system.RegisterRedFunc(red_const.Enum.HeroFirstEndlessStartBtn, HeroFirstEndlessStartBtnRedPoint)
    
    red_system.RegisterRedFunc(red_const.Enum.HeroFirstRewardRed, HeroFirstRewardRedPoint)
    red_system.RegisterRedFunc(red_const.Enum.HeroFirstCultivateRewardRed, HeroFirstCultivateRewardRedPoint)
    red_system.RegisterRedFunc(red_const.Enum.HeroFirstEndlessRewardPage, HeroFirstEndlessRewardPageRedPoint)
    red_system.RegisterRedFunc(red_const.Enum.HeroFirstOneClickBtn, HeroFirstOneClickBtnRedPoint)
    
    red_system.RegisterRedFunc(red_const.Enum.HeroFirstCultivate, HeroFirstCultivateRedPoint)
    red_system.RegisterRedFunc(red_const.Enum.HeroFirstEveryDayBox, HeroFirstEveryDayBoxRedPoint)
    red_system.RegisterRedFunc(red_const.Enum.HeroFirstCultivateTaskRed, HeroFirstCultivateTaskRedPoint)
    red_system.RegisterRedFunc(red_const.Enum.HeroFirstUpgradeStar, HeroFirstUpgradeStarRedPoint)

    
end

---@public function functionOpen模块初始化完成
function InitFinishFun()
    --活动结束后，清除已领取的奖励
    if not IsHeroFirstChargeActivityOpen() then
        local roleID = player_mgr.GetPlayerRoleID()
        local playerPrefsKey = string.format("HeroMiniGameReceivedReward_%s", roleID)
        PlayerPrefs.DeleteKey(playerPrefsKey);
    end
end
---@public function 登录弹窗结束
function LoginPopEnd()
    --活动开始后增加拍脸图
    --if IsHeroFirstChargeActivityOpen() then
    --    local isLockEndless = hero_first_charge_data.GetEndlessIsLock()
    --    local windowName = "ui_hero_first_normal_popup"
    --    if isLockEndless then
    --        windowName = "ui_hero_first_endless_popup"
    --    end
    --    local gw_popups_data = require "gw_popups_data"
    --    gw_popups_data.AddMessage(windowName, function()
    --        local ui_window_mgr = require "ui_window_mgr"
    --        ui_window_mgr:ShowModule(windowName, nil, function()
    --            gw_popups_data.ShowNextMessage(0)
    --        end)
    --    end, gw_popups_data.PopupsType.Popups)
    --end
end

---@public function 打开活动小游戏
function OpenActivityMiniGame(isNormal, levelNum)
    hero_first_charge_data.InitMiniGameLevelIDArr()
    local minigame_mgr = require "minigame_mgr"
    if isNormal then
        local normalIDArr, normalControlIDArr = hero_first_charge_data.GetNormalLevelIDArr()
        minigame_mgr.OpenMiniGame_Campaign(levelNum, normalIDArr, normalControlIDArr)
    else
        local endlessIDArr, endlessControlIDArr = hero_first_charge_data.GetEndlessLevelIDArr()
        minigame_mgr.OpenMiniGame_Campaign(levelNum, endlessIDArr, endlessControlIDArr)
    end
end

---@public function 通过一关普通副本小游戏发送网络请求
---@param levelID number 关卡ID
function PassNormalMiniGameLevelReq(levelID)

    local gw_independent_game_const = require "gw_independent_game_const"
    local msg_pb = require "msg_pb"
    local msg = activity_pb.TMSG_XYX_PASS_LV_REQ()
    local miniGameControlCfg = game_scheme:MiniGameLevelControl_0(levelID, 0)
    msg.lvID = miniGameControlCfg.ID
    msg.LevelType = miniGameControlCfg.LevelID
    msg.unlockType = gw_independent_game_const.UnlockType
    msg.nGameType = miniGameControlCfg.LevelType
    msg.nCollectionID = 0
    --发送通过逻辑
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_XYX_PASS_LV_REQ, msg)
end

---@public function 开启小游戏时,发送战力验证网络请求
---@param heroIDArr number[] 英雄ID数组 (英雄配置ID)
---@param levelID number 关卡ID
function HeroPowerCheckReq(heroIDArr, levelID)
    local net_activity_module = require "net_activity_module"
    local sidArr = {}
    for _, v in ipairs(heroIDArr) do
        local heroEntity = gw_hero_data.GetHeroEntityByCfgId(v)
        if heroEntity then
            table.insert(sidArr, heroEntity:GetHeroSid())
        end
    end
    local msgData = {
        arrHeroIDs = sidArr,
        nStartStageID = levelID,
        nUnlockType = 0,
    }
    net_activity_module.MSG_XYX_ENDLESS_SET_HEROPOWER_REQ(msgData)
end

---@public function 活动是否开启
function IsHeroFirstChargeActivityOpen()
    local activityID = hero_first_charge_data.GetActivityID()
    local isOpen = festival_activity_mgr.GetIsOpenByActivityID(activityID, true)

    local moduleOpenPro_pb = require "moduleOpenPro_pb"
    local net_module_open = require "net_module_open"
    local isModuleOpen = net_module_open.CheckModuleOpen(moduleOpenPro_pb.emModuleID_MiniGameFirstRechargeHeroActivity)
    if not isModuleOpen then
        return false
    end
    
    return isOpen
end

---@public function 跳转到英雄首充礼包
function JumpHeroFirstChargeGift()
    --获取活动id,通过activityType获取当前处于激活状态的活动
    local festival_activity_cfg = require "festival_activity_cfg"
    local activity = festival_activity_mgr.GetActivityDataByActivityCodeType(festival_activity_cfg.ActivityCodeType.FirstRecharge)

    if activity and activity.activityID then
        local activityIDEx = festival_activity_mgr.GetSubActiveId(activity.activityID)
        local rechargeData = gw_firstrecharge_mgr.GetRechargeData(activityIDEx)
        if rechargeData and rechargeData.hasGet then
            --已经购买过，直接打开礼包
            ui_window_mgr:ShowModule("ui_hero_first_recharge_pop", nil, nil, { atyID = hero_first_charge_data.GetActivityID() })
            local reportMsg = {
                ActivityContent_id = 0 --养成成就
            }
            event.EventReport("SCFB_GiftClick", reportMsg)
        elseif rechargeData then
            ui_window_mgr:ShowModule("ui_first_recharge", nil, nil, { activityID = activity.activityID })
            
            local reportMsg = {
                ActivityContent_id = rechargeData.rechargeId --养成成就
            }
            event.EventReport("SCFB_GiftClick", reportMsg)
        end
    end
end

---@public function 打开英雄星级界面
function OpenHeroStarPanel(heroUIType)
    local heroEntity = gw_hero_data.GetHeroEntityByCfgId(hero_first_charge_data.GetFirstChargeHeroID())
    if heroEntity then
        ui_window_mgr:ShowModule("ui_gwhero_base", nil, nil, {
            heroCfgId = hero_first_charge_data.GetFirstChargeHeroID(),
            isUnLock = true,
            index = heroUIType or gw_const.HeroUIType.Star,
        })
    else
        local gw_hero_mgr = require "gw_hero_mgr"
        gw_hero_mgr.OpenHeroDetailUI(hero_first_charge_data.GetFirstChargeHeroID())
    end
end

--region 红点数据处理
---@public function 活动总红点
function HeroFirstChargeActivityRedPoint()
    local red = 0
    red = red + HeroFirstChallengePageRedPoint()
    red = red + HeroFirstCultivateRedPoint()
    return red
end
---@public function 挑战页签红点
function HeroFirstChallengePageRedPoint()
    local red = 0
    red = red + HeroFirstNormalModelRedPoint()
    return red
end
---@public function 普通模式切换按钮红点
function HeroFirstNormalModelRedPoint()
    local red = 0
    red = red + HeroFirstNormalStartBtnRedPoint()
    return red
end

---@public function 普通模式挑战按钮红点
function HeroFirstNormalStartBtnRedPoint()
    local red = 0
    local recordLevelID = GetChallengeNormalLevelID()
    local curLevelID = hero_first_charge_data.GetCurrentNormalLevel()
    if curLevelID > recordLevelID then
        red = 1
    end
    return red
end

---@public function 无尽模式切换按钮红点
function HeroFirstEndlessModelRedPoint()
    local red = 0
    red = red + HeroFirstEndlessStartBtnRedPoint()
    return red
end

---@public function 无尽模式挑战按钮红点
function HeroFirstEndlessStartBtnRedPoint()
    local red = 0
    local isLockEndless = hero_first_charge_data.GetEndlessIsLock()
    if isLockEndless then
        --获取记录时间
        local clickTime = GetClickEndlessTime()
        if util.IsAfterDay(clickTime, os.server_time()) then
            red = 1
        end
    end
    return red
end

---@public function 英雄养成页签红点
function HeroFirstCultivateRedPoint()
    local red = 0
    red = red + HeroFirstEveryDayBoxRedPoint()
    red = red + HeroFirstUpgradeStarRedPoint()

    local taskList = hero_first_charge_data.GetHeroCultivateTaskIDArr()    --雷达数据 获取全部雷达sid
    --遍历全部任务 ,累计红点
    for _, v in pairs(taskList) do
        local result = red_system.GetBuildFunRed(red_const.Enum.HeroFirstCultivateTaskRed, v)
        red = red + result[1]
    end
    
    return red
end
---@public function 每日宝箱红点
function HeroFirstEveryDayBoxRedPoint()
    local red = 0
    local freeRechargeID = hero_first_charge_data.GetEveryDayGiftRechargeID()
    if freeRechargeID then
        local isCanReceive = gw_recharge_mgr.GetRechargeBuyCount(freeRechargeID) == 0
        if isCanReceive then
            red = 1
        end
    end
    return red
end

---@public function 英雄养成任务红点
function HeroFirstCultivateTaskRedPoint(taskID)
    local red = 0
    local gw_task_data = require "gw_task_data"
    local taskData = gw_task_data.GetTaskData(taskID)
    if taskData then
        local taskCfg = game_scheme:TaskMain_0(taskID)
        local targetRate = taskCfg.ConditionValue1 --目标进度
        if taskData.status then
            red = 0
        else
            if taskData.rate >= targetRate then
                --已完成
                red = 1
            else
                red = 0
            end
        end
    end
    return red
end

---@public function 升星跳转按钮红点
function HeroFirstUpgradeStarRedPoint()
    local red = 0
    local isCanUpgradeStar = hero_first_charge_data.IsHeroCanUpgradeStar()
    if isCanUpgradeStar then
        red = 1
    end
    return red
end

---@public function 奖励按钮红点
function HeroFirstRewardRedPoint()
    local red = 0
    red = red + HeroFirstEndlessRewardPageRedPoint()
    red = red + HeroFirstCultivateRewardRedPoint()
    return red
end
---@public function 英雄养成任务奖励预览红点
function HeroFirstCultivateRewardRedPoint()
    local red = 0
    local taskList = hero_first_charge_data.GetHeroCultivateTaskIDArr()    --雷达数据 获取全部雷达sid
    --遍历全部任务 ,累计红点
    for _, v in pairs(taskList) do
        local result = red_system.GetBuildFunRed(red_const.Enum.HeroFirstCultivateTaskRed, v)
        red = red + result[1]
    end
    return red
end

---@public function 奖励无尽页签红点
function HeroFirstEndlessRewardPageRedPoint()
    local red = 0
    red = red + HeroFirstOneClickBtnRedPoint()
    return red
end
---@public function 一键领取按钮红点
function HeroFirstOneClickBtnRedPoint()
    local red = 0
    local gw_independent_game_data = require "gw_independent_game_data"
    local rewardData = gw_independent_game_data.GetWaitToGetRewardInfo()
    if rewardData then
        for _, v in ipairs(rewardData) do
            red = 1
            break
        end
    end
    return red
end



--endregion 

--region 红点监听事件
---@public function 跨天事件
function ServerCrossDayHandle()
    --跨天后，刷新每日宝箱红点
    red_system.TriggerRed(red_const.Enum.HeroFirstEveryDayBox)
    red_system.TriggerRed(red_const.Enum.HeroFirstEndlessStartBtn)
    red_system.TriggerRed(red_const.Enum.HeroFirstNormalStartBtn)
    event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE,hero_first_charge_data.GetActivityID())
end

---@public function 碎片变化时触发升星红点
function GoodsChangeFun(e, id, sid, num)
    local CompositeID, UniversalShardID = hero_first_charge_data.GetFirstChargeHeroDebrisID()
    if id == CompositeID or id == UniversalShardID then
        red_system.TriggerRed(red_const.Enum.HeroFirstUpgradeStar)
        event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE,hero_first_charge_data.GetActivityID())
    end
end

---@public function 小游戏等待领取奖励更新
function MiniGameWaitRewardUpdate()
    red_system.TriggerRed(red_const.Enum.HeroFirstOneClickBtn)
    event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE,hero_first_charge_data.GetActivityID())
end

---@public function 任务刷新，更新红点
function TaskPointUpdate(eventName, taskData, moduleId, moduleList)
    local gw_task_const = require "gw_task_const"
    if moduleList[gw_task_const.TaskModuleType.BPAchievementTask] then
        red_system.TriggerRed(red_const.Enum.HeroFirstCultivateTaskRed)
        event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE,hero_first_charge_data.GetActivityID())
    end
end

---@public function 小游戏过关红点
function MiniGamePassLevel(_,msg)
    local preLevelID = hero_first_charge_data.GetEndlessPreLevelID()
    --判断过的是否是无尽模式解锁的那一关
    if preLevelID and msg.lvID == preLevelID and msg.IsPass == 0 then
        red_system.TriggerRed(red_const.Enum.HeroFirstEndlessStartBtn)
        event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE,hero_first_charge_data.GetActivityID())
    end
    if msg.IsPass == 0 then
        red_system.TriggerRed(red_const.Enum.HeroFirstNormalStartBtn)
    end
end
--endregion

---@public function 记录集结气泡时间
function RecordClickEndlessTime()
    local roleID = player_mgr.GetPlayerRoleID()
    local key = string.format("%s_%s", "ClickEndlessTime_", roleID)
    PlayerPrefs.SetInt(key, os.server_time())
end

---@public 获取气泡集结时间
function GetClickEndlessTime()
    local roleID = player_mgr.GetPlayerRoleID()
    local key = string.format("%s_%s", "ClickEndlessTime_", roleID)
    local time = PlayerPrefs.GetInt(key, 0)
    return time
end

---@public function 记录挑战普通模式时间
function RecordChallengeNormalLevelID(levelID)
    local roleID = player_mgr.GetPlayerRoleID()
    local key = string.format("%s_%s", "ChallengeNormalLevelID_", roleID)
    PlayerPrefs.SetInt(key, levelID)
end

---@public function 获取挑战普通模式时间
function GetChallengeNormalLevelID()
    local roleID = player_mgr.GetPlayerRoleID()
    local key = string.format("%s_%s", "ChallengeNormalLevelID_", roleID)
    local levelID = PlayerPrefs.GetInt(key, 0)
    return levelID
end

function PassMiniGameLevel(levelID)
    local hero_first_charge_mgr = require "hero_first_charge_mgr"
    hero_first_charge_mgr.HeroPowerCheckReq({102,104,105},levelID)
    
    util.DelayCallOnce(0.2,function()
        hero_first_charge_mgr.PassNormalMiniGameLevelReq(levelID)
    end)
end

function Clear()
    hero_first_charge_data.Clear()
end