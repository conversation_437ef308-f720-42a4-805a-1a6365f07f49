local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local game_scheme = require "game_scheme"
local festival_activity_mgr = require "festival_activity_mgr"
local land_revival_data = require "land_revival_data"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"

--region Controller Life
module("ui_land_revival_login_controller")
local controller = nil
local UIController = newClass("ui_land_revival_login_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = data
    self.__base.Init(self, view_name, controller_name)    
    self:GetActivityEndTime()
    self:GetVipBoxRewardData()
    self:GetActivityCommonUIData()
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    if self.CData and self.CData.isAddMessage and self.isClickClose then
        local gw_popups_mgr = require("gw_popups_mgr")
        gw_popups_mgr.StopPop(self.CData.messageKey)
        self.isClickClose = false
    end
    
    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents() 
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic
function  UIController:OnBtnCloseBtnClickedProxy()
	ui_window_mgr:UnloadModule(self.view_name)
end
function  UIController:OnBtnGoToClickedProxy()
    self.isClickClose = true
    --直接去打开对应的入口    
    festival_activity_mgr.OpenActivityUIByActivityID(land_revival_data.GetActivityID())
    ui_window_mgr:UnloadModule(self.view_name)
end

---@public function 获取活动结束时间
function UIController:GetActivityEndTime()
    local activityData = land_revival_data.GetActivityData()
    if activityData then
        local endTime = activityData.endTimeStamp
        self:TriggerUIEvent("SetActivityTimer", endTime)
    end
end

---@public function 获取vip任务宝箱奖励
function UIController:GetVipBoxRewardData()
    self:TriggerUIEvent("InitBoxRewardShow", land_revival_data.GetVipTaskMergeReward())
end

function UIController:GetActivityCommonUIData()
    local cfg = game_scheme:ActivityCommonUI_0(land_revival_data.GetActivityID())
    if cfg then

        --local CustomParam3Data = {}
        --for part in string.gmatch(cfg.CustomParam3, "[^#]+") do
        --    table.insert(CustomParam3Data, part)
        --end

        local CustomParam4Data = {}
        for part in string.gmatch(cfg.CustomParam4, "[^#]+") do
            table.insert(CustomParam4Data, part)
        end
        
        local viewData = {
            --itemID1 =CustomParam3Data[1],
            --itemID2 = CustomParam3Data[2],
            tipLang1 = CustomParam4Data[1],
            tipLang2 = CustomParam4Data[2],
        }
        self:TriggerUIEvent("SetActivityCommonUIData", viewData)
    end
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
