--@region FileHead
-- ui_bubble_tip.txt ---------------------------------
-- author:  罗华冠
-- date:    2022/7/1 19:48:51
-- ver:     1.0
-- desc:    气泡提示弹窗
-------------------------------------------------


--@region Require
local require   = require

local Button        = CS.UnityEngine.UI.Button
local Image         = CS.UnityEngine.UI.Image

local class                 = require "class"
local ui_base               = require "ui_base"
local module_scroll_list    = require "scroll_list"
local net_route             = require "net_route"
local util                  = require "util"
local lang                  = require "lang"
local bubble_mgr            = require "bubble_mgr"
local LeanTween             = CS.LeanTween
local Canvas                = CS.UnityEngine.Canvas
local TextMeshProUGUI       = CS.TMPro.TextMeshProUGUI


--@region ModuleDeclare
module("ui_bubble_tip")
--local interface = require "iui_bubble_tip"
local window = nil
local UIBubbleTip = {}
local parent = nil
local bubbleData = {}

-- bubbletip表中增加关闭方式后，需要增加此枚举
CLOSE_TYPE = {
    dontClose = 0,
    autoClose = 1,
    clickClose = 2,
}


--@region WidgetTable
UIBubbleTip.widget_table = {
    bubble = {path = "bubble", type = "RectTransform"},
    bubbleCanvas = {path = "bubble", type = Canvas},
    bubbleText = {path = "bubble/bg/text", type = TextMeshProUGUI},
    closeBtn = {path = "btn", type = "Button",event_name = "closeEvent"},
    closeImage = {path = "btn", type = "Image"},
}


--@region WindowCtor
function UIBubbleTip:ctor(selfType)
	self.__base:ctor(selfType)
end

-- 播放气泡动画
function UIBubbleTip:BubbleGradientShowAni(sustainTime,showAniTime,closeType)
    local originScale = {x=0,y=0,z=0}
    local endScale = {x=1,y=1,z=1}
    local suTime = sustainTime/100
    local sTime = showAniTime/100
    self.showAniTime = sTime
    self.bubble.localScale = originScale
    self.bubble.gameObject:SetActive(true)

    if self and self.bubble.gameObject and not util.IsObjNull(self.bubble.gameObject) then
        LeanTween.scale(self.bubble.gameObject, endScale, sTime):setOnComplete(function()
            if closeType ~= CLOSE_TYPE.dontClose then
                if window and window:IsValid() then
                    util.DelayCallOnce(suTime,function ()
                        if closeType == CLOSE_TYPE.autoClose then
                            -- 自动关闭
                            if window and window:IsValid() and not util.IsObjNull(window.bubble) then
                                window:BubbleCloseAni()
                            end
                        elseif closeType == CLOSE_TYPE.clickClose then
                            -- 点击关闭
                            if window and window:IsValid() and not util.IsObjNull(window.closeImage) then
                                window.closeImage.enabled = true
                            end
                        end
                        -- 关闭动画
                    end)
                end
            end
        end)
    end
end

function UIBubbleTip:BubbleCloseAni()
    local originScale = {x=0,y=0,z=0}
    LeanTween.scale(self.bubble.gameObject, originScale, self.showAniTime):setOnComplete(function()
        bubble_mgr.CloseBubble()
    end)
end

--@region WindowInit
--[[窗口初始化]]
function UIBubbleTip:Init()
    self:SubscribeEvents()
    net_route.RegisterMsgHandlers(MessageTable)
end 



--@region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIBubbleTip:OnShow()
    self:SetUIState()
    self.bubbleCanvas.sortingOrder = self.curOrder + 1
end

function UIBubbleTip:UpdateUI()
    self.bubbleCanvas.sortingOrder = self.curOrder + 1
end

function UIBubbleTip:SetUIState()
    if bubbleData then
        self:SetBubbleText(bubbleData.langid)
        local sustainTime = bubbleData.bubbleData.data[0] -- 气泡持续时间
        local showAniTime = bubbleData.bubbleData.data[1] -- 气泡显示动画时间
        local autoClose = bubbleData.bubbleData.data[2]   -- 气泡是否自动关闭
        self:BubbleGradientShowAni(sustainTime,showAniTime,autoClose)
   end
end

function UIBubbleTip:SetBubbleText(langID)
    self.bubbleText.text = lang.Get(langID)
end

--@region WindowOnHide
--[[界面隐藏时调用]]
function UIBubbleTip:OnHide()


end



--@region WindowSetInputParam
--[[设置窗口的输入参数。该参数通常是由其它模块或者外部设置进来。需要注意的是，
当调用这个函数的时候，窗口资源可能还是没有加载完成的。
@param p 参数表
]]
function UIBubbleTip:SetInputParam(p)
	self.inputParam = p

    --如果正在显示，则更新一次窗口
    if self.UIRoot and self.UIRoot.activeSelf == true then
        self:UpdateUIPage()
    end
end



--@region WindowBuildUpdateData
--[[构建UI更新数据]]
function UIBubbleTip:BuildUpdateData()


end



--@region WindowUpdateUI
--[[资源加载完成，被显示的时候调用]]
function UIBubbleTip:UpdateUIPage()
	self:BuildUpdateData()


end



--@region WindowClose
function UIBubbleTip:Close()
    net_route.UnregisterMsgHandlers(MessageTable)

    if self:IsValid() then
		self:UnsubscribeEvents()
	end
	self.__base:Close()
    window = nil


end



--@region WindowSubscribeEvents
--[[订阅UI事件]]
function UIBubbleTip:SubscribeEvents()
    self.closeEvent = function ()
        self:BubbleCloseAni()
    end
end



--@region WindowUnsubscribeEvents
--[[退订UI事件]]
function UIBubbleTip:UnsubscribeEvents()


end



--@region WindowBtnFunctions


--@region ScrollItem


--@region WindowInherited

local CUIBubbleTip = class(ui_base, nil, UIBubbleTip)

--@region ModuleFunction
function Show()
    if window == nil then
        window = CUIBubbleTip()
        window._NAME = _NAME
        window:LoadUIResource("ui/prefabs/uibubbletip.prefab", nil, parent.transform, nil, nil, nil)
    end
    window:Show()
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        Hide()
        window:Close()
        window = nil
    end
end

function SetParent(_parent)
    parent = _parent
end

function SetBubbleData(data)
    bubbleData = data
end

--@region RegisterMsg
MessageTable =
{ --///<<< tableStart
} --///<<< tableEnd


