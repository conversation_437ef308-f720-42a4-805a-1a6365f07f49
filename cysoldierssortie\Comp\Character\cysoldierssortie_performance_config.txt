local PerformanceItemType = 
{
    MaxEnemyViewNum = "MaxEnemyViewNum", -- 最大敌人显示数量 （比如: 100 能同屏显示 100个敌人 该选项直接影响GPU 直观影响：耗电 发热 低端机卡顿）
    PostProcess = "PostProcess", --后处理 （比如 :true 开启bloom + tonemap + vignette. GPU影响大 CPU 影响中 直观影响: 低端机卡顿 耗电 发热)
    RealTimeShadow = "RealTimeShadow", --实时阴影 （比如 : true 士兵突击中 场景物体阴影是否开启. CPU 影响中 GPU 影响 中 带宽影响 大 直观影响: 耗电 发热)
    PlaneShadow = "PlaneShadow", -- 平面阴影 （比如 : 37 士兵突击中 角色距离相机多远有 阴影. CPU影响 低 GPU 影响 大. 直观影响: 发热 低端机卡顿)
    LevelUpEffectNum = "LevelUpEffectNum", --升级特效数量 升级特效同时存在的数量 (比如: 10 士兵突击中士兵一起升级 同时能显示的升级特效最大限制: CPU 峰值影响 大 GPU 影响 大， 直观影响: 突发卡顿)
    RenderScale = "RenderScale", --Render Scale 除UI 分辨率外 场景分辨率降低 （比如: 0.7 士兵突击中 非UI分辨率将 降低至0.7（默认最大是0.8）CPU 影响 无。 GPU 影响 极大 带宽影响 极大 直观影响: 耗电 发热)
    AsyncECSCallBackBatchLimit = "AsyncECSCallBackBatchLimit", --ecs 批处理回调一帧最大处理多少个 （ECS 模式下是否启动分帧 当第一次瞬间获得大量士兵 和子弹。 CPU 峰值影响 大 GPU 影响 小： 直观影响: 突发卡顿)
    UpdateSpeedRate = "UpdateSpeedRate", --各种高频Update速率 （各个高频 函数调用频率间隔 影响AI 决策更新 实际伤害结算 物理检测. 比如: 2 则所影响的功能检测时间*2 ） CPU 影响 极大 GPU 影响 无 直观影响 卡顿)
    PhysicDeltaTime = "PhysicDeltaTime", --物理回调间隔 与上述类似
    HUDText = "HUDText", --伤害飘字  (是否显示 飘字） CPU 影响 大 GPU 影响 小 直观影响 卡顿）
    BeHitEffect = "BeHitEffect", --受击特效 （是否显示受击特效） CPU 影响 低 GPU 影响 中 带宽影响 中 直观影响 耗电 卡顿 
    FeedCompEnable = "FeedCompEnable", --反馈组件启用（角色 受击打击反馈 是否关闭, CPU 影响低 GPU 影响低 直观影响 略微卡顿)
    TypeCount = 12 
}

local LevelType = 
{
    eLow = 1,
    eMiddle = 2,
    eHigh = 3,
}

local PerformanceConfig = 
{
    [PerformanceItemType.MaxEnemyViewNum] = 
    {
        [LevelType.eLow] = 100,
        [LevelType.eMiddle] = 100,
        [LevelType.eHigh] = 100
    },
    [PerformanceItemType.PostProcess] =
    {
        [LevelType.eLow] = true,
        [LevelType.eMiddle] = true,
        [LevelType.eHigh] = true,
    },
    [PerformanceItemType.RealTimeShadow] =
    {
        [LevelType.eLow] = false,
        [LevelType.eMiddle] = true,
        [LevelType.eHigh] = true,
    },
    [PerformanceItemType.PlaneShadow] =
    {
        [LevelType.eLow] = 0,
        [LevelType.eMiddle] = 37,
        [LevelType.eHigh] = 37,
    },
    [PerformanceItemType.LevelUpEffectNum] =
    {
        [LevelType.eLow] = 1,
        [LevelType.eMiddle] = 10,
        [LevelType.eHigh] = 10,
    },
    [PerformanceItemType.RenderScale] =
    {
        [LevelType.eLow] = 0.7,
        [LevelType.eMiddle] = 0.8,
        [LevelType.eHigh] = 0.8,
    },
    [PerformanceItemType.AsyncECSCallBackBatchLimit] =
    {
        [LevelType.eLow] = 5,
        [LevelType.eMiddle] = 30,
        [LevelType.eHigh] = 30,
    },
    [PerformanceItemType.UpdateSpeedRate] =
    {
        [LevelType.eLow] = 4,
        [LevelType.eMiddle] = 1,
        [LevelType.eHigh] = 1,
    },
    [PerformanceItemType.PhysicDeltaTime] =
    {
        [LevelType.eLow] = 0.06,
        [LevelType.eMiddle] = 0.04,
        [LevelType.eHigh] = 0.04,
    },
    [PerformanceItemType.HUDText] =
    {
        [LevelType.eLow] = false,
        [LevelType.eMiddle] = true,
        [LevelType.eHigh] = true,
    },
    [PerformanceItemType.BeHitEffect] =
    {
        [LevelType.eLow] = true,
        [LevelType.eMiddle] = true,
        [LevelType.eHigh] = true,
    },
    [PerformanceItemType.FeedCompEnable] =
    {
        [LevelType.eLow] = true,
        [LevelType.eMiddle] = true,
        [LevelType.eHigh] = true,
    },
}

return 
{
    PerformanceItemType = PerformanceItemType,
    PerformanceConfig = PerformanceConfig,
}