--- Created by fgy.
--- DateTime: 2024/10/9 by connan
--- Des:特效管理
--- 控制特效的层级展示
--- 控制特效的加载和销毁    

local require = require

local gw_admin = require "gw_admin"
local gw_sand_effect_helper = require "gw_sand_effect_helper"
local gw_sand_effect_param = require "gw_sand_effect_param"
local gw_effect_object = require "gw_effect_object"
local gw_sand_effect_const = require "gw_sand_effect_const"
local EffectKey = gw_sand_effect_const.Effect

---@class GWSandEffectMgr  特效管理
local GWSandEffectMgr = {}
local mc = {}
local effectShowLevel = 6

--- 构造器
function GWSandEffectMgr.InitSandScene()
    mc.enterSandScene = true
    mc.uniqueId = 0
    mc.effectEntityList = {}
    mc.effectUniqueIDList = {}
    mc.effectTypeList = {}
    mc.effectHelper = gw_sand_effect_helper.new()
    mc.effectHelper:Init()
end

--- 棄用
function GWSandEffectMgr.Dispose()
    if mc.effectHelper then
        mc.effectHelper:Dispose()
    end

    if mc.effectEntityList then
        for k, v in pairs(mc.effectEntityList) do
            if v then
                v:Dispose()
            end
        end
    end
    mc = {}
end

function GWSandEffectMgr.GetShowEffectViewLevel()
    return effectShowLevel
end

---@public 创建特效
---@param key string 配置再gw_sand_effect_const
---@param parent userdata 父物体
---@param effectBaseParam table 特效的基础属性参数
---@param effectParam table 特效的配置参数
---@param callback function 回调函数
function GWSandEffectMgr.CreateEffects(key, effectBaseParam, effectParam, parent, callback)
    if not mc.enterSandScene then
        return
    end

    if not EffectKey[key] then
        gw_admin.SwitchUtility.Error("未再gw_sand_effect_const配置 effect key error:" .. key)
        return
    end

    local id = GWSandEffectMgr.CreateUniqueID(key)
    local newEffectParams = gw_sand_effect_param.NewEffectParam(key, effectParam)
    local newEffectBaseParams = gw_sand_effect_param.NewEffectBaseParam(key, effectBaseParam)
    local effectParent = parent or GWSandEffectMgr.GetEffectLayerParent(newEffectBaseParams.layer)
    local entity = gw_effect_object.CM("gw_effect_object"):Init(id, effectParent, newEffectParams, newEffectBaseParams, callback)

    mc.effectEntityList[id] = entity
    if not mc.effectUniqueIDList[key] then
        mc.effectUniqueIDList[key] = {}
    end
    mc.effectTypeList[id] = key
    mc.effectUniqueIDList[key][id] = entity
    return id
end

function GWSandEffectMgr.GetEffectLayerParent(layer)
    local gw_sand_node = require "gw_sand_node"
    if layer == gw_sand_effect_const.EffectLayer.HighLayer then
        return gw_sand_node.effect3Node()
    elseif layer == gw_sand_effect_const.EffectLayer.LowLayer then
        return gw_sand_node.effect1Node()
    elseif layer == gw_sand_effect_const.EffectLayer.EcsLayer then
        return gw_sand_node.ecsEffect1Node()
    else
        return gw_sand_node.effect2Node()
    end
end

function GWSandEffectMgr.GetEffect(id)
    if not mc.enterSandScene then
        return
    end
    return mc.effectEntityList[id]
end

function GWSandEffectMgr.RemoveEffect(id)
    if not mc.enterSandScene then
        return
    end
    if mc.effectEntityList[id] then
        mc.effectEntityList[id]:Dispose()
        local key = mc.effectTypeList[id]
        if key and mc.effectUniqueIDList[key] then
            mc.effectUniqueIDList[key][id] = nil
        end
        mc.effectEntityList[id] = nil
    end
end

function GWSandEffectMgr.CallEffectFunc(id, funcName, ...)
    if not mc.enterSandScene then
        return
    end
    if mc.effectEntityList[id] then
        mc.effectEntityList[id].CallFuncName(mc.effectEntityList[id], funcName, ...)
        return true
    end
    return false
end

---@public 播放特效
---@param id string 特效id
---@param position userdata 位置
function GWSandEffectMgr.RePlayEffect(id, position)
    if not mc.enterSandScene then
        return
    end

    if not id then
        gw_admin.SwitchUtility.Error("PlayEffect id is nil:")
        return
    end
    GWSandEffectMgr.CallEffectFunc(id, "SetVisible", false)
    GWSandEffectMgr.CallEffectFunc(id, "SetVisible", true)
    if position then
        GWSandEffectMgr.CallEffectFunc(id, "SetLocalPosition", position.x, position.y, position.z)
    end
end

---@public 展示特效
---@param id string 特效id
---@param pos userdata 位置
function GWSandEffectMgr.ShowEffect(id)
    if not mc.enterSandScene then
        return
    end

    if not id then
        gw_admin.SwitchUtility.Error("HideEffect id is nil:")
        return
    end
    GWSandEffectMgr.CallEffectFunc(id, "SetVisible", true)
end

---@public 隐藏特效
---@param id string 特效id
---@param pos userdata 位置
function GWSandEffectMgr.HideEffect(id)
    if not mc.enterSandScene then
        return
    end
    if not id then
        gw_admin.SwitchUtility.Error("HideEffect id is nil:")
        return
    end
    GWSandEffectMgr.CallEffectFunc(id, "SetVisible", false)
end

---@public 隐藏固定类型特效
---@param id string 特效id
function GWSandEffectMgr.HideEffectByType(type)
    if not mc.enterSandScene then
        return
    end
    if not type then
        gw_admin.SwitchUtility.Error("HideEffectByType type is nil:")
        return
    end
    if not mc.effectUniqueIDList[type] then
        return
    end
    for k, v in pairs(mc.effectUniqueIDList[type]) do
        GWSandEffectMgr.HideEffect(k)
    end
end

---@public 隐藏固定类型特效
---@param type string 特效类型
function GWSandEffectMgr.ShowEffectByType(type)
    if not mc.enterSandScene then
        return
    end
    if not type then
        gw_admin.SwitchUtility.Error("HideEffectByType type is nil:")
        return
    end
    if not mc.effectUniqueIDList[type] then
        return
    end
    for k, v in pairs(mc.effectUniqueIDList[type]) do
        GWSandEffectMgr.ShowEffect(k)
    end
end

---@public 隐藏固定类型特效
function GWSandEffectMgr.HideAllEntityEffect()
    if not mc.enterSandScene then
        return
    end

    for k, v in pairs(gw_sand_effect_const.MountEntity) do
        for effectType, _ in pairs(v) do
            GWSandEffectMgr.HideEffectByType(effectType)
        end
    end
end

---@public 展示固定类型特效
function GWSandEffectMgr.ShowAllEntityEffect()
    if not mc.enterSandScene then
        return
    end
    for k, v in pairs(gw_sand_effect_const.MountEntity) do
        for effectType, _ in pairs(v) do
            GWSandEffectMgr.ShowEffectByType(effectType)
        end
    end
end

--生成唯一ID
function GWSandEffectMgr.CreateUniqueID()
    if not mc.uniqueId then
        mc.uniqueId = 1
    else
        mc.uniqueId = mc.uniqueId + 1
    end
    return mc.uniqueId
end

return GWSandEffectMgr