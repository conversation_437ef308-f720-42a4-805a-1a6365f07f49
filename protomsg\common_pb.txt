-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
local tbs_pb=require("tbs_pb")
module('common_pb')


V1M=V(4,"MATCH_MODE_SINGLE",0,0)
V2M=V(4,"MATCH_MODE_DOUBLE",1,1)
V3M=V(4,"MATCH_MODE_SEVERAL",2,2)
V4M=V(4,"MATCH_MODE_COUNTRY",3,3)
E1M=E(3,"MatchModeType",".CSMsg.MatchModeType")
V5M=V(4,"enGameMode_Battle",0,0)
V6M=V(4,"enGameMode_Season",1,1)
V7M=V(4,"enGameMode_Campaign",2,2)
E2M=E(3,"EnGameMode",".CSMsg.EnGameMode")
V8M=V(4,"enMatchModeSolo",0,0)
V9M=V(4,"enMatchModeSquad",1,1)
V10M=V(4,"enMatchModeGuide",2,2)
E3M=E(3,"EnMatchMode",".CSMsg.EnMatchMode")
V11M=V(4,"Unkonwn",0,0)
V12M=V(4,"fighting",1,1)
V13M=V(4,"Gunshot",2,2)
V14M=V(4,"Explode",3,3)
V15M=V(4,"Radiate",4,4)
V16M=V(4,"Traffic",5,5)
V17M=V(4,"Dashed",6,6)
V18M=V(4,"Wound",7,7)
V19M=V(4,"Headshot",8,8)
V20M=V(4,"Comedown",9,9)
V21M=V(4,"TankBomb",10,10)
V22M=V(4,"ZoneBomb",11,11)
V23M=V(4,"BurningBomb",12,12)
E4M=E(3,"enDamageType",".CSMsg.enDamageType")
V24M=V(4,"EMatchPlayMode_NationMode",0,1)
E5M=E(3,"EnPlayMode",".CSMsg.EnPlayMode")
V25M=V(4,"enPersonState_Offline",0,0)
V26M=V(4,"enPersonState_Online",1,1)
V27M=V(4,"enPersonState_Team",2,2)
V28M=V(4,"enPersonState_Gaming",3,3)
E6M=E(3,"EnLobbyPersonState",".CSMsg.EnLobbyPersonState")
V29M=V(4,"enGoldFingerState_Close",0,0)
V30M=V(4,"enGoldFingerState_Open",1,1)
E7M=E(3,"EnGoldFingerState",".CSMsg.EnGoldFingerState")
V31M=V(4,"enPersonGender_Male",0,1)
V32M=V(4,"enPersonGender_Female",1,2)
E8M=E(3,"EnPersonGender",".CSMsg.EnPersonGender")
V33M=V(4,"emTreasureRareNone",0,0)
V34M=V(4,"emTreasureRareRepeated",1,1)
V35M=V(4,"emTreasureRareAwaken",2,2)
V36M=V(4,"emLimitTimeTreasureRare",3,3)
E9M=E(3,"emTreasureRareConvertType",".CSMsg.emTreasureRareConvertType")
V37M=V(4,"enBattleType_Sandbox",0,1)
E10M=E(3,"enBattleType",".CSMsg.enBattleType")
V38M=V(4,"enItemFlag_Carriage_None",0,0)
V39M=V(4,"enItemFlag_Carriage_TakeEx",1,1)
V40M=V(4,"enItemFlag_Carriage_BeReca",2,2)
V41M=V(4,"enItemFlag_Carriage_BeLoot",3,3)
V42M=V(4,"enItemFlag_Carriage_LootEx",4,4)
V43M=V(4,"enItemFlag_Carriage_Base",5,5)
V44M=V(4,"enItemFlag_Carriage_BaseKJ",6,6)
E11M=E(3,"enItemFlag",".CSMsg.enItemFlag")
V45M=V(4,"emGWMC_SandboxMoveCityItem",0,3)
V46M=V(4,"emGWMC_StaminaMaxNum",1,6)
V47M=V(4,"emGWMC_StaminaRecoverTime",2,7)
V48M=V(4,"emGWMC_StaminaGetCnt",3,8)
V49M=V(4,"emGWMC_StaminaGetRecoverNum",4,9)
V50M=V(4,"emGWMC_StaminaGetRecoverCD",5,10)
V51M=V(4,"emGWMC_StaminaBuyInfo",6,11)
V52M=V(4,"emGWMC_StaminaBuyMaxCnt",7,12)
V53M=V(4,"emGWMC_StaminaItemInfo",8,13)
V54M=V(4,"emGWMC_SandboxKillMonType",9,14)
V55M=V(4,"emGWMC_SandboxRefreshTime",10,19)
V56M=V(4,"emGWMC_SandboxRoleInitExteriorId",11,32)
V57M=V(4,"emGWMC_SandboxRoleInitArea",12,33)
V58M=V(4,"emGWMC_SandboxAttackRoleConsume",13,35)
V59M=V(4,"emGWMC_SandboxMDayRewardCount",14,46)
V60M=V(4,"emGWMC_SandboxSDayRewardCount",15,47)
V61M=V(4,"emGWMC_SandboxCreateCityLevel",16,122)
V62M=V(4,"emGWMC_SandboxAllianceMarkDistance",17,144)
V63M=V(4,"emGWMC_SandboxMoveToZoneLevel",18,145)
V64M=V(4,"emGWMC_SandboxSiegeTreasureTimes",19,150)
E12M=E(3,"emGWMapConstantTyp",".CSMsg.emGWMapConstantTyp")
V65M=V(4,"INVALID_SANDBOX_SID",0,0)
V66M=V(4,"SANDBOX_SID_MULTIPLIER",1,10000000)
V67M=V(4,"SANDBOX_WIDTH",2,1000)
V68M=V(4,"SANDBOX_HEIGHT",3,1000)
V69M=V(4,"SANDBOX_TEAM_MAX",4,4)
V70M=V(4,"SANDBOX_DETECT_MAX",5,3)
V71M=V(4,"SANDBOX_GIRD_WIDTH",6,24)
V72M=V(4,"SANDBOX_GIRD_HEIGHT",7,24)
V73M=V(4,"EFFECT_BATTLEPROPID_MIN",8,1)
V74M=V(4,"EFFECT_BATTLEPROPID_MAX",9,300)
V75M=V(4,"EFFECT_PRODUCTIONPROPID_MIN",10,301)
V76M=V(4,"EFFECT_PRODUCTIONPROPID_MAX",11,800)
V77M=V(4,"EFFECT_FUNCTIONALID_MIN",12,1000)
V78M=V(4,"EFFECT_FUNCTIONALID_MAX",13,1100)
V79M=V(4,"EFFECT_PARAM_NUM",14,6)
V80M=V(4,"SOLDIER_LEVEL_MAX",15,10)
V81M=V(4,"SXNDBOX_WORLD_CFG_INDEX",16,8277)
V82M=V(4,"SXNDBOX_DESERT_CFG_INDEX",17,8278)
E13M=E(3,"emConstValue",".CSMsg.emConstValue")
V83M=V(4,"enCppMap_Delay",0,0)
V84M=V(4,"enCppMap_Huodong",1,1)
V85M=V(4,"enCppMap_Max",2,2)
E14M=E(3,"emCppMapType",".CSMsg.emCppMapType")
V86M=V(4,"enBattleResultAttacker_Invalid",0,0)
V87M=V(4,"enBattleResultAttacker_Win",1,1)
V88M=V(4,"enBattleResultDefender_Win",2,2)
V89M=V(4,"enBattleResult_Fail",3,3)
E15M=E(3,"emBattleResult",".CSMsg.emBattleResult")
V90M=V(4,"enTeamBattleStatus_Invalid",0,0)
V91M=V(4,"enTeamBattleStatus_Lose",1,1)
V92M=V(4,"enTeamBattleStatus_Win",2,2)
V93M=V(4,"enTeamBattleStatus_SoldierFail",3,3)
V94M=V(4,"enTeamBattleStatus_Standby",4,4)
V95M=V(4,"enTeamBattleStatus_OutOfLimit",5,5)
E16M=E(3,"emTeamBattleStatus",".CSMsg.emTeamBattleStatus")
V96M=V(4,"enBattleType_Invalid",0,0)
V97M=V(4,"enBattleType_PlayerVSPlayer",1,1)
V98M=V(4,"enBattleType_PlayerVSMonster",2,2)
V99M=V(4,"enBattleType_MonsterVSPlayer",3,3)
V100M=V(4,"enBattleType_CannonVSPlayer",4,4)
E17M=E(3,"emBattleType",".CSMsg.emBattleType")
V101M=V(4,"enSandboxTeamState_Invalid",0,0)
V102M=V(4,"enSandboxTeamState_Idle",1,1)
V103M=V(4,"enSandboxTeamState_Going",2,2)
V104M=V(4,"enSandboxTeamState_Backing",3,3)
V105M=V(4,"enSandboxTeamState_Collecting",4,4)
V106M=V(4,"enSandboxTeamState_Reinforcing",5,5)
V107M=V(4,"enSandboxTeamState_Massing",6,6)
V108M=V(4,"enSandboxTeamState_MassWait",7,7)
V109M=V(4,"enSandboxTeamState_Max",8,8)
E18M=E(3,"emTeamState",".CSMsg.emTeamState")
F1D=F(2,"x",".CSMsg.TVector2.x",1,0,2,false,.0,2,6)
F2D=F(2,"y",".CSMsg.TVector2.y",2,1,2,false,.0,2,6)
M1G=D(1,"TVector2",".CSMsg.TVector2",false,{},{},nil,{})
F3D=F(2,"x",".CSMsg.TVector3.x",1,0,2,false,.0,2,6)
F4D=F(2,"y",".CSMsg.TVector3.y",2,1,2,false,.0,2,6)
F5D=F(2,"z",".CSMsg.TVector3.z",3,2,2,false,.0,2,6)
M2G=D(1,"TVector3",".CSMsg.TVector3",false,{},{},nil,{})
F6D=F(2,"key",".CSMsg.TKeyValue.key",1,0,2,false,0,5,1)
F7D=F(2,"value",".CSMsg.TKeyValue.value",2,1,2,false,0,5,1)
M3G=D(1,"TKeyValue",".CSMsg.TKeyValue",false,{},{},nil,{})
F8D=F(2,"ItemID",".CSMsg.ItemEntity.ItemID",1,0,1,false,0,5,1)
F9D=F(2,"ItemNum",".CSMsg.ItemEntity.ItemNum",2,1,1,false,0,5,1)
F10D=F(2,"ItemFlag",".CSMsg.ItemEntity.ItemFlag",3,2,1,false,nil,14,8)
M4G=D(1,"ItemEntity",".CSMsg.ItemEntity",false,{},{},nil,{})
F11D=F(2,"items",".CSMsg.ItemEntityList.items",1,0,3,false,{},11,10)
M6G=D(1,"ItemEntityList",".CSMsg.ItemEntityList",false,{},{},nil,{})
F12D=F(2,"ItemID",".CSMsg.ItemConvertData.ItemID",1,0,2,false,0,5,1)
F13D=F(2,"ItemNum",".CSMsg.ItemConvertData.ItemNum",2,1,2,false,0,5,1)
F14D=F(2,"ConvertFromRewardID",".CSMsg.ItemConvertData.ConvertFromRewardID",3,2,2,false,0,5,1)
F15D=F(2,"TransforType",".CSMsg.ItemConvertData.TransforType",4,3,2,false,0,5,1)
M7G=D(1,"ItemConvertData",".CSMsg.ItemConvertData",false,{},{},nil,{})
F16D=F(2,"heroSid",".CSMsg.THeroBaseInfo.heroSid",1,0,2,false,0,13,3)
F17D=F(2,"heroID",".CSMsg.THeroBaseInfo.heroID",2,1,2,false,0,13,3)
F18D=F(2,"heroLevel",".CSMsg.THeroBaseInfo.heroLevel",3,2,2,false,0,13,3)
F19D=F(2,"heroPower",".CSMsg.THeroBaseInfo.heroPower",4,3,2,false,0,13,3)
F20D=F(2,"row",".CSMsg.THeroBaseInfo.row",5,4,2,false,0,13,3)
F21D=F(2,"col",".CSMsg.THeroBaseInfo.col",6,5,2,false,0,13,3)
F22D=F(2,"heroStar",".CSMsg.THeroBaseInfo.heroStar",7,6,1,false,0,13,3)
F23D=F(2,"realityHP",".CSMsg.THeroBaseInfo.realityHP",8,7,1,false,0,13,3)
F24D=F(2,"battleHP",".CSMsg.THeroBaseInfo.battleHP",9,8,1,false,0,13,3)
F25D=F(2,"remainHP",".CSMsg.THeroBaseInfo.remainHP",10,9,1,false,0,13,3)
F26D=F(2,"skinID",".CSMsg.THeroBaseInfo.skinID",11,10,1,false,0,13,3)
F27D=F(2,"soldierCapacity",".CSMsg.THeroBaseInfo.soldierCapacity",12,11,1,false,0,13,3)
F28D=F(2,"soldierNum",".CSMsg.THeroBaseInfo.soldierNum",13,12,1,false,0,13,3)
F29D=F(2,"soldierValid",".CSMsg.THeroBaseInfo.soldierValid",14,13,1,false,0,13,3)
F30D=F(2,"equipInfo",".CSMsg.THeroBaseInfo.equipInfo",15,14,3,false,{},11,10)
F31D=F(2,"skillInfo",".CSMsg.THeroBaseInfo.skillInfo",16,15,3,false,{},11,10)
M8G=D(1,"THeroBaseInfo",".CSMsg.THeroBaseInfo",false,{},{},nil,{})
F32D=F(2,"cfgId",".CSMsg.TReportEquipInfo.cfgId",1,0,1,false,0,13,3)
F33D=F(2,"level",".CSMsg.TReportEquipInfo.level",2,1,1,false,0,13,3)
F34D=F(2,"stars",".CSMsg.TReportEquipInfo.stars",3,2,1,false,0,13,3)
M9G=D(1,"TReportEquipInfo",".CSMsg.TReportEquipInfo",false,{},{},nil,{})
F35D=F(2,"cfgId",".CSMsg.TReportSkillInfo.cfgId",1,0,1,false,0,13,3)
F36D=F(2,"level",".CSMsg.TReportSkillInfo.level",2,1,1,false,0,13,3)
F37D=F(2,"stars",".CSMsg.TReportSkillInfo.stars",3,2,1,false,0,13,3)
M10G=D(1,"TReportSkillInfo",".CSMsg.TReportSkillInfo",false,{},{},nil,{})
F38D=F(2,"dbid",".CSMsg.TSimpleTeam.dbid",1,0,2,false,0,13,3)
F39D=F(2,"index",".CSMsg.TSimpleTeam.index",2,1,2,false,0,13,3)
F40D=F(2,"pals",".CSMsg.TSimpleTeam.pals",3,2,3,false,{},11,10)
F41D=F(2,"weaponID",".CSMsg.TSimpleTeam.weaponID",4,3,1,false,0,13,3)
F42D=F(2,"order",".CSMsg.TSimpleTeam.order",5,4,1,false,0,13,3)
F43D=F(2,"soldierInfo",".CSMsg.TSimpleTeam.soldierInfo",6,5,1,false,nil,11,10)
F44D=F(2,"power",".CSMsg.TSimpleTeam.power",7,6,1,false,0,4,4)
F45D=F(2,"weaponModelId",".CSMsg.TSimpleTeam.weaponModelId",8,7,1,false,0,13,3)
M11G=D(1,"TSimpleTeam",".CSMsg.TSimpleTeam",false,{},{},nil,{})
F46D=F(2,"dbid",".CSMsg.TReportTeam.dbid",1,0,2,false,0,13,3)
F47D=F(2,"index",".CSMsg.TReportTeam.index",2,1,2,false,0,13,3)
F48D=F(2,"pals",".CSMsg.TReportTeam.pals",3,2,3,false,{},11,10)
F49D=F(2,"weaponID",".CSMsg.TReportTeam.weaponID",4,3,1,false,0,13,3)
F50D=F(2,"order",".CSMsg.TReportTeam.order",5,4,1,false,0,13,3)
F51D=F(2,"soldierInfo",".CSMsg.TReportTeam.soldierInfo",6,5,1,false,nil,11,10)
F52D=F(2,"damageInfo",".CSMsg.TReportTeam.damageInfo",7,6,1,false,nil,11,10)
F53D=F(2,"isMonster",".CSMsg.TReportTeam.isMonster",8,7,1,false,false,8,7)
F54D=F(2,"teamPower",".CSMsg.TReportTeam.teamPower",9,8,1,false,nil,11,10)
F55D=F(2,"summonedInfo",".CSMsg.TReportTeam.summonedInfo",10,9,1,false,nil,11,10)
F56D=F(2,"technologyInfo",".CSMsg.TReportTeam.technologyInfo",11,10,3,false,{},11,10)
F57D=F(2,"technologyInfo1",".CSMsg.TReportTeam.technologyInfo1",12,11,1,false,0,13,3)
F58D=F(2,"decorateData",".CSMsg.TReportTeam.decorateData",13,12,3,false,{},11,10)
F59D=F(2,"honorWallData",".CSMsg.TReportTeam.honorWallData",14,13,3,false,{},11,10)
M13G=D(1,"TReportTeam",".CSMsg.TReportTeam",false,{},{},nil,{})
F60D=F(2,"heroEquip",".CSMsg.TTeamPower.heroEquip",1,0,1,false,0,13,3)
F61D=F(2,"heroSkill",".CSMsg.TTeamPower.heroSkill",2,1,1,false,0,13,3)
F62D=F(2,"summoned",".CSMsg.TTeamPower.summoned",3,2,1,false,0,13,3)
F63D=F(2,"technology",".CSMsg.TTeamPower.technology",4,3,1,false,0,13,3)
F64D=F(2,"decoration",".CSMsg.TTeamPower.decoration",5,4,1,false,0,13,3)
F65D=F(2,"soldier",".CSMsg.TTeamPower.soldier",6,5,1,false,0,13,3)
F66D=F(2,"honor",".CSMsg.TTeamPower.honor",7,6,1,false,0,13,3)
F67D=F(2,"heroTotal",".CSMsg.TTeamPower.heroTotal",8,7,1,false,0,13,3)
F68D=F(2,"troopTotal",".CSMsg.TTeamPower.troopTotal",9,8,1,false,0,13,3)
M15G=D(1,"TTeamPower",".CSMsg.TTeamPower",false,{},{},nil,{})
F69D=F(2,"selfInfo",".CSMsg.TSummonedInfo.selfInfo",1,0,1,false,nil,11,10)
F70D=F(2,"equipmentInfo",".CSMsg.TSummonedInfo.equipmentInfo",2,1,3,false,{},11,10)
F71D=F(2,"preferment_heroHP",".CSMsg.TSummonedInfo.preferment_heroHP",3,2,1,false,0,13,3)
F72D=F(2,"preferment_heroDef",".CSMsg.TSummonedInfo.preferment_heroDef",4,3,1,false,0,13,3)
F73D=F(2,"preferment_heroAtk",".CSMsg.TSummonedInfo.preferment_heroAtk",5,4,1,false,0,13,3)
F74D=F(2,"adornId",".CSMsg.TSummonedInfo.adornId",6,5,1,false,0,5,1)
F75D=F(2,"advanceLv",".CSMsg.TSummonedInfo.advanceLv",7,6,1,false,0,5,1)
F76D=F(2,"starScheme",".CSMsg.TSummonedInfo.starScheme",8,7,1,false,nil,11,10)
M16G=D(1,"TSummonedInfo",".CSMsg.TSummonedInfo",false,{},{},nil,{})
F77D=F(2,"weaponID",".CSMsg.TMagicWeaponInfo.weaponID",1,0,1,false,0,13,3)
F78D=F(2,"weaponLv",".CSMsg.TMagicWeaponInfo.weaponLv",2,1,1,false,0,13,3)
F79D=F(2,"weaponType",".CSMsg.TMagicWeaponInfo.weaponType",3,2,1,false,0,13,3)
F80D=F(2,"upgradeStage",".CSMsg.TMagicWeaponInfo.upgradeStage",4,3,1,false,0,13,3)
M20G=D(1,"TMagicWeaponInfo",".CSMsg.TMagicWeaponInfo",false,{},{},nil,{})
F81D=F(2,"cfgId",".CSMsg.TTechnologyInfo.cfgId",1,0,1,false,0,13,3)
F82D=F(2,"progress",".CSMsg.TTechnologyInfo.progress",2,1,1,false,0,13,3)
M17G=D(1,"TTechnologyInfo",".CSMsg.TTechnologyInfo",false,{},{},nil,{})
F83D=F(2,"soldierNum",".CSMsg.TSoldierData.soldierNum",1,0,3,false,{},13,3)
F84D=F(2,"soldierWound",".CSMsg.TSoldierData.soldierWound",2,1,3,false,{},13,3)
F85D=F(2,"soldierInjury",".CSMsg.TSoldierData.soldierInjury",3,2,3,false,{},13,3)
F86D=F(2,"soldierDead",".CSMsg.TSoldierData.soldierDead",4,3,3,false,{},13,3)
F87D=F(2,"soldiermorale",".CSMsg.TSoldierData.soldiermorale",5,4,3,false,{},4,4)
F88D=F(2,"backTimeStamp",".CSMsg.TSoldierData.backTimeStamp",6,5,1,false,0,4,4)
F89D=F(2,"palSoldierMax",".CSMsg.TSoldierData.palSoldierMax",7,6,3,false,{},13,3)
F90D=F(2,"palSoldierNum",".CSMsg.TSoldierData.palSoldierNum",8,7,3,false,{},13,3)
F91D=F(2,"palSoldierValid",".CSMsg.TSoldierData.palSoldierValid",9,8,3,false,{},13,3)
M12G=D(1,"TSoldierData",".CSMsg.TSoldierData",false,{},{},nil,{})
F92D=F(2,"roleid",".CSMsg.TBattleReportPlayerInfo.roleid",1,0,2,false,0,5,1)
F93D=F(2,"monsterid",".CSMsg.TBattleReportPlayerInfo.monsterid",2,1,1,false,0,5,1)
F94D=F(2,"worldID",".CSMsg.TBattleReportPlayerInfo.worldID",3,2,1,false,0,5,1)
F95D=F(2,"allianceId",".CSMsg.TBattleReportPlayerInfo.allianceId",4,3,1,false,0,5,1)
F96D=F(2,"userName",".CSMsg.TBattleReportPlayerInfo.userName",5,4,1,false,"",9,9)
F97D=F(2,"faceId",".CSMsg.TBattleReportPlayerInfo.faceId",6,5,1,false,0,13,3)
F98D=F(2,"x",".CSMsg.TBattleReportPlayerInfo.x",7,6,1,false,0,5,1)
F99D=F(2,"y",".CSMsg.TBattleReportPlayerInfo.y",8,7,1,false,0,5,1)
F100D=F(2,"power",".CSMsg.TBattleReportPlayerInfo.power",9,8,1,false,0,5,1)
F101D=F(2,"baselv",".CSMsg.TBattleReportPlayerInfo.baselv",10,9,1,false,0,13,3)
F102D=F(2,"bLeader",".CSMsg.TBattleReportPlayerInfo.bLeader",11,10,1,false,false,8,7)
F103D=F(2,"plunder",".CSMsg.TBattleReportPlayerInfo.plunder",12,11,1,false,0,13,3)
F104D=F(2,"faceFrameId",".CSMsg.TBattleReportPlayerInfo.faceFrameId",13,12,1,false,0,5,1)
F105D=F(2,"soldierMaxNum",".CSMsg.TBattleReportPlayerInfo.soldierMaxNum",14,13,1,false,0,13,3)
F106D=F(2,"cityID",".CSMsg.TBattleReportPlayerInfo.cityID",15,14,1,false,0,5,1)
F107D=F(2,"cityStatus",".CSMsg.TBattleReportPlayerInfo.cityStatus",16,15,1,false,0,5,1)
F108D=F(2,"cityHP",".CSMsg.TBattleReportPlayerInfo.cityHP",17,16,1,false,0,5,1)
F109D=F(2,"teamIndex",".CSMsg.TBattleReportPlayerInfo.teamIndex",18,17,1,false,0,13,3)
F110D=F(2,"faceStr",".CSMsg.TBattleReportPlayerInfo.faceStr",19,18,1,false,"",9,9)
F111D=F(2,"allianceShortName",".CSMsg.TBattleReportPlayerInfo.allianceShortName",20,19,1,false,"",9,9)
F112D=F(2,"sandboxid",".CSMsg.TBattleReportPlayerInfo.sandboxid",21,20,1,false,0,5,1)
M22G=D(1,"TBattleReportPlayerInfo",".CSMsg.TBattleReportPlayerInfo",false,{},{},nil,{})
F113D=F(2,"key",".CSMsg.TOneItemInfo.key",1,0,2,false,0,13,3)
F114D=F(2,"value",".CSMsg.TOneItemInfo.value",2,1,2,false,0,13,3)
M23G=D(1,"TOneItemInfo",".CSMsg.TOneItemInfo",false,{},{},nil,{})
F115D=F(2,"infoList",".CSMsg.TBattleReportItemInfo.infoList",1,0,3,false,{},11,10)
F116D=F(2,"dbid",".CSMsg.TBattleReportItemInfo.dbid",2,1,1,false,0,5,1)
M24G=D(1,"TBattleReportItemInfo",".CSMsg.TBattleReportItemInfo",false,{},{},nil,{})
F117D=F(2,"bLeader",".CSMsg.TOneTeamInfo.bLeader",1,0,2,false,false,8,7)
F118D=F(2,"bAttacker",".CSMsg.TOneTeamInfo.bAttacker",2,1,2,false,false,8,7)
F119D=F(2,"teamInfo",".CSMsg.TOneTeamInfo.teamInfo",3,2,2,false,nil,11,10)
M25G=D(1,"TOneTeamInfo",".CSMsg.TOneTeamInfo",false,{},{},nil,{})
F120D=F(2,"infoList",".CSMsg.TBattleReportTeamInfo.infoList",1,0,3,false,{},11,10)
M26G=D(1,"TBattleReportTeamInfo",".CSMsg.TBattleReportTeamInfo",false,{},{},nil,{})
F121D=F(2,"nTimeStamp",".CSMsg.TSpyMailInfo.nTimeStamp",1,0,2,false,0,4,4)
F122D=F(2,"fromInfo",".CSMsg.TSpyMailInfo.fromInfo",2,1,2,false,nil,11,10)
F123D=F(2,"toInfo",".CSMsg.TSpyMailInfo.toInfo",3,2,2,false,nil,11,10)
F124D=F(2,"itemInfo",".CSMsg.TSpyMailInfo.itemInfo",4,3,1,false,nil,11,10)
F125D=F(2,"teamInfo",".CSMsg.TSpyMailInfo.teamInfo",5,4,1,false,nil,11,10)
M27G=D(1,"TSpyMailInfo",".CSMsg.TSpyMailInfo",false,{},{},nil,{})
F126D=F(2,"bWin",".CSMsg.TBattleReportRoundInfo.bWin",1,0,2,false,false,8,7)
F127D=F(2,"battleID",".CSMsg.TBattleReportRoundInfo.battleID",2,1,2,false,"",9,9)
F128D=F(2,"atkInfo",".CSMsg.TBattleReportRoundInfo.atkInfo",3,2,2,false,nil,11,10)
F129D=F(2,"defInfo",".CSMsg.TBattleReportRoundInfo.defInfo",4,3,2,false,nil,11,10)
F130D=F(2,"roundID",".CSMsg.TBattleReportRoundInfo.roundID",5,4,2,false,0,13,3)
F131D=F(2,"battleType",".CSMsg.TBattleReportRoundInfo.battleType",6,5,2,false,0,13,3)
F132D=F(2,"defHospDeadList",".CSMsg.TBattleReportRoundInfo.defHospDeadList",7,6,1,false,nil,11,10)
F133D=F(2,"defHallDeadList",".CSMsg.TBattleReportRoundInfo.defHallDeadList",8,7,1,false,nil,11,10)
F134D=F(2,"turusNum",".CSMsg.TBattleReportRoundInfo.turusNum",9,8,1,false,0,5,1)
F135D=F(2,"stageType",".CSMsg.TBattleReportRoundInfo.stageType",10,9,1,false,0,13,3)
M28G=D(1,"TBattleReportRoundInfo",".CSMsg.TBattleReportRoundInfo",false,{},{},nil,{})
F136D=F(2,"nTimeStamp",".CSMsg.TBattleReportMailInfo.nTimeStamp",1,0,2,false,0,4,4)
F137D=F(2,"atkInfo",".CSMsg.TBattleReportMailInfo.atkInfo",2,1,3,false,{},11,10)
F138D=F(2,"defInfo",".CSMsg.TBattleReportMailInfo.defInfo",3,2,3,false,{},11,10)
F139D=F(2,"itemInfo",".CSMsg.TBattleReportMailInfo.itemInfo",4,3,3,false,{},11,10)
F140D=F(2,"roundInfo",".CSMsg.TBattleReportMailInfo.roundInfo",5,4,3,false,{},11,10)
F141D=F(2,"bAtkWin",".CSMsg.TBattleReportMailInfo.bAtkWin",6,5,2,false,false,8,7)
F142D=F(2,"nScoreChange",".CSMsg.TBattleReportMailInfo.nScoreChange",7,6,1,false,0,5,1)
M30G=D(1,"TBattleReportMailInfo",".CSMsg.TBattleReportMailInfo",false,{},{},nil,{})
F143D=F(2,"nTimeStamp",".CSMsg.TResourceMailInfo.nTimeStamp",1,0,2,false,0,4,4)
F144D=F(2,"x",".CSMsg.TResourceMailInfo.x",2,1,2,false,0,5,1)
F145D=F(2,"y",".CSMsg.TResourceMailInfo.y",3,2,2,false,0,5,1)
F146D=F(2,"itemInfo",".CSMsg.TResourceMailInfo.itemInfo",4,3,2,false,nil,11,10)
F147D=F(2,"resourceId",".CSMsg.TResourceMailInfo.resourceId",5,4,2,false,0,5,1)
M31G=D(1,"TResourceMailInfo",".CSMsg.TResourceMailInfo",false,{},{},nil,{})
F148D=F(2,"faceId",".CSMsg.TPlayerFaceInfo.faceId",1,0,1,false,0,13,3)
F149D=F(2,"faceFrameId",".CSMsg.TPlayerFaceInfo.faceFrameId",2,1,1,false,0,5,1)
F150D=F(2,"faceStr",".CSMsg.TPlayerFaceInfo.faceStr",3,2,1,false,"",9,9)
M32G=D(1,"TPlayerFaceInfo",".CSMsg.TPlayerFaceInfo",false,{},{},nil,{})
F151D=F(2,"carriageId",".CSMsg.TAllianceTrainCarriageData.carriageId",1,0,2,false,0,5,1)
F152D=F(2,"remainReward",".CSMsg.TAllianceTrainCarriageData.remainReward",2,1,3,false,{},5,1)
M33G=D(1,"TAllianceTrainCarriageData",".CSMsg.TAllianceTrainCarriageData",false,{},{},nil,{})

E1M.values = {V1M,V2M,V3M,V4M}
E2M.values = {V5M,V6M,V7M}
E3M.values = {V8M,V9M,V10M}
E4M.values = {V11M,V12M,V13M,V14M,V15M,V16M,V17M,V18M,V19M,V20M,V21M,V22M,V23M}
E5M.values = {V24M}
E6M.values = {V25M,V26M,V27M,V28M}
E7M.values = {V29M,V30M}
E8M.values = {V31M,V32M}
E9M.values = {V33M,V34M,V35M,V36M}
E10M.values = {V37M}
E11M.values = {V38M,V39M,V40M,V41M,V42M,V43M,V44M}
E12M.values = {V45M,V46M,V47M,V48M,V49M,V50M,V51M,V52M,V53M,V54M,V55M,V56M,V57M,V58M,V59M,V60M,V61M,V62M,V63M,V64M}
E13M.values = {V65M,V66M,V67M,V68M,V69M,V70M,V71M,V72M,V73M,V74M,V75M,V76M,V77M,V78M,V79M,V80M,V81M,V82M}
E14M.values = {V83M,V84M,V85M}
E15M.values = {V86M,V87M,V88M,V89M}
E16M.values = {V90M,V91M,V92M,V93M,V94M,V95M}
E17M.values = {V96M,V97M,V98M,V99M,V100M}
E18M.values = {V101M,V102M,V103M,V104M,V105M,V106M,V107M,V108M,V109M}
M1G.fields={F1D, F2D}
M2G.fields={F3D, F4D, F5D}
M3G.fields={F6D, F7D}
F10D.enum_type=M5G
M4G.fields={F8D, F9D, F10D}
F11D.message_type=M4G
M6G.fields={F11D}
M7G.fields={F12D, F13D, F14D, F15D}
F30D.message_type=M9G
F31D.message_type=M10G
M8G.fields={F16D, F17D, F18D, F19D, F20D, F21D, F22D, F23D, F24D, F25D, F26D, F27D, F28D, F29D, F30D, F31D}
M9G.fields={F32D, F33D, F34D}
M10G.fields={F35D, F36D, F37D}
F40D.message_type=M8G
F43D.message_type=M12G
M11G.fields={F38D, F39D, F40D, F41D, F42D, F43D, F44D, F45D}
F48D.message_type=M8G
F51D.message_type=M12G
F52D.message_type=tbs_pb.M71G
F54D.message_type=M15G
F55D.message_type=M16G
F56D.message_type=M17G
F58D.message_type=tbs_pb.M56G
F59D.message_type=tbs_pb.M57G
M13G.fields={F46D, F47D, F48D, F49D, F50D, F51D, F52D, F53D, F54D, F55D, F56D, F57D, F58D, F59D}
M15G.fields={F60D, F61D, F62D, F63D, F64D, F65D, F66D, F67D, F68D}
F69D.message_type=M20G
F70D.message_type=M20G
F76D.message_type=tbs_pb.M52G
M16G.fields={F69D, F70D, F71D, F72D, F73D, F74D, F75D, F76D}
M20G.fields={F77D, F78D, F79D, F80D}
M17G.fields={F81D, F82D}
M12G.fields={F83D, F84D, F85D, F86D, F87D, F88D, F89D, F90D, F91D}
M22G.fields={F92D, F93D, F94D, F95D, F96D, F97D, F98D, F99D, F100D, F101D, F102D, F103D, F104D, F105D, F106D, F107D, F108D, F109D, F110D, F111D, F112D}
M23G.fields={F113D, F114D}
F115D.message_type=M23G
M24G.fields={F115D, F116D}
F119D.message_type=M13G
M25G.fields={F117D, F118D, F119D}
F120D.message_type=M25G
M26G.fields={F120D}
F122D.message_type=M22G
F123D.message_type=M22G
F124D.message_type=M24G
F125D.message_type=M26G
M27G.fields={F121D, F122D, F123D, F124D, F125D}
F128D.message_type=M13G
F129D.message_type=M13G
F132D.message_type=tbs_pb.M2G
F133D.message_type=tbs_pb.M2G
M28G.fields={F126D, F127D, F128D, F129D, F130D, F131D, F132D, F133D, F134D, F135D}
F137D.message_type=M22G
F138D.message_type=M22G
F139D.message_type=M24G
F140D.message_type=M28G
M30G.fields={F136D, F137D, F138D, F139D, F140D, F141D, F142D}
F146D.message_type=M24G
M31G.fields={F143D, F144D, F145D, F146D, F147D}
M32G.fields={F148D, F149D, F150D}
M33G.fields={F151D, F152D}

BurningBomb = 12
Comedown = 9
Dashed = 6
EFFECT_BATTLEPROPID_MAX = 300
EFFECT_BATTLEPROPID_MIN = 1
EFFECT_FUNCTIONALID_MAX = 1100
EFFECT_FUNCTIONALID_MIN = 1000
EFFECT_PARAM_NUM = 6
EFFECT_PRODUCTIONPROPID_MAX = 800
EFFECT_PRODUCTIONPROPID_MIN = 301
EMatchPlayMode_NationMode = 1
Explode = 3
Gunshot = 2
Headshot = 8
INVALID_SANDBOX_SID = 0
ItemConvertData =M(M7G)
ItemEntity =M(M4G)
ItemEntityList =M(M6G)
MATCH_MODE_COUNTRY = 3
MATCH_MODE_DOUBLE = 1
MATCH_MODE_SEVERAL = 2
MATCH_MODE_SINGLE = 0
Radiate = 4
SANDBOX_DETECT_MAX = 3
SANDBOX_GIRD_HEIGHT = 24
SANDBOX_GIRD_WIDTH = 24
SANDBOX_HEIGHT = 1000
SANDBOX_SID_MULTIPLIER = 10000000
SANDBOX_TEAM_MAX = 4
SANDBOX_WIDTH = 1000
SOLDIER_LEVEL_MAX = 10
SXNDBOX_DESERT_CFG_INDEX = 8278
SXNDBOX_WORLD_CFG_INDEX = 8277
TAllianceTrainCarriageData =M(M33G)
TBattleReportItemInfo =M(M24G)
TBattleReportMailInfo =M(M30G)
TBattleReportPlayerInfo =M(M22G)
TBattleReportRoundInfo =M(M28G)
TBattleReportTeamInfo =M(M26G)
THeroBaseInfo =M(M8G)
TKeyValue =M(M3G)
TMagicWeaponInfo =M(M20G)
TOneItemInfo =M(M23G)
TOneTeamInfo =M(M25G)
TPlayerFaceInfo =M(M32G)
TReportEquipInfo =M(M9G)
TReportSkillInfo =M(M10G)
TReportTeam =M(M13G)
TResourceMailInfo =M(M31G)
TSimpleTeam =M(M11G)
TSoldierData =M(M12G)
TSpyMailInfo =M(M27G)
TSummonedInfo =M(M16G)
TTeamPower =M(M15G)
TTechnologyInfo =M(M17G)
TVector2 =M(M1G)
TVector3 =M(M2G)
TankBomb = 10
Traffic = 5
Unkonwn = 0
Wound = 7
ZoneBomb = 11
emGWMC_SandboxAllianceMarkDistance = 144
emGWMC_SandboxAttackRoleConsume = 35
emGWMC_SandboxCreateCityLevel = 122
emGWMC_SandboxKillMonType = 14
emGWMC_SandboxMDayRewardCount = 46
emGWMC_SandboxMoveCityItem = 3
emGWMC_SandboxMoveToZoneLevel = 145
emGWMC_SandboxRefreshTime = 19
emGWMC_SandboxRoleInitArea = 33
emGWMC_SandboxRoleInitExteriorId = 32
emGWMC_SandboxSDayRewardCount = 47
emGWMC_SandboxSiegeTreasureTimes = 150
emGWMC_StaminaBuyInfo = 11
emGWMC_StaminaBuyMaxCnt = 12
emGWMC_StaminaGetCnt = 8
emGWMC_StaminaGetRecoverCD = 10
emGWMC_StaminaGetRecoverNum = 9
emGWMC_StaminaItemInfo = 13
emGWMC_StaminaMaxNum = 6
emGWMC_StaminaRecoverTime = 7
emLimitTimeTreasureRare = 3
emTreasureRareAwaken = 2
emTreasureRareNone = 0
emTreasureRareRepeated = 1
enBattleResultAttacker_Invalid = 0
enBattleResultAttacker_Win = 1
enBattleResultDefender_Win = 2
enBattleResult_Fail = 3
enBattleType_CannonVSPlayer = 4
enBattleType_Invalid = 0
enBattleType_MonsterVSPlayer = 3
enBattleType_PlayerVSMonster = 2
enBattleType_PlayerVSPlayer = 1
enBattleType_Sandbox = 1
enCppMap_Delay = 0
enCppMap_Huodong = 1
enCppMap_Max = 2
enGameMode_Battle = 0
enGameMode_Campaign = 2
enGameMode_Season = 1
enGoldFingerState_Close = 0
enGoldFingerState_Open = 1
enItemFlag_Carriage_Base = 5
enItemFlag_Carriage_BaseKJ = 6
enItemFlag_Carriage_BeLoot = 3
enItemFlag_Carriage_BeReca = 2
enItemFlag_Carriage_LootEx = 4
enItemFlag_Carriage_None = 0
enItemFlag_Carriage_TakeEx = 1
enMatchModeGuide = 2
enMatchModeSolo = 0
enMatchModeSquad = 1
enPersonGender_Female = 2
enPersonGender_Male = 1
enPersonState_Gaming = 3
enPersonState_Offline = 0
enPersonState_Online = 1
enPersonState_Team = 2
enSandboxTeamState_Backing = 3
enSandboxTeamState_Collecting = 4
enSandboxTeamState_Going = 2
enSandboxTeamState_Idle = 1
enSandboxTeamState_Invalid = 0
enSandboxTeamState_MassWait = 7
enSandboxTeamState_Massing = 6
enSandboxTeamState_Max = 8
enSandboxTeamState_Reinforcing = 5
enTeamBattleStatus_Invalid = 0
enTeamBattleStatus_Lose = 1
enTeamBattleStatus_OutOfLimit = 5
enTeamBattleStatus_SoldierFail = 3
enTeamBattleStatus_Standby = 4
enTeamBattleStatus_Win = 2
fighting = 1

