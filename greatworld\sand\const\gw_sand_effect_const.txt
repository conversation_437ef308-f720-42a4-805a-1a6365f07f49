--- Created by fgy.
--- Changed by connan.
--- DateTime: 2024/10/09 
--- Des:特效配置，先写代码，后续看要不要配置
--- 修改日志:
--- 1. 拆分数据块，并且直接展示所有的可配置字段，提高一定的可读性，避免出现大量的空判断
--- 2. 新增了特效的延迟和layer数据
--- 3. Todo: 以后需要处理差异化数据

local EntityType = GWConst.ESEntityType

---@class GWSandEffectConst
local GWSandEffectConst = {}

GWSandEffectConst.EffectName = {
    --迁城特效
    MoveCity = "MoveCity",
    --预警特效
    Alert = "Alert",
    --基地着火
    BaseFire = "BaseFire",
    --基地灭火
    FireFight = "FireFight",
    --基地保护罩
    BaseProtect = "BaseProtect",
    --个人攻击
    Attack = "Attack",
    --个人攻击(怪物)
    AttackMonster = "AttackMonster",
    --胜利特效
    Victory = "Victory",
    --失败特效
    Defeat = "Defeat",
    --队伍着火
    TeamFire = "TeamFire",
    --增援特效
    Reinforce = "Reinforce",
    --集结组队特效
    MassAssort = "MassAssort",
    --集结攻击特效
    MassAttack = "MassAttack",
    --雷达资源采集点特效
    RadarResource = "RadarResource",
    --雷达宝藏特效
    RadarTreasureFlash = "RadarTreasureFlash",
    --货车着火特效
    CarriageFire = "CarriageFire",
    --中立城池保护罩特效
    NCProtect = "NCProtect",
    --巨炮保护罩特效
    NCProtect_BigGun = "NCProtect_BigGun",
    --中立城池着火特效
    NCFire = "NCFire",
    --通用特效 path 不同 使用相同postion 参数构建
    Common = "Common",
    --巨炮施法特效
    BigGunCast="BigGunCast",
    --巨炮子弹特效
    BigGunBullet="BigGunBullet",
    --巨炮受击特效
    BigGunHit="BigGunHit",
    --国会受击扣兵
    CongressReduceSoldier="CongressReduceSoldier",
    --沙漠风暴积分
    StormScore = "StormScore",
    --基地灭火
    ExtinguishFire = "ExtinguishFire",

    --技能特效
    KingShotSkillEffect = "KingShotSkillEffect",
    
    --跨服迁城
    CongressMoveCity_Red = "CongressMoveCity_Red",
    CongressMoveCity_Purple = "CongressMoveCity_Purple",
    CongressMoveCity_Blue = "CongressMoveCity_Blue",
    CongressMoveCity_Yellow = "CongressMoveCity_Yellow",
    CongressMoveCity_White = "CongressMoveCity_White",
}

GWSandEffectConst.EffectLayer = {
    -- 低于实体
    LowLayer = 1,
    -- 高于实体，低于Hud
    MidLayer = 2,
    -- 最高
    HighLayer = 3,
    -- Ecs
    EcsLayer = 4,
}


-- 基础特效配置
GWSandEffectConst.Effect = {
    [GWSandEffectConst.EffectName.MoveCity] = { resPath = "art/effects/effects/effect_scene_qiancheng_lan/prefabs/effect_scene_qiancheng_lan.prefab", delayTime = 0, showTime = 6, autoRemove = true },
    [GWSandEffectConst.EffectName.Alert] = { resPath = "art/effects/effects/effect_ui_yujing/prefabs/effect_ui_yujing.prefab", delayTime = 0, showTime = 0, autoRemove = false },
    [GWSandEffectConst.EffectName.BaseFire] = { resPath = "art/effects/effects/effect_model_zhaohuo/prefabs/effect_model_zhaohuo.prefab", delayTime = 0, showTime = 0, autoRemove = false },
    [GWSandEffectConst.EffectName.BaseProtect] = { resPath = "art/effects/effects/effect_model_baohuzhao/prefabs/effect_model_baohuzhao.prefab", delayTime = 0, showTime = 0, autoRemove = false },

    [GWSandEffectConst.EffectName.Attack] = { resPath = "art/effects/effects/effect_model_gerenjingong/prefabs/effect_model_gerenjingong.prefab", delayTime = 0, showTime = 0, autoRemove = false },
    [GWSandEffectConst.EffectName.AttackMonster] = { resPath = "art/effects/effects/effect_model_gerenjingongmonster/prefabs/effect_model_gerenjingongmonster.prefab", delayTime = 0, showTime = 0, autoRemove = false },

    [GWSandEffectConst.EffectName.Victory] = { resPath = "art/greatworld/sand/scenenew/effect/effect_victory.prefab", delayTime = 0.5, showTime = 3, autoRemove = true },
    [GWSandEffectConst.EffectName.Defeat] = { resPath = "art/greatworld/sand/scenenew/effect/effect_defeat.prefab", delayTime = 0.5, showTime = 3, autoRemove = true },
    [GWSandEffectConst.EffectName.TeamFire] = { resPath = "art/effects/effects/effect_model_zhanbai/prefabs/effect_model_zhanbai.prefab", delayTime = 0, showTime = 0, autoRemove = false },

    [GWSandEffectConst.EffectName.Reinforce] = { resPath = "art/effects/effects/effect_model_zengyuan/prefabs/effect_model_zengyuan.prefab", delayTime = 0, showTime = 2, autoRemove = true },
    [GWSandEffectConst.EffectName.MassAssort] = { resPath = "art/effects/effects/effect_scene_jijie/prefabs/effect_scene_jijie.prefab", delayTime = 0, showTime = 0, autoRemove = false },
    [GWSandEffectConst.EffectName.MassAttack] = { resPath = "art/effects/effects/effect_model_jijie/prefabs/effect_model_jijie.prefab", delayTime = 0, showTime = 0, autoRemove = false },

    [GWSandEffectConst.EffectName.RadarResource] = { resPath = "art/effects/effects/effect_model_leidawajue/prefabs/effect_model_leidawajue.prefab", delayTime = 0, showTime = 0, autoRemove = false },
    [GWSandEffectConst.EffectName.RadarTreasureFlash] = { resPath = "art/effects/effects/effect_ui_radar_box/prefabs/effect_ui_radar_box.prefab", delayTime = 0, showTime = 0, autoRemove = false },
    [GWSandEffectConst.EffectName.CarriageFire] = { resPath = "art/effects/effects/effect_model_huocheranshao/prefabs/effect_model_huocheranshao.prefab", delayTime = 0, showTime = 0, autoRemove = false },

    [GWSandEffectConst.EffectName.NCFire] = { resPath = "art/effects/effects/effect_ui_citycompetition_fire/prefabs/effect_ui_citycompetition_fire.prefab", delayTime = 0, showTime = 0, autoRemove = false },
    [GWSandEffectConst.EffectName.NCProtect] = { resPath = "art/effects/effects/effect_model_baohuzhao/prefabs/effect_model_baohuzhao.prefab", delayTime = 0, showTime = 0, autoRemove = false },
    [GWSandEffectConst.EffectName.NCProtect_BigGun] = { resPath = "art/effects/effects/effect_model_baohuzhao/prefabs/effect_model_baohuzhao.prefab", delayTime = 0, showTime = 0, autoRemove = false },

    [GWSandEffectConst.EffectName.KingShotSkillEffect] = { resPath = "art/effects/effects/effect_yangcongtouskill2-kingshot/prefabs/effect_yangcongtouskill2-kingshot.prefab", delayTime = 0, showTime = 3, autoRemove = false },
    
    [GWSandEffectConst.EffectName.BigGunCast]={resPath="art/effects/effects/effect_diaoxiangi_atk/prefabs/effect_diaoxiangi_atk.prefab",delayTime=0,showTime=1.5,autoRemove=true},
    [GWSandEffectConst.EffectName.BigGunBullet]={resPath="art/effects/effects/effect_diaoxiangi_fly/prefabs/effect_diaoxiangi_fly.prefab",delayTime=0,showTime=0.7,autoRemove=true},
    [GWSandEffectConst.EffectName.BigGunHit]={resPath="art/effects/effects/effect_diaoxiangi_hit/prefabs/effect_diaoxiangi_hit.prefab",delayTime=0,showTime=1.5,autoRemove=true},
    [GWSandEffectConst.EffectName.CongressReduceSoldier]={resPath="art/greatworld/sand/scenenew/hud/gwbiggunreducesoldier.prefab",delayTime=0,showTime=1,autoRemove=true},

    -- 跨服迁城特效
    [GWSandEffectConst.EffectName.CongressMoveCity_Red] = { resPath = "art/effects/effects/effect_scene_kqiancheng_hong/prefabs/effect_scene_kqiancheng_hong.prefab", delayTime = 0, showTime = 6, autoRemove = true },
    [GWSandEffectConst.EffectName.CongressMoveCity_Purple] = { resPath = "art/effects/effects/effect_scene_kqiancheng_fen/prefabs/effect_scene_kqiancheng_fen.prefab", delayTime = 0, showTime = 6, autoRemove = true },
    [GWSandEffectConst.EffectName.CongressMoveCity_Blue] = { resPath = "art/effects/effects/effect_scene_kqiancheng_lan/prefabs/effect_scene_kqiancheng_lan.prefab", delayTime = 0, showTime = 6, autoRemove = true },
    [GWSandEffectConst.EffectName.CongressMoveCity_Yellow] = { resPath = "art/effects/effects/effect_scene_kqiancheng_huang/prefabs/effect_scene_kqiancheng_huang.prefab", delayTime = 0, showTime = 6, autoRemove = true },
    [GWSandEffectConst.EffectName.CongressMoveCity_White] = { resPath = "art/effects/effects/effect_scene_kqiancheng_bai/prefabs/effect_scene_kqiancheng_bai.prefab", delayTime = 0, showTime = 6, autoRemove = true },

    -- 沙漠风暴
    [GWSandEffectConst.EffectName.StormScore] = { resPath = "art/effects/effects/effect_ui_desert_jfbjx/prefabs/effect_ui_desert_jfbjx.prefab", delayTime = 0, showTime = 2, autoRemove = true },

    [GWSandEffectConst.EffectName.ExtinguishFire] = { resPath = "art/effects/effects/effect_model_miehuo/prefabs/effect_model_miehuo.prefab", delayTime = 0, showTime = 1.5, autoRemove = true },
}

-- 基础特效属性
GWSandEffectConst.EffectBase = {
    [GWSandEffectConst.EffectName.MoveCity] = { order = 600, layer = GWSandEffectConst.EffectLayer.HighLayer, position = Vector3(0, 0, 0), rotate = Vector3(40, 0, 0), offset = Vector3(0, 0, 0), scale = 0.007 },
    [GWSandEffectConst.EffectName.Alert] = { order = 600, layer = GWSandEffectConst.EffectLayer.HighLayer, position = Vector3(0, 0, 0), rotate = Vector3(0, 0, 0), offset = Vector3(0, -0.2, 0), scale = 1 },
    [GWSandEffectConst.EffectName.BaseFire] = { order = 500, layer = GWSandEffectConst.EffectLayer.MidLayer, position = Vector3(0, 0, 0), rotate = Vector3(0, 0, 0), offset = Vector3(0, 0, 0), scale = 1 },
    [GWSandEffectConst.EffectName.BaseProtect] = { order = 500, layer = GWSandEffectConst.EffectLayer.MidLayer, position = Vector3(0, 0, 0), rotate = Vector3(0, 0, 0), offset = Vector3(0, 0, 0), scale = 1 },

    [GWSandEffectConst.EffectName.Attack] = { order = 500, layer = GWSandEffectConst.EffectLayer.MidLayer, position = Vector3(0, 0, 0), rotate = Vector3(0, 0, 0), offset = Vector3(0, 0, 0), scale = 1 },
    [GWSandEffectConst.EffectName.AttackMonster] = { order = 500, layer = GWSandEffectConst.EffectLayer.MidLayer, position = Vector3(0, 0, 0), rotate = Vector3(0, 0, 0), offset = Vector3(0, 0, 0), scale = 1 },
    [GWSandEffectConst.EffectName.Victory] = { order = 700, layer = GWSandEffectConst.EffectLayer.HighLayer, position = Vector3(0, 0, 0), rotate = Vector3(0, 0, 0), offset = Vector3(0, 2, 0), scale = 1 },
    [GWSandEffectConst.EffectName.Defeat] = { order = 700, layer = GWSandEffectConst.EffectLayer.HighLayer, position = Vector3(0, 0, 0), rotate = Vector3(0, 0, 0), offset = Vector3(0, 2, 0), scale = 1 },
    [GWSandEffectConst.EffectName.TeamFire] = { order = 500, layer = GWSandEffectConst.EffectLayer.MidLayer, position = Vector3(0, 0, 0), rotate = Vector3(0, 0, 0), offset = Vector3(0, 0, 0), scale = 1 },
    [GWSandEffectConst.EffectName.Reinforce] = { order = 500, layer = GWSandEffectConst.EffectLayer.MidLayer, position = Vector3(0, 0, 0), rotate = Vector3(0, 0, 0), offset = Vector3(0, 0, 0), scale = 1 },
    [GWSandEffectConst.EffectName.MassAssort] = { order = 500, layer = GWSandEffectConst.EffectLayer.HighLayer, position = Vector3(0, 0, 0), rotate = Vector3(0, 0, 0), offset = Vector3(0, 1.5, 0), scale = 1 },
    [GWSandEffectConst.EffectName.MassAttack] = { order = 500, layer = GWSandEffectConst.EffectLayer.MidLayer, position = Vector3(0, 0, 0), rotate = Vector3(0, 0, 0), offset = Vector3(0, 0, 0), scale = 1 },
    [GWSandEffectConst.EffectName.RadarResource] = { order = 500, layer = GWSandEffectConst.EffectLayer.MidLayer, position = Vector3(0, 0, 0), rotate = Vector3(-40, 0, 0), offset = Vector3(0, 0, 0), scale = 0.006 },
    [GWSandEffectConst.EffectName.RadarTreasureFlash] = { order = 500, layer = GWSandEffectConst.EffectLayer.MidLayer, position = Vector3(0, 0, 0), rotate = Vector3(0, 0, 0), offset = Vector3(-0.3, 0.8, 0), scale = 0.4 },
    [GWSandEffectConst.EffectName.CarriageFire] = { order = 500, layer = GWSandEffectConst.EffectLayer.HighLayer, position = Vector3(0, 0, 0), localRotate = Vector3(0, 0, 0), offset = Vector3(0, 0, 0), scale = 1 },
    [GWSandEffectConst.EffectName.NCFire] = { order = 500, layer = GWSandEffectConst.EffectLayer.MidLayer, position = Vector3(0, 0, 0), rotate = Vector3(0, 0, 0), offset = Vector3(0, 0, 0), scale = 1 },
    [GWSandEffectConst.EffectName.NCProtect] = { order = 500, layer = GWSandEffectConst.EffectLayer.MidLayer, position = Vector3(0, 0, 0), rotate = Vector3(0, 0, 0), offset = Vector3(0, -0.6, 0), scale = 3 },
    [GWSandEffectConst.EffectName.NCProtect_BigGun] = { order = 500, layer = GWSandEffectConst.EffectLayer.MidLayer, position = Vector3(0, 0, 0), rotate = Vector3(0, 0, 0), offset = Vector3(0, -0.6, 0), scale = 1.5 },
    [GWSandEffectConst.EffectName.BigGunCast]={order=500,layer=GWSandEffectConst.EffectLayer.MidLayer,position=Vector3(0,0,0),rotate=Vector3(0,0,0),offset=Vector3(0,0,0),scale=1},
    [GWSandEffectConst.EffectName.BigGunBullet]={order=500,layer=GWSandEffectConst.EffectLayer.MidLayer,position=Vector3(0,0,0),rotate=Vector3(0,0,0),offset=Vector3(0,0,0),scale=1},
    [GWSandEffectConst.EffectName.BigGunHit]={order=500,layer=GWSandEffectConst.EffectLayer.MidLayer,position=Vector3(0,0,0),rotate=Vector3(0,0,0),offset=Vector3(0,0,0),scale=1},
    [GWSandEffectConst.EffectName.CongressReduceSoldier]={order=500,layer=GWSandEffectConst.EffectLayer.MidLayer,position=Vector3(0,0,0),rotate=Vector3(0,0,0),offset=Vector3(0,0,0),scale=1},
    [GWSandEffectConst.EffectName.Common] = { order = 500, layer = GWSandEffectConst.EffectLayer.HighLayer, position = Vector3(0, 0, 0), rotate = Vector3(0, 0, 0), offset = Vector3(0, 0, 0), scale = 1 },
    -- 跨服迁城特效
    [GWSandEffectConst.EffectName.CongressMoveCity_Red] = { order = 600, layer = GWSandEffectConst.EffectLayer.HighLayer, position = Vector3(0, 0, 0), rotate = Vector3(0, 0, 0), offset = Vector3(0, 0, 0), scale = 0.7 },
    [GWSandEffectConst.EffectName.CongressMoveCity_Purple] = { order = 600, layer = GWSandEffectConst.EffectLayer.HighLayer, position = Vector3(0, 0, 0), rotate = Vector3(0, 0, 0), offset = Vector3(0, 0, 0), scale = 0.7 },
    [GWSandEffectConst.EffectName.CongressMoveCity_Blue] = { order = 600, layer = GWSandEffectConst.EffectLayer.HighLayer, position = Vector3(0, 0, 0), rotate = Vector3(0, 0, 0), offset = Vector3(0, 0, 0), scale = 0.7 },
    [GWSandEffectConst.EffectName.CongressMoveCity_Yellow] = { order = 600, layer = GWSandEffectConst.EffectLayer.HighLayer, position = Vector3(0, 0, 0), rotate = Vector3(0, 0, 0), offset = Vector3(0, 0, 0), scale = 0.7 },
    [GWSandEffectConst.EffectName.CongressMoveCity_White] = { order = 600, layer = GWSandEffectConst.EffectLayer.HighLayer, position = Vector3(0, 0, 0), rotate = Vector3(0, 0, 0), offset = Vector3(0, 0, 0), scale = 0.7 },

    [GWSandEffectConst.EffectName.KingShotSkillEffect] =  { order = 600, layer = GWSandEffectConst.EffectLayer.HighLayer, position = Vector3(0, 0, 0), rotate = Vector3(0, 0, 0), offset = Vector3(0, -0.2, 0), scale = 1 },
    
    -- 沙漠风暴
    [GWSandEffectConst.EffectName.StormScore] = { order = 600, layer = GWSandEffectConst.EffectLayer.HighLayer, position = Vector3(0, 0, 0), rotate = Vector3(0, 0, 0), offset = Vector3(0, 0, 0), scale = 0.4 },

    [GWSandEffectConst.EffectName.ExtinguishFire] = { order = 600, layer = GWSandEffectConst.EffectLayer.HighLayer, position = Vector3(0, 0, 0), rotate = Vector3(0, 40, 0), offset = Vector3(0, 0, 0), scale = 0.4 },
}

-- 挂载特效，需要监听对应的方法
GWSandEffectConst.MountEntity = {
    [EntityType.Base] = {
        [GWSandEffectConst.EffectName.BaseFire] = { props = "baseBurn", propsValue = 1 },
        [GWSandEffectConst.EffectName.BaseProtect] = { props = "baseSafetyState", propsValue = 1, },
        [GWSandEffectConst.EffectName.MassAssort] = { props = "massAssort", propsValue = 1, },
    },
    [EntityType.RadarCollection] = {
        [GWSandEffectConst.EffectName.RadarResource] = { props = "isRadarCreate", propsValue = 1, },
    },
    [EntityType.RadarTreasure] = {
        [GWSandEffectConst.EffectName.RadarTreasureFlash] = { props = "isRadarCreate", propsValue = 1, },
    },
    [EntityType.NeutralCity] = {
        [GWSandEffectConst.EffectName.NCFire] = { props = "baseBurn", propsValue = 1, },
        [GWSandEffectConst.EffectName.NCProtect] = { props = "baseSafety", propsValue = 1, },
        [GWSandEffectConst.EffectName.NCProtect_BigGun]= { props = "baseSafety_bigGun", propsValue = 1, },
    },
    [EntityType.Storm] = {
        [GWSandEffectConst.EffectName.NCProtect] = { props = "baseSafetyState", propsValue = 1, },
    },
}

return GWSandEffectConst