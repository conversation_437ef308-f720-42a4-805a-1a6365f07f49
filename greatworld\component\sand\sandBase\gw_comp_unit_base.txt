--- Created by fgy
--- DateTime: 2024/8/26 14:33
--- Des: 沙盘实体基类

local require = require
local newclass = newclass
local table = table
local ipairs = ipairs
local log = require "gw_sand_log"

local gw_gpu_animation_uitl = require "gw_gpu_animation_uitl"
local sand_entity_base = require "sand_entity_base"
local EntityManager = require "entity_manager"
local ESEntityType = gw_const.ESEntityType
local string = string
local util = require "util"
local gw_sand_effect_const = require "gw_sand_effect_const"
local gw_sand_effect_param = require "gw_sand_effect_param"
local gw_const = require "gw_const"
local Vector3 = CS.UnityEngine.Vector3
module("gw_comp_unit_base")
---@class GWCompUnitBase : GWDisposableObject
local GWCompUnitBase = newclass("gw_comp_unit_base", sand_entity_base)

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--- 构造器
function GWCompUnitBase:ctor()
    sand_entity_base.ctor(self)
end

function GWCompUnitBase:InitComp(data)
    if not data.resPath then
        log.Error("data.resPath = nil " .. Edump(data))
    else
        self.hudData.enableEcs = EntityManager.SpriteEcs
        sand_entity_base.InstantiateModelAsync(self,data.resPath, data.parent)
    end
    self:RegisterListener()
end

---@public 加载完成回调
---@see override
function GWCompUnitBase:OnLoaded()
    if self.transform then
        self.transform:GetChild(0).gameObject.name = self.compId
        gw_gpu_animation_uitl.ResetGpuAnimatorStandState(self.transform)
    elseif self.hybridTrans then
        self.hybridTrans:GetChild(0).gameObject.name = self.compId
    end
    sand_entity_base.OnLoaded(self)
    self:SetScale(1)
    self:ResetPos()
    self:UpdateEffect()
    -- 重置动画播放
end

function GWCompUnitBase:OnUpdateData()
    self:AddLoadEvent(function()
        self:UpdateEffect()
    end)    
    sand_entity_base.OnUpdateData(self)
end

function GWCompUnitBase:ResetPos()
    if self.effectEntityList then
        for i, entity in ipairs(self.effectEntityList) do
            if entity then
                if not self.effectPos then
                    self.effectPos = Vector3.zero
                end
                entity:SetLocalPosition(self.serData.pos.x + self.effectPos.x, 0, self.serData.pos.y + self.effectPos.y)
            end
        end
    end
    sand_entity_base.ResetPos(self)
end

function GWCompUnitBase:UpdateEffect()
    if not self.serData then
        return
    end
    
    local type = self.serData.type

    if type ~= ESEntityType.Base then
        return
    end
    local viewLevelType = self.serData:GetFinalKey2()
    if viewLevelType ~= gw_const.ESEntityResViewType.Base then
        return
    end
    local effectId = self.serData:GetEffectId()
    if not effectId then
        return
    end
    if self.effectId and self.effectId == effectId then
        return
    end
    self:DisposeEffect()
    self.effectId = effectId
    local helper_personalInfo = require "helper_personalInfo"
    local effectResPath,_id,effectScale,effectPos,effectBottomPath = helper_personalInfo.GetSchlossEffectPrefabPath(helper_personalInfo.SchlossPrefabType.sand,effectId)
    self.effectPos = effectPos
    self:PlayEffect(effectBottomPath,effectPos,effectScale,gw_sand_effect_const.EffectLayer.EcsLayer)
    --播放顶部效果
    self:PlayEffect(effectResPath,effectPos,effectScale,gw_sand_effect_const.EffectLayer.EcsLayer)
end

function GWCompUnitBase:PlayEffect(path, effectPos, effectScale, effectLayer)
    if string.IsNullOrEmpty(path) then
        return
    end
    local gw_effect_object = require "gw_effect_object"
    local newEffectParams = {
        resPath = path, --读取特效装扮
        delayTime = 0,
        showTime = 0,
        autoRemove = false
    }
    local gw_sand_effect_mgr = require "gw_sand_effect_mgr"
    local createEffectBaseParams = gw_sand_effect_param.CreateEffectBaseParam(Vector3(self.serData.pos.x + effectPos.x, 0, self.serData.pos.y + effectPos.y))
    createEffectBaseParams.layer = effectLayer
    local newEffectBaseParams = gw_sand_effect_param.NewEffectBaseParam(gw_sand_effect_const.EffectName.Common, createEffectBaseParams)
    newEffectBaseParams.scale = effectScale
    local effectParent = gw_sand_effect_mgr.GetEffectLayerParent(newEffectBaseParams.layer)
    if util.IsObjNull(effectParent) then
        return
    end
    local effectEntity = gw_effect_object.CM("gw_effect_object"):Init(99999, effectParent, newEffectParams, newEffectBaseParams)
    if not self.effectEntityList then
        self.effectEntityList = {}
    end
    table.insert(self.effectEntityList, effectEntity)
end

function GWCompUnitBase:DisposeEffect()
    if self.effectEntityList then
        for i, entity in ipairs(self.effectEntityList) do
            if entity then
                entity:Dispose()
                entity = nil
            end
        end
    end
    self.effectEntityList = nil
end

function GWCompUnitBase:RegisterListener()
    if self.serData then
        local viewLevelType = self.serData:GetFinalKey2()
        if not viewLevelType or viewLevelType ~= gw_const.ESEntityResViewType.Base then
            return
        end
        self.OnUpdateDataEvent = function()
            self:OnUpdateData()
        end
        if self.props then
            self.serData.props:AddListener(self.props, self.OnUpdateDataEvent, self)
        end
    end
end

function GWCompUnitBase:UnRegisterListener()
    if self.serData and self.OnUpdateDataEvent then
        if self.props then
            self.serData.props:RemoveListener(self.props, self.OnUpdateDataEvent)
        end
    end
end

--- 回收
function GWCompUnitBase:Recycle()
    sand_entity_base.Recycle(self)
end

--- 弃用
function GWCompUnitBase:Dispose(unloadShowOnly)
    self.effectId = nil
    self.effectPos = nil
    self:DisposeEffect()
    self:UnRegisterListener()
    sand_entity_base.Dispose(self, unloadShowOnly)
end

return GWCompUnitBase
