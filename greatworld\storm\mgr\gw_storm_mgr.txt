--- Created by fgy.
--- DateTime: 2024/5/30 12:13
--- Des:大世界家园配置 只处理配置

local require = require
local pairs = pairs
local ipairs = ipairs
local debug = debug
local os = os
local xpcall = xpcall
local event_alliance_define = require "event_alliance_define"
local game_scheme = require "game_scheme"
local gw_const = require "gw_const"
local net_DesertStrom_module = require "net_DesertStrom_module"
local net_city_module = require "net_city_module"
local event_DesertStrom_define = require("event_DesertStrom_define")
local gw_storm_data_score
local event = require "event"
local util = require "util"
local sandbox_pb
local gw_map_util = require "gw_map_util"
local gw_sand_event_define = require "gw_sand_event_define"
local sandBuff_module_data = require "sandBuff_module_data"
local log = require "log"
local ui_window_mgr = require "ui_window_mgr"
local math = require("math")
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

---@class GWStormMgr
module("gw_storm_mgr")
local GWStormMgr = {}
local self = GWStormMgr --简化写法，静态类中直接也用Self获取自身
local EVENT_HANDLERS = {}
local EVENT_HANDLERS_SHOW = nil --有生命周期，进入战场才添加监听

local RedDot = false --报名阶段的弱红点，因为现有活动弱红点不能满足需求
local isEnterDesertStorm = false --是否已经进入沙漠风暴？该类自己维护生命周期

local historyStartPos = 1
local actionListStartPos = 1

local eachHistoryPageCount = 30 --一次拉多少个
local eachActionPageCount = 30

local maxHistoryCount = -1 --最大上限，-1表示还没开始
local maxActionCount = -1

function GWStormMgr.Init()
    --活动相关的，不卸载
    EVENT_HANDLERS =
    {
        [event_DesertStrom_define.TMSG_DESERTSTROM_ACTIVITY_NTF] = self.OnSetActivityData,
        [event_DesertStrom_define.TMSG_DESERTSTROM_SIGNUP_INFO_NTF] = self.OnSetSignUpData,
        [event_DesertStrom_define.TMSG_DESERTSTROM_MATCH_INFO_NTF] = self.OnSetMatchInfo,
        [event_DesertStrom_define.TMSG_DESERTSTROM_BATTLE_STATUS_NTF] = self.SetBattleInfo,
        [event_DesertStrom_define.TMSG_DESERTSTROM_BATTLE_SID_NTF] = self.OnSetBattleSid,
        [event_DesertStrom_define.TMSG_DESERTSTROM_RANK_SELF_NTF] = self.SetSelfScore,
        [event_DesertStrom_define.TMSG_DESERTSTROM_SCORE_SELF_RSP] = self.SetSelfScore,
        [event_DesertStrom_define.TMSG_DESERTSTROM_MOVE_CD_RSP] = self.SetMoveCityCd,
        [event_DesertStrom_define.ON_DESERTSTROM_ACTIVITY_UPDATE] = self.ShowClockDown,
        [event_DesertStrom_define.DESERTSTROM_SIGNUP_INFO_UPDATE] = self.ShowClockDown,
        [event_alliance_define.EXIT_ALLIANCE] = self.ResetSignUpInfo,
        [event_DesertStrom_define.TMSG_DESERTSTROM_LOG_RSP] = self.OnSetActionList,
        [event_DesertStrom_define.TMSG_DESERTSTROM_RECORD_RSP] = self.OnSetHistory,
    }
    self.RegisterEvents()
    self.scoreBoardTimer = nil
    RedDot = true
end

function GWStormMgr.RegisterEventsShow()
    --战场相关的，没在沙漠风暴不监听
    if not EVENT_HANDLERS_SHOW then
        EVENT_HANDLERS_SHOW = {
            [event_DesertStrom_define.TMSG_BATTLE_HOSPITAL_SOLDIER_RSP] = self.OnSetFieldHospitalData,
            [event_DesertStrom_define.TMSG_DESERTSTROM_RANK_PUBLIC_RSP] = self.OnSetRank,
            [event_DesertStrom_define.TMSG_DESERTSTROM_BATTLE_RESULT_NTF] = self.OnSetBattleResult,
            [event_DesertStrom_define.TMSG_DESERTSTROM_BATTLE_LEAVE_NTF] = self.KickOutStorm,
            [gw_sand_event_define.GW_SAND_FUNCTION_TRIGGER] = self.MoveCityResponse,
            [event.EVENT_SAND_ENTITY_UPDATE] = self.UpdateSandBoxEntity,
            [event.EVENT_SAND_COMP_ENTITY_UPDATE_DELAY] = self.InitSandBoxEntiy,
        }
        for eventName,handler in pairs(EVENT_HANDLERS_SHOW) do
            event.Register(eventName,handler)
        end
    end
end

function GWStormMgr.ResetSignUpInfo()
    self.GetStormDataActivity().ResetSignUpInfoAndMatchInfo()
end

function GWStormMgr.ResetHistory()
    historyStartPos = 1
    maxHistoryCount = -1
    self.GetStormData().ResetHistory()
end

function GWStormMgr.ResetActionRecord()
    actionListStartPos = 1
    maxActionCount = -1
    self.GetStormData().ResetActionRecord()
end

function GWStormMgr.OnSetHistory(_,msg)
    self.GetStormData().SetHistory(msg)
    if #msg.list <= 0 then
        historyStartPos = 0
    else
        historyStartPos = msg.list[#msg.list].index
    end
    maxHistoryCount = msg.index_max
end

function GWStormMgr.OnGetMaxHistoryCount()
    return maxHistoryCount
end

function GWStormMgr.OnCheckHistoryCount(index) --检测当前是否需要立即拉取新的数据
    if historyStartPos - index < eachHistoryPageCount / 2 then
        self:OnGetHistoryReq()
    end
end

function GWStormMgr.OnCheckActionRecordCount(index) --检测当前是否需要立即拉取新的数据
    if actionListStartPos - index < eachActionPageCount / 2 then
        self:OnGetActionListReq()
    end
end

function GWStormMgr.OnSetActionList(_,msg)
    self.GetStormData().SetActionList(msg.log)
    if #msg.log <= 0 then
        actionListStartPos = 0
    else
        actionListStartPos = msg.log[#msg.log].index
    end

    maxActionCount = msg.index_max
    event.Trigger(event_DesertStrom_define.MSG_DESERTSTROM_LOG_UPDATE)
end

function GWStormMgr.OnGetMaxActionRecordCount()
    return maxActionCount
end

function GWStormMgr.OnGetHistoryReq()
    if historyStartPos < maxHistoryCount or maxHistoryCount == -1 then
        local sendData =
        {
            index_beg = historyStartPos,
            index_end = maxHistoryCount <= 0 and eachHistoryPageCount or math.min(historyStartPos + eachHistoryPageCount,maxHistoryCount)
        }
        net_DesertStrom_module.MSG_DESERTSTROM_RECORD_REQ(sendData)
    end
end

function GWStormMgr.OnGetActionListReq()
    if actionListStartPos < maxActionCount or maxActionCount == -1 then
        local sendData =
        {
            index_beg = actionListStartPos,
            index_end = maxActionCount <= 0 and eachActionPageCount or math.min(actionListStartPos + eachActionPageCount,maxActionCount)
        }
        net_DesertStrom_module.MSG_DESERTSTROM_LOG_REQ(sendData)
    end
end

function GWStormMgr.OnGetHistoryData()
    return self.GetStormData().GetHistoryData()
end

function GWStormMgr.OnGetHistory(index)
    return self.GetStormData().GetHistory(index)
end

function GWStormMgr.OnGetActionList(index)
    return self.GetStormData().GetActionList(index)
end

function GWStormMgr.ShowClockDown()
    if isEnterDesertStorm then
        return
    end
    if not GWStormMgr.CheckStormOpen() then
        --没有开放
        if ui_window_mgr:GetWindowObj("ui_desert_storm_activity_tips_panel") then
            ui_window_mgr:UnloadModule("ui_desert_storm_activity_tips_panel")
        end
        return
    end
    local activityData = self.GetStormDataActivity()
    local activityTimeData = activityData.GetActivityTimeData()
    local finishTime = activityTimeData and activityTimeData.endTime - os.server_time() or 0
    if finishTime > 0 then
        if activityData.OnCheckSignUpState() then
            local signUpInfo = activityData.OnGetSignUpInfo()
            local timeList = activityData.OnGetBattleTimeList()[signUpInfo.teamData[event_DesertStrom_define.EnDesertStrom_Team.enDesertStrom_Team_A].battleTimeIndex]
            if timeList then
                local startTime = timeList.battleTime - os.server_time()
                local endTime = timeList.endTime - os.server_time()
                local temp = ui_window_mgr:GetWindowObj("ui_main_slg")
                if temp and temp.rtsf_tipsTrans then
                    if self.scoreBoardTimer then
                        util.RemoveDelayCall(self.scoreBoardTimer)
                        self.scoreBoardTimer = nil
                    end
                    local showData =
                    {
                        uiParent = temp and temp.rtsf_tipsTrans or nil,
                        timeList = timeList
                    }
                    if startTime <= 5 * 60 and endTime > 0 then
                        ui_window_mgr:ShowModule("ui_desert_storm_activity_tips_panel",nil,nil,showData)
                    else
                        local startTimeTicker = startTime - (5 * 60)
                        self.scoreBoardTimer = util.DelayCallOnce(startTimeTicker,function()
                            ui_window_mgr:ShowModule("ui_desert_storm_activity_tips_panel",nil,nil,showData)
                        end)
                    end
                end
            end
        end
    else
        if ui_window_mgr:GetWindowObj("ui_desert_storm_activity_tips_panel") then
            ui_window_mgr:UnloadModule("ui_desert_storm_activity_tips_panel")
        end
    end
end

function GWStormMgr.GetIsStorm()
    return isEnterDesertStorm
end

--打点通用
function GWStormMgr.ReportEvent(eventName, params)
    params = params or {}
    event.Trigger(event.GAME_EVENT_REPORT, eventName, params)
end

--是否开启沙漠风暴（开关，兼容没开的服务器版本）
function GWStormMgr.CheckStormOpen()
    local net_module_open = require "net_module_open"
    local moduleOpenPro_pb = require "moduleOpenPro_pb"
    local isOpen = net_module_open.CheckModuleOpen(moduleOpenPro_pb.emModuleID_DesertStorm)
    return isOpen
	-- local val = require "val"
    -- local value = val.IsTrue("sw_storm_open", 1) --or val.IsTrue("sw_open_guide", 0)
    -- return value
end

function GWStormMgr.SetRedDotState(value)
    RedDot = value
end

function GWStormMgr.OnShowReadyTips()
    local player_prefs = require "player_prefs"
    local showTime = player_prefs.GetCacheData("desertStormReadyTipsShowTime",0) --缓存当前的打开时间
    local showTips = true
    if showTime ~= 0 then
        local cutTime = os.server_time()
        if cutTime - showTime < 1800 then --上次打开的时间距离现在少于30分钟，则表示当前的沙漠风暴和上一次沙漠风暴是同一场
            showTips = false
        end
    end
    if showTips then
        ui_window_mgr:ShowModule("ui_desert_storm_ready_tips_panel",nil,function()
            --触发新手引导 (新手引导内部判断)
            local force_guide_system = require "force_guide_system"
            local force_guide_event = require "force_guide_event"
            force_guide_system.TriEnterEvent(force_guide_event.tEventDesertNotify)
        end)
    else
        local force_guide_system = require "force_guide_system"
        local force_guide_event = require "force_guide_event"
        force_guide_system.TriEnterEvent(force_guide_event.tEventDesertNotify)
    end

end

function GWStormMgr.GetRedDotState()
    return RedDot
end

function GWStormMgr.GetMoveCityCdReq()
    net_DesertStrom_module.MSG_DESERTSTROM_MOVE_CD_REQ()
end

function GWStormMgr.SetMoveCityCd(_,msg)
    local player_mgr = require "player_mgr"
    if msg.roleDbid == player_mgr.GetPlayerRoleID() then
        GWStormMgr.GetStormData().SetMoveCityCd(msg.cdTime)
    end
end

---------获取相关部件----------
function GWStormMgr.GetStormCfg()
    if not GWStormMgr.stormCfg then
        local GWStormCfg = require "gw_storm_cfg"
        GWStormMgr.stormCfg = GWStormCfg
        GWStormMgr.stormCfg.Init()
    end
    return GWStormMgr.stormCfg
end

function GWStormMgr.GetStormData()
    if not GWStormMgr.stormData then
        local GWStormData = require "gw_storm_data"
        GWStormMgr.stormData = GWStormData
        GWStormMgr.stormData.Init()
    end
    return GWStormMgr.stormData
end

function GWStormMgr.GetStormDataActivity()
    if not GWStormMgr.stormDataActivity then
        local GWStormDataActivity = require "gw_storm_data_activity"
        GWStormMgr.stormDataActivity = GWStormDataActivity
        GWStormMgr.stormDataActivity.Init() 
    end
    return GWStormMgr.stormDataActivity
end

---------生命周期-------------start
--进入沙漠风暴（发协议）
function GWStormMgr.EnterStorm()
    if not GWStormMgr.CheckStormOpen() then
        --没有开放
        return
    end
    --local gw_sand_mgr = require "gw_sand_mgr"
    local stormData = self.GetStormData()
    if  stormData then
        local sandboxSid = stormData.GetSandboxSid()
        if sandboxSid then
            local gw_common_util = require "gw_common_util"
            gw_common_util.SwitchToStorm(nil, nil, sandboxSid)
            local gw_popups_trigger_mgr = require "gw_popups_trigger_mgr"
            gw_popups_trigger_mgr.SetAllowShowPopus(false)
            self.RegisterEventsShow()
        else
            log.Error("EnterStorm 进入沙漠风暴失败 找不到sandboxSid = ",sandboxSid)
        end
    else
        log.Error("EnterStorm 进入沙漠风暴失败 找不到stormData")
    end
end

--踢出沙盘（推出联盟等）
function GWStormMgr.KickOutStorm(_,msg)
    if msg and msg.sandboxSid then
        local gw_sand_data = require "gw_sand_data"
        local sandSid = gw_sand_data.selfData.GetVisualSandBoxSid()
        if msg.sandboxSid == sandSid then
            self.ExitStorm()
            log.Error("KickOutStorm 被服务器异常退出沙漠风暴，sandboxSid = ",msg.sandboxSid)
        end
    end
end

--退出沙漠风暴（发协议）
function GWStormMgr.ExitStorm()
    local main_slg_mgr = require "main_slg_mgr"
    main_slg_mgr.OpenCitySceneSwitch(nil,
        function()
            net_DesertStrom_module.MSG_DESERTSTROM_BATTLE_LEAVE_REQ()
        end
    )
end

function GWStormMgr.MoveCityResponse(_, event_name, msg)
    if event_name == "MoveCityResponse" then
        if msg.errCode ~= 0 then
            -- 迁城失败
        else
            local main_slg_data = require "main_slg_data"
            if main_slg_data.GetCurSceneType() == gw_const.ESceneType.Storm then
                self.GetMoveCityCdReq()
            end
        end
    end
end

--打开沙漠风暴主界面(场景切换成功)
function GWStormMgr.ShowWndStorm()
    isEnterDesertStorm = true
    local gw_storm_data_battle = require "gw_storm_data_battle"
    gw_storm_data_battle.Init()
    
    local sxTeamType = gw_const.SandTeamType.Storm
    net_city_module.MSG_CITY_GET_ALL_TROOP_REQ(sxTeamType); --拉取队伍请求
    
    ui_window_mgr:ShowModule("ui_desert_storm_slg",function()
        self.WndStormLoaded()
    end,function()
        self.WndStormClose()
    end)
    if ui_window_mgr:GetWindowObj("ui_desert_storm_activity_tips_panel") then
        ui_window_mgr:UnloadModule("ui_desert_storm_activity_tips_panel")
    end
    local sand_ui_event_define = require "sand_ui_event_define"
    event.Trigger(sand_ui_event_define.GW_MAIN_CHAT_BUBBLE_SHOW,true)
    event.Trigger(event_alliance_define.MAIN_MASS_BUBBLE_REFRESH)
end
local AutoOpen = {
    "ui_storm_mini_map",--小地图
    "ui_storm_important_info",--重要信息界面
}
--沙漠风暴主界面加载出来之后
function GWStormMgr.WndStormLoaded()
    --打开小地图
    local wnd = ui_window_mgr:GetWindowObj("ui_desert_storm_slg")
    if wnd and wnd.rootTrans then
        for k,v in ipairs(AutoOpen) do
            ui_window_mgr:ShowModule(v, nil, nil, { uiParent = wnd.tf_bottomLayer })
        end
    end

    self.OnShowReadyTips()
    
    self.OnGetMatchInfo() --获取一次匹配的数据，避免直接跳过活动界面直接进沙盘拿不到匹配数据
    self.OnGetSelfScore() --获取一次自己的个人积分
    self.GetMoveCityCdReq() --请求获取一次cd
    self.OnGetFieldHospitalData() --获取一次战地医院数据
    event.Trigger(event.REFRESH_ARENA_BUBBLE_SHOW)
end

--沙漠风暴主界面关闭
function GWStormMgr.WndStormClose()
    --关闭小地图
    for k,v in ipairs(AutoOpen) do
        ui_window_mgr:UnloadModule(v)
    end

    local gw_popups_trigger_mgr = require "gw_popups_trigger_mgr"
    gw_popups_trigger_mgr.SetAllowShowPopus(true)
    isEnterDesertStorm = false
    event.Trigger(event.POPUPS_CHECK_STATE)
    self.ShowClockDown()

    local sand_ui_event_define = require "sand_ui_event_define"
    event.Trigger(sand_ui_event_define.GW_MAIN_CHAT_BUBBLE_SHOW)
    event.Trigger(event_alliance_define.MAIN_MASS_BUBBLE_REFRESH)
end
--退出状态机沙漠风暴（清空数据等操作）
function GWStormMgr.ExitStormScene()
    gw_storm_data_score.Clear()
    local gw_storm_data_important = require "gw_storm_data_important"
    gw_storm_data_important.Clear()

    local gw_storm_data_battle = require "gw_storm_data_battle"
    gw_storm_data_battle.Clear()

    --队伍数据战场清空
    local sand_team_data = require "sand_team_data"
    sand_team_data.ClearDataByType(gw_const.SandTeamType.Storm)

    self.UnregisterEventsShow()
    event.Trigger(event.REFRESH_ARENA_BUBBLE_SHOW)
end

-- 沙漠风暴打开战斗地图
function GWStormMgr.OpenBattleMapView()
    local wnd = ui_window_mgr:GetWindowObj("ui_desert_storm_slg")
    if wnd and wnd.rootTrans then
        ui_window_mgr:ShowModule("ui_storm_battle_map", nil, nil, { uiParent = wnd.tf_centerLayer })
    end
end


---------生命周期-------------end


---------协议相关-------------start
function GWStormMgr.RegisterEvents()
    for eventName,handler in pairs(EVENT_HANDLERS) do
        event.Register(eventName,handler)
    end
end

function GWStormMgr.UnregisterEvents()
    for eventName,handler in pairs(EVENT_HANDLERS) do
        event.Unregister(eventName,handler)
    end
end

function GWStormMgr.UnregisterEventsShow()
    if EVENT_HANDLERS_SHOW then
        for eventName,handler in pairs(EVENT_HANDLERS_SHOW) do
            event.Unregister(eventName,handler)
        end
        EVENT_HANDLERS_SHOW = nil
    end
end
--取到战斗结算数据
function GWStormMgr.OnSetBattleResult(_,msg)
    local gw_storm_mgr = require "gw_storm_mgr"
    local sendData =
    {
        iswin = msg.result1.win == true,
        guildpoint_num = msg.result1.own.alliance_score,
        personpoint_num = gw_storm_mgr.GetSelfScore(),
        UnionB_Enemyid = msg.result1.target.alliance.allianceId,
        enemy_guildpoint_num = msg.result1.target.alliance_score,
    }
    gw_storm_mgr.ReportEvent("DesertStorm_Ending",sendData)
    local gw_mgr = require "gw_mgr"
    --if gw_mgr.GetFsmCurScene() == gw_const.ESceneType.Storm then --只有当前在沙漠风暴时会触发
        ui_window_mgr:ShowModule("ui_desert_storm_result_panel",nil,nil,msg)
    --end
end

function GWStormMgr.SetSelfScore(_,msg)
    gw_storm_data_score =  gw_storm_data_score or require "gw_storm_data_score"
    gw_storm_data_score.SetSelfScore(msg)
end

function GWStormMgr.GetTotalSoldierCount()
    return self.GetStormData().GetTotalSoldierCount()
end

function GWStormMgr.GetHurtSoldierCount()
    return self.GetStormData().GetHurtCount()
end

--主动查询联盟积分
function GWStormMgr.OnGetAllianceScore(allianceId)
    net_DesertStrom_module.TMSG_DESERTSTROM_SCORE_ALLIANCE_REQ(allianceId)
end

function GWStormMgr.OnGetSelfScore()
    local alliance_data = require("alliance_data")
    local userAllianceData = alliance_data.GetUserAllianceData()
    if userAllianceData and userAllianceData.allianceId ~= nil then
        net_DesertStrom_module.TMSG_DESERTSTROM_SCORE_SELF_REQ(userAllianceData.allianceId)
    else
        log.Error("获取不到个人的联盟ID！！！")
    end
end

function GWStormMgr.GetSelfScore()
    gw_storm_data_score =  gw_storm_data_score or require "gw_storm_data_score"
    return gw_storm_data_score.GetSelfScore()
end

function GWStormMgr.OnGetSelfScoreGroup()
    gw_storm_data_score =  gw_storm_data_score or require "gw_storm_data_score"
    return gw_storm_data_score.GetSelfScoreGroup()
end

function GWStormMgr.OnGetSelfMaxScoreGroup()
    gw_storm_data_score =  gw_storm_data_score or require "gw_storm_data_score"
    return gw_storm_data_score.GetSelfMaxScoreGroup()
end

--查询积分，参数表示查询的是自己的还是敌方的
function GWStormMgr.OnGetRank(allianceId)
    local data =
    {
        allianceId = allianceId
    }
    net_DesertStrom_module.MSG_DESERTSTROM_RANK_PUBLIC_REQ(data)
end

function GWStormMgr.OnSetRank(_,msg)
    self.GetStormData().SetRank(msg.allianceId,msg)
end

function GWStormMgr.SetBattleInfo(_,msg)
    self.GetStormData().SetBattleInfo(msg)
end

function GWStormMgr.OnSetBattleSid(_,msg)
    self.GetStormData().SetBattleSid(msg)
end

function GWStormMgr.OnSetMatchInfo(_,msg)
    self.GetStormDataActivity().OnSetMatchInfo(msg)
end

function GWStormMgr.OnSetSignUpData(_,msg)
    self.GetStormDataActivity().OnSetSignUpData(msg)
end

function GWStormMgr.OnSetActivityData(_,msg)
    self.GetStormDataActivity().OnSetActivityData(msg)
end

function GWStormMgr.OnSetDesertStormSoldierData(msg)
    self.GetStormData().UpdateTrainningCenterData(msg.trainning.soldiers[1])
end

function GWStormMgr.OnSetHospitalData(msg)
    self.GetStormData().UpdateHospitalData(msg)
end

--主动请求个人医院数据
function GWStormMgr.OnGetPersonalHospitalData()
    local data =
    {
        nPperType = event_DesertStrom_define.EnDSHospitalOperType.enDSHospitalOperType_Req
    }
    net_DesertStrom_module.MSG_DESERT_STROM_PERSON_HOSPITAL_REQ(data)
end

--治疗士兵，在个人医院
function GWStormMgr.OnTreatSoldierAtPersonalHospital(count)
    local soldierId = self.GetStormData().GetSoldierId()
    local data =
    {
        {
            id = soldierId,
            count = count
        }
    }
    net_city_module.MSG_CITY_HOSPITAL_CURE_SOLDIER_REQ(data,gw_const.enSandboxType.enSandboxType_Desert)
end

--领取治疗完成的士兵
function GWStormMgr.OnGetSoldierAtPersonalHospital()
    net_city_module.MSG_CITY_HOSPITAL_GET_SOLDIER_REQ(gw_const.enSandboxType.enSandboxType_Desert)
end

--主动请求战地医院数据
function GWStormMgr.OnGetFieldHospitalData()
    net_DesertStrom_module.MSG_BATTLE_HOSPITAL_SOLDIER_REQ()
end

--领取战地医院里治疗完成的士兵
function GWStormMgr.OnGetSoldierAtBattleHospital()
    net_DesertStrom_module.MSG_BATTLE_HOSPITAL_SOLDIER_GET_REQ()
end

function GWStormMgr.OnSetFieldHospitalData(_,msg)
    self.GetStormData().UpdateFieldHospitalData(msg.hospital)
end

--主动获取报名数据。
function GWStormMgr.GetSignUpData()
    net_DesertStrom_module.MSG_DESERTSTROM_SIGNUP_INFO_REQ()
end

--请求报名。（目前只有A组，其他组后面再拓展）若没有报错，则会返回TMSG_DESERTSTROM_SIGNUP_INFO_NTF
function GWStormMgr.SignUpAction()
    local data =
    {
        enTeam = event_DesertStrom_define.EnDesertStrom_Team.enDesertStrom_Team_A
    }
    net_DesertStrom_module.MSG_DESERTSTROM_SIGNUP_ACTION_REQ(data)
end

--请求选择参战时间，若没有报错则额外返回TMSG_DESERTSTROM_SIGNUP_INFO_NTF
function GWStormMgr.OnSelectBattleTime(battleTimeIndex)
    local data =
    {
        enTeam = event_DesertStrom_define.EnDesertStrom_Team.enDesertStrom_Team_A,
        battle_time_index = battleTimeIndex,
    }
    net_DesertStrom_module.MSG_DESERTSTROM_SIGNUP_SELECT_TIME_REQ(data)
end

--设置参赛成员，若没有报错则额外返回TMSG_DESERTSTROM_SIGNUP_INFO_NTF
function GWStormMgr.OnSetMember(dbid,main,value)
    local data =
    {
        dbid = dbid,
        enTeam = value and event_DesertStrom_define.EnDesertStrom_Team.enDesertStrom_Team_A or event_DesertStrom_define.EnDesertStrom_Team.enDesertStrom_Team_None,
        main = main,
    }
    net_DesertStrom_module.MSG_DESERTSTROM_SIGNUP_SET_MEMBER_REQ(data)
end

--设置自己期望的参赛时间(数组)，若没有报错则额外返回TMSG_DESERTSTROM_SIGNUP_INFO_NTF
function GWStormMgr.OnSetTimeHope(timeList)
    net_DesertStrom_module.MSG_DESERTSTROM_SIGNUP_TIME_HOPE_REQ(timeList)
end

--请求获取匹配数据。
function GWStormMgr.OnGetMatchInfo()
    local data =
    {
        enTeam = event_DesertStrom_define.EnDesertStrom_Team.enDesertStrom_Team_A
    }
    net_DesertStrom_module.MSG_DESERTSTROM_MATCH_INFO_REQ(data)
end

function GWStormMgr.OnSetTimeHopeRsp(_,msg)
    
end

---------协议相关-------------end
function GWStormMgr.Dispose()
    self.UnregisterEvents()
    if self.scoreBoardTimer then
        util.RemoveDelayCall(self.scoreBoardTimer)
        self.scoreBoardTimer = nil;
    end
    RedDot = false
end

--沙盘相关的接口
function GWStormMgr.GetResIdByPb(previewpb)
    if previewpb and previewpb.cfgId then
        local cfgId = previewpb.cfgId
        local cfg = game_scheme:DesertStormBuilding_0(cfgId)
        return self.GetResIdByCfg(cfg)
    end
    return nil
end
function GWStormMgr.GetResIdByCfg(cfg)
    if cfg and cfg.resourceID then
        return cfg.resourceID.data[0]
    end
    return nil
end

--获取建筑加成速度(speed+buff)
function GWStormMgr.GetSpeedByEntity(entity)
    -- if entity and entity.cfg then
    --     local speed = entity.cfg.PersonalPoints.data[0]
    --     local buffAdd = GWStormMgr.GetSpeedBuff()
    --     return speed*(1+buffAdd)
    -- end
    -- return 0
    gw_storm_data_score =  gw_storm_data_score or require "gw_storm_data_score"
    return gw_storm_data_score.GetBuildSpeed(entity.cfg.Building)
end

function GWStormMgr.GetSpeedByCfgId(cfgId,addBuff)
    local cfg = game_scheme:DesertStormBuilding_0(cfgId)
    if  cfg then
        return self.GetSpeedByCfg(cfg,addBuff)
    end
    return 0
end
function GWStormMgr.GetSpeedByCfg(cfg,addBuff)
    local speed = cfg.PersonalPoints.data[0]
    if addBuff then
        speed = speed*(1+self.GetSpeedBuff())
    end
    return speed
end
--获取建筑加成速度(固定+可掠夺)
function GWStormMgr.GetDetailSpeedByEntity(entity)
    gw_storm_data_score =  gw_storm_data_score or require "gw_storm_data_score"
    return gw_storm_data_score.GetBuildSpeedDetail(entity.cfg.Building)
end

function GWStormMgr.GetSpeedBuff()
    return self.GetBuffValue(800005)
end

function GWStormMgr.GetScoreByEntity(entity)
    gw_storm_data_score =  gw_storm_data_score or require "gw_storm_data_score"
    return gw_storm_data_score.GetBuildScoreCurTime(entity.cfg.Building)
end

function GWStormMgr.GetDetailScoreByEntity(entity)
    gw_storm_data_score =  gw_storm_data_score or require "gw_storm_data_score"
    return gw_storm_data_score.GetBuildScoreCurTimeDetail(entity.cfg.Building)
end

--沙漠风暴实体更新（油井用，自己管理一套实体）
function GWStormMgr.UpdateSandBoxEntity(_,msg)
    local gw_common_util = require "gw_common_util"
    if not gw_common_util.CheckInSand_Storm() then
        --只有沙漠风暴才需要
        return
    end
    if msg.SandboxEntity then
        for _, v in ipairs(msg.SandboxEntity) do
            if v.type and v.type == gw_const.ESEntityType.Storm then
                GWStormMgr.UpdateSandBoxEntityOne(v.sid)
            end
        end
    end
    if msg.removeSid then
        for _, sid in ipairs(msg.removeSid) do
            gw_storm_data_score = gw_storm_data_score or require "gw_storm_data_score"
            gw_storm_data_score.UpdateOilEntity(sid,true)
        end
    end
end

function GWStormMgr.UpdateSandBoxEntityOne(sid)
    local entity = gw_map_util.GetServerDataBySid(sid)
    if entity and entity.GetStormAllianceId then
        local aliId = entity:GetStormAllianceId()
        --有占领联盟才计入
        if  aliId and aliId>0 then
            if  entity.cfg and entity.cfg.BuildingTypes == event_DesertStrom_define.STORM_BUILDING_TYPE.OilWell then
                gw_storm_data_score =  gw_storm_data_score or require "gw_storm_data_score"
                gw_storm_data_score.UpdateOilEntity(entity.sid,false)
            end
        end
    end
end

function GWStormMgr.InitSandBoxEntiy()
    local gw_map_util = require "gw_map_util"
    local list = gw_map_util.GetEntityList()
    for i,v in ipairs(list or {}) do
        self.UpdateSandBoxEntityOne(v.sid)
    end
end

function GWStormMgr.GetEntityBypb(pb)
    local sandBoxEntity, aliId,cfgId
    sandBoxEntity = pb
    sandbox_pb = sandbox_pb or require "sandbox_pb"
    for i = 1, #sandBoxEntity.propType do
        if sandBoxEntity.propType[i] == sandbox_pb.enSXEntityProp_DesertStorm_BelongAllianceId then
            aliId = sandBoxEntity.propValue[i]
        elseif sandBoxEntity.propType[i] == sandbox_pb.enSXEntityProp_Cfg_Id then
            cfgId = sandBoxEntity.propValue[i]
        end
    end
    local entity = {
        sid = pb.sid,
        type = pb.type,
        stormAliId = aliId,
        cfg = game_scheme:DesertStormBuilding_0(cfgId),
    }
    return entity
end
--沙漠风暴实体更新（删除的时候用）
function GWStormMgr.UpdateSandBoxEntityRemove(sid)
    gw_storm_data_score =  gw_storm_data_score or require "gw_storm_data_score"
    gw_storm_data_score.UpdateOilEntity(sid,true)
end

--迁城cd
function GWStormMgr.GetMoveCityCD()
    return GWStormMgr.GetStormData().GetMoveCityCd()
end

--是否可以迁城
function GWStormMgr.CheckCanMoveCity()
    local tarTime = self.GetMoveCityCD()
    return os.server_time() >= tarTime
end

--获取当前时间状态（准备阶段-战斗阶段-结束阶段）
function GWStormMgr.GetCurTimeState()
    local curTime = os.server_time()
    local state = event_DesertStrom_define.Battle_Time_State.Null
    local timeEntity = self.GetStormDataActivity().OnGetSelfTeamBattleTimeData()
    if curTime >= timeEntity.endTime  then
        state = event_DesertStrom_define.Battle_Time_State.End
    elseif  curTime >= timeEntity.battleTime then
        state = event_DesertStrom_define.Battle_Time_State.Battle
    elseif  curTime >= timeEntity.enterTime  then
        state = event_DesertStrom_define.Battle_Time_State.Before
    end
    return state
end

function GWStormMgr.CheckBuiildOpen(entity)
    if not entity then
        log.Error("CheckBuiildOpen 传入的entity为nil ",debug.traceback)
        return false
    end
    local unlockTime =  entity:GetStormUnlockTime()
    if  unlockTime and unlockTime>0 then
        local serTime = os.server_time()
        if  serTime<unlockTime then
            return false
        end
    end
    return true
end

--请求建筑队列
function GWStormMgr.SendBuildDefend(entitySid)
    local gw_sand_data = require "gw_sand_data"
    local sandSid = gw_sand_data.selfData.GetVisualSandBoxSid()
    if sandSid then
        net_DesertStrom_module.TMSG_DESERTSTROM_BUILDING_DEFEND_REQ(sandSid,entitySid)
    else
        log.Error("SendBuildDefend 战场沙盘id为nil ",debug.traceback)
    end
end

function GWStormMgr.GetBuffValue(buffId)
    local cfg = sandBuff_module_data.GetBuffData_ByCfgId(buffId)
    if not cfg or not cfg.buffCfg then
        return 0 
    end
    local data = cfg.buffCfg.effect
    local effect = game_scheme:GWMapEffect_0(data)
    if effect then
        return effect.strParam / 10000
    end
    return 0
end

--跳转
function GWStormMgr.JumpByEntity(entity)
    if entity and entity.pos then
        local gw_sand_mgr = require "gw_sand_mgr"
        gw_sand_mgr.JumpToTargetGrid({ x = entity.pos.x, y = entity.pos.y })
    end
end

function GWStormMgr.GetEntityByBuildType(buildType)
    local list = gw_map_util.GetEntityList()
    for i,v in ipairs(list or {}) do
        local entity = gw_map_util.GetServerDataBySid(v.sid)
        if entity and entity.type and entity.type == gw_const.ESEntityType.Storm then
            if entity.cfg and entity.cfg.Building then
                if entity.cfg.BuildingTypes == buildType then
                    return entity
                end
            end
        end
    end
    return nil
end

function GWStormMgr.CheckIsMyAlli(entity)
    local state = entity:GetStormBuildStage()
    if state == event_DesertStrom_define.Storm_Own_State.Friend then
        return true
    end
    return false
end

GWStormMgr.Init()
return GWStormMgr